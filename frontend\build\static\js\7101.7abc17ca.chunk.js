"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4816,7101],{

/***/ 34816:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   addComponent: () => (/* binding */ addComponent),
/* harmony export */   addLayout: () => (/* binding */ addLayout),
/* harmony export */   addTheme: () => (/* binding */ addTheme),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   removeComponent: () => (/* binding */ removeComponent),
/* harmony export */   removeLayout: () => (/* binding */ removeLayout),
/* harmony export */   removeTheme: () => (/* binding */ removeTheme),
/* harmony export */   setActiveTheme: () => (/* binding */ setActiveTheme),
/* harmony export */   setCurrentView: () => (/* binding */ setCurrentView),
/* harmony export */   togglePreviewMode: () => (/* binding */ togglePreviewMode),
/* harmony export */   toggleSidebar: () => (/* binding */ toggleSidebar),
/* harmony export */   updateComponent: () => (/* binding */ updateComponent),
/* harmony export */   updateLayout: () => (/* binding */ updateLayout),
/* harmony export */   updateTheme: () => (/* binding */ updateTheme),
/* harmony export */   websocketConnected: () => (/* binding */ websocketConnected),
/* harmony export */   websocketDisconnected: () => (/* binding */ websocketDisconnected),
/* harmony export */   websocketMessageReceived: () => (/* binding */ websocketMessageReceived)
/* harmony export */ });
/* harmony import */ var _actions_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4318);
/**
 * Compatibility layer for the minimal-store.js
 * This file re-exports actions from the main Redux store to maintain backward compatibility
 *
 * IMPORTANT: This file is designed to avoid circular dependencies by directly defining
 * action creators rather than importing them from other files.
 */

// Import action types directly to avoid circular dependencies


// Define action creators directly to avoid circular dependencies
// Component actions
var addComponent = function addComponent(component) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_COMPONENT */ .oz || 'ADD_COMPONENT',
    payload: component
  };
};
var updateComponent = function updateComponent(componentId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_COMPONENT */ .ei || 'UPDATE_COMPONENT',
    payload: {
      id: componentId,
      props: props
    }
  };
};
var removeComponent = function removeComponent(componentId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_COMPONENT */ .xS || 'REMOVE_COMPONENT',
    payload: {
      id: componentId
    }
  };
};

// Layout actions
var addLayout = function addLayout(layout) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_LAYOUT */ .vs || 'ADD_LAYOUT',
    payload: layout
  };
};
var updateLayout = function updateLayout(layoutId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_LAYOUT */ .Pe || 'UPDATE_LAYOUT',
    payload: {
      id: layoutId,
      props: props
    }
  };
};
var removeLayout = function removeLayout(layoutId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_LAYOUT */ .gV || 'REMOVE_LAYOUT',
    payload: {
      id: layoutId
    }
  };
};

// Theme actions
var addTheme = function addTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_THEME */ .U_ || 'ADD_THEME',
    payload: theme
  };
};
var updateTheme = function updateTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_THEME */ .gk || 'UPDATE_THEME',
    payload: theme
  };
};
var removeTheme = function removeTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_THEME */ .D || 'REMOVE_THEME',
    payload: {
      id: themeId
    }
  };
};
var setActiveTheme = function setActiveTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .SET_ACTIVE_THEME */ .wH || 'SET_ACTIVE_THEME',
    payload: themeId
  };
};

// WebSocket actions
var websocketConnected = function websocketConnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_CONNECTED */ .Kg || 'WEBSOCKET_CONNECTED'
  };
};
var websocketDisconnected = function websocketDisconnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_DISCONNECTED */ .co || 'WEBSOCKET_DISCONNECTED'
  };
};
var websocketMessageReceived = function websocketMessageReceived(message) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WS_MESSAGE_RECEIVED */ .ZH || 'WEBSOCKET_MESSAGE_RECEIVED',
    payload: message
  };
};

// UI actions
var toggleSidebar = function toggleSidebar() {
  return {
    type: 'TOGGLE_SIDEBAR'
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: 'SET_CURRENT_VIEW',
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: 'TOGGLE_PREVIEW_MODE'
  };
};

// Re-export all actions for backward compatibility


// Export a dummy store for backward compatibility
var dummyStore = {
  getState: function getState() {
    return {};
  },
  dispatch: function dispatch() {},
  subscribe: function subscribe() {
    return function () {};
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dummyStore);

/***/ }),

/***/ 91018:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  wu: () => (/* reexport */ BorderEditor/* default */.A),
  AN: () => (/* reexport */ ColorInput/* default */.A),
  xz: () => (/* reexport */ EnhancedComponentProperties/* default */.A),
  RM: () => (/* reexport */ FontSelector/* default */.A),
  Q7: () => (/* reexport */ NumberInput/* default */.A),
  Zf: () => (/* reexport */ PropertyGroup/* default */.A),
  cR: () => (/* reexport */ PropertyPreview/* default */.A),
  U9: () => (/* reexport */ PropertySearch/* default */.A),
  Sq: () => (/* reexport */ property_editor_ShadowEditor),
  dh: () => (/* reexport */ property_editor_SpacingEditor),
  AC: () => (/* reexport */ PropertyTypeDetector/* StyleSchemas */.AC),
  vS: () => (/* reexport */ PropertyTypeDetector/* getComponentProperties */.vS),
  Lp: () => (/* reexport */ PropertyTypeDetector/* getStylePropertiesGrouped */.Lp)
});

// UNUSED EXPORTS: ComponentSchemas, PropertyRenderer, PropertyTypes, detectPropertyType, detectTypeFromValue, formatLabel, validatePropertyValue

// EXTERNAL MODULE: ./src/components/enhanced/property-editor/NumberInput.js
var NumberInput = __webpack_require__(26031);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/ColorInput.js
var ColorInput = __webpack_require__(33399);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./src/design-system/index.js + 7 modules
var design_system = __webpack_require__(79146);
;// ./src/components/enhanced/property-editor/SpacingEditor.js





var _excluded = ["value", "onChange", "type", "showVisual", "showPresets", "unit"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Text = es/* Typography */.o5.Text;
var SpacingContainer = design_system.styled.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n"])));
var SpacingVisual = design_system.styled.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  width: 120px;\n  height: 120px;\n  margin: 16px auto;\n  background: #f5f5f5;\n  border: 2px dashed #d9d9d9;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n"])));
var SpacingBox = design_system.styled.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 60px;\n  height: 60px;\n  background: #1890ff;\n  opacity: 0.3;\n  border-radius: 4px;\n  position: relative;\n"])));
var SpacingInput = design_system.styled.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"])));
var TopInput = (0,design_system.styled)(SpacingInput)(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  top: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"])));
var RightInput = (0,design_system.styled)(SpacingInput)(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  right: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"])));
var BottomInput = (0,design_system.styled)(SpacingInput)(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  bottom: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"])));
var LeftInput = (0,design_system.styled)(SpacingInput)(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  left: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"])));
var ControlsRow = design_system.styled.div(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"])));
var QuickPresets = design_system.styled.div(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: 4px;\n  margin-top: 8px;\n"])));
var PresetButton = (0,design_system.styled)(es/* Button */.$n)(_templateObject1 || (_templateObject1 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"])));

/**
 * Visual spacing editor for margin and padding properties
 */
var SpacingEditor = function SpacingEditor(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$type = _ref.type,
    type = _ref$type === void 0 ? 'margin' : _ref$type,
    _ref$showVisual = _ref.showVisual,
    showVisual = _ref$showVisual === void 0 ? true : _ref$showVisual,
    _ref$showPresets = _ref.showPresets,
    showPresets = _ref$showPresets === void 0 ? true : _ref$showPresets,
    _ref$unit = _ref.unit,
    unit = _ref$unit === void 0 ? 'px' : _ref$unit,
    props = (0,objectWithoutProperties/* default */.A)(_ref, _excluded);
  var _useState = (0,react.useState)({
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    }),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    values = _useState2[0],
    setValues = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    isLinked = _useState4[0],
    setIsLinked = _useState4[1];

  // Parse spacing value on mount and when value changes
  (0,react.useEffect)(function () {
    if (value) {
      var parsed = parseSpacingValue(value);
      setValues(parsed);
    }
  }, [value]);

  // Parse spacing value like "10px 20px" or "10px 20px 30px 40px"
  var parseSpacingValue = function parseSpacingValue(val) {
    if (!val) return {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    };
    if ((0,esm_typeof/* default */.A)(val) === 'object') {
      return {
        top: parseFloat(val.top) || 0,
        right: parseFloat(val.right) || 0,
        bottom: parseFloat(val.bottom) || 0,
        left: parseFloat(val.left) || 0
      };
    }
    var parts = val.toString().split(/\s+/).map(function (p) {
      return parseFloat(p.replace(/[^\d.-]/g, '')) || 0;
    });
    switch (parts.length) {
      case 1:
        return {
          top: parts[0],
          right: parts[0],
          bottom: parts[0],
          left: parts[0]
        };
      case 2:
        return {
          top: parts[0],
          right: parts[1],
          bottom: parts[0],
          left: parts[1]
        };
      case 3:
        return {
          top: parts[0],
          right: parts[1],
          bottom: parts[2],
          left: parts[1]
        };
      case 4:
        return {
          top: parts[0],
          right: parts[1],
          bottom: parts[2],
          left: parts[3]
        };
      default:
        return {
          top: 0,
          right: 0,
          bottom: 0,
          left: 0
        };
    }
  };

  // Format values for output
  var formatSpacingValue = function formatSpacingValue(vals) {
    var top = vals.top,
      right = vals.right,
      bottom = vals.bottom,
      left = vals.left;

    // If all values are the same, return single value
    if (top === right && right === bottom && bottom === left) {
      return "".concat(top).concat(unit);
    }

    // If top/bottom and left/right are the same
    if (top === bottom && left === right) {
      return "".concat(top).concat(unit, " ").concat(right).concat(unit);
    }

    // Return all four values
    return "".concat(top).concat(unit, " ").concat(right).concat(unit, " ").concat(bottom).concat(unit, " ").concat(left).concat(unit);
  };

  // Handle individual value change
  var handleValueChange = function handleValueChange(side, newValue) {
    var newValues = _objectSpread({}, values);
    if (isLinked) {
      // Update all sides when linked
      newValues.top = newValue;
      newValues.right = newValue;
      newValues.bottom = newValue;
      newValues.left = newValue;
    } else {
      // Update only the specific side
      newValues[side] = newValue;
    }
    setValues(newValues);
    onChange === null || onChange === void 0 || onChange(formatSpacingValue(newValues));
  };

  // Handle preset click
  var handlePresetClick = function handlePresetClick(preset) {
    var newValues = parseSpacingValue(preset);
    setValues(newValues);
    onChange === null || onChange === void 0 || onChange(formatSpacingValue(newValues));
  };

  // Toggle linked state
  var toggleLinked = function toggleLinked() {
    setIsLinked(!isLinked);
  };
  var presets = ['0px', '4px', '8px', '12px', '16px', '24px', '32px', '8px 16px', '16px 24px'];
  return /*#__PURE__*/react.createElement(SpacingContainer, null, /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(Text, {
    strong: true,
    style: {
      fontSize: '12px'
    }
  }, type === 'margin' ? 'Margin' : 'Padding'), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: isLinked ? 'primary' : 'default',
    size: "small",
    icon: isLinked ? /*#__PURE__*/react.createElement(icons_es/* LinkOutlined */.t7c, null) : /*#__PURE__*/react.createElement(icons_es/* UnlockOutlined */.Rrh, null),
    onClick: toggleLinked,
    title: isLinked ? 'Unlink sides' : 'Link all sides'
  })), showVisual && /*#__PURE__*/react.createElement(SpacingVisual, null, /*#__PURE__*/react.createElement(TopInput, null, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    size: "small",
    value: values.top,
    onChange: function onChange(val) {
      return handleValueChange('top', val || 0);
    },
    style: {
      width: 50
    },
    min: 0
  })), /*#__PURE__*/react.createElement(RightInput, null, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    size: "small",
    value: values.right,
    onChange: function onChange(val) {
      return handleValueChange('right', val || 0);
    },
    style: {
      width: 50
    },
    min: 0
  })), /*#__PURE__*/react.createElement(BottomInput, null, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    size: "small",
    value: values.bottom,
    onChange: function onChange(val) {
      return handleValueChange('bottom', val || 0);
    },
    style: {
      width: 50
    },
    min: 0
  })), /*#__PURE__*/react.createElement(LeftInput, null, /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    size: "small",
    value: values.left,
    onChange: function onChange(val) {
      return handleValueChange('left', val || 0);
    },
    style: {
      width: 50
    },
    min: 0
  })), /*#__PURE__*/react.createElement(SpacingBox, null)), /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(Text, {
    style: {
      fontSize: '12px',
      minWidth: '30px'
    }
  }, "Top:"), /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    value: values.top,
    onChange: function onChange(val) {
      return handleValueChange('top', val || 0);
    },
    style: {
      flex: 1
    },
    min: 0,
    addonAfter: unit
  })), /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(Text, {
    style: {
      fontSize: '12px',
      minWidth: '30px'
    }
  }, "Right:"), /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    value: values.right,
    onChange: function onChange(val) {
      return handleValueChange('right', val || 0);
    },
    style: {
      flex: 1
    },
    min: 0,
    addonAfter: unit
  })), /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(Text, {
    style: {
      fontSize: '12px',
      minWidth: '30px'
    }
  }, "Bottom:"), /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    value: values.bottom,
    onChange: function onChange(val) {
      return handleValueChange('bottom', val || 0);
    },
    style: {
      flex: 1
    },
    min: 0,
    addonAfter: unit
  })), /*#__PURE__*/react.createElement(ControlsRow, null, /*#__PURE__*/react.createElement(Text, {
    style: {
      fontSize: '12px',
      minWidth: '30px'
    }
  }, "Left:"), /*#__PURE__*/react.createElement(es/* InputNumber */.YI, {
    value: values.left,
    onChange: function onChange(val) {
      return handleValueChange('left', val || 0);
    },
    style: {
      flex: 1
    },
    min: 0,
    addonAfter: unit
  }))), showPresets && /*#__PURE__*/react.createElement(QuickPresets, null, /*#__PURE__*/react.createElement(Text, {
    style: {
      fontSize: '12px',
      marginRight: '8px'
    }
  }, "Presets:"), presets.map(function (preset, index) {
    return /*#__PURE__*/react.createElement(PresetButton, {
      key: index,
      onClick: function onClick() {
        return handlePresetClick(preset);
      },
      title: "Apply ".concat(preset)
    }, preset);
  })));
};
/* harmony default export */ const property_editor_SpacingEditor = (SpacingEditor);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/BorderEditor.js
var BorderEditor = __webpack_require__(90545);
;// ./src/components/enhanced/property-editor/ShadowEditor.js




var ShadowEditor_excluded = ["value", "onChange", "showPreview"];
var ShadowEditor_templateObject, ShadowEditor_templateObject2, ShadowEditor_templateObject3, ShadowEditor_templateObject4, ShadowEditor_templateObject5;





var ShadowEditor_Text = es/* Typography */.o5.Text;
var ShadowContainer = design_system.styled.div(ShadowEditor_templateObject || (ShadowEditor_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n"])));
var ShadowPreview = design_system.styled.div(ShadowEditor_templateObject2 || (ShadowEditor_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n  height: 80px;\n  margin: 12px 0;\n  background: #ffffff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n  box-shadow: ", ";\n  border: 1px solid #f0f0f0;\n"])), function (props) {
  return props.shadowStyle || 'none';
});
var PropertyRow = design_system.styled.div(ShadowEditor_templateObject3 || (ShadowEditor_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"])));
var PropertyLabel = (0,design_system.styled)(ShadowEditor_Text)(ShadowEditor_templateObject4 || (ShadowEditor_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"])));
var PreviewBox = design_system.styled.div(ShadowEditor_templateObject5 || (ShadowEditor_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 60px;\n  height: 40px;\n  background: #1890ff;\n  border-radius: 4px;\n  opacity: 0.8;\n"])));

/**
 * Visual shadow editor with offset, blur, spread, and color controls
 */
var ShadowEditor = function ShadowEditor(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    props = (0,objectWithoutProperties/* default */.A)(_ref, ShadowEditor_excluded);
  var _useState = (0,react.useState)('0px'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    offsetX = _useState2[0],
    setOffsetX = _useState2[1];
  var _useState3 = (0,react.useState)('2px'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    offsetY = _useState4[0],
    setOffsetY = _useState4[1];
  var _useState5 = (0,react.useState)('4px'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    blurRadius = _useState6[0],
    setBlurRadius = _useState6[1];
  var _useState7 = (0,react.useState)('0px'),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    spreadRadius = _useState8[0],
    setSpreadRadius = _useState8[1];
  var _useState9 = (0,react.useState)('rgba(0, 0, 0, 0.1)'),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    shadowColor = _useState0[0],
    setShadowColor = _useState0[1];
  var _useState1 = (0,react.useState)(false),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    inset = _useState10[0],
    setInset = _useState10[1];

  // Parse shadow value on mount and when value changes
  (0,react.useEffect)(function () {
    if (value) {
      var parsed = parseShadowValue(value);
      setOffsetX(parsed.offsetX);
      setOffsetY(parsed.offsetY);
      setBlurRadius(parsed.blurRadius);
      setSpreadRadius(parsed.spreadRadius);
      setShadowColor(parsed.color);
      setInset(parsed.inset);
    }
  }, [value]);

  // Parse shadow value like "2px 4px 8px rgba(0,0,0,0.1)" or object
  var parseShadowValue = function parseShadowValue(val) {
    if (!val || val === 'none') {
      return {
        offsetX: '0px',
        offsetY: '2px',
        blurRadius: '4px',
        spreadRadius: '0px',
        color: 'rgba(0, 0, 0, 0.1)',
        inset: false
      };
    }
    if ((0,esm_typeof/* default */.A)(val) === 'object') {
      return {
        offsetX: val.offsetX || '0px',
        offsetY: val.offsetY || '2px',
        blurRadius: val.blurRadius || '4px',
        spreadRadius: val.spreadRadius || '0px',
        color: val.color || 'rgba(0, 0, 0, 0.1)',
        inset: val.inset || false
      };
    }
    if (typeof val === 'string') {
      // Parse string like "inset 2px 4px 8px 2px rgba(0,0,0,0.1)"
      var shadowString = val.trim();
      var isInset = false;
      if (shadowString.startsWith('inset ')) {
        isInset = true;
        shadowString = shadowString.replace('inset ', '');
      }

      // Extract color (rgba, rgb, hex, or named color)
      var color = 'rgba(0, 0, 0, 0.1)';
      var colorMatch = shadowString.match(/(rgba?\([^)]+\)|hsla?\([^)]+\)|#[a-fA-F0-9]{3,8}|\b\w+\b)$/);
      if (colorMatch) {
        color = colorMatch[1];
        shadowString = shadowString.replace(colorMatch[1], '').trim();
      }

      // Parse remaining values (offsetX offsetY blurRadius spreadRadius)
      var values = shadowString.split(/\s+/).filter(function (v) {
        return v;
      });
      return {
        offsetX: values[0] || '0px',
        offsetY: values[1] || '2px',
        blurRadius: values[2] || '4px',
        spreadRadius: values[3] || '0px',
        color: color,
        inset: isInset
      };
    }
    return {
      offsetX: '0px',
      offsetY: '2px',
      blurRadius: '4px',
      spreadRadius: '0px',
      color: 'rgba(0, 0, 0, 0.1)',
      inset: false
    };
  };

  // Format shadow value for output
  var formatShadowValue = function formatShadowValue(x, y, blur, spread, color, isInset) {
    var parts = [x, y, blur, spread, color];
    var shadowValue = parts.join(' ');
    return isInset ? "inset ".concat(shadowValue) : shadowValue;
  };

  // Handle value changes
  var handleValueChange = function handleValueChange(property, newValue) {
    var newOffsetX = offsetX;
    var newOffsetY = offsetY;
    var newBlurRadius = blurRadius;
    var newSpreadRadius = spreadRadius;
    var newShadowColor = shadowColor;
    var newInset = inset;
    switch (property) {
      case 'offsetX':
        newOffsetX = newValue;
        setOffsetX(newValue);
        break;
      case 'offsetY':
        newOffsetY = newValue;
        setOffsetY(newValue);
        break;
      case 'blurRadius':
        newBlurRadius = newValue;
        setBlurRadius(newValue);
        break;
      case 'spreadRadius':
        newSpreadRadius = newValue;
        setSpreadRadius(newValue);
        break;
      case 'color':
        newShadowColor = newValue;
        setShadowColor(newValue);
        break;
      case 'inset':
        newInset = newValue;
        setInset(newValue);
        break;
    }
    var formattedValue = formatShadowValue(newOffsetX, newOffsetY, newBlurRadius, newSpreadRadius, newShadowColor, newInset);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };
  var currentShadowStyle = formatShadowValue(offsetX, offsetY, blurRadius, spreadRadius, shadowColor, inset);
  return /*#__PURE__*/react.createElement(ShadowContainer, null, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Inset:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: inset,
    onChange: function onChange(checked) {
      return handleValueChange('inset', checked);
    },
    size: "small"
  })), /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Offset X:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(NumberInput/* default */.A, {
    value: offsetX,
    onChange: function onChange(val) {
      return handleValueChange('offsetX', val);
    },
    min: -50,
    max: 50,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Offset Y:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(NumberInput/* default */.A, {
    value: offsetY,
    onChange: function onChange(val) {
      return handleValueChange('offsetY', val);
    },
    min: -50,
    max: 50,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Blur:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(NumberInput/* default */.A, {
    value: blurRadius,
    onChange: function onChange(val) {
      return handleValueChange('blurRadius', val);
    },
    min: 0,
    max: 100,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Spread:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(NumberInput/* default */.A, {
    value: spreadRadius,
    onChange: function onChange(val) {
      return handleValueChange('spreadRadius', val);
    },
    min: -50,
    max: 50,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react.createElement(PropertyRow, null, /*#__PURE__*/react.createElement(PropertyLabel, null, "Color:"), /*#__PURE__*/react.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react.createElement(ColorInput/* default */.A, {
    value: shadowColor,
    onChange: function onChange(val) {
      return handleValueChange('color', val);
    },
    placeholder: "Shadow color"
  }))), showPreview && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react.createElement(ShadowEditor_Text, {
    style: {
      fontSize: '12px',
      marginBottom: '4px'
    }
  }, "Preview:"), /*#__PURE__*/react.createElement(ShadowPreview, {
    shadowStyle: currentShadowStyle
  }, /*#__PURE__*/react.createElement(PreviewBox, null)), /*#__PURE__*/react.createElement(ShadowEditor_Text, {
    type: "secondary",
    style: {
      fontSize: '11px',
      textAlign: 'center'
    }
  }, currentShadowStyle))));
};
/* harmony default export */ const property_editor_ShadowEditor = (ShadowEditor);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/FontSelector.js
var FontSelector = __webpack_require__(86970);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/PropertyRenderer.js
var PropertyRenderer = __webpack_require__(5184);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/PropertyTypeDetector.js
var PropertyTypeDetector = __webpack_require__(3221);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/PropertySearch.js
var PropertySearch = __webpack_require__(92351);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/PropertyGroup.js
var PropertyGroup = __webpack_require__(95732);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/PropertyPreview.js
var PropertyPreview = __webpack_require__(95527);
// EXTERNAL MODULE: ./src/components/enhanced/property-editor/EnhancedComponentProperties.js
var EnhancedComponentProperties = __webpack_require__(928);
;// ./src/components/enhanced/property-editor/index.js
// Enhanced Property Editor Components







// Property Type Detection and Rendering



// Property Organization and Management




// Enhanced Property Editor


/***/ })

}]);