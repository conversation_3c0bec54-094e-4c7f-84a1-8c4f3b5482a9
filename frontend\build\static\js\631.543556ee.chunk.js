"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[631],{

/***/ 51162:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(17053);


var _templateObject, _templateObject2;





var StatusContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: inline-flex;\n  align-items: center;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.05);\n  }\n"])));
var StatusText = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.span(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  margin-left: 8px;\n  font-size: 12px;\n  @media (max-width: 768px) {\n    display: none;\n  }\n"])));

/**
 * WebSocketStatus component displays the current WebSocket connection status
 * with appropriate visual indicators.
 */
var WebSocketStatus = function WebSocketStatus() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('disconnected'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    status = _useState2[0],
    setStatus = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    lastError = _useState4[0],
    setLastError = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    reconnectCount = _useState6[0],
    setReconnectCount = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState7, 2),
    tooltipVisible = _useState8[0],
    setTooltipVisible = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(navigator.onLine),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState9, 2),
    isOnline = _useState0[0],
    setIsOnline = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState1, 2),
    offlineQueueStatus = _useState10[0],
    setOfflineQueueStatus = _useState10[1];
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    // Set initial state
    setStatus(_services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.isConnected ? 'connected' : 'disconnected');
    setIsOnline(navigator.onLine);
    setReconnectCount(0);

    // Get initial offline queue status if available
    try {
      if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus === 'function') {
        var queueStatus = _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus();
        setOfflineQueueStatus(queueStatus);
      }
    } catch (error) {
      console.error('Error getting offline queue status:', error);
    }

    // Subscribe to WebSocket events
    var handleConnect = function handleConnect() {
      setStatus('connected');
      setReconnectCount(0);
    };
    var handleDisconnect = function handleDisconnect(event) {
      setStatus('disconnected');
      if (event && event.code) {
        setLastError({
          message: "Disconnected (code: ".concat(event.code, ")"),
          timestamp: new Date()
        });
      }
    };
    var handleConnecting = function handleConnecting() {
      setStatus('connecting');
    };
    var handleError = function handleError(error) {
      setStatus('error');
      setLastError(error);
    };
    var handleReconnectScheduled = function handleReconnectScheduled(data) {
      setStatus('connecting');
      setReconnectCount(data.attempt);
    };
    var handleNetworkStatus = function handleNetworkStatus(data) {
      setIsOnline(data.online);
    };
    var handleOfflineQueueUpdate = function handleOfflineQueueUpdate() {
      try {
        if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus === 'function') {
          var _queueStatus = _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus();
          setOfflineQueueStatus(_queueStatus);
        }
      } catch (error) {
        console.error('Error getting updated offline queue status:', error);
      }
    };

    // Register event listeners using the new API
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('connect', handleConnect);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('disconnect', handleDisconnect);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('connecting', handleConnecting);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('error', handleError);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('reconnect_scheduled', handleReconnectScheduled);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('network_status', handleNetworkStatus);

    // Register offline queue events if the methods exist
    if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on === 'function') {
      if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus === 'function') {
        _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('message_queued_offline', handleOfflineQueueUpdate);
        _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('offline_queue_loaded', handleOfflineQueueUpdate);
        _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('offline_queue_cleared', handleOfflineQueueUpdate);
      }
    }

    // Also listen for browser online/offline events
    var handleBrowserOnline = function handleBrowserOnline() {
      return setIsOnline(true);
    };
    var handleBrowserOffline = function handleBrowserOffline() {
      return setIsOnline(false);
    };
    window.addEventListener('online', handleBrowserOnline);
    window.addEventListener('offline', handleBrowserOffline);

    // Cleanup event listeners on unmount
    return function () {
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('connect', handleConnect);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('disconnect', handleDisconnect);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('connecting', handleConnecting);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('error', handleError);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('reconnect_scheduled', handleReconnectScheduled);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('network_status', handleNetworkStatus);

      // Unregister offline queue events if the methods exist
      if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off === 'function') {
        if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus === 'function') {
          _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('message_queued_offline', handleOfflineQueueUpdate);
          _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('offline_queue_loaded', handleOfflineQueueUpdate);
          _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('offline_queue_cleared', handleOfflineQueueUpdate);
        }
      }
      window.removeEventListener('online', handleBrowserOnline);
      window.removeEventListener('offline', handleBrowserOffline);
    };
  }, []);

  // Determine status display properties
  var getStatusProps = function getStatusProps() {
    // First check if browser is offline
    if (!isOnline) {
      var queueCount = (offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) || 0;
      return {
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CloudOutlined */ .gDm, {
          style: {
            color: '#faad14'
          }
        }),
        text: queueCount > 0 ? "Offline (".concat(queueCount, ")") : 'Offline',
        status: 'warning',
        tooltip: 'Browser is offline. ' + (queueCount > 0 ? "".concat(queueCount, " messages queued for delivery when online.") : 'No queued messages.')
      };
    }

    // If online but has offline queue
    if (isOnline && (offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && status === 'connected') {
      return {
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CloudDownloadOutlined */ .Gkj, {
          style: {
            color: '#1890ff'
          }
        }),
        text: "Syncing (".concat(offlineQueueStatus.count, ")"),
        status: 'processing',
        tooltip: "Connected. Syncing ".concat(offlineQueueStatus.count, " queued messages.")
      };
    }

    // Normal connection states
    switch (status) {
      case 'connected':
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .LinkOutlined */ .t7c, {
            style: {
              color: '#52c41a'
            }
          }),
          text: 'Connected',
          status: 'success',
          tooltip: 'WebSocket connection established'
        };
      case 'disconnected':
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DisconnectOutlined */ .Bu6, {
            style: {
              color: '#bfbfbf'
            }
          }),
          text: 'Disconnected',
          status: 'default',
          tooltip: lastError ? "Disconnected: ".concat(lastError.message) : 'WebSocket disconnected'
        };
      case 'connecting':
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .LoadingOutlined */ .NKq, {
            style: {
              color: '#1890ff'
            }
          }),
          text: reconnectCount > 0 ? "Reconnecting (".concat(reconnectCount, ")") : 'Connecting',
          status: 'processing',
          tooltip: 'Attempting to establish WebSocket connection'
        };
      case 'error':
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .WarningOutlined */ .v7y, {
            style: {
              color: '#ff4d4f'
            }
          }),
          text: 'Connection Error',
          status: 'error',
          tooltip: lastError ? "Error: ".concat(lastError.message) : 'WebSocket connection error'
        };
      default:
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DisconnectOutlined */ .Bu6, {
            style: {
              color: '#bfbfbf'
            }
          }),
          text: 'Unknown',
          status: 'default',
          tooltip: 'WebSocket status unknown'
        };
    }
  };
  var statusProps = getStatusProps();

  // Handle manual reconnect attempt
  var handleReconnect = function handleReconnect() {
    if (!isOnline) {
      // Can't reconnect when offline, but we can show a message
      alert('You are currently offline. Please check your internet connection.');
      return;
    }
    if (status !== 'connected') {
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.reconnect();
    } else if ((offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.processOfflineQueue === 'function') {
      // If connected but has offline queue, process it
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.processOfflineQueue();
    }
  };

  // Handle clearing the offline queue
  var handleClearOfflineQueue = function handleClearOfflineQueue(e) {
    e.stopPropagation(); // Prevent triggering the parent onClick

    if ((offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.clearOfflineQueue === 'function') {
      var count = _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.clearOfflineQueue();
      alert("Cleared ".concat(count, " messages from the offline queue."));
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, statusProps.tooltip), lastError && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 4
      }
    }, "Last error: ", lastError.message, lastError.timestamp && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '0.8em',
        opacity: 0.8
      }
    }, new Date(lastError.timestamp).toLocaleTimeString())), (offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 4,
        borderTop: '1px solid rgba(255,255,255,0.2)',
        paddingTop: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, "Offline queue: ", offlineQueueStatus.count, " message", offlineQueueStatus.count !== 1 ? 's' : ''), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("button", {
      onClick: handleClearOfflineQueue,
      style: {
        background: 'rgba(255,255,255,0.2)',
        border: 'none',
        padding: '2px 8px',
        borderRadius: '4px',
        cursor: 'pointer',
        fontSize: '0.8em'
      }
    }, "Clear Queue"))), !isOnline && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 8,
        fontSize: '0.9em'
      }
    }, "You are offline. Check your internet connection."), isOnline && status !== 'connected' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 8,
        fontSize: '0.9em'
      }
    }, "Click to reconnect"), isOnline && status === 'connected' && (offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 8,
        fontSize: '0.9em'
      }
    }, "Click to sync offline messages")),
    open: tooltipVisible,
    onOpenChange: setTooltipVisible
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StatusContainer, {
    onClick: handleReconnect,
    "data-testid": "websocket-status"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
    status: statusProps.status
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    size: 4
  }, statusProps.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StatusText, null, statusProps.text))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WebSocketStatus);

/***/ }),

/***/ 90631:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_CollaborationTestPage)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./src/services/UserPresenceService.js
var UserPresenceService = __webpack_require__(11378);
// EXTERNAL MODULE: ./src/services/SharedEditingService.js
var SharedEditingService = __webpack_require__(49645);
// EXTERNAL MODULE: ./src/services/WebSocketService.js
var WebSocketService = __webpack_require__(17053);
// EXTERNAL MODULE: ./src/components/UserPresenceIndicator.js
var UserPresenceIndicator = __webpack_require__(86132);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
;// ./src/components/UserPresenceList.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text;

/**
 * UserPresenceList component
 * Displays a list of users and their presence status
 */
var UserPresenceList = function UserPresenceList(_ref) {
  var _ref$title = _ref.title,
    title = _ref$title === void 0 ? 'Online Users' : _ref$title,
    _ref$showOffline = _ref.showOffline,
    showOffline = _ref$showOffline === void 0 ? false : _ref$showOffline,
    _ref$maxUsers = _ref.maxUsers,
    maxUsers = _ref$maxUsers === void 0 ? 10 : _ref$maxUsers,
    _ref$emptyText = _ref.emptyText,
    emptyText = _ref$emptyText === void 0 ? 'No users online' : _ref$emptyText,
    _ref$style = _ref.style,
    style = _ref$style === void 0 ? {} : _ref$style;
  var _useState = (0,react.useState)([]),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    users = _useState2[0],
    setUsers = _useState2[1];

  // Get users on mount and when they change
  (0,react.useEffect)(function () {
    // Get initial users
    updateUserList();

    // Listen for presence updates
    var handlePresenceUpdate = function handlePresenceUpdate() {
      updateUserList();
    };
    var handleUserJoined = function handleUserJoined() {
      updateUserList();
    };
    var handleUserLeft = function handleUserLeft() {
      updateUserList();
    };

    // Register event listeners
    UserPresenceService/* default */.A.on('presence_update', handlePresenceUpdate);
    UserPresenceService/* default */.A.on('user_joined', handleUserJoined);
    UserPresenceService/* default */.A.on('user_left', handleUserLeft);

    // Clean up event listeners
    return function () {
      UserPresenceService/* default */.A.off('presence_update', handlePresenceUpdate);
      UserPresenceService/* default */.A.off('user_joined', handleUserJoined);
      UserPresenceService/* default */.A.off('user_left', handleUserLeft);
    };
  }, [showOffline, maxUsers]);

  // Update user list
  var updateUserList = function updateUserList() {
    // Get all users
    var allUsers = [];
    UserPresenceService/* default */.A.users.forEach(function (user) {
      // Skip offline users if showOffline is false
      if (!showOffline && user.status === 'offline') {
        return;
      }
      allUsers.push(user);
    });

    // Sort users by status (online first, then away, then busy, then offline)
    allUsers.sort(function (a, b) {
      var statusOrder = {
        online: 0,
        away: 1,
        busy: 2,
        offline: 3
      };
      var statusA = statusOrder[a.status] || 3;
      var statusB = statusOrder[b.status] || 3;
      if (statusA !== statusB) {
        return statusA - statusB;
      }

      // If status is the same, sort by username
      return (a.username || a.userId).localeCompare(b.username || b.userId);
    });

    // Limit to maxUsers
    var limitedUsers = allUsers.slice(0, maxUsers);
    setUsers(limitedUsers);
  };
  return /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react.createElement(icons_es/* TeamOutlined */.QM0, {
      style: {
        marginRight: 8
      }
    }), /*#__PURE__*/react.createElement("span", null, title), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        marginLeft: 8
      }
    }, "(", users.length, ")")),
    style: _objectSpread({}, style),
    size: "small"
  }, users.length === 0 ? /*#__PURE__*/react.createElement(es/* Empty */.Sv, {
    description: emptyText,
    image: es/* Empty */.Sv.PRESENTED_IMAGE_SIMPLE
  }) : /*#__PURE__*/react.createElement(es/* List */.B8, {
    dataSource: users,
    renderItem: function renderItem(user) {
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, null, /*#__PURE__*/react.createElement(UserPresenceIndicator/* default */.A, {
        userId: user.userId,
        username: user.username,
        showUsername: true,
        size: "default",
        avatarSize: 32
      }));
    }
  }));
};
/* harmony default export */ const components_UserPresenceList = (UserPresenceList);
// EXTERNAL MODULE: ./src/components/SharedEditor.js
var SharedEditor = __webpack_require__(96903);
// EXTERNAL MODULE: ./src/components/WebSocketStatus.js
var WebSocketStatus = __webpack_require__(51162);
;// ./src/pages/CollaborationTestPage.js











var Header = es/* Layout */.PE.Header,
  Content = es/* Layout */.PE.Content;
var CollaborationTestPage_Title = es/* Typography */.o5.Title,
  CollaborationTestPage_Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;
var TabPane = es/* Tabs */.tU.TabPane;

/**
 * CollaborationTestPage component
 * Demonstrates real-time collaboration features
 */
var CollaborationTestPage = function CollaborationTestPage() {
  var _useState = (0,react.useState)(''),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    userId = _useState2[0],
    setUserId = _useState2[1];
  var _useState3 = (0,react.useState)(''),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    username = _useState4[0],
    setUsername = _useState4[1];
  var _useState5 = (0,react.useState)(false),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    isInitialized = _useState6[0],
    setIsInitialized = _useState6[1];
  var _useState7 = (0,react.useState)('shared-doc-1'),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    documentId = _useState8[0],
    setDocumentId = _useState8[1];
  var _useState9 = (0,react.useState)('online'),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    status = _useState0[0],
    setStatus = _useState0[1];
  var _useState1 = (0,react.useState)(WebSocketService/* default */.A.isConnected),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    isConnected = _useState10[0],
    setIsConnected = _useState10[1];

  // Initialize services when user info is provided
  var initializeServices = function initializeServices() {
    if (!userId || !username) {
      return;
    }

    // Initialize user presence service
    UserPresenceService/* default */.A.init({
      userId: userId,
      username: username,
      userData: {
        status: status
      }
    });

    // Initialize shared editing service
    SharedEditingService/* default */.A.init({
      userId: userId,
      username: username
    });
    setIsInitialized(true);
  };

  // Handle WebSocket connection status changes
  (0,react.useEffect)(function () {
    var handleConnect = function handleConnect() {
      setIsConnected(true);
    };
    var handleDisconnect = function handleDisconnect() {
      setIsConnected(false);
    };
    WebSocketService/* default */.A.on('connect', handleConnect);
    WebSocketService/* default */.A.on('disconnect', handleDisconnect);
    return function () {
      WebSocketService/* default */.A.off('connect', handleConnect);
      WebSocketService/* default */.A.off('disconnect', handleDisconnect);
    };
  }, []);

  // Clean up on unmount
  (0,react.useEffect)(function () {
    return function () {
      if (isInitialized) {
        UserPresenceService/* default */.A.cleanup();
        SharedEditingService/* default */.A.cleanup();
      }
    };
  }, [isInitialized]);

  // Handle status change
  var handleStatusChange = function handleStatusChange(newStatus) {
    setStatus(newStatus);
    if (isInitialized) {
      UserPresenceService/* default */.A.sendPresence({
        userId: userId,
        status: newStatus
      });
    }
  };

  // Render user setup form
  var renderUserSetup = function renderUserSetup() {
    return /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      title: "User Setup",
      style: {
        marginBottom: 16
      }
    }, /*#__PURE__*/react.createElement(es/* Form */.lV, {
      layout: "vertical"
    }, /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      label: "User ID",
      required: true
    }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
      prefix: /*#__PURE__*/react.createElement(icons_es/* UserOutlined */.qmv, null),
      value: userId,
      onChange: function onChange(e) {
        return setUserId(e.target.value);
      },
      placeholder: "Enter a unique user ID"
    })), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      label: "Username",
      required: true
    }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
      prefix: /*#__PURE__*/react.createElement(icons_es/* UserOutlined */.qmv, null),
      value: username,
      onChange: function onChange(e) {
        return setUsername(e.target.value);
      },
      placeholder: "Enter your display name"
    })), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      label: "Status"
    }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
      value: status,
      onChange: handleStatusChange,
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react.createElement(es/* Select */.l6.Option, {
      value: "online"
    }, "Online"), /*#__PURE__*/react.createElement(es/* Select */.l6.Option, {
      value: "away"
    }, "Away"), /*#__PURE__*/react.createElement(es/* Select */.l6.Option, {
      value: "busy"
    }, "Busy"), /*#__PURE__*/react.createElement(es/* Select */.l6.Option, {
      value: "offline"
    }, "Offline"))), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "primary",
      onClick: initializeServices,
      disabled: !userId || !username || isInitialized
    }, "Start Collaborating"))));
  };

  // Render collaboration interface
  var renderCollaborationInterface = function renderCollaborationInterface() {
    return /*#__PURE__*/react.createElement(es/* Row */.fI, {
      gutter: 16
    }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
      span: 18
    }, /*#__PURE__*/react.createElement(SharedEditor/* default */.A, {
      documentId: documentId,
      title: "Collaborative Document",
      userId: userId,
      username: username,
      style: {
        marginBottom: 16
      }
    }), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      title: "Document ID",
      size: "small",
      style: {
        marginBottom: 16
      }
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Input */.pd, {
      value: documentId,
      onChange: function onChange(e) {
        return setDocumentId(e.target.value);
      },
      placeholder: "Enter document ID",
      style: {
        width: 200
      }
    }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      onClick: function onClick() {
        // Leave current document
        SharedEditingService/* default */.A.leaveDocument(documentId);

        // Join new document
        SharedEditingService/* default */.A.joinDocument(documentId);
      }
    }, "Join Document"))), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      title: "Your Presence",
      size: "small"
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(CollaborationTestPage_Text, {
      strong: true
    }, "Current Status: "), /*#__PURE__*/react.createElement(es/* Select */.l6, {
      value: status,
      onChange: handleStatusChange,
      style: {
        width: 120
      }
    }, /*#__PURE__*/react.createElement(es/* Select */.l6.Option, {
      value: "online"
    }, "Online"), /*#__PURE__*/react.createElement(es/* Select */.l6.Option, {
      value: "away"
    }, "Away"), /*#__PURE__*/react.createElement(es/* Select */.l6.Option, {
      value: "busy"
    }, "Busy"), /*#__PURE__*/react.createElement(es/* Select */.l6.Option, {
      value: "offline"
    }, "Offline"))), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(CollaborationTestPage_Text, {
      strong: true
    }, "Your Presence: "), /*#__PURE__*/react.createElement(UserPresenceIndicator/* default */.A, {
      userId: userId,
      username: username
    })), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(CollaborationTestPage_Text, {
      strong: true
    }, "WebSocket Status: "), /*#__PURE__*/react.createElement(WebSocketStatus/* default */.A, null))))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
      span: 6
    }, /*#__PURE__*/react.createElement(components_UserPresenceList, {
      title: "Collaborators",
      showOffline: true,
      style: {
        marginBottom: 16
      }
    }), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      title: "Typing Indicator Test",
      size: "small",
      style: {
        marginBottom: 16
      }
    }, /*#__PURE__*/react.createElement(es/* Input */.pd.TextArea, {
      placeholder: "Type here to trigger typing indicator...",
      onChange: function onChange() {
        UserPresenceService/* default */.A.updateTyping(true, documentId);

        // Clear typing indicator after 2 seconds
        setTimeout(function () {
          UserPresenceService/* default */.A.updateTyping(false, documentId);
        }, 2000);
      },
      rows: 3
    })), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      title: "Help",
      size: "small"
    }, /*#__PURE__*/react.createElement(Paragraph, null, /*#__PURE__*/react.createElement("ul", null, /*#__PURE__*/react.createElement("li", null, "Enter text in the shared editor to collaborate in real-time"), /*#__PURE__*/react.createElement("li", null, "Change your status to see presence updates"), /*#__PURE__*/react.createElement("li", null, "Type in the typing indicator test area to trigger typing notifications"), /*#__PURE__*/react.createElement("li", null, "Change the document ID and join to collaborate on different documents"))))));
  };
  return /*#__PURE__*/react.createElement(es/* Layout */.PE, {
    style: {
      minHeight: '100vh'
    }
  }, /*#__PURE__*/react.createElement(Header, {
    style: {
      background: '#fff',
      padding: '0 16px'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react.createElement(CollaborationTestPage_Title, {
    level: 3,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react.createElement(icons_es/* TeamOutlined */.QM0, null), " Real-time Collaboration Test"), isInitialized && /*#__PURE__*/react.createElement(UserPresenceIndicator/* default */.A, {
    userId: userId,
    username: username,
    size: "default",
    avatarSize: 32
  }))), /*#__PURE__*/react.createElement(Content, {
    style: {
      padding: '16px'
    }
  }, !isConnected && /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "WebSocket Disconnected",
    description: "The WebSocket connection is currently disconnected. Collaboration features may not work properly.",
    type: "warning",
    showIcon: true,
    style: {
      marginBottom: 16
    }
  }), !isInitialized ? renderUserSetup() : renderCollaborationInterface()));
};
/* harmony default export */ const pages_CollaborationTestPage = (CollaborationTestPage);

/***/ })

}]);