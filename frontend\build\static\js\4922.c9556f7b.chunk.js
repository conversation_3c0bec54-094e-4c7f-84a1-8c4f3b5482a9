"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4922],{

/***/ 16030:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71468);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);
/* harmony import */ var _redux_minimal_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(34816);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(79146);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(86020);
/* harmony import */ var _property_editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(91018);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }










// Import enhanced component builder with fallback
var EnhancedComponentBuilder;
try {
  EnhancedComponentBuilder = (__webpack_require__(18294)/* ["default"] */ .A);
} catch (error) {
  console.warn('Enhanced component builder not available, using fallback');
  EnhancedComponentBuilder = null;
}
var ComponentBuilderContainer = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[4]);
var ComponentGrid = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[4]);
var ComponentPreview = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: white;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[4], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.neutral[200], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].borderRadius */ .Ay.borderRadius.md);
var PropertyEditor = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[3]);
var PropertyGroup = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[2]);
var ComponentItem = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['isSelected'].includes(prop);
  }
})(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  cursor: pointer;\n  transition: ", ";\n  border: 2px solid ", ";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ", ";\n  }\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].transitions */ .Ay.transitions["default"], function (props) {
  return props.isSelected ? _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.primary.main : 'transparent';
}, _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].shadows */ .Ay.shadows.md);
var EmptyState = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ", ";\n  background-color: ", ";\n  border-radius: ", ";\n  text-align: center;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[8], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.neutral[100], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].borderRadius */ .Ay.borderRadius.md);
var componentTypes = [{
  value: 'container',
  label: 'Container'
}, {
  value: 'text',
  label: 'Text'
}, {
  value: 'button',
  label: 'Button'
}, {
  value: 'input',
  label: 'Input Field'
}, {
  value: 'image',
  label: 'Image'
}, {
  value: 'card',
  label: 'Card'
}, {
  value: 'list',
  label: 'List'
}, {
  value: 'custom',
  label: 'Custom'
}];
var ComponentBuilder = function ComponentBuilder() {
  // Check if enhanced mode is available
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(EnhancedComponentBuilder !== null),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 1),
    useEnhancedMode = _useState2[0];

  // If enhanced mode is enabled and available, use the enhanced component builder
  if (useEnhancedMode && EnhancedComponentBuilder) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(EnhancedComponentBuilder, null);
  }

  // Fallback to original implementation
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useDispatch */ .wA)();

  // Fix selector path to match store structure
  var components = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$app, _state$appData;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || ((_state$appData = state.appData) === null || _state$appData === void 0 ? void 0 : _state$appData.components) || [];
  });

  // Add loading state
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    isLoading = _useState4[0],
    setIsLoading = _useState4[1];

  // All useState hooks must be called before any conditional returns
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(''),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    componentName = _useState6[0],
    setComponentName = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('container'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    componentType = _useState8[0],
    setComponentType = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('{}'),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    componentProps = _useState0[0],
    setComponentProps = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    selectedComponent = _useState10[0],
    setSelectedComponent = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    editMode = _useState12[0],
    setEditMode = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({}),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState13, 2),
    errors = _useState14[0],
    setErrors = _useState14[1];

  // Function to create sample components
  var createSampleComponents = function createSampleComponents() {
    var sampleComponents = [{
      id: 'button-1',
      name: 'Primary Button',
      type: 'button',
      props: {
        text: 'Click Me',
        variant: 'primary',
        size: 'medium',
        onClick: 'handleButtonClick'
      },
      createdAt: new Date().toISOString()
    }, {
      id: 'text-1',
      name: 'Header Text',
      type: 'text',
      props: {
        content: 'Welcome to App Builder',
        variant: 'h1',
        color: '#2563EB',
        align: 'center'
      },
      createdAt: new Date().toISOString()
    }, {
      id: 'input-1',
      name: 'Email Input',
      type: 'input',
      props: {
        label: 'Email Address',
        placeholder: 'Enter your email',
        type: 'email',
        required: true,
        validation: 'email'
      },
      createdAt: new Date().toISOString()
    }, {
      id: 'card-1',
      name: 'Feature Card',
      type: 'card',
      props: {
        title: 'Easy to Use',
        description: 'Build applications with a simple drag-and-drop interface',
        image: 'https://via.placeholder.com/150',
        elevation: 'md'
      },
      createdAt: new Date().toISOString()
    }];

    // Add each sample component to the store
    sampleComponents.forEach(function (component) {
      dispatch((0,_redux_minimal_store__WEBPACK_IMPORTED_MODULE_8__.addComponent)(component));
    });
  };
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    // Initialize component
    var init = /*#__PURE__*/function () {
      var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              try {
                setIsLoading(true);

                // Check if we already have components
                if (components.length === 0) {
                  // Add sample components if none exist
                  createSampleComponents();
                }
                setIsLoading(false);
              } catch (error) {
                console.error('Failed to initialize ComponentBuilder:', error);
                setIsLoading(false);
              }
            case 1:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function init() {
        return _ref.apply(this, arguments);
      };
    }();
    init();
  }, [components.length, dispatch]);

  // All useEffect hooks must be called before any conditional returns
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    console.log('ComponentBuilder mounting...');
    return function () {
      console.log('ComponentBuilder unmounting...');
    };
  }, []);
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    console.log('Components updated:', components);
  }, [components]);
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (Object.keys(errors).length > 0) {
      console.error('ComponentBuilder errors:', errors);
    }
  }, [errors]);
  if (isLoading) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Loading ComponentBuilder...");
  }
  var validateForm = function validateForm() {
    var newErrors = {};
    if (!componentName.trim()) {
      newErrors.name = 'Component name is required';
    }
    try {
      if (componentProps) {
        JSON.parse(componentProps);
      }
    } catch (error) {
      newErrors.props = 'Invalid JSON format';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  var handleAddComponent = function handleAddComponent() {
    if (!validateForm()) return;
    try {
      var propsObject = componentProps ? JSON.parse(componentProps) : {};
      var newComponent = {
        id: Date.now().toString(),
        name: componentName.trim(),
        type: componentType,
        props: propsObject,
        createdAt: new Date().toISOString()
      };
      dispatch((0,_redux_minimal_store__WEBPACK_IMPORTED_MODULE_8__.addComponent)(newComponent)).then(function () {
        // Reset form
        setComponentName('');
        setComponentType('container');
        setComponentProps('{}');
        setErrors({});
      })["catch"](function (error) {
        console.error('Failed to add component:', error);
        setErrors({
          submit: 'Failed to add component'
        });
      });
    } catch (error) {
      setErrors(_objectSpread(_objectSpread({}, errors), {}, {
        props: error.message
      }));
    }
  };
  var handleUpdateComponent = function handleUpdateComponent() {
    if (!selectedComponent || !validateForm()) return;
    try {
      var propsObject = componentProps ? JSON.parse(componentProps) : {};
      var updatedComponent = _objectSpread(_objectSpread({}, selectedComponent), {}, {
        name: componentName.trim(),
        type: componentType,
        props: propsObject,
        updatedAt: new Date().toISOString()
      });
      dispatch((0,_redux_minimal_store__WEBPACK_IMPORTED_MODULE_8__.updateComponent)(updatedComponent));

      // Reset form and exit edit mode
      setComponentName('');
      setComponentType('container');
      setComponentProps('{}');
      setSelectedComponent(null);
      setEditMode(false);
      setErrors({});
    } catch (error) {
      setErrors(_objectSpread(_objectSpread({}, errors), {}, {
        props: error.message
      }));
    }
  };
  var handleRemoveComponent = function handleRemoveComponent(id) {
    dispatch((0,_redux_minimal_store__WEBPACK_IMPORTED_MODULE_8__.removeComponent)(id));

    // If the removed component was selected, reset the form
    if (selectedComponent && selectedComponent.id === id) {
      setComponentName('');
      setComponentType('container');
      setComponentProps('{}');
      setSelectedComponent(null);
      setEditMode(false);
    }
  };
  var handleSelectComponent = function handleSelectComponent(component) {
    setSelectedComponent(component);
    setComponentName(component.name);
    setComponentType(component.type);
    setComponentProps(JSON.stringify(component.props, null, 2));
    setEditMode(true);
    setErrors({});
  };
  var handleCancelEdit = function handleCancelEdit() {
    setComponentName('');
    setComponentType('container');
    setComponentProps('{}');
    setSelectedComponent(null);
    setEditMode(false);
    setErrors({});
  };
  var handleDuplicateComponent = function handleDuplicateComponent(component) {
    var duplicatedComponent = _objectSpread(_objectSpread({}, component), {}, {
      id: Date.now().toString(),
      name: "".concat(component.name, " (Copy)"),
      createdAt: new Date().toISOString()
    });
    dispatch((0,_redux_minimal_store__WEBPACK_IMPORTED_MODULE_8__.addComponent)(duplicatedComponent));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentBuilderContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Header, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Title, null, editMode ? 'Edit Component' : 'Create Component'), editMode && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Button, {
    variant: "text",
    size: "small",
    onClick: handleCancelEdit,
    startIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CloseOutlined */ .r$3, null)
  }, "Cancel")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Content, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(PropertyEditor, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(PropertyGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Input, {
    label: "Component Name",
    value: componentName,
    onChange: function onChange(e) {
      return setComponentName(e.target.value);
    },
    placeholder: "Enter component name",
    fullWidth: true,
    error: !!errors.name,
    helperText: errors.name
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(PropertyGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Select, {
    label: "Component Type",
    value: componentType,
    onChange: function onChange(e) {
      return setComponentType(e.target.value);
    },
    options: componentTypes,
    fullWidth: true
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(PropertyGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Input, {
    label: "Component Props (JSON)",
    value: componentProps,
    onChange: function onChange(e) {
      return setComponentProps(e.target.value);
    },
    placeholder: "{\"text\": \"Hello\", \"color\": \"blue\"}",
    fullWidth: true,
    error: !!errors.props,
    helperText: errors.props,
    as: "textarea",
    rows: 5,
    style: {
      fontFamily: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].typography */ .Ay.typography.fontFamily.code
    }
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Footer, null, editMode ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Button, {
    variant: "primary",
    onClick: handleUpdateComponent,
    startIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SaveOutlined */ .ylI, null)
  }, "Update Component") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Button, {
    variant: "primary",
    onClick: handleAddComponent,
    startIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .PlusOutlined */ .bW0, null)
  }, "Add Component"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Header, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Title, null, "Component Library")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Content, null, components.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(EmptyState, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      fontSize: '48px',
      color: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.neutral[400],
      marginBottom: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[4]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .AppstoreOutlined */ .rS9, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("h3", null, "No Components Yet"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", null, "Create your first component to get started")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentGrid, null, components.map(function (component) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentItem, {
      key: component.id,
      isSelected: selectedComponent && selectedComponent.id === component.id
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card, {
      elevation: "sm"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Header, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        fontWeight: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].typography */ .Ay.typography.fontWeight.semibold
      }
    }, component.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        fontSize: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].typography */ .Ay.typography.fontSize.sm,
        color: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.neutral[500]
      }
    }, component.type)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        display: 'flex',
        gap: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[1]
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleDuplicateComponent(component);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CopyOutlined */ .wq3, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleSelectComponent(component);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .EditOutlined */ .xjh, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Button, {
      variant: "text",
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleRemoveComponent(component.id);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .DeleteOutlined */ .SUY, null)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Content, {
      onClick: function onClick() {
        return handleSelectComponent(component);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentPreview, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("pre", {
      style: {
        margin: 0,
        overflow: 'auto',
        maxHeight: '100px'
      }
    }, JSON.stringify(component.props, null, 2))))));
  })))), selectedComponent && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Header, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Title, null, "Component Properties")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_9__.Card.Content, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_property_editor__WEBPACK_IMPORTED_MODULE_11__/* .EnhancedComponentProperties */ .xz, {
    component: selectedComponent,
    onUpdate: function onUpdate(updatedComponent) {
      dispatch((0,_redux_minimal_store__WEBPACK_IMPORTED_MODULE_8__.updateComponent)(updatedComponent));
    }
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponentBuilder);

/***/ }),

/***/ 90545:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _NumberInput__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(26031);
/* harmony import */ var _ColorInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(33399);




var _excluded = ["value", "onChange", "showPreview"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4;






var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var BorderContainer = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 100%;\n"])));
var BorderPreview = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 100%;\n  height: 60px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n  border: ", ";\n"])), function (props) {
  return props.borderStyle || '1px solid #d9d9d9';
});
var PropertyRow = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"])));
var PropertyLabel = (0,_design_system__WEBPACK_IMPORTED_MODULE_7__.styled)(Text)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  min-width: 60px;\n  font-size: 12px;\n  font-weight: 500;\n"])));

/**
 * Visual border editor with style, width, and color controls
 */
var BorderEditor = function BorderEditor(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref, _excluded);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('solid'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    borderStyle = _useState2[0],
    setBorderStyle = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('1px'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    borderWidth = _useState4[0],
    setBorderWidth = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('#d9d9d9'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    borderColor = _useState6[0],
    setBorderColor = _useState6[1];

  // Parse border value on mount and when value changes
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (value) {
      var parsed = parseBorderValue(value);
      setBorderStyle(parsed.style);
      setBorderWidth(parsed.width);
      setBorderColor(parsed.color);
    }
  }, [value]);

  // Parse border value like "1px solid #000" or object
  var parseBorderValue = function parseBorderValue(val) {
    if (!val) return {
      style: 'solid',
      width: '1px',
      color: '#d9d9d9'
    };
    if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(val) === 'object') {
      return {
        style: val.style || 'solid',
        width: val.width || '1px',
        color: val.color || '#d9d9d9'
      };
    }
    if (typeof val === 'string') {
      // Parse string like "1px solid #000"
      var parts = val.split(/\s+/);
      var width = '1px';
      var style = 'solid';
      var color = '#d9d9d9';
      parts.forEach(function (part) {
        if (part.match(/^\d+(\.\d+)?(px|em|rem|%)$/)) {
          width = part;
        } else if (['none', 'solid', 'dashed', 'dotted', 'double', 'groove', 'ridge', 'inset', 'outset'].includes(part)) {
          style = part;
        } else if (part.startsWith('#') || part.startsWith('rgb') || part.startsWith('hsl') || isNamedColor(part)) {
          color = part;
        }
      });
      return {
        style: style,
        width: width,
        color: color
      };
    }
    return {
      style: 'solid',
      width: '1px',
      color: '#d9d9d9'
    };
  };

  // Check if a string is a named color
  var isNamedColor = function isNamedColor(color) {
    var namedColors = ['black', 'white', 'red', 'green', 'blue', 'yellow', 'orange', 'purple', 'pink', 'brown', 'gray', 'grey', 'transparent'];
    return namedColors.includes(color.toLowerCase());
  };

  // Format border value for output
  var formatBorderValue = function formatBorderValue(style, width, color) {
    if (style === 'none') {
      return 'none';
    }
    return "".concat(width, " ").concat(style, " ").concat(color);
  };

  // Handle style change
  var handleStyleChange = function handleStyleChange(newStyle) {
    setBorderStyle(newStyle);
    var formattedValue = formatBorderValue(newStyle, borderWidth, borderColor);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };

  // Handle width change
  var handleWidthChange = function handleWidthChange(newWidth) {
    setBorderWidth(newWidth);
    var formattedValue = formatBorderValue(borderStyle, newWidth, borderColor);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };

  // Handle color change
  var handleColorChange = function handleColorChange(newColor) {
    setBorderColor(newColor);
    var formattedValue = formatBorderValue(borderStyle, borderWidth, newColor);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };
  var borderStyles = [{
    value: 'none',
    label: 'None'
  }, {
    value: 'solid',
    label: 'Solid'
  }, {
    value: 'dashed',
    label: 'Dashed'
  }, {
    value: 'dotted',
    label: 'Dotted'
  }, {
    value: 'double',
    label: 'Double'
  }, {
    value: 'groove',
    label: 'Groove'
  }, {
    value: 'ridge',
    label: 'Ridge'
  }, {
    value: 'inset',
    label: 'Inset'
  }, {
    value: 'outset',
    label: 'Outset'
  }];
  var currentBorderStyle = formatBorderValue(borderStyle, borderWidth, borderColor);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(BorderContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyRow, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyLabel, null, "Style:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: borderStyle,
    onChange: handleStyleChange,
    style: {
      flex: 1
    },
    size: "small"
  }, borderStyles.map(function (style) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
      key: style.value,
      value: style.value
    }, style.label);
  }))), borderStyle !== 'none' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyRow, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyLabel, null, "Width:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_NumberInput__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
    value: borderWidth,
    onChange: handleWidthChange,
    min: 0,
    max: 20,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem'],
    size: "small"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyRow, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyLabel, null, "Color:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ColorInput__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {
    value: borderColor,
    onChange: handleColorChange,
    placeholder: "Border color"
  })))), showPreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Divider */ .cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      fontSize: '12px',
      marginBottom: '4px'
    }
  }, "Preview:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(BorderPreview, {
    borderStyle: currentBorderStyle
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BorderOutlined */ .bnM, {
    style: {
      fontSize: '24px',
      color: '#8c8c8c'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: '11px',
      textAlign: 'center'
    }
  }, currentBorderStyle))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BorderEditor);

/***/ })

}]);