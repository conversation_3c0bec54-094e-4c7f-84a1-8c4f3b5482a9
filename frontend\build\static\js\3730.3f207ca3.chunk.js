"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3730],{

/***/ 43730:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_ProjectsPage)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/react-router-dom/dist/index.js + 1 modules
var dist = __webpack_require__(11080);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
;// ./src/utils/performanceMonitor.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Performance monitoring utility for the App Builder application.
 * 
 * This utility provides functions to measure and report performance metrics.
 */

// Store for performance marks and measures
var performanceStore = {
  marks: {},
  measures: {},
  resources: [],
  errors: []
};

/**
 * Initialize the performance monitor.
 * 
 * @returns {Object} The performance monitor API.
 */
var initPerformanceMonitor = function initPerformanceMonitor() {
  // Check if the Performance API is available
  if (!window.performance) {
    console.warn('Performance API is not available in this browser.');
    return null;
  }

  // Clear existing performance data
  performanceStore.marks = {};
  performanceStore.measures = {};
  performanceStore.resources = [];
  performanceStore.errors = [];

  // Set up resource timing buffer
  if (window.performance.setResourceTimingBufferSize) {
    window.performance.setResourceTimingBufferSize(500);
  }

  // Set up observers
  setupObservers();
  return {
    mark: mark,
    measure: measure,
    getMarks: getMarks,
    getMeasures: getMeasures,
    getResourceTimings: getResourceTimings,
    getErrors: getErrors,
    clearMarks: clearMarks,
    clearMeasures: clearMeasures,
    clearResourceTimings: clearResourceTimings,
    clearErrors: clearErrors,
    getPerformanceReport: getPerformanceReport
  };
};

/**
 * Set up performance observers.
 */
var setupObservers = function setupObservers() {
  // Set up resource timing observer
  if (window.PerformanceObserver) {
    try {
      // Resource timing observer
      var resourceObserver = new PerformanceObserver(function (list) {
        var entries = list.getEntries();
        performanceStore.resources = [].concat((0,toConsumableArray/* default */.A)(performanceStore.resources), (0,toConsumableArray/* default */.A)(entries));
      });
      resourceObserver.observe({
        entryTypes: ['resource']
      });

      // Long task observer
      var longTaskObserver = new PerformanceObserver(function (list) {
        var entries = list.getEntries();
        entries.forEach(function (entry) {
          console.warn('Long task detected:', entry);
          performanceStore.errors.push({
            type: 'long-task',
            message: "Long task detected: ".concat(entry.name, " (").concat(entry.duration, "ms)"),
            timestamp: new Date().toISOString(),
            details: entry
          });
        });
      });
      longTaskObserver.observe({
        entryTypes: ['longtask']
      });

      // Paint timing observer
      var paintObserver = new PerformanceObserver(function (list) {
        var entries = list.getEntries();
        entries.forEach(function (entry) {
          mark("paint-".concat(entry.name), entry.startTime);
        });
      });
      paintObserver.observe({
        entryTypes: ['paint']
      });

      // First Input Delay observer
      var fidObserver = new PerformanceObserver(function (list) {
        var entries = list.getEntries();
        entries.forEach(function (entry) {
          mark("fid-".concat(entry.name), entry.startTime);
          if (entry.duration > 100) {
            performanceStore.errors.push({
              type: 'fid',
              message: "High First Input Delay: ".concat(entry.duration, "ms"),
              timestamp: new Date().toISOString(),
              details: entry
            });
          }
        });
      });
      fidObserver.observe({
        entryTypes: ['first-input']
      });

      // Layout Shift observer
      var lsObserver = new PerformanceObserver(function (list) {
        var entries = list.getEntries();
        entries.forEach(function (entry) {
          if (entry.value > 0.1) {
            performanceStore.errors.push({
              type: 'layout-shift',
              message: "High Layout Shift: ".concat(entry.value),
              timestamp: new Date().toISOString(),
              details: entry
            });
          }
        });
      });
      lsObserver.observe({
        entryTypes: ['layout-shift']
      });
    } catch (error) {
      console.warn('Error setting up PerformanceObserver:', error);
    }
  }
};

/**
 * Create a performance mark.
 * 
 * @param {string} name - The name of the mark.
 * @param {number} [startTime] - Optional start time for the mark.
 */
var mark = function mark(name, startTime) {
  try {
    if (startTime !== undefined) {
      window.performance.mark(name, {
        startTime: startTime
      });
    } else {
      window.performance.mark(name);
    }

    // Store the mark
    var marks = window.performance.getEntriesByName(name, 'mark');
    if (marks.length > 0) {
      performanceStore.marks[name] = marks[marks.length - 1];
    }
  } catch (error) {
    console.warn("Error creating mark \"".concat(name, "\":"), error);
  }
};

/**
 * Create a performance measure between two marks.
 * 
 * @param {string} name - The name of the measure.
 * @param {string} startMark - The name of the start mark.
 * @param {string} endMark - The name of the end mark.
 */
var measure = function measure(name, startMark, endMark) {
  try {
    window.performance.measure(name, startMark, endMark);

    // Store the measure
    var measures = window.performance.getEntriesByName(name, 'measure');
    if (measures.length > 0) {
      performanceStore.measures[name] = measures[measures.length - 1];
    }
  } catch (error) {
    console.warn("Error creating measure \"".concat(name, "\":"), error);
  }
};

/**
 * Get all performance marks.
 * 
 * @returns {Object} All performance marks.
 */
var getMarks = function getMarks() {
  return _objectSpread({}, performanceStore.marks);
};

/**
 * Get all performance measures.
 * 
 * @returns {Object} All performance measures.
 */
var getMeasures = function getMeasures() {
  return _objectSpread({}, performanceStore.measures);
};

/**
 * Get all resource timings.
 * 
 * @returns {Array} All resource timings.
 */
var getResourceTimings = function getResourceTimings() {
  return (0,toConsumableArray/* default */.A)(performanceStore.resources);
};

/**
 * Get all performance errors.
 * 
 * @returns {Array} All performance errors.
 */
var getErrors = function getErrors() {
  return (0,toConsumableArray/* default */.A)(performanceStore.errors);
};

/**
 * Clear all performance marks.
 */
var clearMarks = function clearMarks() {
  try {
    window.performance.clearMarks();
    performanceStore.marks = {};
  } catch (error) {
    console.warn('Error clearing marks:', error);
  }
};

/**
 * Clear all performance measures.
 */
var clearMeasures = function clearMeasures() {
  try {
    window.performance.clearMeasures();
    performanceStore.measures = {};
  } catch (error) {
    console.warn('Error clearing measures:', error);
  }
};

/**
 * Clear all resource timings.
 */
var clearResourceTimings = function clearResourceTimings() {
  try {
    window.performance.clearResourceTimings();
    performanceStore.resources = [];
  } catch (error) {
    console.warn('Error clearing resource timings:', error);
  }
};

/**
 * Clear all performance errors.
 */
var clearErrors = function clearErrors() {
  performanceStore.errors = [];
};

/**
 * Get a comprehensive performance report.
 * 
 * @returns {Object} A comprehensive performance report.
 */
var getPerformanceReport = function getPerformanceReport() {
  return {
    marks: getMarks(),
    measures: getMeasures(),
    resources: getResourceTimings(),
    errors: getErrors(),
    navigation: window.performance.timing ? {
      navigationStart: window.performance.timing.navigationStart,
      loadEventEnd: window.performance.timing.loadEventEnd,
      domComplete: window.performance.timing.domComplete,
      domInteractive: window.performance.timing.domInteractive,
      domContentLoadedEventEnd: window.performance.timing.domContentLoadedEventEnd
    } : null,
    memory: window.performance.memory ? {
      jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit,
      totalJSHeapSize: window.performance.memory.totalJSHeapSize,
      usedJSHeapSize: window.performance.memory.usedJSHeapSize
    } : null
  };
};
/* harmony default export */ const performanceMonitor = ((/* unused pure expression or super */ null && (initPerformanceMonitor)));
;// ./src/hooks/usePerformanceMonitor.js




/**
 * Hook for monitoring performance in React components.
 * 
 * @param {Object} options - Options for the performance monitor.
 * @param {boolean} options.enabled - Whether the performance monitor is enabled.
 * @param {boolean} options.autoMarkRenders - Whether to automatically mark component renders.
 * @param {boolean} options.autoMarkEffects - Whether to automatically mark effect runs.
 * @param {boolean} options.autoMarkEvents - Whether to automatically mark event handlers.
 * @param {number} options.reportInterval - Interval in milliseconds to generate performance reports.
 * @param {Function} options.onReport - Callback function to handle performance reports.
 * @returns {Object} Performance monitor API and state.
 */
var usePerformanceMonitor = function usePerformanceMonitor() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _ref$enabled = _ref.enabled,
    enabled = _ref$enabled === void 0 ? "production" === 'development' : _ref$enabled,
    _ref$autoMarkRenders = _ref.autoMarkRenders,
    autoMarkRenders = _ref$autoMarkRenders === void 0 ? true : _ref$autoMarkRenders,
    _ref$autoMarkEffects = _ref.autoMarkEffects,
    autoMarkEffects = _ref$autoMarkEffects === void 0 ? true : _ref$autoMarkEffects,
    _ref$autoMarkEvents = _ref.autoMarkEvents,
    autoMarkEvents = _ref$autoMarkEvents === void 0 ? true : _ref$autoMarkEvents,
    _ref$reportInterval = _ref.reportInterval,
    reportInterval = _ref$reportInterval === void 0 ? 10000 : _ref$reportInterval,
    _ref$onReport = _ref.onReport,
    onReport = _ref$onReport === void 0 ? null : _ref$onReport;
  // State for the performance monitor
  var _useState = (0,react.useState)(enabled),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    isEnabled = _useState2[0],
    setIsEnabled = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    isInitialized = _useState4[0],
    setIsInitialized = _useState4[1];

  // Refs for the performance monitor and component name
  var monitorRef = (0,react.useRef)(null);
  var componentNameRef = (0,react.useRef)("Component-".concat(Math.random().toString(36).substr(2, 9)));
  var renderCountRef = (0,react.useRef)(0);
  var effectCountRef = (0,react.useRef)(0);
  var eventCountRef = (0,react.useRef)(0);
  var reportIntervalIdRef = (0,react.useRef)(null);

  // Initialize the performance monitor
  (0,react.useEffect)(function () {
    if (isEnabled && !isInitialized) {
      monitorRef.current = initPerformanceMonitor();
      setIsInitialized(true);

      // Mark component mount
      if (monitorRef.current) {
        monitorRef.current.mark("".concat(componentNameRef.current, "-mount"));
      }
    }
    return function () {
      // Mark component unmount
      if (monitorRef.current) {
        monitorRef.current.mark("".concat(componentNameRef.current, "-unmount"));
        monitorRef.current.measure("".concat(componentNameRef.current, "-lifetime"), "".concat(componentNameRef.current, "-mount"), "".concat(componentNameRef.current, "-unmount"));
      }

      // Clear report interval
      if (reportIntervalIdRef.current) {
        clearInterval(reportIntervalIdRef.current);
      }
    };
  }, [isEnabled, isInitialized]);

  // Set up automatic reporting
  (0,react.useEffect)(function () {
    if (isEnabled && isInitialized && onReport && reportInterval > 0) {
      reportIntervalIdRef.current = setInterval(function () {
        if (monitorRef.current) {
          var report = monitorRef.current.getPerformanceReport();
          onReport(report);
        }
      }, reportInterval);
      return function () {
        clearInterval(reportIntervalIdRef.current);
      };
    }
  }, [isEnabled, isInitialized, onReport, reportInterval]);

  // Mark component render
  (0,react.useEffect)(function () {
    if (isEnabled && isInitialized && autoMarkRenders && monitorRef.current) {
      renderCountRef.current += 1;
      monitorRef.current.mark("".concat(componentNameRef.current, "-render-").concat(renderCountRef.current));
      if (renderCountRef.current > 1) {
        monitorRef.current.measure("".concat(componentNameRef.current, "-render-time-").concat(renderCountRef.current), "".concat(componentNameRef.current, "-render-").concat(renderCountRef.current - 1), "".concat(componentNameRef.current, "-render-").concat(renderCountRef.current));
      }
    }
  });

  // Create a wrapper for effect functions
  var wrapEffect = (0,react.useCallback)(function (effectFn) {
    var effectName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'effect';
    if (!isEnabled || !isInitialized || !autoMarkEffects || !monitorRef.current) {
      return effectFn;
    }
    return function () {
      effectCountRef.current += 1;
      var effectId = "".concat(componentNameRef.current, "-").concat(effectName, "-").concat(effectCountRef.current);
      monitorRef.current.mark("".concat(effectId, "-start"));
      try {
        var result = effectFn.apply(void 0, arguments);

        // Handle promises
        if (result && typeof result.then === 'function') {
          return result.then(function (value) {
            monitorRef.current.mark("".concat(effectId, "-end"));
            monitorRef.current.measure("".concat(effectId, "-time"), "".concat(effectId, "-start"), "".concat(effectId, "-end"));
            return value;
          })["catch"](function (error) {
            monitorRef.current.mark("".concat(effectId, "-error"));
            monitorRef.current.measure("".concat(effectId, "-error-time"), "".concat(effectId, "-start"), "".concat(effectId, "-error"));
            throw error;
          });
        }

        // Handle synchronous functions
        monitorRef.current.mark("".concat(effectId, "-end"));
        monitorRef.current.measure("".concat(effectId, "-time"), "".concat(effectId, "-start"), "".concat(effectId, "-end"));
        return result;
      } catch (error) {
        monitorRef.current.mark("".concat(effectId, "-error"));
        monitorRef.current.measure("".concat(effectId, "-error-time"), "".concat(effectId, "-start"), "".concat(effectId, "-error"));
        throw error;
      }
    };
  }, [isEnabled, isInitialized, autoMarkEffects]);

  // Create a wrapper for event handlers
  var wrapEvent = (0,react.useCallback)(function (eventHandler) {
    var eventName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'event';
    if (!isEnabled || !isInitialized || !autoMarkEvents || !monitorRef.current) {
      return eventHandler;
    }
    return function () {
      eventCountRef.current += 1;
      var eventId = "".concat(componentNameRef.current, "-").concat(eventName, "-").concat(eventCountRef.current);
      monitorRef.current.mark("".concat(eventId, "-start"));
      try {
        var result = eventHandler.apply(void 0, arguments);

        // Handle promises
        if (result && typeof result.then === 'function') {
          return result.then(function (value) {
            monitorRef.current.mark("".concat(eventId, "-end"));
            monitorRef.current.measure("".concat(eventId, "-time"), "".concat(eventId, "-start"), "".concat(eventId, "-end"));
            return value;
          })["catch"](function (error) {
            monitorRef.current.mark("".concat(eventId, "-error"));
            monitorRef.current.measure("".concat(eventId, "-error-time"), "".concat(eventId, "-start"), "".concat(eventId, "-error"));
            throw error;
          });
        }

        // Handle synchronous functions
        monitorRef.current.mark("".concat(eventId, "-end"));
        monitorRef.current.measure("".concat(eventId, "-time"), "".concat(eventId, "-start"), "".concat(eventId, "-end"));
        return result;
      } catch (error) {
        monitorRef.current.mark("".concat(eventId, "-error"));
        monitorRef.current.measure("".concat(eventId, "-error-time"), "".concat(eventId, "-start"), "".concat(eventId, "-error"));
        throw error;
      }
    };
  }, [isEnabled, isInitialized, autoMarkEvents]);

  // Set component name
  var setComponentName = (0,react.useCallback)(function (name) {
    componentNameRef.current = name;
  }, []);

  // Enable/disable the performance monitor
  var setEnabled = (0,react.useCallback)(function (enabled) {
    setIsEnabled(enabled);
  }, []);

  // Get the current performance report
  var getReport = (0,react.useCallback)(function () {
    if (isEnabled && isInitialized && monitorRef.current) {
      return monitorRef.current.getPerformanceReport();
    }
    return null;
  }, [isEnabled, isInitialized]);

  // Clear all performance data
  var clearData = (0,react.useCallback)(function () {
    if (isEnabled && isInitialized && monitorRef.current) {
      monitorRef.current.clearMarks();
      monitorRef.current.clearMeasures();
      monitorRef.current.clearResourceTimings();
      monitorRef.current.clearErrors();
    }
  }, [isEnabled, isInitialized]);
  return {
    isEnabled: isEnabled,
    isInitialized: isInitialized,
    setEnabled: setEnabled,
    setComponentName: setComponentName,
    wrapEffect: wrapEffect,
    wrapEvent: wrapEvent,
    getReport: getReport,
    clearData: clearData,
    mark: (0,react.useCallback)(function (name) {
      if (isEnabled && isInitialized && monitorRef.current) {
        monitorRef.current.mark("".concat(componentNameRef.current, "-").concat(name));
      }
    }, [isEnabled, isInitialized]),
    measure: (0,react.useCallback)(function (name, startMark, endMark) {
      if (isEnabled && isInitialized && monitorRef.current) {
        monitorRef.current.measure("".concat(componentNameRef.current, "-").concat(name), "".concat(componentNameRef.current, "-").concat(startMark), "".concat(componentNameRef.current, "-").concat(endMark));
      }
    }, [isEnabled, isInitialized])
  };
};
/* harmony default export */ const hooks_usePerformanceMonitor = (usePerformanceMonitor);
;// ./src/pages/ProjectsPage.js





var _templateObject, _templateObject2, _templateObject3;
function ProjectsPage_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function ProjectsPage_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ProjectsPage_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ProjectsPage_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }







var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text;
var Option = es/* Select */.l6.Option;
var TabPane = es/* Tabs */.tU.TabPane;

// Styled components
var ProjectsContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 24px;\n"])));
var ProjectHeader = styled_components_browser_esm/* default */.Ay.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n  gap: 16px;\n"])));
var SearchContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n"])));
var ProjectsPage = function ProjectsPage() {
  var _useState = (0,react.useState)([]),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    projects = _useState2[0],
    setProjects = _useState2[1];
  var _useState3 = (0,react.useState)(true),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react.useState)(''),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    searchText = _useState6[0],
    setSearchText = _useState6[1];
  var _useState7 = (0,react.useState)(false),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    modalVisible = _useState8[0],
    setModalVisible = _useState8[1];
  var _useState9 = (0,react.useState)(false),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    drawerVisible = _useState0[0],
    setDrawerVisible = _useState0[1];
  var _useState1 = (0,react.useState)(null),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    currentProject = _useState10[0],
    setCurrentProject = _useState10[1];
  var _Form$useForm = es/* Form */.lV.useForm(),
    _Form$useForm2 = (0,slicedToArray/* default */.A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var navigate = (0,dist/* useNavigate */.Zp)();

  // Initialize performance monitoring
  var performance = hooks_usePerformanceMonitor({
    enabled: "production" === 'development'
  });

  // Set component name for performance monitoring
  performance.setComponentName('ProjectsPage');

  // Fetch projects
  (0,react.useEffect)(function () {
    performance.mark('fetch-projects-start');
    var fetchProjects = /*#__PURE__*/function () {
      var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
        var mockProjects, _t;
        return regenerator_default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 1;
              return new Promise(function (resolve) {
                return setTimeout(resolve, 1000);
              });
            case 1:
              // Mock data
              mockProjects = [{
                id: '1',
                name: 'E-commerce Dashboard',
                description: 'A dashboard for an e-commerce website',
                status: 'active',
                createdAt: '2023-05-15T10:30:00Z',
                updatedAt: '2023-05-20T14:45:00Z',
                owner: 'John Doe',
                team: ['John Doe', 'Jane Smith'],
                tags: ['dashboard', 'e-commerce'],
                template: 'dashboard'
              }, {
                id: '2',
                name: 'Blog Template',
                description: 'A template for a blog website',
                status: 'completed',
                createdAt: '2023-04-10T09:15:00Z',
                updatedAt: '2023-04-25T16:20:00Z',
                owner: 'Jane Smith',
                team: ['Jane Smith', 'Bob Johnson'],
                tags: ['blog', 'template'],
                template: 'blog'
              }, {
                id: '3',
                name: 'Mobile App UI',
                description: 'UI design for a mobile app',
                status: 'archived',
                createdAt: '2023-03-05T11:45:00Z',
                updatedAt: '2023-03-15T13:10:00Z',
                owner: 'Bob Johnson',
                team: ['Bob Johnson'],
                tags: ['mobile', 'ui'],
                template: 'mobile'
              }, {
                id: '4',
                name: 'Landing Page',
                description: 'A landing page for a product',
                status: 'active',
                createdAt: '2023-05-01T08:30:00Z',
                updatedAt: '2023-05-10T15:45:00Z',
                owner: 'John Doe',
                team: ['John Doe', 'Alice Williams'],
                tags: ['landing', 'marketing'],
                template: 'landing'
              }, {
                id: '5',
                name: 'Admin Panel',
                description: 'An admin panel for a web application',
                status: 'active',
                createdAt: '2023-04-20T13:15:00Z',
                updatedAt: '2023-05-05T10:30:00Z',
                owner: 'Alice Williams',
                team: ['Alice Williams', 'John Doe'],
                tags: ['admin', 'dashboard'],
                template: 'admin'
              }];
              setProjects(mockProjects);
              _context.next = 3;
              break;
            case 2:
              _context.prev = 2;
              _t = _context["catch"](0);
              console.error('Error fetching projects:', _t);
              es/* message */.iU.error('Failed to load projects');
            case 3:
              _context.prev = 3;
              setLoading(false);
              performance.mark('fetch-projects-end');
              performance.measure('fetch-projects', 'fetch-projects-start', 'fetch-projects-end');
              return _context.finish(3);
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 2, 3, 4]]);
      }));
      return function fetchProjects() {
        return _ref.apply(this, arguments);
      };
    }();
    fetchProjects();
  }, [performance]);

  // Handle create project
  var handleCreateProject = /*#__PURE__*/function () {
    var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(values) {
      var newProject, _t2;
      return regenerator_default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            performance.mark('create-project-start');
            _context2.prev = 1;
            _context2.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 1000);
            });
          case 2:
            // Create new project
            newProject = {
              id: String(projects.length + 1),
              name: values.name,
              description: values.description,
              status: 'active',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              owner: 'Current User',
              team: ['Current User'],
              tags: values.tags || [],
              template: values.template
            }; // Add to projects
            setProjects([].concat((0,toConsumableArray/* default */.A)(projects), [newProject]));

            // Close modal
            setModalVisible(false);

            // Reset form
            form.resetFields();

            // Show success message
            es/* message */.iU.success('Project created successfully');

            // Navigate to the new project
            navigate("/app-builder?project=".concat(newProject.id));
            _context2.next = 4;
            break;
          case 3:
            _context2.prev = 3;
            _t2 = _context2["catch"](1);
            console.error('Error creating project:', _t2);
            es/* message */.iU.error('Failed to create project');
          case 4:
            _context2.prev = 4;
            performance.mark('create-project-end');
            performance.measure('create-project', 'create-project-start', 'create-project-end');
            return _context2.finish(4);
          case 5:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 3, 4, 5]]);
    }));
    return function handleCreateProject(_x) {
      return _ref2.apply(this, arguments);
    };
  }();

  // Handle delete project
  var handleDeleteProject = /*#__PURE__*/function () {
    var _ref3 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3(id) {
      var _t3;
      return regenerator_default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            performance.mark('delete-project-start');
            _context3.prev = 1;
            _context3.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 1000);
            });
          case 2:
            // Remove from projects
            setProjects(projects.filter(function (project) {
              return project.id !== id;
            }));

            // Show success message
            es/* message */.iU.success('Project deleted successfully');
            _context3.next = 4;
            break;
          case 3:
            _context3.prev = 3;
            _t3 = _context3["catch"](1);
            console.error('Error deleting project:', _t3);
            es/* message */.iU.error('Failed to delete project');
          case 4:
            _context3.prev = 4;
            performance.mark('delete-project-end');
            performance.measure('delete-project', 'delete-project-start', 'delete-project-end');
            return _context3.finish(4);
          case 5:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 3, 4, 5]]);
    }));
    return function handleDeleteProject(_x2) {
      return _ref3.apply(this, arguments);
    };
  }();

  // Handle view project
  var handleViewProject = function handleViewProject(project) {
    setCurrentProject(project);
    setDrawerVisible(true);
  };

  // Handle edit project
  var handleEditProject = function handleEditProject(project) {
    navigate("/app-builder?project=".concat(project.id));
  };

  // Handle export project
  var handleExportProject = function handleExportProject(project) {
    es/* message */.iU.info("Exporting project: ".concat(project.name));
  };

  // Handle duplicate project
  var handleDuplicateProject = /*#__PURE__*/function () {
    var _ref4 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4(project) {
      var duplicateProject, _t4;
      return regenerator_default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            performance.mark('duplicate-project-start');
            _context4.prev = 1;
            _context4.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 1000);
            });
          case 2:
            // Create duplicate project
            duplicateProject = ProjectsPage_objectSpread(ProjectsPage_objectSpread({}, project), {}, {
              id: String(projects.length + 1),
              name: "".concat(project.name, " (Copy)"),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }); // Add to projects
            setProjects([].concat((0,toConsumableArray/* default */.A)(projects), [duplicateProject]));

            // Show success message
            es/* message */.iU.success('Project duplicated successfully');
            _context4.next = 4;
            break;
          case 3:
            _context4.prev = 3;
            _t4 = _context4["catch"](1);
            console.error('Error duplicating project:', _t4);
            es/* message */.iU.error('Failed to duplicate project');
          case 4:
            _context4.prev = 4;
            performance.mark('duplicate-project-end');
            performance.measure('duplicate-project', 'duplicate-project-start', 'duplicate-project-end');
            return _context4.finish(4);
          case 5:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 3, 4, 5]]);
    }));
    return function handleDuplicateProject(_x3) {
      return _ref4.apply(this, arguments);
    };
  }();

  // Filter projects by search text
  var filteredProjects = projects.filter(function (project) {
    var searchLower = searchText.toLowerCase();
    return project.name.toLowerCase().includes(searchLower) || project.description.toLowerCase().includes(searchLower) || project.owner.toLowerCase().includes(searchLower) || project.tags.some(function (tag) {
      return tag.toLowerCase().includes(searchLower);
    });
  });

  // Table columns
  var columns = [{
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    sorter: function sorter(a, b) {
      return a.name.localeCompare(b.name);
    }
  }, {
    title: 'Description',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  }, {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: function render(status) {
      return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
        color: status === 'active' ? 'green' : status === 'completed' ? 'blue' : 'default'
      }, status.toUpperCase());
    },
    filters: [{
      text: 'Active',
      value: 'active'
    }, {
      text: 'Completed',
      value: 'completed'
    }, {
      text: 'Archived',
      value: 'archived'
    }],
    onFilter: function onFilter(value, record) {
      return record.status === value;
    }
  }, {
    title: 'Created',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: function render(date) {
      return new Date(date).toLocaleDateString();
    },
    sorter: function sorter(a, b) {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    }
  }, {
    title: 'Owner',
    dataIndex: 'owner',
    key: 'owner'
  }, {
    title: 'Tags',
    dataIndex: 'tags',
    key: 'tags',
    render: function render(tags) {
      return /*#__PURE__*/react.createElement(react.Fragment, null, tags.map(function (tag) {
        return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
          key: tag
        }, tag);
      }));
    }
  }, {
    title: 'Actions',
    key: 'actions',
    render: function render(_, record) {
      return /*#__PURE__*/react.createElement(es/* Space */.$x, {
        size: "small"
      }, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "View"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        icon: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
        size: "small",
        onClick: function onClick() {
          return handleViewProject(record);
        }
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Edit"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        icon: /*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, null),
        size: "small",
        onClick: function onClick() {
          return handleEditProject(record);
        }
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Export"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        icon: /*#__PURE__*/react.createElement(icons_es/* ExportOutlined */.PZg, null),
        size: "small",
        onClick: function onClick() {
          return handleExportProject(record);
        }
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Duplicate"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        icon: /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, null),
        size: "small",
        onClick: function onClick() {
          return handleDuplicateProject(record);
        }
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Delete"
      }, /*#__PURE__*/react.createElement(es/* Popconfirm */.iS, {
        title: "Are you sure you want to delete this project?",
        onConfirm: function onConfirm() {
          return handleDeleteProject(record.id);
        },
        okText: "Yes",
        cancelText: "No"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        icon: /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null),
        size: "small",
        danger: true
      }))));
    }
  }];
  return /*#__PURE__*/react.createElement(ProjectsContainer, null, /*#__PURE__*/react.createElement(ProjectHeader, null, /*#__PURE__*/react.createElement(Title, {
    level: 2
  }, "Projects"), /*#__PURE__*/react.createElement(SearchContainer, null, /*#__PURE__*/react.createElement(es/* Input */.pd, {
    placeholder: "Search projects",
    prefix: /*#__PURE__*/react.createElement(icons_es/* SearchOutlined */.VrN, null),
    value: searchText,
    onChange: function onChange(e) {
      return setSearchText(e.target.value);
    },
    style: {
      width: 250
    }
  }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null),
    onClick: function onClick() {
      return setModalVisible(true);
    }
  }, "New Project"))), /*#__PURE__*/react.createElement(es/* Card */.Zp, null, loading ? /*#__PURE__*/react.createElement(es/* Skeleton */.EA, {
    active: true,
    paragraph: {
      rows: 10
    }
  }) : /*#__PURE__*/react.createElement(es/* Table */.XI, {
    dataSource: filteredProjects,
    columns: columns,
    rowKey: "id",
    pagination: {
      pageSize: 10
    },
    locale: {
      emptyText: /*#__PURE__*/react.createElement(es/* Empty */.Sv, {
        description: "No projects found",
        image: es/* Empty */.Sv.PRESENTED_IMAGE_SIMPLE
      })
    }
  })), /*#__PURE__*/react.createElement(es/* Modal */.aF, {
    title: "Create New Project",
    visible: modalVisible,
    onCancel: function onCancel() {
      return setModalVisible(false);
    },
    footer: null
  }, /*#__PURE__*/react.createElement(es/* Form */.lV, {
    form: form,
    layout: "vertical",
    onFinish: handleCreateProject
  }, /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
    name: "name",
    label: "Project Name",
    rules: [{
      required: true,
      message: 'Please enter a project name'
    }]
  }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
    placeholder: "Enter project name"
  })), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
    name: "description",
    label: "Description"
  }, /*#__PURE__*/react.createElement(es/* Input */.pd.TextArea, {
    placeholder: "Enter project description",
    rows: 4
  })), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
    name: "template",
    label: "Template",
    rules: [{
      required: true,
      message: 'Please select a template'
    }]
  }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
    placeholder: "Select a template"
  }, /*#__PURE__*/react.createElement(Option, {
    value: "blank"
  }, "Blank"), /*#__PURE__*/react.createElement(Option, {
    value: "dashboard"
  }, "Dashboard"), /*#__PURE__*/react.createElement(Option, {
    value: "blog"
  }, "Blog"), /*#__PURE__*/react.createElement(Option, {
    value: "ecommerce"
  }, "E-commerce"), /*#__PURE__*/react.createElement(Option, {
    value: "landing"
  }, "Landing Page"), /*#__PURE__*/react.createElement(Option, {
    value: "admin"
  }, "Admin Panel"), /*#__PURE__*/react.createElement(Option, {
    value: "mobile"
  }, "Mobile App"))), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
    name: "tags",
    label: "Tags"
  }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
    mode: "tags",
    placeholder: "Add tags",
    style: {
      width: '100%'
    }
  })), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    htmlType: "submit"
  }, "Create Project")))), /*#__PURE__*/react.createElement(es/* Drawer */._s, {
    title: currentProject === null || currentProject === void 0 ? void 0 : currentProject.name,
    width: 600,
    placement: "right",
    onClose: function onClose() {
      return setDrawerVisible(false);
    },
    visible: drawerVisible
  }, currentProject && /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    defaultActiveKey: "details"
  }, /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null), " Details"),
    key: "details"
  }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Description"), /*#__PURE__*/react.createElement(Text, null, currentProject.description), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Status"), /*#__PURE__*/react.createElement(es/* Tag */.vw, {
    color: currentProject.status === 'active' ? 'green' : currentProject.status === 'completed' ? 'blue' : 'default'
  }, currentProject.status.toUpperCase()), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Created"), /*#__PURE__*/react.createElement(Text, null, new Date(currentProject.createdAt).toLocaleString()), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Last Updated"), /*#__PURE__*/react.createElement(Text, null, new Date(currentProject.updatedAt).toLocaleString()), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Tags"), currentProject.tags.map(function (tag) {
    return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
      key: tag
    }, tag);
  }), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Template"), /*#__PURE__*/react.createElement(Text, null, currentProject.template))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* TeamOutlined */.QM0, null), " Team"),
    key: "team"
  }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Owner"), /*#__PURE__*/react.createElement(Text, null, currentProject.owner), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Team Members"), /*#__PURE__*/react.createElement("ul", null, currentProject.team.map(function (member) {
    return /*#__PURE__*/react.createElement("li", {
      key: member
    }, member);
  })))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* HistoryOutlined */.dUu, null), " History"),
    key: "history"
  }, /*#__PURE__*/react.createElement(es/* Empty */.Sv, {
    description: "No history available"
  })), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null), " Settings"),
    key: "settings"
  }, /*#__PURE__*/react.createElement(es/* Empty */.Sv, {
    description: "No settings available"
  })))));
};
/* harmony default export */ const pages_ProjectsPage = (ProjectsPage);

/***/ })

}]);