"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8256],{

/***/ 18256:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);


var _templateObject, _templateObject2, _templateObject3, _templateObject4;




var Title = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;
var RangePicker = antd__WEBPACK_IMPORTED_MODULE_3__/* .DatePicker */ .lr.RangePicker;
var Option = antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6.Option;
var AnalyticsContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  padding: 16px;\n  background: #f5f5f5;\n  height: 100%;\n  overflow-y: auto;\n"])));
var MetricCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  \n  .ant-card-body {\n    padding: 16px;\n  }\n"])));
var TrendIndicator = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  color: ", ";\n  font-size: 12px;\n  margin-top: 4px;\n"])), function (props) {
  return props.trend === 'up' ? '#52c41a' : props.trend === 'down' ? '#ff4d4f' : '#666';
});
var ActivityItem = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 0;\n  border-bottom: 1px solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n"])));
var AnalyticsPanel = function AnalyticsPanel(_ref) {
  var _ref$projectId = _ref.projectId,
    projectId = _ref$projectId === void 0 ? null : _ref$projectId,
    _ref$timeRange = _ref.timeRange,
    timeRange = _ref$timeRange === void 0 ? '7d' : _ref$timeRange,
    _ref$onExport = _ref.onExport,
    onExport = _ref$onExport === void 0 ? function () {} : _ref$onExport,
    _ref$realTime = _ref.realTime,
    realTime = _ref$realTime === void 0 ? false : _ref$realTime;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(timeRange),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    selectedTimeRange = _useState4[0],
    setSelectedTimeRange = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({}),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    metrics = _useState6[0],
    setMetrics = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState7, 2),
    activities = _useState8[0],
    setActivities = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState9, 2),
    componentStats = _useState0[0],
    setComponentStats = _useState0[1];

  // Mock analytics data
  var generateMockData = function generateMockData() {
    return {
      metrics: {
        totalViews: {
          value: 1247,
          trend: 'up',
          change: 12.5
        },
        uniqueUsers: {
          value: 89,
          trend: 'up',
          change: 8.3
        },
        avgSessionTime: {
          value: 245,
          trend: 'down',
          change: -3.2
        },
        bounceRate: {
          value: 34.2,
          trend: 'down',
          change: -5.1
        },
        conversionRate: {
          value: 12.8,
          trend: 'up',
          change: 15.7
        },
        errorRate: {
          value: 2.1,
          trend: 'down',
          change: -12.3
        }
      },
      activities: [{
        id: 1,
        type: 'component_added',
        description: 'Button component added to main layout',
        user: 'John Doe',
        timestamp: new Date(Date.now() - 300000),
        impact: 'positive'
      }, {
        id: 2,
        type: 'layout_changed',
        description: 'Grid layout applied to dashboard',
        user: 'Jane Smith',
        timestamp: new Date(Date.now() - 600000),
        impact: 'neutral'
      }, {
        id: 3,
        type: 'theme_updated',
        description: 'Color scheme changed to dark mode',
        user: 'Mike Johnson',
        timestamp: new Date(Date.now() - 900000),
        impact: 'positive'
      }, {
        id: 4,
        type: 'component_removed',
        description: 'Unused input field removed',
        user: 'Sarah Wilson',
        timestamp: new Date(Date.now() - 1200000),
        impact: 'negative'
      }, {
        id: 5,
        type: 'export_generated',
        description: 'React code exported successfully',
        user: 'Tom Brown',
        timestamp: new Date(Date.now() - 1800000),
        impact: 'positive'
      }],
      componentStats: [{
        name: 'Button',
        usage: 85,
        performance: 92,
        errors: 2
      }, {
        name: 'Input',
        usage: 78,
        performance: 88,
        errors: 5
      }, {
        name: 'Card',
        usage: 65,
        performance: 95,
        errors: 1
      }, {
        name: 'Table',
        usage: 45,
        performance: 82,
        errors: 8
      }, {
        name: 'Modal',
        usage: 32,
        performance: 90,
        errors: 3
      }]
    };
  };

  // Load analytics data
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    setLoading(true);

    // Simulate API call
    setTimeout(function () {
      var data = generateMockData();
      setMetrics(data.metrics);
      setActivities(data.activities);
      setComponentStats(data.componentStats);
      setLoading(false);
    }, 1000);
  }, [selectedTimeRange, projectId]);

  // Real-time updates
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!realTime) return;
    var interval = setInterval(function () {
      var data = generateMockData();
      setMetrics(data.metrics);
    }, 30000); // Update every 30 seconds

    return function () {
      return clearInterval(interval);
    };
  }, [realTime]);
  var formatTime = function formatTime(timestamp) {
    var now = new Date();
    var diff = now - timestamp;
    var minutes = Math.floor(diff / 60000);
    var hours = Math.floor(diff / 3600000);
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return "".concat(minutes, "m ago");
    return "".concat(hours, "h ago");
  };
  var getActivityIcon = function getActivityIcon(type) {
    switch (type) {
      case 'component_added':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .RiseOutlined */ .Ugv, {
          style: {
            color: '#52c41a'
          }
        });
      case 'component_removed':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .FallOutlined */ .CXw, {
          style: {
            color: '#ff4d4f'
          }
        });
      case 'layout_changed':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BarChartOutlined */ .cd5, {
          style: {
            color: '#1890ff'
          }
        });
      case 'theme_updated':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BgColorsOutlined */ .Ebl, {
          style: {
            color: '#722ed1'
          }
        });
      case 'export_generated':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DownloadOutlined */ .jsW, {
          style: {
            color: '#fa8c16'
          }
        });
      default:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ClockCircleOutlined */ .L8Y, null);
    }
  };
  var renderMetricCard = function renderMetricCard(key, metric, title) {
    var suffix = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '';
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(MetricCard, {
      key: key
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
      title: title,
      value: metric.value,
      suffix: suffix,
      valueStyle: {
        fontSize: '24px',
        fontWeight: 'bold'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TrendIndicator, {
      trend: metric.change > 0 ? 'up' : 'down'
    }, metric.change > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .RiseOutlined */ .Ugv, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .FallOutlined */ .CXw, null), Math.abs(metric.change), "% vs last period"));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(AnalyticsContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
    level: 4,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BarChartOutlined */ .cd5, {
    style: {
      marginRight: 8
    }
  }), "Analytics Dashboard"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: selectedTimeRange,
    onChange: setSelectedTimeRange,
    style: {
      width: 120
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "1d"
  }, "Last 24h"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "7d"
  }, "Last 7 days"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "30d"
  }, "Last 30 days"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "90d"
  }, "Last 90 days")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: "Refresh Data"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ReloadOutlined */ .KF4, null),
    loading: loading,
    onClick: function onClick() {
      setLoading(true);
      setTimeout(function () {
        return setLoading(false);
      }, 1000);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: "Export Report"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DownloadOutlined */ .jsW, null),
    onClick: onExport
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Row */ .fI, {
    gutter: [16, 16],
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, renderMetricCard('views', metrics.totalViews || {}, 'Total Views')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, renderMetricCard('users', metrics.uniqueUsers || {}, 'Unique Users')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, renderMetricCard('session', metrics.avgSessionTime || {}, 'Avg Session', 's')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, renderMetricCard('bounce', metrics.bounceRate || {}, 'Bounce Rate', '%')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, renderMetricCard('conversion', metrics.conversionRate || {}, 'Conversion', '%')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    xs: 24,
    sm: 12,
    lg: 8
  }, renderMetricCard('errors', metrics.errorRate || {}, 'Error Rate', '%'))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
    title: "Component Performance",
    style: {
      height: 400
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8, {
    dataSource: componentStats,
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: 8
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        strong: true
      }, item.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: "secondary"
      }, "Usage: ", item.usage, "%"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: item.errors > 5 ? 'danger' : 'secondary'
      }, "Errors: ", item.errors))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Progress */ .ke, {
        percent: item.performance,
        size: "small",
        status: item.performance > 90 ? 'success' : item.performance > 70 ? 'normal' : 'exception'
      })));
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
    title: "Recent Activity",
    style: {
      height: 400
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      maxHeight: 320,
      overflowY: 'auto'
    }
  }, activities.map(function (activity) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ActivityItem, {
      key: activity.id
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: 12
      }
    }, getActivityIcon(activity.type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '14px'
      }
    }, activity.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, "by ", activity.user, " \u2022 ", formatTime(activity.timestamp)))));
  }))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnalyticsPanel);

/***/ })

}]);