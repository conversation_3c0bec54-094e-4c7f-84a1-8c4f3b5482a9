"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3913],{

/***/ 38812:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   O2: () => (/* binding */ DashboardSkeleton)
/* harmony export */ });
/* unused harmony exports TableSkeleton, CardSkeleton, FormSkeleton */
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(70572);

var _templateObject;



var SkeletonCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n"])));
var TableSkeleton = function TableSkeleton(_ref) {
  var _ref$rows = _ref.rows,
    rows = _ref$rows === void 0 ? 5 : _ref$rows;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA, {
    active: true,
    paragraph: {
      rows: 0
    }
  }), Array.from({
    length: rows
  }).map(function (_, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA, {
      key: "table-skeleton-row-".concat(index),
      active: true,
      paragraph: {
        rows: 0
      },
      style: {
        marginBottom: 16
      }
    });
  }));
};
var CardSkeleton = function CardSkeleton(_ref2) {
  var _ref2$rows = _ref2.rows,
    rows = _ref2$rows === void 0 ? 3 : _ref2$rows;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(SkeletonCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA, {
    active: true,
    paragraph: {
      rows: rows
    }
  }));
};
var DashboardSkeleton = function DashboardSkeleton(_ref3) {
  var _ref3$cards = _ref3.cards,
    cards = _ref3$cards === void 0 ? 4 : _ref3$cards;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
      gap: 24
    }
  }, Array.from({
    length: cards
  }).map(function (_, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(SkeletonCard, {
      key: "dashboard-skeleton-card-".concat(index)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA, {
      active: true,
      paragraph: {
        rows: 2
      }
    }));
  }));
};
var FormSkeleton = function FormSkeleton(_ref4) {
  var _ref4$fields = _ref4.fields,
    fields = _ref4$fields === void 0 ? 4 : _ref4$fields;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, Array.from({
    length: fields
  }).map(function (_, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
      key: "form-skeleton-field-".concat(index)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA.Input, {
      style: {
        width: 150,
        marginBottom: 8
      },
      active: true
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA.Input, {
      style: {
        width: '100%',
        height: 40
      },
      active: true
    }));
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Skeleton */ .EA.Button, {
    style: {
      width: 120,
      height: 40,
      marginTop: 16
    },
    active: true
  }));
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  TableSkeleton: TableSkeleton,
  CardSkeleton: CardSkeleton,
  DashboardSkeleton: DashboardSkeleton,
  FormSkeleton: FormSkeleton
});

/***/ }),

/***/ 56294:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(16918);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(11080);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71468);
/* harmony import */ var _redux_reducers_uiReducer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(85331);
/* harmony import */ var _services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(17053);
/* harmony import */ var _components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(66894);
/* harmony import */ var _components_common_SkeletonLoaders__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(38812);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

// Optimized Ant Design imports for better tree-shaking








var HomePage = function HomePage() {
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useDispatch */ .wA)();
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(_services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.getConnectionState()),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    connectionState = _useState4[0],
    setConnectionState = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      websocket: connectionState.connected,
      api: true,
      database: true,
      storage: Math.random() > 0.2 // Randomly simulate storage issues
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    systemStatus = _useState6[0],
    setSystemStatus = _useState6[1];

  // Set current view in Redux store
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    dispatch((0,_redux_reducers_uiReducer__WEBPACK_IMPORTED_MODULE_7__/* .setCurrentView */ .tI)('home'));

    // Simulate loading
    var timer = setTimeout(function () {
      setLoading(false);
    }, 1000);
    return function () {
      return clearTimeout(timer);
    };
  }, [dispatch]);

  // Listen for WebSocket events
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleConnect = function handleConnect() {
      var state = _services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.getConnectionState();
      setConnectionState(state);
      setSystemStatus(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          websocket: true
        });
      });
    };
    var handleDisconnect = function handleDisconnect() {
      var state = _services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.getConnectionState();
      setConnectionState(state);
      setSystemStatus(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          websocket: false
        });
      });
    };

    // Register event listeners
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.addEventListener('connect', handleConnect);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.addEventListener('disconnect', handleDisconnect);

    // Initial state
    setConnectionState(_services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.getConnectionState());
    setSystemStatus(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        websocket: connectionState.connected
      });
    });

    // Cleanup event listeners on unmount
    return function () {
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.removeEventListener('connect', handleConnect);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.removeEventListener('disconnect', handleDisconnect);
    };
  }, []);

  // Calculate overall system status
  var getOverallStatus = function getOverallStatus() {
    var statusValues = Object.values(systemStatus);
    if (statusValues.every(function (status) {
      return status === true;
    })) {
      return {
        status: 'success',
        text: 'All Systems Operational'
      };
    } else if (statusValues.filter(function (status) {
      return status === false;
    }).length > 1) {
      return {
        status: 'error',
        text: 'Multiple Systems Down'
      };
    } else {
      return {
        status: 'warning',
        text: 'Partial System Outage'
      };
    }
  };
  var overallStatus = getOverallStatus();

  // Render loading state
  if (loading) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .PageContainer */ .LN, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedTitle */ .p1, {
      level: 2
    }, "Welcome to App Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedParagraph */ .T6, null, "Build, test, and deploy your applications with ease."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_SkeletonLoaders__WEBPACK_IMPORTED_MODULE_10__/* .DashboardSkeleton */ .O2, {
      cards: 4
    }));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .PageContainer */ .LN, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedTitle */ .p1, {
    level: 2
  }, "Welcome to App Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedParagraph */ .T6, null, "Build, test, and deploy your applications with ease. Monitor your WebSocket connections, diagnose network issues, and optimize performance."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Alert */ .Fc, {
    type: overallStatus.status,
    message: overallStatus.text,
    showIcon: true,
    style: {
      marginBottom: 24
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .GridLayout */ .JT, {
    columns: 2,
    columnsSm: 1
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedCard */ .Lv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedTitle */ .p1, {
    level: 4
  }, "System Status"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8, {
    size: "large",
    dataSource: [{
      name: 'WebSocket Service',
      status: systemStatus.websocket,
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ApiOutlined */ .bfv, null),
      route: '/websocket-diagnostics'
    }, {
      name: 'API Service',
      status: systemStatus.api,
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ApiOutlined */ .bfv, null),
      route: '/dashboard'
    }, {
      name: 'Database',
      status: systemStatus.database,
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SettingOutlined */ .JO7, null),
      route: '/dashboard'
    }, {
      name: 'Storage Service',
      status: systemStatus.storage,
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SettingOutlined */ .JO7, null),
      route: '/dashboard'
    }],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8.Item, {
        actions: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_5__/* .Link */ .N_, {
          to: item.route
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
          type: "link",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .RightOutlined */ .Xq1, null)
        }, "Details"))]
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8.Item.Meta, {
        avatar: item.icon,
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .FlexContainer */ .n5, {
          justify: "space-between"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", null, item.name), item.status ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tag */ .vw, {
          color: "success",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckCircleOutlined */ .hWy, null)
        }, "Operational") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tag */ .vw, {
          color: "error",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .WarningOutlined */ .v7y, null)
        }, "Down"))
      }));
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedCard */ .Lv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedTitle */ .p1, {
    level: 4
  }, "Quick Actions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .FlexContainer */ .n5, {
    direction: "column",
    gap: 16
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_5__/* .Link */ .N_, {
    to: "/"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .PrimaryButton */ .jn, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DashboardOutlined */ .zpd, null),
    block: true
  }, "App Builder")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_5__/* .Link */ .N_, {
    to: "/app-builder"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .PrimaryButton */ .jn, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DashboardOutlined */ .zpd, null),
    block: true
  }, "App Builder (Alternative)")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_5__/* .Link */ .N_, {
    to: "/websocket"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .PrimaryButton */ .jn, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ApiOutlined */ .bfv, null),
    block: true
  }, "WebSocket Manager")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_5__/* .Link */ .N_, {
    to: "/websocket-diagnostics"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .PrimaryButton */ .jn, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ApiOutlined */ .bfv, null),
    block: true
  }, "WebSocket Diagnostics")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_5__/* .Link */ .N_, {
    to: "/network-diagnostic"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .PrimaryButton */ .jn, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ApiOutlined */ .bfv, null),
    block: true
  }, "Network Diagnostics"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Divider */ .cG, {
    style: {
      margin: '32px 0 24px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .GridLayout */ .JT, {
    columns: 3,
    columnsMd: 2,
    columnsSm: 1
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedCard */ .Lv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "WebSocket Status",
    value: connectionState.connected ? "Connected" : "Disconnected",
    valueStyle: {
      color: connectionState.connected ? '#3f8600' : '#cf1322'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_5__/* .Link */ .N_, {
    to: "/websocket-diagnostics"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "primary",
    size: "small"
  }, "View Details")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedCard */ .Lv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Connection Uptime",
    value: connectionState.connected ? "Active" : "Inactive",
    suffix: connectionState.connected ? "now" : "",
    valueStyle: {
      color: connectionState.connected ? '#3f8600' : '#cf1322'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_5__/* .Link */ .N_, {
    to: "/websocket-diagnostics"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "primary",
    size: "small"
  }, "View Details")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_components_common_StyledComponents__WEBPACK_IMPORTED_MODULE_9__/* .EnhancedCard */ .Lv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Reconnect Attempts",
    value: connectionState.reconnectAttempts || 0,
    valueStyle: {
      color: connectionState.reconnectAttempts > 0 ? '#faad14' : '#3f8600'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "primary",
    size: "small",
    onClick: function onClick() {
      return _services_WebSocketService__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A.reconnect();
    },
    disabled: connectionState.connected
  }, "Reconnect")))), connectionState.lastError && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Alert */ .Fc, {
    type: "error",
    message: "WebSocket Error",
    description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, connectionState.lastError.message), connectionState.lastError.timestamp && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        color: 'rgba(0, 0, 0, 0.45)',
        marginTop: 8
      }
    }, new Date(connectionState.lastError.timestamp).toLocaleString())),
    showIcon: true,
    style: {
      marginTop: 24
    }
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);

/***/ }),

/***/ 66894:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   JT: () => (/* binding */ GridLayout),
/* harmony export */   LN: () => (/* binding */ PageContainer),
/* harmony export */   Lv: () => (/* binding */ EnhancedCard),
/* harmony export */   T6: () => (/* binding */ EnhancedParagraph),
/* harmony export */   jn: () => (/* binding */ PrimaryButton),
/* harmony export */   n5: () => (/* binding */ FlexContainer),
/* harmony export */   p1: () => (/* binding */ EnhancedTitle),
/* harmony export */   pK: () => (/* binding */ DashboardCard)
/* harmony export */ });
/* unused harmony exports Section, SecondaryButton, EnhancedInput, EnhancedTable */
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(70572);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);

var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10;


var Title = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Paragraph;
var Content = antd__WEBPACK_IMPORTED_MODULE_2__/* .Layout */ .PE.Content;

// Page container with proper spacing
var PageContainer = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(Content)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: 24px;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  \n  @media (max-width: 768px) {\n    padding: 16px;\n  }\n"])));

// Section with proper spacing and optional background
var Section = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  margin-bottom: 32px;\n  padding: ", ";\n  background-color: ", ";\n  border-radius: 8px;\n"])), function (props) {
  return props.padded ? '24px' : '0';
}, function (props) {
  return props.background ? props.background : 'transparent';
});

// Enhanced card with consistent styling
var EnhancedCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\n  }\n  \n  .ant-card-head {\n    border-bottom: 1px solid #f0f0f0;\n  }\n  \n  .ant-card-head-title {\n    font-weight: 600;\n  }\n"])));

// Dashboard card for metrics and stats
var DashboardCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(EnhancedCard)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  height: 100%;\n  \n  .ant-card-body {\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    height: calc(100% - 57px); // Adjust for card header\n  }\n"])));

// Primary button with enhanced styling
var PrimaryButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  height: 40px;\n  font-weight: 500;\n  \n  &.ant-btn-primary {\n    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);\n    \n    &:hover {\n      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n    }\n  }\n"])));

// Secondary button with enhanced styling
var SecondaryButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  height: 40px;\n  font-weight: 500;\n"])));

// Enhanced title with proper spacing
var EnhancedTitle = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(Title)(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  margin-bottom: ", " !important;\n  color: ", ";\n"])), function (props) {
  return props.noMargin ? '0' : '24px';
}, function (props) {
  return props.color || 'inherit';
});

// Enhanced paragraph with proper spacing
var EnhancedParagraph = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(Paragraph)(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  margin-bottom: ", " !important;\n  font-size: 16px;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.65);\n"])), function (props) {
  return props.noMargin ? '0' : '16px';
});

// Enhanced input with consistent styling
var EnhancedInput = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Input */ .pd)(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  height: 40px;\n"])));

// Enhanced table with consistent styling
var EnhancedTable = (0,styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Table */ .XI)(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  .ant-table {\n    border-radius: 8px;\n    overflow: hidden;\n  }\n  \n  .ant-table-thead > tr > th {\n    background-color: #fafafa;\n    font-weight: 600;\n  }\n"])));

// Grid layout for dashboard cards
var GridLayout = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 24px;\n  margin-bottom: 32px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n"])));

// Flex container for layout
var FlexContainer = styled_components__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Ay.div(_templateObject10 || (_templateObject10 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: ", ";\n  justify-content: ", ";\n  align-items: ", ";\n  flex-wrap: ", ";\n  gap: ", "px;\n"])), function (props) {
  return props.direction || 'row';
}, function (props) {
  return props.justify || 'flex-start';
}, function (props) {
  return props.align || 'flex-start';
}, function (props) {
  return props.wrap || 'nowrap';
}, function (props) {
  return props.gap || '0';
});
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  PageContainer: PageContainer,
  Section: Section,
  EnhancedCard: EnhancedCard,
  DashboardCard: DashboardCard,
  PrimaryButton: PrimaryButton,
  SecondaryButton: SecondaryButton,
  EnhancedTitle: EnhancedTitle,
  EnhancedParagraph: EnhancedParagraph,
  EnhancedInput: EnhancedInput,
  EnhancedTable: EnhancedTable,
  GridLayout: GridLayout,
  FlexContainer: FlexContainer
});

/***/ }),

/***/ 85331:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   tI: () => (/* binding */ setCurrentView)
/* harmony export */ });
/* unused harmony exports TOGGLE_SIDEBAR, SET_CURRENT_VIEW, TOGGLE_PREVIEW_MODE, UI_LOADING_START, UI_LOADING_COMPLETE, toggleSidebar, togglePreviewMode, startLoading, completeLoading */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UI Reducer
 *
 * This reducer handles UI state, including sidebar, current view, and preview mode.
 */

// Action types
var TOGGLE_SIDEBAR = 'TOGGLE_SIDEBAR';
var SET_CURRENT_VIEW = 'SET_CURRENT_VIEW';
var TOGGLE_PREVIEW_MODE = 'TOGGLE_PREVIEW_MODE';
var UI_LOADING_START = 'UI_LOADING_START';
var UI_LOADING_COMPLETE = 'UI_LOADING_COMPLETE';

// Initial state
var initialState = {
  sidebarOpen: true,
  currentView: 'components',
  previewMode: false,
  loading: false
};

/**
 * UI reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
var uiReducer = function uiReducer() {
  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;
  var action = arguments.length > 1 ? arguments[1] : undefined;
  switch (action.type) {
    case TOGGLE_SIDEBAR:
      return _objectSpread(_objectSpread({}, state), {}, {
        sidebarOpen: !state.sidebarOpen
      });
    case SET_CURRENT_VIEW:
      return _objectSpread(_objectSpread({}, state), {}, {
        currentView: action.payload
      });
    case TOGGLE_PREVIEW_MODE:
      return _objectSpread(_objectSpread({}, state), {}, {
        previewMode: !state.previewMode
      });
    case UI_LOADING_START:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: true
      });
    case UI_LOADING_COMPLETE:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: false
      });
    default:
      return state;
  }
};

// Action creators
var toggleSidebar = function toggleSidebar() {
  return {
    type: TOGGLE_SIDEBAR
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: SET_CURRENT_VIEW,
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: TOGGLE_PREVIEW_MODE
  };
};
var startLoading = function startLoading() {
  return {
    type: UI_LOADING_START
  };
};
var completeLoading = function completeLoading() {
  return {
    type: UI_LOADING_COMPLETE
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (uiReducer)));

/***/ })

}]);