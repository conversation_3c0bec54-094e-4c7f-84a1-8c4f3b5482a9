"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6089],{

/***/ 66072:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);


var _templateObject, _templateObject2, _templateObject3, _templateObject4;




var Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;

/**
 * Advanced Color Picker
 * 
 * Enhanced color picker with presets, gradients, and advanced color manipulation.
 */

var ColorContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  .ant-color-picker-trigger {\n    width: 100%;\n    height: 32px;\n    border-radius: 6px;\n    border: 1px solid #d9d9d9;\n    cursor: pointer;\n    transition: all 0.2s;\n    \n    &:hover {\n      border-color: #40a9ff;\n    }\n  }\n"])));
var PresetColors = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(8, 1fr);\n  gap: 4px;\n  margin: 8px 0;\n"])));
var PresetColor = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  width: 24px;\n  height: 24px;\n  border-radius: 4px;\n  cursor: pointer;\n  border: 2px solid ", ";\n  transition: all 0.2s;\n  \n  &:hover {\n    transform: scale(1.1);\n    border-color: #40a9ff;\n  }\n"])), function (props) {
  return props.selected ? '#1890ff' : 'transparent';
});
var GradientPreview = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  width: 100%;\n  height: 32px;\n  border-radius: 6px;\n  border: 1px solid #d9d9d9;\n  background: ", ";\n  cursor: pointer;\n  transition: all 0.2s;\n  \n  &:hover {\n    border-color: #40a9ff;\n  }\n"])), function (props) {
  return props.gradient;
});
var ColorPicker = function ColorPicker(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? '#1890ff' : _ref$value,
    onChange = _ref.onChange,
    _ref$showPresets = _ref.showPresets,
    showPresets = _ref$showPresets === void 0 ? true : _ref$showPresets,
    _ref$showGradients = _ref.showGradients,
    showGradients = _ref$showGradients === void 0 ? false : _ref$showGradients,
    _ref$showAlpha = _ref.showAlpha,
    showAlpha = _ref$showAlpha === void 0 ? true : _ref$showAlpha,
    _ref$presets = _ref.presets,
    presets = _ref$presets === void 0 ? [] : _ref$presets,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'default' : _ref$size;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(value),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    currentValue = _useState2[0],
    setCurrentValue = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    showAdvanced = _useState4[0],
    setShowAdvanced = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    gradientMode = _useState6[0],
    setGradientMode = _useState6[1];

  // Default color presets
  var defaultPresets = ['#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d', '#bae637', '#73d13d', '#40a9ff', '#1890ff', '#722ed1', '#eb2f96', '#f5222d', '#fa541c', '#fa8c16', '#fadb14', '#a0d911', '#52c41a', '#13c2c2', '#1890ff', '#2f54eb', '#722ed1', '#eb2f96', '#000000', '#434343', '#666666', '#999999', '#cccccc', '#eeeeee', '#ffffff'];
  var colorPresets = presets.length > 0 ? presets : defaultPresets;

  // Gradient presets
  var gradientPresets = ['linear-gradient(45deg, #ff6b6b, #feca57)', 'linear-gradient(45deg, #48cae4, #023e8a)', 'linear-gradient(45deg, #f093fb, #f5576c)', 'linear-gradient(45deg, #4facfe, #00f2fe)', 'linear-gradient(45deg, #43e97b, #38f9d7)', 'linear-gradient(45deg, #fa709a, #fee140)', 'linear-gradient(45deg, #a8edea, #fed6e3)', 'linear-gradient(45deg, #ff9a9e, #fecfef)'];
  var handleColorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (color, hex) {
    var newValue = hex || color;
    setCurrentValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  }, [onChange]);
  var handlePresetClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (color) {
    handleColorChange(color);
  }, [handleColorChange]);
  var handleGradientClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (gradient) {
    handleColorChange(gradient);
  }, [handleColorChange]);
  var parseColorValue = function parseColorValue(colorValue) {
    if (typeof colorValue === 'string') {
      if (colorValue.startsWith('linear-gradient') || colorValue.startsWith('radial-gradient')) {
        return {
          type: 'gradient',
          value: colorValue
        };
      }
      return {
        type: 'color',
        value: colorValue
      };
    }
    return {
      type: 'color',
      value: '#1890ff'
    };
  };
  var colorInfo = parseColorValue(currentValue);
  var renderAdvancedPanel = function renderAdvancedPanel() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
      size: "small",
      style: {
        width: 280
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      strong: true
    }, "Color Value"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Input */ .pd, {
      value: currentValue,
      onChange: function onChange(e) {
        return handleColorChange(e.target.value);
      },
      placeholder: "Enter color value",
      style: {
        marginTop: 4
      }
    })), showPresets && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      strong: true
    }, "Color Presets"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(PresetColors, null, colorPresets.map(function (color, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(PresetColor, {
        key: index,
        style: {
          backgroundColor: color
        },
        selected: currentValue === color,
        onClick: function onClick() {
          return handlePresetClick(color);
        },
        title: color
      });
    }))), showGradients && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      strong: true
    }, "Gradient Presets"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%',
        marginTop: 8
      }
    }, gradientPresets.map(function (gradient, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(GradientPreview, {
        key: index,
        gradient: gradient,
        onClick: function onClick() {
          return handleGradientClick(gradient);
        },
        title: gradient
      });
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BgColorsOutlined */ .Ebl, null),
      onClick: function onClick() {
        return setGradientMode(!gradientMode);
      }
    }, gradientMode ? 'Solid Color' : 'Gradient'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .EyeOutlined */ .Om2, null),
      onClick: function onClick() {
        // Eye dropper functionality would go here
        console.log('Eye dropper not implemented');
      }
    }, "Pick")))));
  };
  if (colorInfo.type === 'gradient') {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ColorContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Popover */ .AM, {
      content: renderAdvancedPanel(),
      trigger: "click",
      placement: "bottomLeft",
      open: showAdvanced,
      onOpenChange: setShowAdvanced
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(GradientPreview, {
      gradient: currentValue,
      style: {
        height: size === 'small' ? 24 : size === 'large' ? 40 : 32
      }
    })));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ColorContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .ColorPicker */ .sk, {
    value: currentValue,
    onChange: handleColorChange,
    showText: true,
    disabled: disabled,
    size: size,
    presets: showPresets ? [{
      label: 'Recommended',
      colors: colorPresets.slice(0, 10)
    }, {
      label: 'Recent',
      colors: colorPresets.slice(10, 20)
    }] : [],
    panelRender: function panelRender(panel) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          width: 280
        }
      }, panel, showAdvanced && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          marginTop: 12,
          paddingTop: 12,
          borderTop: '1px solid #f0f0f0'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
        direction: "vertical",
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        strong: true
      }, "Hex Value"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Input */ .pd, {
        value: currentValue,
        onChange: function onChange(e) {
          return handleColorChange(e.target.value);
        },
        placeholder: "#1890ff",
        style: {
          marginTop: 4
        }
      })), showGradients && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        strong: true
      }, "Gradients"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
        direction: "vertical",
        style: {
          width: '100%',
          marginTop: 8
        }
      }, gradientPresets.slice(0, 4).map(function (gradient, index) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(GradientPreview, {
          key: index,
          gradient: gradient,
          onClick: function onClick() {
            return handleGradientClick(gradient);
          },
          style: {
            height: 24
          }
        });
      }))))));
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    size: "small",
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BgColorsOutlined */ .Ebl, null),
    onClick: function onClick() {
      return setShowAdvanced(!showAdvanced);
    }
  }, showAdvanced ? 'Simple' : 'Advanced'), showGradients && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    size: "small",
    type: "text",
    onClick: function onClick() {
      return setGradientMode(!gradientMode);
    }
  }, "Gradient"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ColorPicker);

/***/ }),

/***/ 76990:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



var Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6.Option;

/**
 * Animation Editor
 * 
 * Editor for CSS animations and transitions with live preview.
 */

var AnimationEditor = function AnimationEditor(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? {} : _ref$value,
    onChange = _ref.onChange,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    isPlaying = _useState2[0],
    setIsPlaying = _useState2[1];
  var animationTypes = [{
    label: 'Fade In',
    value: 'fadeIn'
  }, {
    label: 'Fade Out',
    value: 'fadeOut'
  }, {
    label: 'Slide In Left',
    value: 'slideInLeft'
  }, {
    label: 'Slide In Right',
    value: 'slideInRight'
  }, {
    label: 'Slide In Up',
    value: 'slideInUp'
  }, {
    label: 'Slide In Down',
    value: 'slideInDown'
  }, {
    label: 'Bounce In',
    value: 'bounceIn'
  }, {
    label: 'Zoom In',
    value: 'zoomIn'
  }, {
    label: 'Rotate In',
    value: 'rotateIn'
  }, {
    label: 'Pulse',
    value: 'pulse'
  }, {
    label: 'Shake',
    value: 'shake'
  }, {
    label: 'Wobble',
    value: 'wobble'
  }];
  var easingFunctions = [{
    label: 'Linear',
    value: 'linear'
  }, {
    label: 'Ease',
    value: 'ease'
  }, {
    label: 'Ease In',
    value: 'ease-in'
  }, {
    label: 'Ease Out',
    value: 'ease-out'
  }, {
    label: 'Ease In Out',
    value: 'ease-in-out'
  }, {
    label: 'Cubic Bezier',
    value: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
  }];
  var handleChange = function handleChange(property, newValue) {
    var updated = _objectSpread(_objectSpread({}, value), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, property, newValue));
    if (onChange) onChange(updated);
  };
  var getAnimationCSS = function getAnimationCSS() {
    var _value$type = value.type,
      type = _value$type === void 0 ? 'fadeIn' : _value$type,
      _value$duration = value.duration,
      duration = _value$duration === void 0 ? 1 : _value$duration,
      _value$delay = value.delay,
      delay = _value$delay === void 0 ? 0 : _value$delay,
      _value$easing = value.easing,
      easing = _value$easing === void 0 ? 'ease' : _value$easing,
      _value$iterations = value.iterations,
      iterations = _value$iterations === void 0 ? 1 : _value$iterations,
      _value$direction = value.direction,
      direction = _value$direction === void 0 ? 'normal' : _value$direction,
      _value$fillMode = value.fillMode,
      fillMode = _value$fillMode === void 0 ? 'both' : _value$fillMode;
    return {
      animation: "".concat(type, " ").concat(duration, "s ").concat(easing, " ").concat(delay, "s ").concat(iterations === -1 ? 'infinite' : iterations, " ").concat(direction, " ").concat(fillMode),
      animationPlayState: isPlaying ? 'running' : 'paused'
    };
  };
  var keyframes = {
    fadeIn: "\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    ",
    fadeOut: "\n      @keyframes fadeOut {\n        from { opacity: 1; }\n        to { opacity: 0; }\n      }\n    ",
    slideInLeft: "\n      @keyframes slideInLeft {\n        from { transform: translateX(-100%); }\n        to { transform: translateX(0); }\n      }\n    ",
    slideInRight: "\n      @keyframes slideInRight {\n        from { transform: translateX(100%); }\n        to { transform: translateX(0); }\n      }\n    ",
    slideInUp: "\n      @keyframes slideInUp {\n        from { transform: translateY(100%); }\n        to { transform: translateY(0); }\n      }\n    ",
    slideInDown: "\n      @keyframes slideInDown {\n        from { transform: translateY(-100%); }\n        to { transform: translateY(0); }\n      }\n    ",
    bounceIn: "\n      @keyframes bounceIn {\n        0%, 20%, 40%, 60%, 80%, 100% { animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }\n        0% { opacity: 0; transform: scale3d(0.3, 0.3, 0.3); }\n        20% { transform: scale3d(1.1, 1.1, 1.1); }\n        40% { transform: scale3d(0.9, 0.9, 0.9); }\n        60% { opacity: 1; transform: scale3d(1.03, 1.03, 1.03); }\n        80% { transform: scale3d(0.97, 0.97, 0.97); }\n        100% { opacity: 1; transform: scale3d(1, 1, 1); }\n      }\n    ",
    zoomIn: "\n      @keyframes zoomIn {\n        from { opacity: 0; transform: scale3d(0.3, 0.3, 0.3); }\n        50% { opacity: 1; }\n      }\n    ",
    rotateIn: "\n      @keyframes rotateIn {\n        from { transform: rotate3d(0, 0, 1, -200deg); opacity: 0; }\n        to { transform: translate3d(0, 0, 0); opacity: 1; }\n      }\n    ",
    pulse: "\n      @keyframes pulse {\n        from { transform: scale3d(1, 1, 1); }\n        50% { transform: scale3d(1.05, 1.05, 1.05); }\n        to { transform: scale3d(1, 1, 1); }\n      }\n    ",
    shake: "\n      @keyframes shake {\n        from, to { transform: translate3d(0, 0, 0); }\n        10%, 30%, 50%, 70%, 90% { transform: translate3d(-10px, 0, 0); }\n        20%, 40%, 60%, 80% { transform: translate3d(10px, 0, 0); }\n      }\n    ",
    wobble: "\n      @keyframes wobble {\n        from { transform: translate3d(0, 0, 0); }\n        15% { transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg); }\n        30% { transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg); }\n        45% { transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg); }\n        60% { transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg); }\n        75% { transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg); }\n        to { transform: translate3d(0, 0, 0); }\n      }\n    "
  };
  var playAnimation = function playAnimation() {
    setIsPlaying(true);
    setTimeout(function () {
      return setIsPlaying(false);
    }, (value.duration || 1) * 1000);
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    strong: true
  }, "Animation"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Animation Type"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: value.type || 'fadeIn',
    onChange: function onChange(val) {
      return handleChange('type', val);
    },
    style: {
      width: '100%',
      marginTop: 4
    }
  }, animationTypes.map(function (type) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
      key: type.value,
      value: type.value
    }, type.label);
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Duration"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .InputNumber */ .YI, {
    value: value.duration || 1,
    onChange: function onChange(val) {
      return handleChange('duration', val);
    },
    min: 0.1,
    max: 10,
    step: 0.1,
    style: {
      width: '100%',
      marginTop: 4
    },
    addonAfter: "s"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Delay"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .InputNumber */ .YI, {
    value: value.delay || 0,
    onChange: function onChange(val) {
      return handleChange('delay', val);
    },
    min: 0,
    max: 5,
    step: 0.1,
    style: {
      width: '100%',
      marginTop: 4
    },
    addonAfter: "s"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Easing"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: value.easing || 'ease',
    onChange: function onChange(val) {
      return handleChange('easing', val);
    },
    style: {
      width: '100%',
      marginTop: 4
    }
  }, easingFunctions.map(function (easing) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
      key: easing.value,
      value: easing.value
    }, easing.label);
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Iterations"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    style: {
      width: '100%',
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .InputNumber */ .YI, {
    value: value.iterations === -1 ? 'infinite' : value.iterations || 1,
    onChange: function onChange(val) {
      return handleChange('iterations', val === 'infinite' ? -1 : val);
    },
    min: 1,
    max: 10,
    style: {
      flex: 1
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Switch */ .dO, {
    checked: value.iterations === -1,
    onChange: function onChange(checked) {
      return handleChange('iterations', checked ? -1 : 1);
    },
    checkedChildren: "\u221E",
    unCheckedChildren: "1"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Direction"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: value.direction || 'normal',
    onChange: function onChange(val) {
      return handleChange('direction', val);
    },
    style: {
      width: '100%',
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "normal"
  }, "Normal"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "reverse"
  }, "Reverse"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "alternate"
  }, "Alternate"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "alternate-reverse"
  }, "Alternate Reverse"))), showPreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
    size: "small",
    title: "Preview",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .PlayCircleOutlined */ .VgC, null),
      onClick: playAnimation,
      disabled: isPlaying
    }, "Play"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ReloadOutlined */ .KF4, null),
      onClick: function onClick() {
        setIsPlaying(false);
        setTimeout(function () {
          return playAnimation();
        }, 100);
      }
    }, "Replay"))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("style", null, keyframes[value.type || 'fadeIn']), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: _objectSpread({
      width: 100,
      height: 60,
      backgroundColor: '#1890ff',
      borderRadius: 4,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontSize: 12,
      margin: '20px auto'
    }, getAnimationCSS())
  }, "Preview")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
    size: "small",
    title: "CSS Output"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("pre", {
    style: {
      fontSize: 11,
      background: '#f5f5f5',
      padding: 8,
      borderRadius: 4,
      margin: 0,
      overflow: 'auto'
    }
  }, "animation: ".concat(value.type || 'fadeIn', " ").concat(value.duration || 1, "s ").concat(value.easing || 'ease', " ").concat(value.delay || 0, "s ").concat(value.iterations === -1 ? 'infinite' : value.iterations || 1, " ").concat(value.direction || 'normal', " ").concat(value.fillMode || 'both', ";"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnimationEditor);

/***/ }),

/***/ 82353:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);




var _templateObject, _templateObject2, _templateObject3;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;

/**
 * Advanced Spacing Editor
 * 
 * Visual editor for margin, padding, and other spacing properties.
 */

var SpacingContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  .spacing-visual {\n    position: relative;\n    width: 200px;\n    height: 150px;\n    margin: 16px auto;\n    background: #f5f5f5;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n  }\n  \n  .margin-area {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: rgba(255, 193, 7, 0.2);\n    border: 1px solid #ffc107;\n    border-radius: 6px;\n  }\n  \n  .padding-area {\n    position: absolute;\n    background: rgba(40, 167, 69, 0.2);\n    border: 1px solid #28a745;\n    border-radius: 4px;\n  }\n  \n  .content-area {\n    position: absolute;\n    background: rgba(0, 123, 255, 0.2);\n    border: 1px solid #007bff;\n    border-radius: 2px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 12px;\n    color: #007bff;\n  }\n"])));
var SpacingInput = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n  \n  .spacing-label {\n    min-width: 60px;\n    font-size: 12px;\n    color: #666;\n  }\n  \n  .ant-input-number {\n    width: 60px;\n  }\n"])));
var SpacingGrid = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: 1fr auto 1fr;\n  grid-template-rows: 1fr auto 1fr;\n  gap: 4px;\n  width: 120px;\n  margin: 0 auto;\n  \n  .spacing-input {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .center-element {\n    grid-column: 2;\n    grid-row: 2;\n    background: #f0f0f0;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    padding: 8px;\n    text-align: center;\n    font-size: 11px;\n    color: #666;\n  }\n"])));
var SpacingEditor = function SpacingEditor(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? {
      margin: 0,
      padding: 0
    } : _ref$value,
    onChange = _ref.onChange,
    _ref$properties = _ref.properties,
    properties = _ref$properties === void 0 ? ['margin', 'padding'] : _ref$properties,
    _ref$units = _ref.units,
    units = _ref$units === void 0 ? ['px', 'rem', 'em', '%'] : _ref$units,
    _ref$showVisual = _ref.showVisual,
    showVisual = _ref$showVisual === void 0 ? true : _ref$showVisual,
    _ref$showPresets = _ref.showPresets,
    showPresets = _ref$showPresets === void 0 ? true : _ref$showPresets;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    linkedMargin = _useState2[0],
    setLinkedMargin = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    linkedPadding = _useState4[0],
    setLinkedPadding = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('px'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    unit = _useState6[0],
    setUnit = _useState6[1];

  // Parse spacing value (can be number, string, or object)
  var parseSpacing = function parseSpacing(spacing) {
    if (typeof spacing === 'number') {
      return {
        top: spacing,
        right: spacing,
        bottom: spacing,
        left: spacing
      };
    }
    if (typeof spacing === 'string') {
      var values = spacing.split(' ').map(function (v) {
        return parseInt(v) || 0;
      });
      if (values.length === 1) return {
        top: values[0],
        right: values[0],
        bottom: values[0],
        left: values[0]
      };
      if (values.length === 2) return {
        top: values[0],
        right: values[1],
        bottom: values[0],
        left: values[1]
      };
      if (values.length === 4) return {
        top: values[0],
        right: values[1],
        bottom: values[2],
        left: values[3]
      };
    }
    if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(spacing) === 'object') {
      return {
        top: spacing.top || 0,
        right: spacing.right || 0,
        bottom: spacing.bottom || 0,
        left: spacing.left || 0
      };
    }
    return {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    };
  };
  var margin = parseSpacing(value.margin);
  var padding = parseSpacing(value.padding);
  var handleSpacingChange = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (property, side, newValue) {
    var currentSpacing = parseSpacing(value[property]);
    var updatedSpacing = _objectSpread(_objectSpread({}, currentSpacing), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, side, newValue));

    // If linked, update all sides
    if (property === 'margin' && linkedMargin || property === 'padding' && linkedPadding) {
      updatedSpacing.top = newValue;
      updatedSpacing.right = newValue;
      updatedSpacing.bottom = newValue;
      updatedSpacing.left = newValue;
    }
    var newValue_obj = _objectSpread(_objectSpread({}, value), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, property, updatedSpacing));
    if (onChange) {
      onChange(newValue_obj);
    }
  }, [value, onChange, linkedMargin, linkedPadding]);
  var handlePresetClick = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (preset) {
    if (onChange) {
      onChange(preset);
    }
  }, [onChange]);
  var formatSpacingValue = function formatSpacingValue(spacing) {
    var top = spacing.top,
      right = spacing.right,
      bottom = spacing.bottom,
      left = spacing.left;
    if (top === right && right === bottom && bottom === left) {
      return "".concat(top).concat(unit);
    }
    return "".concat(top).concat(unit, " ").concat(right).concat(unit, " ").concat(bottom).concat(unit, " ").concat(left).concat(unit);
  };
  var presets = [{
    label: 'None',
    value: {
      margin: 0,
      padding: 0
    }
  }, {
    label: 'Small',
    value: {
      margin: 8,
      padding: 8
    }
  }, {
    label: 'Medium',
    value: {
      margin: 16,
      padding: 16
    }
  }, {
    label: 'Large',
    value: {
      margin: 24,
      padding: 24
    }
  }, {
    label: 'Card',
    value: {
      margin: 16,
      padding: 24
    }
  }, {
    label: 'Button',
    value: {
      margin: 4,
      padding: {
        top: 8,
        right: 16,
        bottom: 8,
        left: 16
      }
    }
  }];
  var renderVisualEditor = function renderVisualEditor() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SpacingContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "spacing-visual"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "margin-area",
      style: {
        top: "".concat(margin.top, "px"),
        right: "".concat(margin.right, "px"),
        bottom: "".concat(margin.bottom, "px"),
        left: "".concat(margin.left, "px")
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "padding-area",
      style: {
        top: "".concat(padding.top, "px"),
        right: "".concat(padding.right, "px"),
        bottom: "".concat(padding.bottom, "px"),
        left: "".concat(padding.left, "px")
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "content-area"
    }, "Content")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        textAlign: 'center',
        marginTop: 8
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      style: {
        fontSize: 11,
        color: '#ffc107'
      }
    }, "\u25A0 Margin"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      style: {
        fontSize: 11,
        color: '#28a745'
      }
    }, "\u25A0 Padding"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      style: {
        fontSize: 11,
        color: '#007bff'
      }
    }, "\u25A0 Content"))));
  };
  var renderSpacingInputs = function renderSpacingInputs(property, spacing, linked, setLinked) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, {
      size: "small",
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
        style: {
          textTransform: 'capitalize'
        }
      }, property), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
        type: "text",
        size: "small",
        icon: linked ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LinkOutlined */ .t7c, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DisconnectOutlined */ .Bu6, null),
        onClick: function onClick() {
          return setLinked(!linked);
        },
        title: linked ? 'Unlink sides' : 'Link all sides'
      }))
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SpacingGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "spacing-input",
      style: {
        gridColumn: 2,
        gridRow: 1
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .InputNumber */ .YI, {
      size: "small",
      value: spacing.top,
      onChange: function onChange(val) {
        return handleSpacingChange(property, 'top', val || 0);
      },
      min: 0,
      placeholder: "0"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "spacing-input",
      style: {
        gridColumn: 1,
        gridRow: 2
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .InputNumber */ .YI, {
      size: "small",
      value: spacing.left,
      onChange: function onChange(val) {
        return handleSpacingChange(property, 'left', val || 0);
      },
      min: 0,
      placeholder: "0"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "center-element"
    }, property), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "spacing-input",
      style: {
        gridColumn: 3,
        gridRow: 2
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .InputNumber */ .YI, {
      size: "small",
      value: spacing.right,
      onChange: function onChange(val) {
        return handleSpacingChange(property, 'right', val || 0);
      },
      min: 0,
      placeholder: "0"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      className: "spacing-input",
      style: {
        gridColumn: 2,
        gridRow: 3
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .InputNumber */ .YI, {
      size: "small",
      value: spacing.bottom,
      onChange: function onChange(val) {
        return handleSpacingChange(property, 'bottom', val || 0);
      },
      min: 0,
      placeholder: "0"
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        marginTop: 12,
        textAlign: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 11
      }
    }, formatSpacingValue(spacing))));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "Spacing"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: unit,
    onChange: setUnit,
    size: "small",
    style: {
      width: 60
    }
  }, units.map(function (u) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
      key: u,
      value: u
    }, u);
  }))), showVisual && renderVisualEditor(), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, properties.includes('margin') && renderSpacingInputs('margin', margin, linkedMargin, setLinkedMargin), properties.includes('padding') && renderSpacingInputs('padding', padding, linkedPadding, setLinkedPadding)), showPresets && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, {
    size: "small",
    title: "Presets"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    wrap: true
  }, presets.map(function (preset, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      key: index,
      size: "small",
      onClick: function onClick() {
        return handlePresetClick(preset.value);
      }
    }, preset.label);
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpacingEditor);

/***/ }),

/***/ 84477:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



var Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6.Option;

/**
 * Typography Editor
 * 
 * Advanced typography editor with font selection, sizing, and styling options.
 */

var TypographyEditor = function TypographyEditor(_ref) {
  var _ref$value = _ref.value,
    value = _ref$value === void 0 ? {} : _ref$value,
    onChange = _ref.onChange,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('The quick brown fox jumps over the lazy dog'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    previewText = _useState2[0],
    setPreviewText = _useState2[1];
  var fontFamilies = ['Arial, sans-serif', 'Helvetica, sans-serif', 'Georgia, serif', 'Times New Roman, serif', 'Courier New, monospace', 'Verdana, sans-serif', 'Trebuchet MS, sans-serif', 'Impact, sans-serif'];
  var fontWeights = [{
    label: 'Thin',
    value: 100
  }, {
    label: 'Light',
    value: 300
  }, {
    label: 'Normal',
    value: 400
  }, {
    label: 'Medium',
    value: 500
  }, {
    label: 'Semi Bold',
    value: 600
  }, {
    label: 'Bold',
    value: 700
  }, {
    label: 'Extra Bold',
    value: 800
  }, {
    label: 'Black',
    value: 900
  }];
  var handleChange = function handleChange(property, newValue) {
    var updated = _objectSpread(_objectSpread({}, value), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, property, newValue));
    if (onChange) onChange(updated);
  };
  var previewStyle = {
    fontFamily: value.fontFamily || 'Arial, sans-serif',
    fontSize: "".concat(value.fontSize || 16, "px"),
    fontWeight: value.fontWeight || 400,
    fontStyle: value.fontStyle || 'normal',
    textDecoration: value.textDecoration || 'none',
    lineHeight: value.lineHeight || 1.5,
    letterSpacing: "".concat(value.letterSpacing || 0, "px"),
    textAlign: value.textAlign || 'left',
    color: value.color || '#000000'
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    strong: true
  }, "Typography"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Font Family"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: value.fontFamily || 'Arial, sans-serif',
    onChange: function onChange(val) {
      return handleChange('fontFamily', val);
    },
    style: {
      width: '100%',
      marginTop: 4
    }
  }, fontFamilies.map(function (font) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
      key: font,
      value: font,
      style: {
        fontFamily: font
      }
    }, font.split(',')[0]);
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Size"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .InputNumber */ .YI, {
    value: value.fontSize || 16,
    onChange: function onChange(val) {
      return handleChange('fontSize', val);
    },
    min: 8,
    max: 72,
    style: {
      width: '100%',
      marginTop: 4
    },
    addonAfter: "px"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Weight"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: value.fontWeight || 400,
    onChange: function onChange(val) {
      return handleChange('fontWeight', val);
    },
    style: {
      width: '100%',
      marginTop: 4
    }
  }, fontWeights.map(function (weight) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
      key: weight.value,
      value: weight.value
    }, weight.label);
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Style"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BoldOutlined */ ._Kc, null),
    type: value.fontWeight >= 600 ? 'primary' : 'default',
    onClick: function onClick() {
      return handleChange('fontWeight', value.fontWeight >= 600 ? 400 : 700);
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ItalicOutlined */ .H83, null),
    type: value.fontStyle === 'italic' ? 'primary' : 'default',
    onClick: function onClick() {
      return handleChange('fontStyle', value.fontStyle === 'italic' ? 'normal' : 'italic');
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .UnderlineOutlined */ .xaI, null),
    type: value.textDecoration === 'underline' ? 'primary' : 'default',
    onClick: function onClick() {
      return handleChange('textDecoration', value.textDecoration === 'underline' ? 'none' : 'underline');
    }
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Line Height: ", value.lineHeight || 1.5), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Slider */ .Ap, {
    value: value.lineHeight || 1.5,
    onChange: function onChange(val) {
      return handleChange('lineHeight', val);
    },
    min: 1,
    max: 3,
    step: 0.1,
    style: {
      marginTop: 4
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Letter Spacing: ", value.letterSpacing || 0, "px"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Slider */ .Ap, {
    value: value.letterSpacing || 0,
    onChange: function onChange(val) {
      return handleChange('letterSpacing', val);
    },
    min: -2,
    max: 10,
    step: 0.5,
    style: {
      marginTop: 4
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    style: {
      fontSize: 12,
      color: '#666'
    }
  }, "Text Align"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: value.textAlign || 'left',
    onChange: function onChange(val) {
      return handleChange('textAlign', val);
    },
    style: {
      width: '100%',
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "left"
  }, "Left"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "center"
  }, "Center"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "right"
  }, "Right"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "justify"
  }, "Justify"))), showPreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
    size: "small",
    title: "Preview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: previewStyle
  }, previewText)));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TypographyEditor);

/***/ })

}]);