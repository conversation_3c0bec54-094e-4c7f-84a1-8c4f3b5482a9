"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6474],{

/***/ 928:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71468);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79146);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(86020);
/* harmony import */ var _redux_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(81616);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(2543);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(91018);




var _excluded = ["name"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }









// Import enhanced property editor components

var Title = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_6__/* .Tabs */ .tU.TabPane;
var PropertiesContainer = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n"])));
var PropertiesContent = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n"])));
var PropertiesActions = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: 16px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n"])));
var TabContent = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: 0;\n"])));

/**
 * Enhanced ComponentProperties component with advanced property editing capabilities
 * Now includes real-time preview updates and collaborative editing support
 */
var EnhancedComponentProperties = function EnhancedComponentProperties(_ref) {
  var component = _ref.component,
    onUpdate = _ref.onUpdate,
    onRealTimeUpdate = _ref.onRealTimeUpdate,
    _ref$enableRealTimePr = _ref.enableRealTimePreview,
    enableRealTimePreview = _ref$enableRealTimePr === void 0 ? true : _ref$enableRealTimePr,
    _ref$enableCollaborat = _ref.enableCollaboration,
    enableCollaboration = _ref$enableCollaborat === void 0 ? false : _ref$enableCollaborat,
    _ref$collaborativeSes = _ref.collaborativeSession,
    collaborativeSession = _ref$collaborativeSes === void 0 ? null : _ref$collaborativeSes;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useDispatch */ .wA)();
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('basic'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({}),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    propertyValues = _useState4[0],
    setPropertyValues = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({}),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    filteredProperties = _useState6[0],
    setFilteredProperties = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    hasUnsavedChanges = _useState8[0],
    setHasUnsavedChanges = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(enableRealTimePreview),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    isRealTimeEnabled = _useState0[0],
    setIsRealTimeEnabled = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    isUpdating = _useState10[0],
    setIsUpdating = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    lastUpdateTime = _useState12[0],
    setLastUpdateTime = _useState12[1];

  // Refs for debouncing and performance
  var updateTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var lastValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)({});

  // Initialize property values when component changes
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (component) {
      var initialValues = _objectSpread(_objectSpread({
        name: component.name
      }, component.props), component.style);
      setPropertyValues(initialValues);
      setHasUnsavedChanges(false);
    }
  }, [component]);

  // Get component-specific properties
  var componentProperties = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    if (!(component !== null && component !== void 0 && component.type)) return {};
    return (0,_index__WEBPACK_IMPORTED_MODULE_12__/* .getComponentProperties */ .vS)(component.type);
  }, [component === null || component === void 0 ? void 0 : component.type]);

  // Get style properties grouped by category
  var stylePropertiesGrouped = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    return (0,_index__WEBPACK_IMPORTED_MODULE_12__/* .getStylePropertiesGrouped */ .Lp)();
  }, []);

  // Debounced real-time update function
  var debouncedRealTimeUpdate = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    return (0,lodash__WEBPACK_IMPORTED_MODULE_11__.debounce)(function (updatedComponent) {
      if (isRealTimeEnabled && onRealTimeUpdate) {
        setIsUpdating(true);
        onRealTimeUpdate(updatedComponent);
        setLastUpdateTime(new Date());

        // Clear updating state after a short delay
        setTimeout(function () {
          return setIsUpdating(false);
        }, 300);
      }
    }, 300);
  }, [isRealTimeEnabled, onRealTimeUpdate]);

  // Immediate visual update function (no debounce)
  var immediateUpdate = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (updatedComponent) {
    if (onUpdate) {
      onUpdate(updatedComponent);
    }
  }, [onUpdate]);

  // Handle property value change with real-time updates
  var handlePropertyChange = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (propertyName, newValue, schema) {
    var newPropertyValues = _objectSpread(_objectSpread({}, propertyValues), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, propertyName, newValue));
    setPropertyValues(newPropertyValues);
    setHasUnsavedChanges(true);

    // Create updated component
    var updatedComponent = _objectSpread(_objectSpread({}, component), {}, {
      name: propertyName === 'name' ? newValue : newPropertyValues.name,
      props: _objectSpread(_objectSpread({}, component.props), propertyName !== 'name' && !_index__WEBPACK_IMPORTED_MODULE_12__/* .StyleSchemas */ .AC[propertyName] ? (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, propertyName, newValue) : {}),
      style: _objectSpread(_objectSpread({}, component.style), _index__WEBPACK_IMPORTED_MODULE_12__/* .StyleSchemas */ .AC[propertyName] ? (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, propertyName, newValue) : {})
    });

    // Immediate visual update for responsive UI
    immediateUpdate(updatedComponent);

    // Debounced real-time update for performance
    if (isRealTimeEnabled) {
      debouncedRealTimeUpdate(updatedComponent);
    }

    // Store last values for comparison
    lastValuesRef.current = newPropertyValues;
  }, [propertyValues, component, isRealTimeEnabled, immediateUpdate, debouncedRealTimeUpdate]);

  // Handle save changes
  var handleSave = function handleSave() {
    if (!component) return;
    var name = propertyValues.name,
      otherValues = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(propertyValues, _excluded);
    var props = {};
    var style = {};

    // Separate props and styles
    Object.entries(otherValues).forEach(function (_ref4) {
      var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref4, 2),
        key = _ref5[0],
        value = _ref5[1];
      if (_index__WEBPACK_IMPORTED_MODULE_12__/* .StyleSchemas */ .AC[key]) {
        style[key] = value;
      } else {
        props[key] = value;
      }
    });
    var updatedComponent = _objectSpread(_objectSpread({}, component), {}, {
      name: name || component.name,
      props: _objectSpread(_objectSpread({}, component.props), props),
      style: _objectSpread(_objectSpread({}, component.style), style)
    });

    // Dispatch update action
    dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_10__/* .updateComponent */ .ZP)(updatedComponent));
    setHasUnsavedChanges(false);

    // Call onUpdate callback
    if (onUpdate) {
      onUpdate(updatedComponent);
    }
  };

  // Handle reset all changes
  var handleResetAll = function handleResetAll() {
    if (component) {
      var initialValues = _objectSpread(_objectSpread({
        name: component.name
      }, component.props), component.style);
      setPropertyValues(initialValues);
      setHasUnsavedChanges(false);
    }
  };

  // Handle property filter
  var handlePropertyFilter = function handlePropertyFilter(filtered, filterInfo) {
    setFilteredProperties(filtered);
  };

  // Cleanup debounced function on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    return function () {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      debouncedRealTimeUpdate.cancel();
    };
  }, [debouncedRealTimeUpdate]);

  // Handle real-time toggle
  var handleRealTimeToggle = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (enabled) {
    setIsRealTimeEnabled(enabled);
    if (enabled && hasUnsavedChanges) {
      // Trigger immediate update when enabling real-time mode
      var updatedComponent = _objectSpread(_objectSpread({}, component), {}, {
        name: propertyValues.name || component.name,
        props: _objectSpread({}, component.props),
        style: _objectSpread({}, component.style)
      });

      // Separate props and styles from propertyValues
      Object.entries(propertyValues).forEach(function (_ref6) {
        var _ref7 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref6, 2),
          key = _ref7[0],
          value = _ref7[1];
        if (key === 'name') return;
        if (_index__WEBPACK_IMPORTED_MODULE_12__/* .StyleSchemas */ .AC[key]) {
          updatedComponent.style[key] = value;
        } else {
          updatedComponent.props[key] = value;
        }
      });
      debouncedRealTimeUpdate(updatedComponent);
    }
  }, [hasUnsavedChanges, component, propertyValues, debouncedRealTimeUpdate]);

  // If no component is selected, show a message
  if (!component) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertiesContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        padding: '24px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      type: "secondary"
    }, "Select a component to edit its properties")));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertiesContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      padding: '12px 16px',
      borderBottom: '1px solid #f0f0f0',
      background: '#fafafa',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true,
    style: {
      fontSize: '14px'
    }
  }, component.name || component.type, " Properties"), isUpdating && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SyncOutlined */ .OmY, {
    spin: true,
    style: {
      color: '#1890ff'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, enableRealTimePreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: "Toggle real-time preview updates"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Switch */ .dO, {
    size: "small",
    checked: isRealTimeEnabled,
    onChange: handleRealTimeToggle,
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .EyeOutlined */ .Om2, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .EyeOutlined */ .Om2, null)
  })), lastUpdateTime && isRealTimeEnabled && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Badge */ .Ex, {
    status: "success",
    text: "Updated ".concat(lastUpdateTime.toLocaleTimeString()),
    style: {
      fontSize: '11px'
    }
  }), enableCollaboration && collaborativeSession && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Badge */ .Ex, {
    count: collaborativeSession.collaboratorCount || 0,
    showZero: false,
    style: {
      backgroundColor: '#52c41a'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: "Active collaborators"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    size: "small",
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SyncOutlined */ .OmY, null)
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_index__WEBPACK_IMPORTED_MODULE_12__/* .PropertyPreview */ .cR, {
    component: component,
    properties: _objectSpread(_objectSpread({}, componentProperties), _index__WEBPACK_IMPORTED_MODULE_12__/* .StyleSchemas */ .AC),
    values: propertyValues,
    onReset: handleResetAll,
    showPreview: true,
    showCode: false,
    showValidation: true,
    realTimeEnabled: isRealTimeEnabled
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertiesContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Properties",
    key: "basic"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_index__WEBPACK_IMPORTED_MODULE_12__/* .PropertySearch */ .U9, {
    properties: componentProperties,
    onFilter: handlePropertyFilter,
    placeholder: "Search component properties..."
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_index__WEBPACK_IMPORTED_MODULE_12__/* .PropertyGroup */ .Zf, {
    groupName: "basic",
    properties: {
      name: {
        type: 'text',
        label: 'Component Name',
        required: true,
        placeholder: 'Enter component name'
      }
    },
    values: propertyValues,
    onChange: handlePropertyChange,
    componentType: component.type,
    defaultExpanded: true
  }), Object.keys(componentProperties).length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_index__WEBPACK_IMPORTED_MODULE_12__/* .PropertyGroup */ .Zf, {
    groupName: "component",
    properties: componentProperties,
    values: propertyValues,
    onChange: handlePropertyChange,
    componentType: component.type,
    defaultExpanded: true
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Styling",
    key: "style"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_index__WEBPACK_IMPORTED_MODULE_12__/* .PropertySearch */ .U9, {
    properties: _index__WEBPACK_IMPORTED_MODULE_12__/* .StyleSchemas */ .AC,
    onFilter: handlePropertyFilter,
    placeholder: "Search style properties..."
  }), Object.entries(stylePropertiesGrouped).map(function (_ref8) {
    var _ref9 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref8, 2),
      groupName = _ref9[0],
      groupProperties = _ref9[1];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_index__WEBPACK_IMPORTED_MODULE_12__/* .PropertyGroup */ .Zf, {
      key: groupName,
      groupName: groupName,
      properties: groupProperties,
      values: propertyValues,
      onChange: handlePropertyChange,
      componentType: component.type,
      defaultExpanded: groupName === 'dimensions' || groupName === 'colors'
    });
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Advanced",
    key: "advanced"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_index__WEBPACK_IMPORTED_MODULE_12__/* .PropertyGroup */ .Zf, {
    groupName: "advanced",
    properties: {
      customProps: {
        type: 'json',
        label: 'Custom Properties',
        placeholder: 'Enter custom properties as JSON',
        description: 'Additional properties not covered by the standard options'
      },
      customStyles: {
        type: 'json',
        label: 'Custom Styles',
        placeholder: 'Enter custom styles as JSON',
        description: 'Additional CSS styles not covered by the standard options'
      }
    },
    values: propertyValues,
    onChange: handlePropertyChange,
    componentType: component.type,
    defaultExpanded: true
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertiesActions, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
    style: {
      width: '100%',
      justifyContent: 'space-between'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, hasUnsavedChanges && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "warning",
    style: {
      fontSize: '12px'
    }
  }, "Unsaved changes")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .UndoOutlined */ .Xrf, null),
    onClick: handleResetAll,
    disabled: !hasUnsavedChanges
  }, "Reset"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    type: "primary",
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SaveOutlined */ .ylI, null),
    onClick: handleSave,
    disabled: !hasUnsavedChanges
  }, "Apply Changes")))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedComponentProperties);

/***/ }),

/***/ 3221:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AC: () => (/* binding */ StyleSchemas),
/* harmony export */   Le: () => (/* binding */ detectPropertyType),
/* harmony export */   Lp: () => (/* binding */ getStylePropertiesGrouped),
/* harmony export */   lj: () => (/* binding */ validatePropertyValue),
/* harmony export */   vS: () => (/* binding */ getComponentProperties),
/* harmony export */   zZ: () => (/* binding */ PropertyTypes)
/* harmony export */ });
/* unused harmony exports ComponentSchemas, detectTypeFromValue, formatLabel */
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(82284);


/**
 * Property Type Detection System
 * Automatically detects property types and provides metadata for rendering appropriate UI controls
 */

// Property type definitions
var PropertyTypes = {
  TEXT: 'text',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  COLOR: 'color',
  SELECT: 'select',
  SPACING: 'spacing',
  BORDER: 'border',
  SHADOW: 'shadow',
  FONT: 'font',
  ARRAY: 'array',
  OBJECT: 'object',
  JSON: 'json'
};

// Component property schemas
var ComponentSchemas = {
  button: {
    text: {
      type: PropertyTypes.TEXT,
      label: 'Button Text',
      placeholder: 'Enter button text'
    },
    variant: {
      type: PropertyTypes.SELECT,
      label: 'Variant',
      options: [{
        value: 'primary',
        label: 'Primary'
      }, {
        value: 'secondary',
        label: 'Secondary'
      }, {
        value: 'text',
        label: 'Text'
      }, {
        value: 'link',
        label: 'Link'
      }, {
        value: 'ghost',
        label: 'Ghost'
      }, {
        value: 'dashed',
        label: 'Dashed'
      }]
    },
    size: {
      type: PropertyTypes.SELECT,
      label: 'Size',
      options: [{
        value: 'small',
        label: 'Small'
      }, {
        value: 'medium',
        label: 'Medium'
      }, {
        value: 'large',
        label: 'Large'
      }]
    },
    disabled: {
      type: PropertyTypes.BOOLEAN,
      label: 'Disabled'
    },
    block: {
      type: PropertyTypes.BOOLEAN,
      label: 'Full Width'
    },
    onClick: {
      type: PropertyTypes.TEXT,
      label: 'onClick Handler',
      placeholder: 'Enter function name'
    }
  },
  text: {
    content: {
      type: PropertyTypes.TEXT,
      label: 'Text Content',
      multiline: true,
      placeholder: 'Enter text content'
    },
    variant: {
      type: PropertyTypes.SELECT,
      label: 'Variant',
      options: [{
        value: 'h1',
        label: 'Heading 1'
      }, {
        value: 'h2',
        label: 'Heading 2'
      }, {
        value: 'h3',
        label: 'Heading 3'
      }, {
        value: 'h4',
        label: 'Heading 4'
      }, {
        value: 'h5',
        label: 'Heading 5'
      }, {
        value: 'h6',
        label: 'Heading 6'
      }, {
        value: 'p',
        label: 'Paragraph'
      }, {
        value: 'span',
        label: 'Span'
      }]
    },
    color: {
      type: PropertyTypes.COLOR,
      label: 'Text Color'
    },
    align: {
      type: PropertyTypes.SELECT,
      label: 'Text Alignment',
      options: [{
        value: 'left',
        label: 'Left'
      }, {
        value: 'center',
        label: 'Center'
      }, {
        value: 'right',
        label: 'Right'
      }, {
        value: 'justify',
        label: 'Justify'
      }]
    }
  },
  input: {
    label: {
      type: PropertyTypes.TEXT,
      label: 'Input Label',
      placeholder: 'Enter input label'
    },
    placeholder: {
      type: PropertyTypes.TEXT,
      label: 'Placeholder',
      placeholder: 'Enter placeholder text'
    },
    type: {
      type: PropertyTypes.SELECT,
      label: 'Input Type',
      options: [{
        value: 'text',
        label: 'Text'
      }, {
        value: 'password',
        label: 'Password'
      }, {
        value: 'email',
        label: 'Email'
      }, {
        value: 'number',
        label: 'Number'
      }, {
        value: 'tel',
        label: 'Telephone'
      }, {
        value: 'url',
        label: 'URL'
      }]
    },
    required: {
      type: PropertyTypes.BOOLEAN,
      label: 'Required'
    },
    disabled: {
      type: PropertyTypes.BOOLEAN,
      label: 'Disabled'
    },
    validation: {
      type: PropertyTypes.SELECT,
      label: 'Validation',
      options: [{
        value: 'none',
        label: 'None'
      }, {
        value: 'email',
        label: 'Email'
      }, {
        value: 'url',
        label: 'URL'
      }, {
        value: 'phone',
        label: 'Phone'
      }, {
        value: 'custom',
        label: 'Custom'
      }]
    }
  },
  card: {
    title: {
      type: PropertyTypes.TEXT,
      label: 'Card Title',
      placeholder: 'Enter card title'
    },
    description: {
      type: PropertyTypes.TEXT,
      label: 'Description',
      multiline: true,
      placeholder: 'Enter card description'
    },
    image: {
      type: PropertyTypes.TEXT,
      label: 'Image URL',
      placeholder: 'Enter image URL'
    },
    elevation: {
      type: PropertyTypes.SELECT,
      label: 'Elevation',
      options: [{
        value: 'none',
        label: 'None'
      }, {
        value: 'sm',
        label: 'Small'
      }, {
        value: 'md',
        label: 'Medium'
      }, {
        value: 'lg',
        label: 'Large'
      }]
    },
    bordered: {
      type: PropertyTypes.BOOLEAN,
      label: 'Bordered'
    }
  }
};

// Style property schemas (common across all components)
var StyleSchemas = {
  // Dimensions
  width: {
    type: PropertyTypes.TEXT,
    label: 'Width',
    placeholder: 'e.g., 100%, 200px',
    group: 'dimensions'
  },
  height: {
    type: PropertyTypes.TEXT,
    label: 'Height',
    placeholder: 'e.g., 100%, 200px',
    group: 'dimensions'
  },
  minWidth: {
    type: PropertyTypes.TEXT,
    label: 'Min Width',
    placeholder: 'e.g., 100px',
    group: 'dimensions'
  },
  maxWidth: {
    type: PropertyTypes.TEXT,
    label: 'Max Width',
    placeholder: 'e.g., 500px',
    group: 'dimensions'
  },
  minHeight: {
    type: PropertyTypes.TEXT,
    label: 'Min Height',
    placeholder: 'e.g., 100px',
    group: 'dimensions'
  },
  maxHeight: {
    type: PropertyTypes.TEXT,
    label: 'Max Height',
    placeholder: 'e.g., 500px',
    group: 'dimensions'
  },
  // Spacing
  margin: {
    type: PropertyTypes.SPACING,
    label: 'Margin',
    group: 'spacing'
  },
  padding: {
    type: PropertyTypes.SPACING,
    label: 'Padding',
    group: 'spacing'
  },
  // Typography
  fontSize: {
    type: PropertyTypes.NUMBER,
    label: 'Font Size',
    min: 8,
    max: 72,
    unit: 'px',
    units: ['px', 'em', 'rem', '%'],
    group: 'typography'
  },
  fontWeight: {
    type: PropertyTypes.SELECT,
    label: 'Font Weight',
    options: [{
      value: 'normal',
      label: 'Normal'
    }, {
      value: 'bold',
      label: 'Bold'
    }, {
      value: 'lighter',
      label: 'Lighter'
    }, {
      value: 'bolder',
      label: 'Bolder'
    }, {
      value: '100',
      label: '100'
    }, {
      value: '200',
      label: '200'
    }, {
      value: '300',
      label: '300'
    }, {
      value: '400',
      label: '400'
    }, {
      value: '500',
      label: '500'
    }, {
      value: '600',
      label: '600'
    }, {
      value: '700',
      label: '700'
    }, {
      value: '800',
      label: '800'
    }, {
      value: '900',
      label: '900'
    }],
    group: 'typography'
  },
  lineHeight: {
    type: PropertyTypes.NUMBER,
    label: 'Line Height',
    min: 0.5,
    max: 3,
    step: 0.1,
    precision: 1,
    showUnit: false,
    group: 'typography'
  },
  fontFamily: {
    type: PropertyTypes.FONT,
    label: 'Font Family',
    group: 'typography'
  },
  // Colors
  color: {
    type: PropertyTypes.COLOR,
    label: 'Text Color',
    group: 'colors'
  },
  backgroundColor: {
    type: PropertyTypes.COLOR,
    label: 'Background Color',
    group: 'colors'
  },
  // Border
  border: {
    type: PropertyTypes.BORDER,
    label: 'Border',
    group: 'border'
  },
  borderTop: {
    type: PropertyTypes.BORDER,
    label: 'Border Top',
    group: 'border'
  },
  borderRight: {
    type: PropertyTypes.BORDER,
    label: 'Border Right',
    group: 'border'
  },
  borderBottom: {
    type: PropertyTypes.BORDER,
    label: 'Border Bottom',
    group: 'border'
  },
  borderLeft: {
    type: PropertyTypes.BORDER,
    label: 'Border Left',
    group: 'border'
  },
  borderRadius: {
    type: PropertyTypes.NUMBER,
    label: 'Border Radius',
    min: 0,
    max: 50,
    unit: 'px',
    units: ['px', 'em', 'rem', '%'],
    group: 'border'
  },
  // Shadow
  boxShadow: {
    type: PropertyTypes.SHADOW,
    label: 'Box Shadow',
    group: 'shadow'
  },
  textShadow: {
    type: PropertyTypes.SHADOW,
    label: 'Text Shadow',
    group: 'shadow'
  },
  // Layout
  display: {
    type: PropertyTypes.SELECT,
    label: 'Display',
    options: [{
      value: 'block',
      label: 'Block'
    }, {
      value: 'inline',
      label: 'Inline'
    }, {
      value: 'inline-block',
      label: 'Inline Block'
    }, {
      value: 'flex',
      label: 'Flex'
    }, {
      value: 'grid',
      label: 'Grid'
    }, {
      value: 'none',
      label: 'None'
    }],
    group: 'layout'
  },
  position: {
    type: PropertyTypes.SELECT,
    label: 'Position',
    options: [{
      value: 'static',
      label: 'Static'
    }, {
      value: 'relative',
      label: 'Relative'
    }, {
      value: 'absolute',
      label: 'Absolute'
    }, {
      value: 'fixed',
      label: 'Fixed'
    }, {
      value: 'sticky',
      label: 'Sticky'
    }],
    group: 'layout'
  }
};

/**
 * Detect property type based on property name and value
 */
var detectPropertyType = function detectPropertyType(propertyName, value, componentType) {
  // Check component-specific schema first
  if (componentType && ComponentSchemas[componentType] && ComponentSchemas[componentType][propertyName]) {
    return ComponentSchemas[componentType][propertyName];
  }

  // Check style schema
  if (StyleSchemas[propertyName]) {
    return StyleSchemas[propertyName];
  }

  // Fallback to value-based detection
  return detectTypeFromValue(propertyName, value);
};

/**
 * Detect property type from value and property name patterns
 */
var detectTypeFromValue = function detectTypeFromValue(propertyName, value) {
  var name = propertyName.toLowerCase();

  // Color detection
  if (name.includes('color') || name.includes('background')) {
    return {
      type: PropertyTypes.COLOR,
      label: formatLabel(propertyName)
    };
  }

  // Number detection
  if (typeof value === 'number' || typeof value === 'string' && /^\d+(\.\d+)?(px|em|rem|%)?$/.test(value)) {
    return {
      type: PropertyTypes.NUMBER,
      label: formatLabel(propertyName)
    };
  }

  // Boolean detection
  if (typeof value === 'boolean') {
    return {
      type: PropertyTypes.BOOLEAN,
      label: formatLabel(propertyName)
    };
  }

  // Spacing detection
  if (name.includes('margin') || name.includes('padding')) {
    return {
      type: PropertyTypes.SPACING,
      label: formatLabel(propertyName)
    };
  }

  // Border detection
  if (name.includes('border') && !name.includes('radius')) {
    return {
      type: PropertyTypes.BORDER,
      label: formatLabel(propertyName)
    };
  }

  // Shadow detection
  if (name.includes('shadow')) {
    return {
      type: PropertyTypes.SHADOW,
      label: formatLabel(propertyName)
    };
  }

  // Font detection
  if (name.includes('font') && !name.includes('size') && !name.includes('weight')) {
    return {
      type: PropertyTypes.FONT,
      label: formatLabel(propertyName)
    };
  }

  // Array detection
  if (Array.isArray(value)) {
    return {
      type: PropertyTypes.ARRAY,
      label: formatLabel(propertyName)
    };
  }

  // Object detection
  if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(value) === 'object' && value !== null) {
    return {
      type: PropertyTypes.OBJECT,
      label: formatLabel(propertyName)
    };
  }

  // Default to text
  return {
    type: PropertyTypes.TEXT,
    label: formatLabel(propertyName)
  };
};

/**
 * Format property name into a readable label
 */
var formatLabel = function formatLabel(propertyName) {
  return propertyName.replace(/([A-Z])/g, ' $1').replace(/^./, function (str) {
    return str.toUpperCase();
  }).trim();
};

/**
 * Get all properties for a component type
 */
var getComponentProperties = function getComponentProperties(componentType) {
  return ComponentSchemas[componentType] || {};
};

/**
 * Get style properties grouped by category
 */
var getStylePropertiesGrouped = function getStylePropertiesGrouped() {
  var grouped = {};
  Object.entries(StyleSchemas).forEach(function (_ref) {
    var _ref2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref, 2),
      key = _ref2[0],
      schema = _ref2[1];
    var group = schema.group || 'other';
    if (!grouped[group]) {
      grouped[group] = {};
    }
    grouped[group][key] = schema;
  });
  return grouped;
};

/**
 * Validate property value based on its type
 */
var validatePropertyValue = function validatePropertyValue(value, schema) {
  if (!schema) return {
    valid: true
  };
  switch (schema.type) {
    case PropertyTypes.NUMBER:
      var num = parseFloat(value);
      if (isNaN(num)) {
        return {
          valid: false,
          error: 'Must be a valid number'
        };
      }
      if (schema.min !== undefined && num < schema.min) {
        return {
          valid: false,
          error: "Must be at least ".concat(schema.min)
        };
      }
      if (schema.max !== undefined && num > schema.max) {
        return {
          valid: false,
          error: "Must be at most ".concat(schema.max)
        };
      }
      break;
    case PropertyTypes.COLOR:
      var colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/;
      if (value && !colorRegex.test(value)) {
        return {
          valid: false,
          error: 'Must be a valid color value'
        };
      }
      break;
    case PropertyTypes.SELECT:
      if (schema.options && value && !schema.options.some(function (opt) {
        return opt.value === value;
      })) {
        return {
          valid: false,
          error: 'Must be one of the available options'
        };
      }
      break;
  }
  return {
    valid: true
  };
};

/***/ }),

/***/ 5184:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(53986);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(3221);
/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(91018);



var _excluded = ["propertyName", "value", "onChange", "componentType", "schema", "showValidation"];





var TextArea = antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd.TextArea;
var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;
var Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text;

/**
 * Renders the appropriate input component based on property type
 */
var PropertyRenderer = function PropertyRenderer(_ref) {
  var _propertySchema$optio;
  var propertyName = _ref.propertyName,
    value = _ref.value,
    onChange = _ref.onChange,
    componentType = _ref.componentType,
    schema = _ref.schema,
    _ref$showValidation = _ref.showValidation,
    showValidation = _ref$showValidation === void 0 ? true : _ref$showValidation,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref, _excluded);
  // Get property schema (either provided or detected)
  var propertySchema = schema || (0,_PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .detectPropertyType */ .Le)(propertyName, value, componentType);

  // Validate current value
  var validation = showValidation ? (0,_PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .validatePropertyValue */ .lj)(value, propertySchema) : {
    valid: true
  };

  // Handle value change with validation
  var handleChange = function handleChange(newValue) {
    if (onChange) {
      onChange(newValue, propertyName, propertySchema);
    }
  };

  // Render validation error
  var renderValidationError = function renderValidationError() {
    if (!showValidation || validation.valid) return null;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      type: "danger",
      style: {
        fontSize: '12px',
        display: 'block',
        marginTop: '4px'
      }
    }, validation.error);
  };

  // Render tooltip if description is provided
  var renderTooltip = function renderTooltip() {
    if (!propertySchema.description && !propertySchema.tooltip) return null;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: propertySchema.description || propertySchema.tooltip
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: '#8c8c8c',
        marginLeft: '4px'
      }
    }));
  };

  // Render based on property type
  switch (propertySchema.type) {
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.TEXT:
      if (propertySchema.multiline) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
          value: value,
          onChange: function onChange(e) {
            return handleChange(e.target.value);
          },
          placeholder: propertySchema.placeholder,
          rows: propertySchema.rows || 3,
          status: !validation.valid ? 'error' : ''
        }, props)), renderTooltip(), renderValidationError());
      }
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: function onChange(e) {
          return handleChange(e.target.value);
        },
        placeholder: propertySchema.placeholder,
        status: !validation.valid ? 'error' : ''
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.NUMBER:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_index__WEBPACK_IMPORTED_MODULE_7__/* .NumberInput */ .Q7, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: handleChange,
        min: propertySchema.min,
        max: propertySchema.max,
        step: propertySchema.step,
        precision: propertySchema.precision,
        unit: propertySchema.unit,
        units: propertySchema.units,
        showSlider: propertySchema.showSlider,
        showUnit: propertySchema.showUnit,
        placeholder: propertySchema.placeholder,
        tooltip: propertySchema.tooltip
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.BOOLEAN:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        checked: value,
        onChange: handleChange
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.COLOR:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_index__WEBPACK_IMPORTED_MODULE_7__/* .ColorInput */ .AN, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: handleChange,
        showPresets: propertySchema.showPresets,
        showModeToggle: propertySchema.showModeToggle,
        presets: propertySchema.presets,
        placeholder: propertySchema.placeholder
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.SELECT:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: handleChange,
        placeholder: propertySchema.placeholder,
        style: {
          width: '100%'
        },
        status: !validation.valid ? 'error' : ''
      }, props), (_propertySchema$optio = propertySchema.options) === null || _propertySchema$optio === void 0 ? void 0 : _propertySchema$optio.map(function (option) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
          key: option.value,
          value: option.value
        }, option.label);
      })), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.SPACING:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_index__WEBPACK_IMPORTED_MODULE_7__/* .SpacingEditor */ .dh, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: handleChange,
        type: propertyName.includes('margin') ? 'margin' : 'padding',
        showVisual: propertySchema.showVisual,
        showPresets: propertySchema.showPresets,
        unit: propertySchema.unit
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.BORDER:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_index__WEBPACK_IMPORTED_MODULE_7__/* .BorderEditor */ .wu, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: handleChange,
        showPreview: propertySchema.showPreview
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.SHADOW:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_index__WEBPACK_IMPORTED_MODULE_7__/* .ShadowEditor */ .Sq, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: handleChange,
        showPreview: propertySchema.showPreview
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.FONT:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_index__WEBPACK_IMPORTED_MODULE_7__/* .FontSelector */ .RM, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: handleChange,
        showPreview: propertySchema.showPreview
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.ARRAY:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: Array.isArray(value) ? JSON.stringify(value, null, 2) : value,
        onChange: function onChange(e) {
          try {
            var parsed = JSON.parse(e.target.value);
            handleChange(parsed);
          } catch (error) {
            // Keep the raw string value for now
            handleChange(e.target.value);
          }
        },
        placeholder: propertySchema.placeholder || 'Enter array as JSON',
        rows: 4,
        status: !validation.valid ? 'error' : ''
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.OBJECT:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(value) === 'object' ? JSON.stringify(value, null, 2) : value,
        onChange: function onChange(e) {
          try {
            var parsed = JSON.parse(e.target.value);
            handleChange(parsed);
          } catch (error) {
            // Keep the raw string value for now
            handleChange(e.target.value);
          }
        },
        placeholder: propertySchema.placeholder || 'Enter object as JSON',
        rows: 6,
        status: !validation.valid ? 'error' : ''
      }, props)), renderTooltip(), renderValidationError());
    case _PropertyTypeDetector__WEBPACK_IMPORTED_MODULE_6__/* .PropertyTypes */ .zZ.JSON:
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: typeof value === 'string' ? value : JSON.stringify(value, null, 2),
        onChange: function onChange(e) {
          return handleChange(e.target.value);
        },
        placeholder: propertySchema.placeholder || 'Enter JSON',
        rows: 8,
        status: !validation.valid ? 'error' : '',
        style: {
          fontFamily: 'monospace'
        }
      }, props)), renderTooltip(), renderValidationError());
    default:
      // Fallback to text input
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({
        value: value,
        onChange: function onChange(e) {
          return handleChange(e.target.value);
        },
        placeholder: propertySchema.placeholder || 'Enter value',
        status: !validation.valid ? 'error' : ''
      }, props)), renderTooltip(), renderValidationError());
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PropertyRenderer);

/***/ }),

/***/ 26031:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);




var _excluded = ["value", "onChange", "min", "max", "step", "unit", "units", "showSlider", "showUnit", "placeholder", "tooltip", "precision"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4;




var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;
var NumberInputContainer = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 100%;\n"])));
var InputGroup = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"])));
var SliderContainer = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  flex: 1;\n  margin-left: 8px;\n"])));
var UnitSelector = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.select(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: 4px 8px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  background: white;\n  font-size: 12px;\n  min-width: 50px;\n"])));

/**
 * Enhanced number input with constraints, step controls, and optional slider
 */
var NumberInput = function NumberInput(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$min = _ref.min,
    min = _ref$min === void 0 ? 0 : _ref$min,
    _ref$max = _ref.max,
    max = _ref$max === void 0 ? 100 : _ref$max,
    _ref$step = _ref.step,
    step = _ref$step === void 0 ? 1 : _ref$step,
    _ref$unit = _ref.unit,
    unit = _ref$unit === void 0 ? 'px' : _ref$unit,
    _ref$units = _ref.units,
    units = _ref$units === void 0 ? ['px', '%', 'rem', 'em', 'vh', 'vw'] : _ref$units,
    _ref$showSlider = _ref.showSlider,
    showSlider = _ref$showSlider === void 0 ? false : _ref$showSlider,
    _ref$showUnit = _ref.showUnit,
    showUnit = _ref$showUnit === void 0 ? true : _ref$showUnit,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? 'Enter value' : _ref$placeholder,
    tooltip = _ref.tooltip,
    _ref$precision = _ref.precision,
    precision = _ref$precision === void 0 ? 0 : _ref$precision,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref, _excluded);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    numericValue = _useState2[0],
    setNumericValue = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(unit),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    selectedUnit = _useState4[0],
    setSelectedUnit = _useState4[1];

  // Parse value on mount and when value changes
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (value) {
      var parsed = parseValue(value);
      setNumericValue(parsed.number);
      setSelectedUnit(parsed.unit);
    }
  }, [value]);

  // Parse a value string like "10px" into number and unit
  var parseValue = function parseValue(val) {
    if (typeof val === 'number') {
      return {
        number: val,
        unit: selectedUnit
      };
    }
    if (typeof val === 'string') {
      var match = val.match(/^(-?\d*\.?\d+)(.*)$/);
      if (match) {
        return {
          number: parseFloat(match[1]),
          unit: match[2] || selectedUnit
        };
      }
    }
    return {
      number: 0,
      unit: selectedUnit
    };
  };

  // Format value for output
  var formatValue = function formatValue(num, unitStr) {
    if (showUnit && unitStr) {
      return "".concat(num).concat(unitStr);
    }
    return num;
  };

  // Handle numeric value change
  var handleNumberChange = function handleNumberChange(newValue) {
    if (newValue !== null && newValue !== undefined) {
      setNumericValue(newValue);
      var formattedValue = formatValue(newValue, selectedUnit);
      onChange === null || onChange === void 0 || onChange(formattedValue);
    }
  };

  // Handle unit change
  var handleUnitChange = function handleUnitChange(e) {
    var newUnit = e.target.value;
    setSelectedUnit(newUnit);
    var formattedValue = formatValue(numericValue, newUnit);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(NumberInputContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(InputGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .InputNumber */ .YI, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({
    value: numericValue,
    onChange: handleNumberChange,
    min: min,
    max: max,
    step: step,
    precision: precision,
    placeholder: placeholder,
    style: {
      flex: 1
    }
  }, props)), showUnit && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(UnitSelector, {
    value: selectedUnit,
    onChange: handleUnitChange
  }, units.map(function (u) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("option", {
      key: u,
      value: u
    }, u);
  })), tooltip && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
    title: tooltip
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .InfoCircleOutlined */ .rUN, {
    style: {
      color: '#8c8c8c'
    }
  }))), showSlider && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SliderContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Slider */ .Ap, {
    value: numericValue,
    onChange: handleNumberChange,
    min: min,
    max: max,
    step: step,
    tooltip: {
      formatter: function formatter(val) {
        return "".concat(val).concat(selectedUnit);
      }
    }
  })), (min !== undefined || max !== undefined) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: '12px'
    }
  }, "Range: ", min, " - ", max));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NumberInput);

/***/ }),

/***/ 33399:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79146);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(86020);





var _excluded = ["value", "onChange", "showPresets", "showModeToggle", "presets", "placeholder"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;





var Text = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text;
var ColorInputContainer = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  width: 100%;\n"])));
var ColorPreview = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  width: 32px;\n  height: 32px;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ", ";\n  position: relative;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg, #ccc 25%, transparent 25%), \n                linear-gradient(-45deg, #ccc 25%, transparent 25%), \n                linear-gradient(45deg, transparent 75%, #ccc 75%), \n                linear-gradient(-45deg, transparent 75%, #ccc 75%);\n    background-size: 8px 8px;\n    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;\n    z-index: -1;\n  }\n"])), function (props) {
  return props.color || '#ffffff';
});
var ColorModeToggle = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: flex;\n  gap: 4px;\n  margin-bottom: 8px;\n"])));
var ModeButton = (0,_design_system__WEBPACK_IMPORTED_MODULE_8__.styled)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"])));
var PresetGrid = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(8, 1fr);\n  gap: 4px;\n  margin-top: 8px;\n"])));
var PresetColor = _design_system__WEBPACK_IMPORTED_MODULE_8__.styled.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  width: 24px;\n  height: 24px;\n  border-radius: 2px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  background: ", ";\n  \n  &:hover {\n    border-color: #1890ff;\n    transform: scale(1.1);\n  }\n"])), function (props) {
  return props.color;
});

/**
 * Enhanced color picker with multiple format support and presets
 */
var ColorInput = function ColorInput(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$showPresets = _ref.showPresets,
    showPresets = _ref$showPresets === void 0 ? true : _ref$showPresets,
    _ref$showModeToggle = _ref.showModeToggle,
    showModeToggle = _ref$showModeToggle === void 0 ? true : _ref$showModeToggle,
    _ref$presets = _ref.presets,
    presets = _ref$presets === void 0 ? [] : _ref$presets,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? 'Enter color' : _ref$placeholder,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref, _excluded);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('hex'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    colorMode = _useState2[0],
    setColorMode = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(value || '#ffffff'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    colorValue = _useState4[0],
    setColorValue = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(''),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    inputValue = _useState6[0],
    setInputValue = _useState6[1];

  // Default color presets
  var defaultPresets = ['#ffffff', '#f5f5f5', '#d9d9d9', '#bfbfbf', '#8c8c8c', '#595959', '#262626', '#000000', '#ff4d4f', '#ff7a45', '#ffa940', '#ffec3d', '#bae637', '#73d13d', '#40a9ff', '#597ef7', '#9254de', '#f759ab', '#ff85c0', '#ffc069'].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_design_system_theme__WEBPACK_IMPORTED_MODULE_9__/* ["default"].colors */ .Ay.colors.primary ? [_design_system_theme__WEBPACK_IMPORTED_MODULE_9__/* ["default"].colors */ .Ay.colors.primary[500]] : []), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_design_system_theme__WEBPACK_IMPORTED_MODULE_9__/* ["default"].colors */ .Ay.colors.secondary ? [_design_system_theme__WEBPACK_IMPORTED_MODULE_9__/* ["default"].colors */ .Ay.colors.secondary[500]] : []), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(presets));
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (value) {
      setColorValue(value);
      setInputValue(formatColorForMode(value, colorMode));
    }
  }, [value, colorMode]);

  // Convert color to different formats
  var formatColorForMode = function formatColorForMode(color, mode) {
    if (!color) return '';
    try {
      // Simple format conversion (could be enhanced with a color library)
      switch (mode) {
        case 'hex':
          return color.startsWith('#') ? color : "#".concat(color);
        case 'rgb':
          return convertToRgb(color);
        case 'hsl':
          return convertToHsl(color);
        default:
          return color;
      }
    } catch (error) {
      return color;
    }
  };

  // Simple hex to RGB conversion
  var convertToRgb = function convertToRgb(hex) {
    if (!hex.startsWith('#')) return hex;
    var r = parseInt(hex.slice(1, 3), 16);
    var g = parseInt(hex.slice(3, 5), 16);
    var b = parseInt(hex.slice(5, 7), 16);
    return "rgb(".concat(r, ", ").concat(g, ", ").concat(b, ")");
  };

  // Simple hex to HSL conversion (simplified)
  var convertToHsl = function convertToHsl(hex) {
    if (!hex.startsWith('#')) return hex;
    // This is a simplified conversion - in a real app you'd use a color library
    return "hsl(0, 0%, 50%)"; // Placeholder
  };
  var handleColorChange = function handleColorChange(color) {
    var colorString = color.toHexString();
    setColorValue(colorString);
    setInputValue(formatColorForMode(colorString, colorMode));
    onChange === null || onChange === void 0 || onChange(colorString);
  };
  var handleInputChange = function handleInputChange(e) {
    var newValue = e.target.value;
    setInputValue(newValue);

    // Validate and update color if valid
    if (isValidColor(newValue)) {
      setColorValue(newValue);
      onChange === null || onChange === void 0 || onChange(newValue);
    }
  };
  var handlePresetClick = function handlePresetClick(color) {
    setColorValue(color);
    setInputValue(formatColorForMode(color, colorMode));
    onChange === null || onChange === void 0 || onChange(color);
  };
  var isValidColor = function isValidColor(color) {
    // Simple color validation
    var hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    var rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/;
    var hslRegex = /^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/;
    return hexRegex.test(color) || rgbRegex.test(color) || hslRegex.test(color);
  };
  var colorPickerContent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      width: 280
    }
  }, showModeToggle && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ColorModeToggle, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ModeButton, {
    type: colorMode === 'hex' ? 'primary' : 'default',
    size: "small",
    onClick: function onClick() {
      return setColorMode('hex');
    }
  }, "HEX"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ModeButton, {
    type: colorMode === 'rgb' ? 'primary' : 'default',
    size: "small",
    onClick: function onClick() {
      return setColorMode('rgb');
    }
  }, "RGB"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ModeButton, {
    type: colorMode === 'hsl' ? 'primary' : 'default',
    size: "small",
    onClick: function onClick() {
      return setColorMode('hsl');
    }
  }, "HSL")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Divider */ .cG, {
    style: {
      margin: '8px 0'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .ColorPicker */ .sk, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({
    value: colorValue,
    onChange: handleColorChange,
    showText: true,
    size: "large"
  }, props)), showPresets && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Divider */ .cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    strong: true,
    style: {
      fontSize: '12px'
    }
  }, "Color Presets"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(PresetGrid, null, defaultPresets.slice(0, 24).map(function (preset, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(PresetColor, {
      key: index,
      color: preset,
      onClick: function onClick() {
        return handlePresetClick(preset);
      },
      title: preset
    });
  }))));
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ColorInputContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x.Compact, {
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Popover */ .AM, {
    content: colorPickerContent,
    trigger: "click",
    placement: "bottomLeft"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ColorPreview, {
    color: colorValue
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .BgColorsOutlined */ .Ebl, {
    style: {
      color: 'rgba(0,0,0,0.3)'
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Input */ .pd, {
    value: inputValue,
    onChange: handleInputChange,
    placeholder: placeholder,
    style: {
      flex: 1
    }
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ColorInput);

/***/ }),

/***/ 86970:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _NumberInput__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(26031);




var _excluded = ["value", "onChange", "showPreview"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4;





var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var FontContainer = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 100%;\n"])));
var FontPreview = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 100%;\n  padding: 16px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  font-family: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  line-height: ", ";\n  text-align: center;\n"])), function (props) {
  return props.fontFamily || 'inherit';
}, function (props) {
  return props.fontSize || '16px';
}, function (props) {
  return props.fontWeight || 'normal';
}, function (props) {
  return props.lineHeight || '1.5';
});
var PropertyRow = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"])));
var PropertyLabel = (0,_design_system__WEBPACK_IMPORTED_MODULE_7__.styled)(Text)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"])));

/**
 * Font selector with family, size, weight, and line height controls
 */
var FontSelector = function FontSelector(_ref) {
  var value = _ref.value,
    onChange = _ref.onChange,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref, _excluded);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('inherit'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    fontFamily = _useState2[0],
    setFontFamily = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('16px'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    fontSize = _useState4[0],
    setFontSize = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('normal'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    fontWeight = _useState6[0],
    setFontWeight = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('1.5'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    lineHeight = _useState8[0],
    setLineHeight = _useState8[1];

  // Parse font value on mount and when value changes
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (value) {
      var parsed = parseFontValue(value);
      setFontFamily(parsed.family);
      setFontSize(parsed.size);
      setFontWeight(parsed.weight);
      setLineHeight(parsed.lineHeight);
    }
  }, [value]);

  // Parse font value (object or individual properties)
  var parseFontValue = function parseFontValue(val) {
    if (!val) {
      return {
        family: 'inherit',
        size: '16px',
        weight: 'normal',
        lineHeight: '1.5'
      };
    }
    if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(val) === 'object') {
      return {
        family: val.fontFamily || val.family || 'inherit',
        size: val.fontSize || val.size || '16px',
        weight: val.fontWeight || val.weight || 'normal',
        lineHeight: val.lineHeight || '1.5'
      };
    }

    // If it's a string, assume it's just the font family
    return {
      family: val,
      size: fontSize,
      weight: fontWeight,
      lineHeight: lineHeight
    };
  };

  // Format font value for output
  var formatFontValue = function formatFontValue(family, size, weight, height) {
    return {
      fontFamily: family,
      fontSize: size,
      fontWeight: weight,
      lineHeight: height
    };
  };

  // Handle value changes
  var handleValueChange = function handleValueChange(property, newValue) {
    var newFamily = fontFamily;
    var newSize = fontSize;
    var newWeight = fontWeight;
    var newLineHeight = lineHeight;
    switch (property) {
      case 'family':
        newFamily = newValue;
        setFontFamily(newValue);
        break;
      case 'size':
        newSize = newValue;
        setFontSize(newValue);
        break;
      case 'weight':
        newWeight = newValue;
        setFontWeight(newValue);
        break;
      case 'lineHeight':
        newLineHeight = newValue;
        setLineHeight(newValue);
        break;
    }
    var formattedValue = formatFontValue(newFamily, newSize, newWeight, newLineHeight);
    onChange === null || onChange === void 0 || onChange(formattedValue);
  };
  var fontFamilies = [{
    value: 'inherit',
    label: 'Inherit'
  }, {
    value: 'Arial, sans-serif',
    label: 'Arial'
  }, {
    value: 'Helvetica, Arial, sans-serif',
    label: 'Helvetica'
  }, {
    value: '"Times New Roman", Times, serif',
    label: 'Times New Roman'
  }, {
    value: 'Georgia, serif',
    label: 'Georgia'
  }, {
    value: '"Courier New", Courier, monospace',
    label: 'Courier New'
  }, {
    value: 'Verdana, Geneva, sans-serif',
    label: 'Verdana'
  }, {
    value: '"Trebuchet MS", Helvetica, sans-serif',
    label: 'Trebuchet MS'
  }, {
    value: '"Lucida Sans Unicode", "Lucida Grande", sans-serif',
    label: 'Lucida Sans'
  }, {
    value: 'Impact, Charcoal, sans-serif',
    label: 'Impact'
  }, {
    value: '"Comic Sans MS", cursive',
    label: 'Comic Sans MS'
  }, {
    value: '"Palatino Linotype", "Book Antiqua", Palatino, serif',
    label: 'Palatino'
  }, {
    value: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif',
    label: 'Inter'
  }, {
    value: '"Roboto", sans-serif',
    label: 'Roboto'
  }, {
    value: '"Open Sans", sans-serif',
    label: 'Open Sans'
  }, {
    value: '"Lato", sans-serif',
    label: 'Lato'
  }, {
    value: '"Montserrat", sans-serif',
    label: 'Montserrat'
  }, {
    value: '"Source Sans Pro", sans-serif',
    label: 'Source Sans Pro'
  }];
  var fontWeights = [{
    value: '100',
    label: 'Thin (100)'
  }, {
    value: '200',
    label: 'Extra Light (200)'
  }, {
    value: '300',
    label: 'Light (300)'
  }, {
    value: 'normal',
    label: 'Normal (400)'
  }, {
    value: '500',
    label: 'Medium (500)'
  }, {
    value: '600',
    label: 'Semi Bold (600)'
  }, {
    value: 'bold',
    label: 'Bold (700)'
  }, {
    value: '800',
    label: 'Extra Bold (800)'
  }, {
    value: '900',
    label: 'Black (900)'
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FontContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyRow, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyLabel, null, "Family:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: fontFamily,
    onChange: function onChange(val) {
      return handleValueChange('family', val);
    },
    style: {
      flex: 1
    },
    size: "small",
    showSearch: true,
    placeholder: "Select font family"
  }, fontFamilies.map(function (font) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
      key: font.value,
      value: font.value
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
      style: {
        fontFamily: font.value
      }
    }, font.label));
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyRow, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyLabel, null, "Size:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_NumberInput__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
    value: fontSize,
    onChange: function onChange(val) {
      return handleValueChange('size', val);
    },
    min: 8,
    max: 72,
    step: 1,
    unit: "px",
    units: ['px', 'em', 'rem', '%'],
    size: "small"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyRow, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyLabel, null, "Weight:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: fontWeight,
    onChange: function onChange(val) {
      return handleValueChange('weight', val);
    },
    style: {
      flex: 1
    },
    size: "small"
  }, fontWeights.map(function (weight) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
      key: weight.value,
      value: weight.value
    }, weight.label);
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyRow, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyLabel, null, "Line Height:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_NumberInput__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {
    value: lineHeight,
    onChange: function onChange(val) {
      return handleValueChange('lineHeight', val);
    },
    min: 0.5,
    max: 3,
    step: 0.1,
    precision: 1,
    showUnit: false,
    size: "small",
    tooltip: "Line height as a multiplier (e.g., 1.5 = 150%)"
  }))), showPreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Divider */ .cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      fontSize: '12px',
      marginBottom: '4px'
    }
  }, "Preview:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FontPreview, {
    fontFamily: fontFamily,
    fontSize: fontSize,
    fontWeight: fontWeight,
    lineHeight: lineHeight
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FontSizeOutlined */ .ld1, {
    style: {
      marginRight: '8px'
    }
  }), "The quick brown fox jumps over the lazy dog"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: '11px',
      textAlign: 'center'
    }
  }, fontFamily, " \u2022 ", fontSize, " \u2022 ", fontWeight, " \u2022 ", lineHeight))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FontSelector);

/***/ }),

/***/ 92351:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(79146);



var _excluded = ["properties", "onFilter", "showGroupFilter", "showTypeFilter", "placeholder"];
var _templateObject, _templateObject2, _templateObject3;




var Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;
var SearchContainer = _design_system__WEBPACK_IMPORTED_MODULE_6__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n"])));
var FilterRow = _design_system__WEBPACK_IMPORTED_MODULE_6__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"])));
var TagContainer = _design_system__WEBPACK_IMPORTED_MODULE_6__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n"])));

/**
 * Property search and filter component
 */
var PropertySearch = function PropertySearch(_ref) {
  var _ref$properties = _ref.properties,
    properties = _ref$properties === void 0 ? {} : _ref$properties,
    onFilter = _ref.onFilter,
    _ref$showGroupFilter = _ref.showGroupFilter,
    showGroupFilter = _ref$showGroupFilter === void 0 ? true : _ref$showGroupFilter,
    _ref$showTypeFilter = _ref.showTypeFilter,
    showTypeFilter = _ref$showTypeFilter === void 0 ? true : _ref$showTypeFilter,
    _ref$placeholder = _ref.placeholder,
    placeholder = _ref$placeholder === void 0 ? 'Search properties...' : _ref$placeholder,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref, _excluded);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    searchTerm = _useState2[0],
    setSearchTerm = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('all'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    selectedGroup = _useState4[0],
    setSelectedGroup = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('all'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    selectedType = _useState6[0],
    setSelectedType = _useState6[1];

  // Extract unique groups and types from properties
  var _useMemo = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
      var groupSet = new Set();
      var typeSet = new Set();
      Object.values(properties).forEach(function (property) {
        if (property.group) {
          groupSet.add(property.group);
        }
        if (property.type) {
          typeSet.add(property.type);
        }
      });
      return {
        groups: Array.from(groupSet).sort(),
        types: Array.from(typeSet).sort()
      };
    }, [properties]),
    groups = _useMemo.groups,
    types = _useMemo.types;

  // Filter properties based on search term, group, and type
  var filteredProperties = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var filtered = {};
    Object.entries(properties).forEach(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref2, 2),
        key = _ref3[0],
        property = _ref3[1];
      var matchesSearch = !searchTerm || key.toLowerCase().includes(searchTerm.toLowerCase()) || property.label && property.label.toLowerCase().includes(searchTerm.toLowerCase()) || property.description && property.description.toLowerCase().includes(searchTerm.toLowerCase());
      var matchesGroup = selectedGroup === 'all' || property.group === selectedGroup;
      var matchesType = selectedType === 'all' || property.type === selectedType;
      if (matchesSearch && matchesGroup && matchesType) {
        filtered[key] = property;
      }
    });
    return filtered;
  }, [properties, searchTerm, selectedGroup, selectedType]);

  // Handle filter changes
  var handleSearchChange = function handleSearchChange(e) {
    var value = e.target.value;
    setSearchTerm(value);
    onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
      searchTerm: value,
      group: selectedGroup,
      type: selectedType
    });
  };
  var handleGroupChange = function handleGroupChange(value) {
    setSelectedGroup(value);
    onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
      searchTerm: searchTerm,
      group: value,
      type: selectedType
    });
  };
  var handleTypeChange = function handleTypeChange(value) {
    setSelectedType(value);
    onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
      searchTerm: searchTerm,
      group: selectedGroup,
      type: value
    });
  };
  var handleClear = function handleClear() {
    setSearchTerm('');
    setSelectedGroup('all');
    setSelectedType('all');
    onFilter === null || onFilter === void 0 || onFilter(properties, {
      searchTerm: '',
      group: 'all',
      type: 'all'
    });
  };

  // Get active filter count
  var activeFilters = [searchTerm && 'search', selectedGroup !== 'all' && 'group', selectedType !== 'all' && 'type'].filter(Boolean);

  // Format group and type names for display
  var formatName = function formatName(name) {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(SearchContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(FilterRow, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SearchOutlined */ .VrN, null),
    placeholder: placeholder,
    value: searchTerm,
    onChange: handleSearchChange,
    allowClear: true,
    style: {
      flex: 1
    }
  }), showGroupFilter && groups.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6, {
    value: selectedGroup,
    onChange: handleGroupChange,
    style: {
      minWidth: 120
    },
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
    value: "all"
  }, "All Groups"), groups.map(function (group) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
      key: group,
      value: group
    }, formatName(group));
  })), showTypeFilter && types.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6, {
    value: selectedType,
    onChange: handleTypeChange,
    style: {
      minWidth: 100
    },
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
    value: "all"
  }, "All Types"), types.map(function (type) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
      key: type,
      value: type
    }, formatName(type));
  })), activeFilters.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ClearOutlined */ .ohj, {
    onClick: handleClear,
    style: {
      cursor: 'pointer',
      color: '#8c8c8c'
    },
    title: "Clear all filters"
  })), activeFilters.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
    size: 4,
    style: {
      marginBottom: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FilterOutlined */ .Lxx, {
    style: {
      fontSize: '12px',
      color: '#8c8c8c'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: '12px'
    }
  }, "Active filters:")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TagContainer, null, searchTerm && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
    closable: true,
    onClose: function onClose() {
      setSearchTerm('');
      onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
        searchTerm: '',
        group: selectedGroup,
        type: selectedType
      });
    },
    size: "small"
  }, "Search: \"", searchTerm, "\""), selectedGroup !== 'all' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
    closable: true,
    onClose: function onClose() {
      setSelectedGroup('all');
      onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
        searchTerm: searchTerm,
        group: 'all',
        type: selectedType
      });
    },
    size: "small"
  }, "Group: ", formatName(selectedGroup)), selectedType !== 'all' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
    closable: true,
    onClose: function onClose() {
      setSelectedType('all');
      onFilter === null || onFilter === void 0 || onFilter(filteredProperties, {
        searchTerm: searchTerm,
        group: selectedGroup,
        type: 'all'
      });
    },
    size: "small"
  }, "Type: ", formatName(selectedType)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
    style: {
      marginTop: 8,
      fontSize: '12px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "Showing ", Object.keys(filteredProperties).length, " of ", Object.keys(properties).length, " properties")));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PropertySearch);

/***/ }),

/***/ 95527:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(79146);






var _excluded = ["component", "properties", "values", "showPreview", "showCode", "showValidation", "onReset"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Text = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Title;
var PreviewContainer = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background: white;\n  border-bottom: 1px solid #f0f0f0;\n"])));
var PreviewArea = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  min-height: 120px;\n  padding: 16px;\n  background: ", ";\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  overflow: hidden;\n"])), function (props) {
  return props.background || '#f5f5f5';
});
var PreviewElement = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  transition: all 0.2s ease;\n  ", "\n"])), function (props) {
  return props.styles || '';
});
var CodePreview = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.pre(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  background: #f6f8fa;\n  border: 1px solid #e1e4e8;\n  border-radius: 4px;\n  padding: 12px;\n  font-size: 12px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  overflow-x: auto;\n  max-height: 200px;\n  margin: 0;\n"])));
var ValidationMessage = _design_system__WEBPACK_IMPORTED_MODULE_9__.styled.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  margin-top: 8px;\n"])));

/**
 * Real-time preview component for property changes
 */
var PropertyPreview = function PropertyPreview(_ref) {
  var component = _ref.component,
    _ref$properties = _ref.properties,
    properties = _ref$properties === void 0 ? {} : _ref$properties,
    _ref$values = _ref.values,
    values = _ref$values === void 0 ? {} : _ref$values,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    _ref$showCode = _ref.showCode,
    showCode = _ref$showCode === void 0 ? false : _ref$showCode,
    _ref$showValidation = _ref.showValidation,
    showValidation = _ref$showValidation === void 0 ? true : _ref$showValidation,
    onReset = _ref.onReset,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_ref, _excluded);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(showPreview),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    previewEnabled = _useState2[0],
    setPreviewEnabled = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(showCode),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    codeVisible = _useState4[0],
    setCodeVisible = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({}),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    validationErrors = _useState6[0],
    setValidationErrors = _useState6[1];

  // Generate styles from current property values
  var generatedStyles = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(function () {
    var styles = {};
    Object.entries(values).forEach(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref2, 2),
        key = _ref3[0],
        value = _ref3[1];
      if (value !== undefined && value !== null && value !== '') {
        // Convert camelCase to kebab-case for CSS
        var cssProperty = key.replace(/([A-Z])/g, '-$1').toLowerCase();

        // Handle special cases
        if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(value) === 'object' && value !== null) {
          // For complex objects like font settings
          if (key === 'font' || key === 'fontFamily') {
            Object.entries(value).forEach(function (_ref4) {
              var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref4, 2),
                subKey = _ref5[0],
                subValue = _ref5[1];
              var cssSubProperty = subKey.replace(/([A-Z])/g, '-$1').toLowerCase();
              styles[cssSubProperty] = subValue;
            });
          } else {
            // Convert object to string representation
            styles[cssProperty] = JSON.stringify(value);
          }
        } else {
          styles[cssProperty] = value;
        }
      }
    });
    return styles;
  }, [values]);

  // Generate CSS string for code preview
  var cssCode = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(function () {
    var cssLines = Object.entries(generatedStyles).map(function (_ref6) {
      var _ref7 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref6, 2),
        property = _ref7[0],
        value = _ref7[1];
      return "  ".concat(property, ": ").concat(value, ";");
    });
    return ".component {\n".concat(cssLines.join('\n'), "\n}");
  }, [generatedStyles]);

  // Generate inline styles object
  var inlineStyles = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(function () {
    var styles = {};
    Object.entries(generatedStyles).forEach(function (_ref8) {
      var _ref9 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref8, 2),
        property = _ref9[0],
        value = _ref9[1];
      // Convert kebab-case back to camelCase for React inline styles
      var reactProperty = property.replace(/-([a-z])/g, function (match, letter) {
        return letter.toUpperCase();
      });
      styles[reactProperty] = value;
    });
    return styles;
  }, [generatedStyles]);

  // Validate current values
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    var errors = {};
    Object.entries(values).forEach(function (_ref0) {
      var _ref1 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref0, 2),
        key = _ref1[0],
        value = _ref1[1];
      var schema = properties[key];
      if (schema && value !== undefined && value !== null && value !== '') {
        // Basic validation
        if (schema.type === 'number') {
          var num = parseFloat(value);
          if (isNaN(num)) {
            errors[key] = 'Must be a valid number';
          } else if (schema.min !== undefined && num < schema.min) {
            errors[key] = "Must be at least ".concat(schema.min);
          } else if (schema.max !== undefined && num > schema.max) {
            errors[key] = "Must be at most ".concat(schema.max);
          }
        } else if (schema.type === 'color') {
          var colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/;
          if (!colorRegex.test(value)) {
            errors[key] = 'Must be a valid color value';
          }
        }
      }
    });
    setValidationErrors(errors);
  }, [values, properties]);

  // Render preview element based on component type
  var renderPreviewElement = function renderPreviewElement() {
    var elementProps = {
      style: inlineStyles,
      className: 'preview-element'
    };
    switch (component === null || component === void 0 ? void 0 : component.type) {
      case 'button':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("button", elementProps, values.text || 'Button');
      case 'text':
        var Tag = values.variant || 'p';
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Tag, elementProps, values.content || 'Sample text');
      case 'input':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("input", (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, elementProps, {
          type: values.type || 'text',
          placeholder: values.placeholder || 'Enter text',
          disabled: values.disabled
        }));
      case 'card':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, elementProps, {
          style: _objectSpread(_objectSpread({}, inlineStyles), {}, {
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            padding: '16px',
            minWidth: '200px'
          })
        }), values.title && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("h4", {
          style: {
            margin: '0 0 8px 0'
          }
        }, values.title), values.description && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", {
          style: {
            margin: 0,
            color: '#666'
          }
        }, values.description));
      default:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, elementProps, {
          style: _objectSpread(_objectSpread({}, inlineStyles), {}, {
            padding: '16px',
            background: '#fff',
            border: '1px solid #d9d9d9',
            borderRadius: '4px'
          })
        }), (component === null || component === void 0 ? void 0 : component.name) || 'Component', " Preview");
    }
  };
  var hasErrors = Object.keys(validationErrors).length > 0;
  var hasChanges = Object.keys(values).some(function (key) {
    return values[key] !== undefined && values[key] !== '' && values[key] !== null;
  });
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(PreviewContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small",
    title: "Property Preview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
    style: {
      width: '100%',
      justifyContent: 'space-between'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: previewEnabled,
    onChange: setPreviewEnabled,
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .EyeOutlined */ .Om2, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .EyeInvisibleOutlined */ .LCF, null),
    size: "small"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      fontSize: '12px'
    }
  }, "Live Preview")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CodeOutlined */ .C$o, null),
    onClick: function onClick() {
      return setCodeVisible(!codeVisible);
    },
    style: {
      fontSize: '12px'
    }
  }, codeVisible ? 'Hide' : 'Show', " CSS"), hasChanges && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .UndoOutlined */ .Xrf, null),
    onClick: onReset,
    style: {
      fontSize: '12px'
    }
  }, "Reset All"))), previewEnabled && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(PreviewArea, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(PreviewElement, {
    styles: Object.entries(inlineStyles).map(function (_ref10) {
      var _ref11 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref10, 2),
        k = _ref11[0],
        v = _ref11[1];
      return "".concat(k, ": ").concat(v, ";");
    }).join(' ')
  }, renderPreviewElement())), codeVisible && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(react__WEBPACK_IMPORTED_MODULE_6__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Divider */ .cG, {
    style: {
      margin: '8px 0'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    strong: true,
    style: {
      fontSize: '12px'
    }
  }, "Generated CSS:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(CodePreview, null, cssCode))), showValidation && hasErrors && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ValidationMessage, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Alert */ .Fc, {
    message: "Validation Errors",
    description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("ul", {
      style: {
        margin: 0,
        paddingLeft: '16px'
      }
    }, Object.entries(validationErrors).map(function (_ref12) {
      var _ref13 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref12, 2),
        key = _ref13[0],
        error = _ref13[1];
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("li", {
        key: key,
        style: {
          fontSize: '12px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("strong", null, key, ":"), " ", error);
    })),
    type: "error",
    size: "small",
    showIcon: true
  })), showValidation && !hasErrors && hasChanges && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ValidationMessage, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Alert */ .Fc, {
    message: "All properties are valid",
    type: "success",
    size: "small",
    showIcon: true
  })))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PropertyPreview);

/***/ }),

/***/ 95732:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _PropertyRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(5184);




var _excluded = ["groupName", "properties", "values", "onChange", "componentType", "collapsible", "defaultExpanded", "showResetAll", "showPropertyCount"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8;





var Panel = antd__WEBPACK_IMPORTED_MODULE_5__/* .Collapse */ .SD.Panel;
var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;
var GroupContainer = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n"])));
var GroupHeader = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: #fafafa;\n  border: 1px solid #f0f0f0;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  \n  &:hover {\n    background: #f5f5f5;\n  }\n"])));
var GroupTitle = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var GroupActions = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"])));
var PropertyItem = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n"])));
var PropertyLabel = _design_system__WEBPACK_IMPORTED_MODULE_7__.styled.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8px;\n"])));
var PropertyName = (0,_design_system__WEBPACK_IMPORTED_MODULE_7__.styled)(Text)(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-weight: 500;\n  font-size: 13px;\n"])));
var PropertyDescription = (0,_design_system__WEBPACK_IMPORTED_MODULE_7__.styled)(Text)(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-size: 12px;\n  color: #8c8c8c;\n  display: block;\n  margin-top: 2px;\n"])));

/**
 * Property group component for organizing and displaying properties
 */
var PropertyGroup = function PropertyGroup(_ref) {
  var groupName = _ref.groupName,
    _ref$properties = _ref.properties,
    properties = _ref$properties === void 0 ? {} : _ref$properties,
    _ref$values = _ref.values,
    values = _ref$values === void 0 ? {} : _ref$values,
    onChange = _ref.onChange,
    componentType = _ref.componentType,
    _ref$collapsible = _ref.collapsible,
    collapsible = _ref$collapsible === void 0 ? true : _ref$collapsible,
    _ref$defaultExpanded = _ref.defaultExpanded,
    defaultExpanded = _ref$defaultExpanded === void 0 ? true : _ref$defaultExpanded,
    _ref$showResetAll = _ref.showResetAll,
    showResetAll = _ref$showResetAll === void 0 ? true : _ref$showResetAll,
    _ref$showPropertyCoun = _ref.showPropertyCount,
    showPropertyCount = _ref$showPropertyCoun === void 0 ? true : _ref$showPropertyCoun,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref, _excluded);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(defaultExpanded),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    isExpanded = _useState2[0],
    setIsExpanded = _useState2[1];

  // Get group icon based on group name
  var getGroupIcon = function getGroupIcon(name) {
    var iconMap = {
      basic: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, null),
      dimensions: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ColumnWidthOutlined */ .x18, null),
      spacing: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LayoutOutlined */ .hy2, null),
      typography: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FontSizeOutlined */ .ld1, null),
      colors: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BgColorsOutlined */ .Ebl, null),
      border: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BorderOutlined */ .bnM, null),
      shadow: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .HighlightOutlined */ .NSj, null),
      layout: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LayoutOutlined */ .hy2, null)
    };
    return iconMap[name.toLowerCase()] || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, null);
  };

  // Format group name for display
  var formatGroupName = function formatGroupName(name) {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
  };

  // Handle property value change
  var handlePropertyChange = function handlePropertyChange(newValue, propertyName, schema) {
    if (onChange) {
      onChange(propertyName, newValue, schema);
    }
  };

  // Handle reset all properties in group
  var handleResetAll = function handleResetAll(e) {
    e.stopPropagation();
    Object.keys(properties).forEach(function (propertyName) {
      var schema = properties[propertyName];
      var defaultValue = schema.defaultValue || '';
      handlePropertyChange(defaultValue, propertyName, schema);
    });
  };

  // Handle reset individual property
  var handleResetProperty = function handleResetProperty(propertyName, e) {
    e.stopPropagation();
    var schema = properties[propertyName];
    var defaultValue = schema.defaultValue || '';
    handlePropertyChange(defaultValue, propertyName, schema);
  };

  // Toggle group expansion
  var toggleExpanded = function toggleExpanded() {
    if (collapsible) {
      setIsExpanded(!isExpanded);
    }
  };

  // Count properties with non-default values
  var modifiedCount = Object.keys(properties).filter(function (key) {
    var value = values[key];
    var defaultValue = properties[key].defaultValue || '';
    return value !== defaultValue && value !== '' && value !== null && value !== undefined;
  }).length;
  var propertyCount = Object.keys(properties).length;
  if (propertyCount === 0) {
    return null;
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(GroupContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(GroupHeader, {
    onClick: toggleExpanded
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(GroupTitle, null, collapsible && (isExpanded ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownOutlined */ .lHd, {
    style: {
      fontSize: '12px'
    }
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RightOutlined */ .Xq1, {
    style: {
      fontSize: '12px'
    }
  })), getGroupIcon(groupName), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true,
    style: {
      fontSize: '14px'
    }
  }, formatGroupName(groupName)), showPropertyCount && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    size: 4
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
    count: propertyCount,
    size: "small",
    color: "#f0f0f0",
    style: {
      color: '#8c8c8c'
    }
  }), modifiedCount > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
    count: modifiedCount,
    size: "small",
    color: "#1890ff"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(GroupActions, null, showResetAll && modifiedCount > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
    title: "Reset all properties in this group"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UndoOutlined */ .Xrf, null),
    onClick: handleResetAll,
    style: {
      fontSize: '12px'
    }
  })))), isExpanded && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      border: '1px solid #f0f0f0',
      borderTop: 'none',
      borderRadius: '0 0 4px 4px'
    }
  }, Object.entries(properties).map(function (_ref2) {
    var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref2, 2),
      propertyName = _ref3[0],
      schema = _ref3[1];
    var currentValue = values[propertyName];
    var hasValue = currentValue !== undefined && currentValue !== '' && currentValue !== null;
    var isModified = hasValue && currentValue !== (schema.defaultValue || '');
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyItem, {
      key: propertyName
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyLabel, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyName, null, schema.label || propertyName, schema.required && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      type: "danger"
    }, " *")), schema.description && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyDescription, null, schema.description)), isModified && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
      title: "Reset to default"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UndoOutlined */ .Xrf, null),
      onClick: function onClick(e) {
        return handleResetProperty(propertyName, e);
      },
      style: {
        fontSize: '12px'
      }
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_PropertyRenderer__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({
      propertyName: propertyName,
      value: currentValue,
      onChange: handlePropertyChange,
      componentType: componentType,
      schema: schema,
      size: "small"
    }, props)));
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PropertyGroup);

/***/ })

}]);