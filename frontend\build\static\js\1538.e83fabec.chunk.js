"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1538,9157],{

/***/ 71538:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(5556);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(35346);
/* harmony import */ var _services_aiDesignService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(3288);





function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





var TabPane = antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU.TabPane;

/**
 * Enhanced AI Plugin component for App Builder
 * Provides layout suggestions, component combinations, and app analysis
 */
var EnhancedAIPlugin = function EnhancedAIPlugin(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$layouts = _ref.layouts,
    layouts = _ref$layouts === void 0 ? [] : _ref$layouts,
    _ref$selectedComponen = _ref.selectedComponent,
    selectedComponent = _ref$selectedComponen === void 0 ? null : _ref$selectedComponen,
    onApplyLayoutSuggestion = _ref.onApplyLayoutSuggestion,
    onApplyComponentCombination = _ref.onApplyComponentCombination,
    onComponentAdd = _ref.onComponentAdd,
    _ref$context = _ref.context,
    context = _ref$context === void 0 ? {} : _ref$context;
  // State management
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('layout'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    layoutSuggestions = _useState4[0],
    setLayoutSuggestions = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    combinationSuggestions = _useState6[0],
    setCombinationSuggestions = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    appAnalysis = _useState8[0],
    setAppAnalysis = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({
      layout: false,
      combinations: false,
      analysis: false
    }),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState9, 2),
    loading = _useState0[0],
    setLoading = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState1, 2),
    error = _useState10[0],
    setError = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(new Set()),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState11, 2),
    appliedSuggestions = _useState12[0],
    setAppliedSuggestions = _useState12[1];

  // Load suggestions when components change
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (components.length > 0) {
      loadLayoutSuggestions();
      loadCombinationSuggestions();
      loadAppAnalysis();
    }
  }, [components, selectedComponent]);

  // Load layout suggestions
  var loadLayoutSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
    var response, _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: true
            });
          });
          setError(null);
          _context.prev = 1;
          _context.next = 2;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.generateLayoutSuggestions(components, layouts, context);
        case 2:
          response = _context.sent;
          setLayoutSuggestions(response.suggestions || []);
          _context.next = 4;
          break;
        case 3:
          _context.prev = 3;
          _t = _context["catch"](1);
          setError("Failed to load layout suggestions: ".concat(_t.message));
        case 4:
          _context.prev = 4;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: false
            });
          });
          return _context.finish(4);
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 3, 4, 5]]);
  })), [components, layouts, context]);

  // Load component combination suggestions
  var loadCombinationSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2() {
    var response, _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: true
            });
          });
          setError(null);
          _context2.prev = 1;
          _context2.next = 2;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.generateComponentCombinations(components, selectedComponent, context);
        case 2:
          response = _context2.sent;
          setCombinationSuggestions(response.suggestions || []);
          _context2.next = 4;
          break;
        case 3:
          _context2.prev = 3;
          _t2 = _context2["catch"](1);
          setError("Failed to load combination suggestions: ".concat(_t2.message));
        case 4:
          _context2.prev = 4;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: false
            });
          });
          return _context2.finish(4);
        case 5:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 3, 4, 5]]);
  })), [components, selectedComponent, context]);

  // Load app analysis
  var loadAppAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3() {
    var response, _t3;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              analysis: true
            });
          });
          _context3.prev = 1;
          _context3.next = 2;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.analyzeAppStructure(components, layouts);
        case 2:
          response = _context3.sent;
          setAppAnalysis(response.analysis || null);
          _context3.next = 4;
          break;
        case 3:
          _context3.prev = 3;
          _t3 = _context3["catch"](1);
          console.warn('Failed to load app analysis:', _t3);
        case 4:
          _context3.prev = 4;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              analysis: false
            });
          });
          return _context3.finish(4);
        case 5:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[1, 3, 4, 5]]);
  })), [components, layouts]);

  // Handle applying layout suggestion
  var handleApplyLayoutSuggestion = function handleApplyLayoutSuggestion(suggestion) {
    if (onApplyLayoutSuggestion) {
      onApplyLayoutSuggestion(suggestion);
      setAppliedSuggestions(function (prev) {
        return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [suggestion.id]));
      });
    }
  };

  // Handle applying component combination
  var handleApplyComponentCombination = function handleApplyComponentCombination(suggestion) {
    if (onApplyComponentCombination) {
      onApplyComponentCombination(suggestion);
      setAppliedSuggestions(function (prev) {
        return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [suggestion.id]));
      });
    } else if (onComponentAdd && suggestion.missing_components) {
      // Fallback: add missing components one by one
      suggestion.missing_components.forEach(function (componentType) {
        onComponentAdd(componentType);
      });
      setAppliedSuggestions(function (prev) {
        return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [suggestion.id]));
      });
    }
  };

  // Refresh all suggestions
  var handleRefresh = function handleRefresh() {
    _services_aiDesignService__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.clearCache();
    loadLayoutSuggestions();
    loadCombinationSuggestions();
    loadAppAnalysis();
  };

  // Render layout suggestions tab
  var renderLayoutSuggestions = function renderLayoutSuggestions() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        padding: '8px 0'
      }
    }, loading.layout ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Spin */ .tK, {
      tip: "Analyzing your app structure..."
    })) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        maxHeight: '400px',
        overflowY: 'auto'
      }
    }, layoutSuggestions.map(function (suggestion) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
        key: suggestion.id,
        size: "small",
        style: {
          marginBottom: '12px'
        },
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, suggestion.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Badge */ .Ex, {
          count: suggestion.score,
          style: {
            backgroundColor: '#52c41a'
          }
        })),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          type: appliedSuggestions.has(suggestion.id) ? 'default' : 'primary',
          size: "small",
          icon: appliedSuggestions.has(suggestion.id) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CheckOutlined */ .JIb, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .LayoutOutlined */ .hy2, null),
          onClick: function onClick() {
            return handleApplyLayoutSuggestion(suggestion);
          },
          disabled: appliedSuggestions.has(suggestion.id)
        }, appliedSuggestions.has(suggestion.id) ? 'Applied' : 'Apply')
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
        style: {
          margin: '0 0 8px 0',
          fontSize: '12px',
          color: '#666'
        }
      }, suggestion.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
        style: {
          margin: 0,
          fontSize: '11px',
          fontStyle: 'italic'
        }
      }, suggestion.explanation));
    }), layoutSuggestions.length === 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px',
        color: '#999'
      }
    }, "No layout suggestions available. Add more components to get suggestions.")));
  };

  // Render component combinations tab
  var renderComponentCombinations = function renderComponentCombinations() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        padding: '8px 0'
      }
    }, loading.combinations ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Spin */ .tK, {
      tip: "Finding component combinations..."
    })) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        maxHeight: '400px',
        overflowY: 'auto'
      }
    }, combinationSuggestions.map(function (suggestion) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
        key: suggestion.id,
        size: "small",
        style: {
          marginBottom: '12px'
        },
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, suggestion.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Badge */ .Ex, {
          count: suggestion.score,
          style: {
            backgroundColor: '#1890ff'
          }
        })),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          type: appliedSuggestions.has(suggestion.id) ? 'default' : 'primary',
          size: "small",
          icon: appliedSuggestions.has(suggestion.id) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CheckOutlined */ .JIb, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .AppstoreOutlined */ .rS9, null),
          onClick: function onClick() {
            return handleApplyComponentCombination(suggestion);
          },
          disabled: appliedSuggestions.has(suggestion.id)
        }, appliedSuggestions.has(suggestion.id) ? 'Applied' : 'Add')
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
        style: {
          margin: '0 0 8px 0',
          fontSize: '12px',
          color: '#666'
        }
      }, suggestion.description), suggestion.missing_components && suggestion.missing_components.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
        style: {
          margin: '0 0 8px 0',
          fontSize: '11px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("strong", null, "Missing:"), " ", suggestion.missing_components.join(', ')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
        style: {
          margin: 0,
          fontSize: '11px',
          fontStyle: 'italic'
        }
      }, suggestion.explanation));
    }), combinationSuggestions.length === 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px',
        color: '#999'
      }
    }, "No combination suggestions available. Select a component to get targeted suggestions.")));
  };

  // Render app analysis tab
  var renderAppAnalysis = function renderAppAnalysis() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        padding: '8px 0'
      }
    }, loading.analysis ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Spin */ .tK, {
      tip: "Analyzing app structure..."
    })) : appAnalysis ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      size: "small",
      style: {
        marginBottom: '12px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("strong", null, "Components:"), " ", appAnalysis.component_count), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("strong", null, "App Type:"), " ", appAnalysis.app_type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("strong", null, "Complexity:"), " ", appAnalysis.complexity_score), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("strong", null, "Has Navigation:"), " ", appAnalysis.has_navigation ? 'Yes' : 'No'))), Object.keys(appAnalysis.component_types).length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      size: "small",
      title: "Component Types"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '8px'
      }
    }, Object.entries(appAnalysis.component_types).map(function (_ref5) {
      var _ref6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref5, 2),
        type = _ref6[0],
        count = _ref6[1];
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Badge */ .Ex, {
        key: type,
        count: count,
        style: {
          backgroundColor: '#722ed1'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", {
        style: {
          padding: '4px 8px',
          background: '#f0f0f0',
          borderRadius: '4px'
        }
      }, type));
    })))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px',
        color: '#999'
      }
    }, "No analysis data available."));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BulbOutlined */ .o3f, {
      style: {
        marginRight: '8px'
      }
    }), "AI Design Assistant"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tooltip */ .m_, {
      title: "Refresh suggestions"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ReloadOutlined */ .KF4, null),
      onClick: handleRefresh,
      loading: loading.layout || loading.combinations || loading.analysis
    }))),
    size: "small",
    style: {
      width: '100%'
    }
  }, error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Alert */ .Fc, {
    message: "Error",
    description: error,
    type: "error",
    closable: true,
    style: {
      marginBottom: '16px'
    },
    onClose: function onClose() {
      return setError(null);
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .LayoutOutlined */ .hy2, null), "Layouts"),
    key: "layout"
  }, renderLayoutSuggestions()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .AppstoreOutlined */ .rS9, null), "Components"),
    key: "combinations"
  }, renderComponentCombinations()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BarChartOutlined */ .cd5, null), "Analysis"),
    key: "analysis"
  }, renderAppAnalysis())));
};
EnhancedAIPlugin.propTypes = {
  components: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().array),
  layouts: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().array),
  selectedComponent: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().object),
  onApplyLayoutSuggestion: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onApplyComponentCombination: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onComponentAdd: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  context: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().object)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedAIPlugin);

/***/ })

}]);