"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9787],{

/***/ 9771:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Integrated Tutorial Assistant Component
 * 
 * Enhanced tutorial system specifically designed for the App Builder with
 * interactive overlays, context-aware help, and seamless integration.
 */





var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;
var Step = antd__WEBPACK_IMPORTED_MODULE_5__/* .Steps */ .gj.Step;

// Styled Components
var TutorialContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: relative;\n"])));
var TutorialCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  width: 380px;\n  z-index: 1000;\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\n  border-radius: 12px;\n  border: none;\n  \n  .ant-card-head {\n    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n    color: white;\n    border-radius: 12px 12px 0 0;\n    border: none;\n  }\n  \n  .ant-card-head-title {\n    color: white;\n    font-weight: 600;\n  }\n  \n  .ant-card-body {\n    padding: 20px;\n  }\n"])));
var ProgressContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin: 16px 0;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 8px;\n"])));
var StepIndicator = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8px;\n"])));
var ContextualHint = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-weight: 500;\n  z-index: 999;\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n  animation: fadeInBounce 0.5s ease-out;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 100%;\n    left: 50%;\n    margin-left: -6px;\n    border-width: 6px;\n    border-style: solid;\n    border-color: #1890ff transparent transparent transparent;\n  }\n  \n  @keyframes fadeInBounce {\n    0% { opacity: 0; transform: translateY(-10px) scale(0.8); }\n    100% { opacity: 1; transform: translateY(0) scale(1); }\n  }\n"])));
var TutorialOverlay = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  z-index: 998;\n  pointer-events: ", ";\n  backdrop-filter: blur(2px);\n"])), function (props) {
  return props.allowInteraction ? 'none' : 'auto';
});
var Spotlight = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  border: 3px solid #1890ff;\n  border-radius: 12px;\n  box-shadow: \n    0 0 0 4px rgba(24, 144, 255, 0.3),\n    0 0 0 9999px rgba(0, 0, 0, 0.6);\n  pointer-events: none;\n  z-index: 999;\n  animation: pulse 2s infinite;\n  \n  @keyframes pulse {\n    0% { \n      box-shadow: \n        0 0 0 4px rgba(24, 144, 255, 0.3),\n        0 0 0 8px rgba(24, 144, 255, 0.1),\n        0 0 0 9999px rgba(0, 0, 0, 0.6);\n    }\n    50% { \n      box-shadow: \n        0 0 0 8px rgba(24, 144, 255, 0.2),\n        0 0 0 16px rgba(24, 144, 255, 0.05),\n        0 0 0 9999px rgba(0, 0, 0, 0.6);\n    }\n    100% { \n      box-shadow: \n        0 0 0 4px rgba(24, 144, 255, 0.3),\n        0 0 0 8px rgba(24, 144, 255, 0.1),\n        0 0 0 9999px rgba(0, 0, 0, 0.6);\n    }\n  }\n"])));

// App Builder specific tutorial steps
var APP_BUILDER_TUTORIALS = {
  getting_started: {
    id: 'getting_started',
    title: 'Getting Started with App Builder',
    description: 'Learn the basics of creating applications',
    category: 'beginner',
    estimatedDuration: 5,
    steps: [{
      id: 'welcome',
      title: 'Welcome to App Builder!',
      content: 'App Builder lets you create applications visually using drag-and-drop components. Let\'s start with a quick tour of the interface.',
      target: null,
      position: 'center'
    }, {
      id: 'component-palette',
      title: 'Component Palette',
      content: 'This is where you\'ll find all available components. Drag components from here to the canvas to start building your app.',
      target: '[data-tutorial="component-palette"]',
      position: 'right'
    }, {
      id: 'canvas-area',
      title: 'Canvas Area',
      content: 'This is your workspace where you can drop and arrange components. You can also select components here to edit their properties.',
      target: '[data-tutorial="canvas-area"]',
      position: 'left'
    }, {
      id: 'property-editor',
      title: 'Property Editor',
      content: 'When you select a component, you can customize its properties here. Change colors, text, sizes, and more.',
      target: '[data-tutorial="property-editor"]',
      position: 'left'
    }, {
      id: 'preview',
      title: 'Preview Your App',
      content: 'Click the preview button to see how your app will look to users. You can test interactions and responsiveness.',
      target: '[data-tutorial="preview-button"]',
      position: 'bottom'
    }]
  },
  advanced_features: {
    id: 'advanced_features',
    title: 'Advanced Features',
    description: 'Explore powerful features for complex applications',
    category: 'advanced',
    estimatedDuration: 8,
    steps: [{
      id: 'testing-tools',
      title: 'Testing Tools',
      content: 'Use the testing tools to validate your components and ensure they work correctly across different scenarios.',
      target: '[data-tutorial="testing-tools"]',
      position: 'top'
    }, {
      id: 'data-management',
      title: 'Data Management',
      content: 'Manage your application data with our powerful data binding and state management tools.',
      target: '[data-tutorial="data-management"]',
      position: 'top'
    }, {
      id: 'performance-monitor',
      title: 'Performance Monitoring',
      content: 'Monitor your application\'s performance and get optimization suggestions to improve user experience.',
      target: '[data-tutorial="performance-monitor"]',
      position: 'top'
    }, {
      id: 'code-export',
      title: 'Code Export',
      content: 'Export your application to multiple frameworks including React, Vue, Angular, and more with TypeScript support.',
      target: '[data-tutorial="code-export"]',
      position: 'top'
    }]
  }
};

/**
 * IntegratedTutorialAssistant Component
 */
var IntegratedTutorialAssistant = function IntegratedTutorialAssistant(_ref) {
  var _ref$enableAutoStart = _ref.enableAutoStart,
    enableAutoStart = _ref$enableAutoStart === void 0 ? false : _ref$enableAutoStart,
    _ref$showContextualHe = _ref.showContextualHelp,
    showContextualHelp = _ref$showContextualHe === void 0 ? true : _ref$showContextualHe,
    onTutorialComplete = _ref.onTutorialComplete,
    onTutorialSkip = _ref.onTutorialSkip,
    _ref$features = _ref.features,
    features = _ref$features === void 0 ? [] : _ref$features;
  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    isActive = _useState2[0],
    setIsActive = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    currentTutorial = _useState4[0],
    setCurrentTutorial = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    currentStepIndex = _useState6[0],
    setCurrentStepIndex = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    isPaused = _useState8[0],
    setIsPaused = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    showTutorialList = _useState0[0],
    setShowTutorialList = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    showSettings = _useState10[0],
    setShowSettings = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    contextualHints = _useState12[0],
    setContextualHints = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState13, 2),
    spotlightPosition = _useState14[0],
    setSpotlightPosition = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set()),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState15, 2),
    completedTutorials = _useState16[0],
    setCompletedTutorials = _useState16[1];
  var _useState17 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      autoAdvance: false,
      showHints: true,
      playSound: false,
      animationSpeed: 'normal'
    }),
    _useState18 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState17, 2),
    assistantSettings = _useState18[0],
    setAssistantSettings = _useState18[1];

  // Get current tutorial step
  var currentStep = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    if (!currentTutorial || currentStepIndex >= currentTutorial.steps.length) return null;
    return currentTutorial.steps[currentStepIndex];
  }, [currentTutorial, currentStepIndex]);

  // Calculate progress percentage
  var progressPercentage = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    if (!currentTutorial) return 0;
    return Math.round((currentStepIndex + 1) / currentTutorial.steps.length * 100);
  }, [currentTutorial, currentStepIndex]);

  // Update spotlight position when step changes
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (!(currentStep !== null && currentStep !== void 0 && currentStep.target) || !isActive) {
      setSpotlightPosition(null);
      return;
    }
    var updateSpotlight = function updateSpotlight() {
      var element = document.querySelector(currentStep.target);
      if (element) {
        var rect = element.getBoundingClientRect();
        setSpotlightPosition({
          top: rect.top - 12,
          left: rect.left - 12,
          width: rect.width + 24,
          height: rect.height + 24
        });
      }
    };

    // Delay to allow for DOM updates
    setTimeout(updateSpotlight, 100);
    window.addEventListener('resize', updateSpotlight);
    return function () {
      return window.removeEventListener('resize', updateSpotlight);
    };
  }, [currentStep, isActive]);

  // Handle contextual hints
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (!showContextualHelp || isActive || !assistantSettings.showHints) {
      setContextualHints([]);
      return;
    }
    var hints = [];
    var elements = document.querySelectorAll('[data-tutorial]');
    elements.forEach(function (element) {
      var tutorialId = element.getAttribute('data-tutorial');
      var rect = element.getBoundingClientRect();
      if (rect.top >= 0 && rect.bottom <= window.innerHeight && rect.width > 0 && rect.height > 0) {
        hints.push({
          id: tutorialId,
          element: element,
          position: {
            top: rect.top - 45,
            left: rect.left + rect.width / 2 - 75
          },
          text: getHintText(tutorialId)
        });
      }
    });
    setContextualHints(hints.slice(0, 2)); // Limit to 2 hints to avoid clutter
  }, [showContextualHelp, isActive, assistantSettings.showHints]);

  // Get hint text for tutorial elements
  var getHintText = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (tutorialId) {
    var hintMap = {
      'component-palette': '🎨 Drag components from here',
      'property-editor': '⚙️ Customize properties here',
      'canvas-area': '🎯 Drop components here',
      'preview-button': '👁️ Preview your app',
      'export-button': '📤 Export your code',
      'testing-tools': '🧪 Test your components',
      'data-management': '📊 Manage your data',
      'performance-monitor': '⚡ Monitor performance',
      'code-export': '💻 Export to frameworks'
    };
    return hintMap[tutorialId] || '💡 Click for help';
  }, []);

  // Handle tutorial start
  var handleStartTutorial = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (tutorialId) {
    var tutorial = APP_BUILDER_TUTORIALS[tutorialId];
    if (!tutorial) return;
    setCurrentTutorial(tutorial);
    setCurrentStepIndex(0);
    setIsActive(true);
    setIsPaused(false);
    setShowTutorialList(false);
    antd__WEBPACK_IMPORTED_MODULE_5__/* .notification */ .Ew.success({
      message: 'Tutorial Started',
      description: "Starting \"".concat(tutorial.title, "\" tutorial"),
      duration: 3,
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BookOutlined */ .E6Q, {
        style: {
          color: '#1890ff'
        }
      })
    });
  }, []);

  // Handle next step
  var handleNextStep = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (!currentTutorial) return;
    if (currentStepIndex < currentTutorial.steps.length - 1) {
      setCurrentStepIndex(function (prev) {
        return prev + 1;
      });
    } else {
      // Tutorial completed
      handleTutorialComplete();
    }
  }, [currentTutorial, currentStepIndex]);

  // Handle previous step
  var handlePreviousStep = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(function (prev) {
        return prev - 1;
      });
    }
  }, [currentStepIndex]);

  // Handle tutorial completion
  var handleTutorialComplete = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (!currentTutorial) return;
    setCompletedTutorials(function (prev) {
      return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [currentTutorial.id]));
    });
    setIsActive(false);
    setCurrentTutorial(null);
    setCurrentStepIndex(0);
    if (onTutorialComplete) {
      onTutorialComplete(currentTutorial);
    }
    antd__WEBPACK_IMPORTED_MODULE_5__/* .notification */ .Ew.success({
      message: 'Tutorial Completed! 🎉',
      description: 'Great job! You\'ve mastered this feature.',
      duration: 5,
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .TrophyOutlined */ .tMQ, {
        style: {
          color: '#faad14'
        }
      })
    });
  }, [currentTutorial, onTutorialComplete]);

  // Handle tutorial skip
  var handleTutorialSkip = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (!currentTutorial) return;
    setIsActive(false);
    setCurrentTutorial(null);
    setCurrentStepIndex(0);
    if (onTutorialSkip) {
      onTutorialSkip(currentTutorial);
    }
    antd__WEBPACK_IMPORTED_MODULE_5__/* .notification */ .Ew.info({
      message: 'Tutorial Skipped',
      description: 'You can restart it anytime from the tutorial list.',
      duration: 3
    });
  }, [currentTutorial, onTutorialSkip]);

  // Handle pause/resume
  var handlePauseResume = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    setIsPaused(function (prev) {
      return !prev;
    });
  }, []);

  // Auto-start tutorial for new users
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (enableAutoStart && !isActive && completedTutorials.size === 0) {
      setTimeout(function () {
        handleStartTutorial('getting_started');
      }, 2000);
    }
  }, [enableAutoStart, isActive, completedTutorials.size, handleStartTutorial]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TutorialContainer, null, isActive && !isPaused && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TutorialOverlay, {
    allowInteraction: (currentStep === null || currentStep === void 0 ? void 0 : currentStep.type) === 'interactive'
  }, spotlightPosition && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Spotlight, {
    style: {
      top: spotlightPosition.top,
      left: spotlightPosition.left,
      width: spotlightPosition.width,
      height: spotlightPosition.height
    }
  })), contextualHints.map(function (hint) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ContextualHint, {
      key: hint.id,
      style: {
        top: hint.position.top,
        left: hint.position.left
      }
    }, hint.text);
  }), isActive && currentStep && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TutorialCard, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BookOutlined */ .E6Q, null), currentTutorial.title, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
      count: "".concat(currentStepIndex + 1, "/").concat(currentTutorial.steps.length),
      style: {
        backgroundColor: 'rgba(255,255,255,0.2)'
      }
    })),
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      type: "text",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CloseOutlined */ .r$3, null),
      onClick: handleTutorialSkip,
      style: {
        color: 'white'
      },
      size: "small"
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 5,
    style: {
      marginBottom: 8,
      color: '#1890ff'
    }
  }, currentStep.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, {
    style: {
      marginBottom: 16,
      color: '#666'
    }
  }, currentStep.content), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ProgressContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(StepIndicator, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: 12
    }
  }, "Progress"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true,
    style: {
      color: '#1890ff'
    }
  }, progressPercentage, "%")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Progress */ .ke, {
    percent: progressPercentage,
    size: "small",
    strokeColor: "#1890ff",
    trailColor: "#f0f0f0",
    showInfo: false
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Row */ .fI, {
    justify: "space-between",
    align: "middle"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StepBackwardOutlined */ .Y53, null),
    onClick: handlePreviousStep,
    disabled: currentStepIndex === 0,
    size: "small"
  }, "Previous"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: isPaused ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PlayCircleOutlined */ .VgC, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PauseCircleOutlined */ .Xcy, null),
    onClick: handlePauseResume,
    size: "small"
  }, isPaused ? 'Resume' : 'Pause'))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StepForwardOutlined */ .QES, null),
    onClick: handleTutorialSkip,
    size: "small"
  }, "Skip"), currentStepIndex === currentTutorial.steps.length - 1 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckCircleOutlined */ .hWy, null),
    onClick: handleTutorialComplete,
    size: "small"
  }, "Complete") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StepForwardOutlined */ .QES, null),
    onClick: handleNextStep,
    size: "small"
  }, "Next")))))), !isActive && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .FloatButton */ .ff.Group, {
    trigger: "click",
    type: "primary",
    style: {
      right: 24,
      bottom: 24
    },
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .QuestionCircleOutlined */ .faO, null),
    tooltip: "Tutorial Assistant"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .FloatButton */ .ff, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BookOutlined */ .E6Q, null),
    tooltip: "Browse Tutorials",
    onClick: function onClick() {
      return setShowTutorialList(true);
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .FloatButton */ .ff, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BulbOutlined */ .o3f, null),
    tooltip: "Toggle Hints",
    onClick: function onClick() {
      return setAssistantSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          showHints: !prev.showHints
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .FloatButton */ .ff, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, null),
    tooltip: "Tutorial Settings",
    onClick: function onClick() {
      return setShowSettings(true);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Modal */ .aF, {
    title: "Available Tutorials",
    open: showTutorialList,
    onCancel: function onCancel() {
      return setShowTutorialList(false);
    },
    footer: null,
    width: 600
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary"
  }, "Choose a tutorial to learn about App Builder features. Completed tutorials are marked with a trophy.")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .List */ .B8, {
    dataSource: Object.values(APP_BUILDER_TUTORIALS),
    renderItem: function renderItem(tutorial) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .List */ .B8.Item, {
        actions: [completedTutorials.has(tutorial.id) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tag */ .vw, {
          color: "gold",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .TrophyOutlined */ .tMQ, null)
        }, "Completed") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
          type: "primary",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PlayCircleOutlined */ .VgC, null),
          onClick: function onClick() {
            return handleStartTutorial(tutorial.id);
          }
        }, "Start")]
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .List */ .B8.Item.Meta, {
        avatar: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Avatar */ .eu, {
          icon: tutorial.category === 'beginner' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BookOutlined */ .E6Q, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RocketOutlined */ .PKb, null),
          style: {
            backgroundColor: tutorial.category === 'beginner' ? '#52c41a' : '#1890ff'
          }
        }),
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, tutorial.title, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tag */ .vw, {
          color: tutorial.category === 'beginner' ? 'green' : 'blue'
        }, tutorial.category)),
        description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, {
          style: {
            margin: 0,
            marginBottom: 4
          }
        }, tutorial.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
          type: "secondary",
          style: {
            fontSize: 12
          }
        }, "\u23F1\uFE0F ", tutorial.estimatedDuration, " minutes \u2022 ", tutorial.steps.length, " steps"))
      }));
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Modal */ .aF, {
    title: "Tutorial Settings",
    open: showSettings,
    onCancel: function onCancel() {
      return setShowSettings(false);
    },
    footer: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      key: "close",
      onClick: function onClick() {
        return setShowSettings(false);
      }
    }, "Close")]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "Display Options"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Row */ .fI, {
    gutter: [16, 8]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: assistantSettings.showHints,
    onChange: function onChange(checked) {
      return setAssistantSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          showHints: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Show Contextual Hints")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: assistantSettings.autoAdvance,
    onChange: function onChange(checked) {
      return setAssistantSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          autoAdvance: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Auto-advance Steps")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: assistantSettings.playSound,
    onChange: function onChange(checked) {
      return setAssistantSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          playSound: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Play Sound Effects"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "Tutorial Progress"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary"
  }, "Completed: ", completedTutorials.size, " / ", Object.keys(APP_BUILDER_TUTORIALS).length, " tutorials"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Progress */ .ke, {
    percent: Math.round(completedTutorials.size / Object.keys(APP_BUILDER_TUTORIALS).length * 100),
    size: "small",
    style: {
      marginTop: 8
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "Quick Actions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    wrap: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BookOutlined */ .E6Q, null),
    onClick: function onClick() {
      setShowSettings(false);
      handleStartTutorial('getting_started');
    }
  }, "Restart Getting Started"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RocketOutlined */ .PKb, null),
    onClick: function onClick() {
      setShowSettings(false);
      handleStartTutorial('advanced_features');
    }
  }, "Advanced Features"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CloseOutlined */ .r$3, null),
    onClick: function onClick() {
      setCompletedTutorials(new Set());
      antd__WEBPACK_IMPORTED_MODULE_5__/* .notification */ .Ew.success({
        message: 'Progress Reset',
        description: 'All tutorial progress has been reset.',
        duration: 3
      });
    }
  }, "Reset Progress")))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IntegratedTutorialAssistant);

/***/ }),

/***/ 39446:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;
var Step = antd__WEBPACK_IMPORTED_MODULE_5__/* .Steps */ .gj.Step;
var TutorialOverlay = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  pointer-events: ", ";\n  opacity: ", ";\n  transition: all 0.3s ease;\n"])), function (props) {
  return props.active ? 'all' : 'none';
}, function (props) {
  return props.active ? 1 : 0;
});
var TutorialSpotlight = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  border: 3px solid #1890ff;\n  border-radius: 8px;\n  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);\n  pointer-events: none;\n  transition: all 0.3s ease;\n  z-index: 10000;\n"])));
var TutorialCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  max-width: 400px;\n  z-index: 10001;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  \n  .ant-card-head {\n    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);\n    color: white;\n    border-radius: 12px 12px 0 0;\n    \n    .ant-card-head-title {\n      color: white;\n    }\n  }\n"])));
var HelpButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  bottom: 24px;\n  right: 24px;\n  width: 56px;\n  height: 56px;\n  border-radius: 50%;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n"])));
var ContextualHint = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  background: #1890ff;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 500;\n  z-index: 1001;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 100%;\n    left: 50%;\n    transform: translateX(-50%);\n    border: 6px solid transparent;\n    border-top-color: #1890ff;\n  }\n"])));
var IntegratedTutorialSystem = function IntegratedTutorialSystem(_ref) {
  var children = _ref.children,
    onTutorialComplete = _ref.onTutorialComplete;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    tutorialActive = _useState2[0],
    setTutorialActive = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    currentStep = _useState4[0],
    setCurrentStep = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    tutorialVisible = _useState6[0],
    setTutorialVisible = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    helpDrawerVisible = _useState8[0],
    setHelpDrawerVisible = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      autoStart: false,
      showHints: true,
      skipCompleted: true
    }),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    tutorialSettings = _useState0[0],
    setTutorialSettings = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set()),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    completedSteps = _useState10[0],
    setCompletedSteps = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      top: 0,
      left: 0,
      width: 0,
      height: 0
    }),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    spotlightPosition = _useState12[0],
    setSpotlightPosition = _useState12[1];
  var tutorialSteps = [{
    id: 'welcome',
    title: 'Welcome to App Builder',
    content: 'Let\'s take a quick tour of the main features. This tutorial will help you get started with building your first application.',
    target: null,
    action: 'Click "Next" to continue',
    position: 'center'
  }, {
    id: 'component-palette',
    title: 'Component Palette',
    content: 'This is where you\'ll find all available components. Drag components from here to the canvas to start building your app.',
    target: '[data-tutorial="component-palette"]',
    action: 'Try dragging a component to the canvas',
    position: 'right'
  }, {
    id: 'canvas-area',
    title: 'Design Canvas',
    content: 'This is your design canvas where you can drop components and arrange them. Components can be resized and repositioned here.',
    target: '[data-tutorial="canvas-area"]',
    action: 'Drop a component here to see it in action',
    position: 'left'
  }, {
    id: 'property-editor',
    title: 'Property Editor',
    content: 'When you select a component, its properties appear here. You can customize colors, text, sizes, and behavior.',
    target: '[data-tutorial="property-editor"]',
    action: 'Select a component to see its properties',
    position: 'left'
  }, {
    id: 'layout-tools',
    title: 'Layout Tools',
    content: 'Use these tools to align components, distribute spacing, and manage the layout of your design.',
    target: '[data-tutorial="layout-tools"]',
    action: 'Try the alignment tools with multiple components',
    position: 'bottom'
  }, {
    id: 'theme-manager',
    title: 'Theme Manager',
    content: 'Create and apply themes to maintain consistent styling across your application. Themes control colors, fonts, and spacing.',
    target: '[data-tutorial="theme-manager"]',
    action: 'Create a custom theme or apply an existing one',
    position: 'top'
  }, {
    id: 'preview-mode',
    title: 'Preview Mode',
    content: 'Switch to preview mode to see how your application will look and behave for end users.',
    target: '[data-tutorial="preview-mode"]',
    action: 'Toggle preview mode to test your app',
    position: 'bottom'
  }, {
    id: 'completion',
    title: 'Tutorial Complete!',
    content: 'Great job! You\'ve learned the basics of App Builder. You\'re now ready to create amazing applications.',
    target: null,
    action: 'Start building your app!',
    position: 'center'
  }];
  var currentTutorialStep = tutorialSteps[currentStep];
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (tutorialActive && currentTutorialStep !== null && currentTutorialStep !== void 0 && currentTutorialStep.target) {
      var targetElement = document.querySelector(currentTutorialStep.target);
      if (targetElement) {
        var rect = targetElement.getBoundingClientRect();
        setSpotlightPosition({
          top: rect.top - 8,
          left: rect.left - 8,
          width: rect.width + 16,
          height: rect.height + 16
        });
      }
    }
  }, [tutorialActive, currentStep, currentTutorialStep]);
  var startTutorial = function startTutorial() {
    setTutorialActive(true);
    setTutorialVisible(true);
    setCurrentStep(0);
  };
  var nextStep = function nextStep() {
    if (currentStep < tutorialSteps.length - 1) {
      setCompletedSteps(function (prev) {
        return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [tutorialSteps[currentStep].id]));
      });
      setCurrentStep(currentStep + 1);
    } else {
      completeTutorial();
    }
  };
  var previousStep = function previousStep() {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  var skipTutorial = function skipTutorial() {
    setTutorialActive(false);
    setTutorialVisible(false);
    setCurrentStep(0);
  };
  var completeTutorial = function completeTutorial() {
    setTutorialActive(false);
    setTutorialVisible(false);
    setCompletedSteps(function (prev) {
      return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(tutorialSteps.map(function (step) {
        return step.id;
      }))));
    });
    if (onTutorialComplete) {
      onTutorialComplete();
    }
  };
  var getTutorialCardPosition = function getTutorialCardPosition() {
    if (!(currentTutorialStep !== null && currentTutorialStep !== void 0 && currentTutorialStep.position) || currentTutorialStep.position === 'center') {
      return {
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)'
      };
    }
    var position = currentTutorialStep.position;
    var top = spotlightPosition.top,
      left = spotlightPosition.left,
      width = spotlightPosition.width,
      height = spotlightPosition.height;
    switch (position) {
      case 'right':
        return {
          top: top + height / 2,
          left: left + width + 20,
          transform: 'translateY(-50%)'
        };
      case 'left':
        return {
          top: top + height / 2,
          right: window.innerWidth - left + 20,
          transform: 'translateY(-50%)'
        };
      case 'bottom':
        return {
          top: top + height + 20,
          left: left + width / 2,
          transform: 'translateX(-50%)'
        };
      case 'top':
        return {
          bottom: window.innerHeight - top + 20,
          left: left + width / 2,
          transform: 'translateX(-50%)'
        };
      default:
        return {
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        };
    }
  };
  var ContextualHelp = function ContextualHelp(_ref2) {
    var target = _ref2.target,
      hint = _ref2.hint,
      visible = _ref2.visible;
    var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
        top: 0,
        left: 0
      }),
      _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState13, 2),
      position = _useState14[0],
      setPosition = _useState14[1];
    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
      if (visible && target) {
        var element = document.querySelector(target);
        if (element) {
          var rect = element.getBoundingClientRect();
          setPosition({
            top: rect.top - 40,
            left: rect.left + rect.width / 2
          });
        }
      }
    }, [visible, target]);
    if (!visible) return null;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ContextualHint, {
      style: {
        top: position.top,
        left: position.left,
        transform: 'translateX(-50%)'
      }
    }, hint);
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TutorialOverlay, {
    active: tutorialActive
  }, (currentTutorialStep === null || currentTutorialStep === void 0 ? void 0 : currentTutorialStep.target) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TutorialSpotlight, {
    style: {
      top: spotlightPosition.top,
      left: spotlightPosition.left,
      width: spotlightPosition.width,
      height: spotlightPosition.height
    }
  })), tutorialVisible && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TutorialCard, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BookOutlined */ .E6Q, null), currentTutorialStep === null || currentTutorialStep === void 0 ? void 0 : currentTutorialStep.title, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
      count: "".concat(currentStep + 1, "/").concat(tutorialSteps.length)
    })),
    style: getTutorialCardPosition(),
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      type: "text",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CloseOutlined */ .r$3, null),
      onClick: skipTutorial,
      style: {
        color: 'white'
      }
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Progress */ .ke, {
    percent: (currentStep + 1) / tutorialSteps.length * 100,
    size: "small",
    showInfo: false
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, {
    style: {
      margin: 0
    }
  }, currentTutorialStep === null || currentTutorialStep === void 0 ? void 0 : currentTutorialStep.content), (currentTutorialStep === null || currentTutorialStep === void 0 ? void 0 : currentTutorialStep.action) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      background: '#f0f2f5',
      padding: '8px 12px',
      borderRadius: '6px',
      fontSize: '12px',
      color: '#666'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BulbOutlined */ .o3f, {
    style: {
      marginRight: 4
    }
  }), currentTutorialStep.action), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    onClick: previousStep,
    disabled: currentStep === 0,
    size: "small"
  }, "Previous"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    onClick: skipTutorial,
    size: "small"
  }, "Skip Tutorial"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    onClick: nextStep,
    icon: currentStep === tutorialSteps.length - 1 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckCircleOutlined */ .hWy, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PlayCircleOutlined */ .VgC, null),
    size: "small"
  }, currentStep === tutorialSteps.length - 1 ? 'Complete' : 'Next'))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
    count: tutorialSettings.showHints ? '?' : 0,
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(HelpButton, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .QuestionCircleOutlined */ .faO, null),
    onClick: function onClick() {
      return setHelpDrawerVisible(true);
    },
    title: "Get Help"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Drawer */ ._s, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BookOutlined */ .E6Q, null), "Help & Tutorials"),
    placement: "right",
    onClose: function onClose() {
      return setHelpDrawerVisible(false);
    },
    visible: helpDrawerVisible,
    width: 400
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 4
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RocketOutlined */ .PKb, {
    style: {
      color: '#1890ff',
      marginRight: 8
    }
  }), "Quick Start"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    block: true,
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PlayCircleOutlined */ .VgC, null),
    onClick: function onClick() {
      setHelpDrawerVisible(false);
      startTutorial();
    }
  }, "Start Interactive Tutorial")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 4
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, {
    style: {
      color: '#722ed1',
      marginRight: 8
    }
  }), "Tutorial Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, null, "Auto-start for new users"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: tutorialSettings.autoStart,
    onChange: function onChange(checked) {
      return setTutorialSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          autoStart: checked
        });
      });
    },
    size: "small"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, null, "Show contextual hints"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: tutorialSettings.showHints,
    onChange: function onChange(checked) {
      return setTutorialSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          showHints: checked
        });
      });
    },
    size: "small"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, null, "Skip completed steps"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: tutorialSettings.skipCompleted,
    onChange: function onChange(checked) {
      return setTutorialSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          skipCompleted: checked
        });
      });
    },
    size: "small"
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 4
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EyeOutlined */ .Om2, {
    style: {
      color: '#52c41a',
      marginRight: 8
    }
  }), "Tutorial Progress"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Steps */ .gj, {
    direction: "vertical",
    size: "small",
    current: -1
  }, tutorialSteps.map(function (step, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Step, {
      key: step.id,
      title: step.title,
      status: completedSteps.has(step.id) ? 'finish' : 'wait',
      icon: completedSteps.has(step.id) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckCircleOutlined */ .hWy, null) : undefined
    });
  }))))), tutorialSettings.showHints && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ContextualHelp, {
    target: "[data-tutorial='component-palette']",
    hint: "Drag components from here",
    visible: !tutorialActive && !completedSteps.has('component-palette')
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ContextualHelp, {
    target: "[data-tutorial='property-editor']",
    hint: "Customize component properties",
    visible: !tutorialActive && !completedSteps.has('property-editor')
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IntegratedTutorialSystem);

/***/ }),

/***/ 89292:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export useContextualHelp */
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(40961);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);
/* harmony import */ var _TutorialManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(82479);
/* harmony import */ var _TutorialStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(52461);
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(98835);



var _templateObject, _templateObject2, _templateObject3;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Contextual Help System
 * 
 * Provides context-aware help tooltips, smart suggestions, and
 * contextual help panels based on user actions and current UI state.
 */









var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title;

// Styled Components
var HelpTooltip = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  background: #fff;\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  padding: 12px;\n  max-width: 280px;\n  z-index: ", ";\n  pointer-events: auto;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    width: 0;\n    height: 0;\n    border-style: solid;\n    border-width: 6px 6px 0 6px;\n    border-color: #fff transparent transparent transparent;\n    bottom: -6px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n"])), _types__WEBPACK_IMPORTED_MODULE_10__/* .Z_INDEX */ .Mu.TUTORIAL_TOOLTIP);
var HelpPanel = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 50%;\n  right: 20px;\n  transform: translateY(-50%);\n  width: 320px;\n  max-height: 70vh;\n  overflow-y: auto;\n  z-index: ", ";\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n"])), _types__WEBPACK_IMPORTED_MODULE_10__/* .Z_INDEX */ .Mu.TUTORIAL_MODAL);
var SmartSuggestion = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  background: #1890ff;\n  color: white;\n  padding: 12px 16px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n  z-index: ", ";\n  max-width: 300px;\n  cursor: pointer;\n  animation: slideIn 0.3s ease-out;\n  \n  @keyframes slideIn {\n    from {\n      transform: translateX(100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n"])), _types__WEBPACK_IMPORTED_MODULE_10__/* .Z_INDEX */ .Mu.TUTORIAL_TOOLTIP);

// Help Context Definitions
var HELP_CONTEXTS = (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.COMPONENT_PALETTE, (0,_types__WEBPACK_IMPORTED_MODULE_10__/* .createHelpContext */ .pS)({
  id: 'component_palette_help',
  type: _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.COMPONENT_PALETTE,
  title: 'Component Palette',
  content: 'Drag components from here to the preview area to build your application. Each component has different properties you can customize.',
  triggers: ['hover', 'focus'],
  conditions: ['first_visit', 'no_components_added'],
  relatedTutorials: ['getting_started', 'component_basics']
})), _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PREVIEW_AREA, (0,_types__WEBPACK_IMPORTED_MODULE_10__/* .createHelpContext */ .pS)({
  id: 'preview_area_help',
  type: _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PREVIEW_AREA,
  title: 'Preview Area',
  content: 'This is where your application comes to life. Drop components here and see them rendered in real-time.',
  triggers: ['empty_state', 'first_drop'],
  conditions: ['no_components'],
  relatedTutorials: ['getting_started', 'drag_drop_basics']
})), _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PROPERTY_EDITOR, (0,_types__WEBPACK_IMPORTED_MODULE_10__/* .createHelpContext */ .pS)({
  id: 'property_editor_help',
  type: _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PROPERTY_EDITOR,
  title: 'Property Editor',
  content: 'Customize your selected component here. Change colors, text, sizes, and behavior to match your design.',
  triggers: ['component_selected'],
  conditions: ['first_selection'],
  relatedTutorials: ['component_properties', 'styling_basics']
})), _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.DRAG_DROP, (0,_types__WEBPACK_IMPORTED_MODULE_10__/* .createHelpContext */ .pS)({
  id: 'drag_drop_help',
  type: _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.DRAG_DROP,
  title: 'Drag & Drop',
  content: 'Drag components from the palette and drop them in the preview area. You can also rearrange existing components.',
  triggers: ['drag_start', 'hover_long'],
  conditions: ['struggling_with_drag'],
  relatedTutorials: ['drag_drop_tutorial']
}));
var ContextualHelp = function ContextualHelp(_ref) {
  var _ref$userId = _ref.userId,
    userId = _ref$userId === void 0 ? 'anonymous' : _ref$userId;
  var _useTutorial = (0,_TutorialManager__WEBPACK_IMPORTED_MODULE_8__/* .useTutorial */ .s8)(),
    startTutorial = _useTutorial.startTutorial,
    preferences = _useTutorial.preferences;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    activeHelp = _useState2[0],
    setActiveHelp = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
      x: 0,
      y: 0
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    helpPosition = _useState4[0],
    setHelpPosition = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    showPanel = _useState6[0],
    setShowPanel = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState7, 2),
    smartSuggestion = _useState8[0],
    setSmartSuggestion = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
      hoverTime: 0,
      clickCount: 0,
      lastAction: null,
      strugglingWith: null
    }),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState9, 2),
    userBehavior = _useState0[0],
    setUserBehavior = _useState0[1];
  var hoverTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);
  var behaviorTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);

  // Track user behavior for smart suggestions
  var trackUserBehavior = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (action, element) {
    var duration = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
    setUserBehavior(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        lastAction: action,
        clickCount: action === 'click' ? prev.clickCount + 1 : prev.clickCount,
        hoverTime: action === 'hover' ? duration : prev.hoverTime
      });
    });

    // Detect struggling patterns
    if (action === 'hover' && duration > 3000) {
      setUserBehavior(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          strugglingWith: element
        });
      });
      showSmartSuggestion(element);
    }
  }, []);

  // Show smart suggestion based on user behavior
  var showSmartSuggestion = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (context) {
    if (!preferences.showContextualHelp) return;
    var helpContext = HELP_CONTEXTS[context];
    if (!helpContext) return;

    // Check if already shown
    var shownContexts = _TutorialStorage__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.getShownHelpContexts(userId);
    if (helpContext.showOnce && shownContexts.includes(helpContext.id)) {
      return;
    }
    setSmartSuggestion(_objectSpread(_objectSpread({}, helpContext), {}, {
      onAccept: function onAccept() {
        if (helpContext.relatedTutorials.length > 0) {
          startTutorial(helpContext.relatedTutorials[0]);
        }
        setSmartSuggestion(null);
        _TutorialStorage__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.markHelpContextShown(helpContext.id, userId);
      },
      onDismiss: function onDismiss() {
        setSmartSuggestion(null);
        _TutorialStorage__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A.markHelpContextShown(helpContext.id, userId);
      }
    }));
  }, [preferences.showContextualHelp, userId, startTutorial]);

  // Show contextual help tooltip
  var showContextualHelp = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (context, element, position) {
    if (!preferences.showTooltips) return;
    var helpContext = HELP_CONTEXTS[context];
    if (!helpContext) return;
    setActiveHelp(helpContext);
    setHelpPosition(position);

    // Auto-hide after 5 seconds
    setTimeout(function () {
      setActiveHelp(null);
    }, 5000);
  }, [preferences.showTooltips]);

  // Set up event listeners for contextual help
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!preferences.showContextualHelp) return;
    var handleMouseEnter = function handleMouseEnter(e) {
      var element = e.target;
      var startTime = Date.now();

      // Component palette help
      if (element.closest('[data-help-context="component-palette"]')) {
        hoverTimerRef.current = setTimeout(function () {
          var duration = Date.now() - startTime;
          trackUserBehavior('hover', _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.COMPONENT_PALETTE, duration);
          var rect = element.getBoundingClientRect();
          showContextualHelp(_types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.COMPONENT_PALETTE, element, {
            x: rect.left + rect.width / 2,
            y: rect.top - 10
          });
        }, 1500);
      }

      // Preview area help
      if (element.closest('[data-help-context="preview-area"]')) {
        var hasComponents = document.querySelectorAll('[data-component-id]').length > 0;
        if (!hasComponents) {
          hoverTimerRef.current = setTimeout(function () {
            var duration = Date.now() - startTime;
            trackUserBehavior('hover', _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PREVIEW_AREA, duration);
            var rect = element.getBoundingClientRect();
            showContextualHelp(_types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PREVIEW_AREA, element, {
              x: rect.left + rect.width / 2,
              y: rect.top + 20
            });
          }, 2000);
        }
      }

      // Property editor help
      if (element.closest('[data-help-context="property-editor"]')) {
        hoverTimerRef.current = setTimeout(function () {
          var duration = Date.now() - startTime;
          trackUserBehavior('hover', _types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PROPERTY_EDITOR, duration);
          var rect = element.getBoundingClientRect();
          showContextualHelp(_types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PROPERTY_EDITOR, element, {
            x: rect.left - 10,
            y: rect.top + rect.height / 2
          });
        }, 1000);
      }
    };
    var handleMouseLeave = function handleMouseLeave() {
      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
        hoverTimerRef.current = null;
      }
    };
    var handleClick = function handleClick(e) {
      trackUserBehavior('click', e.target.dataset.helpContext);
      setActiveHelp(null); // Hide help on click
    };

    // Add event listeners
    document.addEventListener('mouseenter', handleMouseEnter, true);
    document.addEventListener('mouseleave', handleMouseLeave, true);
    document.addEventListener('click', handleClick, true);
    return function () {
      document.removeEventListener('mouseenter', handleMouseEnter, true);
      document.removeEventListener('mouseleave', handleMouseLeave, true);
      document.removeEventListener('click', handleClick, true);
      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
      }
    };
  }, [preferences.showContextualHelp, trackUserBehavior, showContextualHelp]);

  // Detect empty states and show suggestions
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    var checkEmptyStates = function checkEmptyStates() {
      var hasComponents = document.querySelectorAll('[data-component-id]').length > 0;
      if (!hasComponents) {
        behaviorTimerRef.current = setTimeout(function () {
          showSmartSuggestion(_types__WEBPACK_IMPORTED_MODULE_10__/* .HELP_CONTEXT_TYPES */ .ek.PREVIEW_AREA);
        }, 10000); // Show suggestion after 10 seconds of empty state
      }
    };
    checkEmptyStates();
    return function () {
      if (behaviorTimerRef.current) {
        clearTimeout(behaviorTimerRef.current);
      }
    };
  }, [showSmartSuggestion]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, activeHelp && /*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_4__.createPortal)(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(HelpTooltip, {
    style: {
      left: helpPosition.x,
      top: helpPosition.y
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .QuestionCircleOutlined */ .faO, {
    style: {
      color: '#1890ff'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    strong: true
  }, activeHelp.title)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, activeHelp.content), activeHelp.relatedTutorials.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "link",
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PlayCircleOutlined */ .VgC, null),
    onClick: function onClick() {
      startTutorial(activeHelp.relatedTutorials[0]);
      setActiveHelp(null);
    }
  }, "Start Tutorial"))), document.body), smartSuggestion && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(SmartSuggestion, {
    onClick: smartSuggestion.onAccept
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BulbOutlined */ .o3f, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    style: {
      color: 'white',
      fontWeight: 'bold'
    }
  }, "Need help?"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CloseOutlined */ .r$3, null),
    onClick: function onClick(e) {
      e.stopPropagation();
      smartSuggestion.onDismiss();
    },
    style: {
      color: 'white',
      padding: 0
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    style: {
      color: 'white'
    }
  }, smartSuggestion.content), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    style: {
      color: 'rgba(255,255,255,0.8)',
      fontSize: '12px'
    }
  }, "Click to start tutorial"))), showPanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(HelpPanel, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BookOutlined */ .E6Q, null), "Contextual Help"),
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      type: "text",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CloseOutlined */ .r$3, null),
      onClick: function onClick() {
        return setShowPanel(false);
      }
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: "Context-Aware Help",
    description: "Get help based on what you're currently doing in the App Builder.",
    type: "info",
    showIcon: true
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 5
  }, "Available Help Topics"), Object.values(HELP_CONTEXTS).map(function (context) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, {
      key: context.id,
      size: "small",
      style: {
        marginBottom: 8
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
      direction: "vertical",
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, context.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      type: "secondary"
    }, context.content), context.relatedTutorials.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
      wrap: true
    }, context.relatedTutorials.map(function (tutorialId) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tag */ .vw, {
        key: tutorialId,
        color: "blue",
        style: {
          cursor: 'pointer'
        },
        onClick: function onClick() {
          return startTutorial(tutorialId);
        }
      }, tutorialId.replace('_', ' '));
    }))));
  }))));
};

// Hook for components to trigger contextual help
var useContextualHelp = function useContextualHelp() {
  var showHelp = useCallback(function (context, element) {
    var event = new CustomEvent('showContextualHelp', {
      detail: {
        context: context,
        element: element
      }
    });
    window.dispatchEvent(event);
  }, []);
  return {
    showHelp: showHelp
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContextualHelp);

/***/ })

}]);