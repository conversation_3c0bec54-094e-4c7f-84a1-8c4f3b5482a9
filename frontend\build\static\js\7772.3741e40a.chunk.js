"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[7772],{

/***/ 87772:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);




function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



var Title = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Paragraph;
var ServiceWorkerTest = function ServiceWorkerTest() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({
      supported: false,
      registered: false,
      active: false,
      installing: false,
      waiting: false,
      error: null
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    swStatus = _useState2[0],
    setSwStatus = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({
      available: false,
      caches: [],
      totalSize: 0
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    cacheStatus = _useState4[0],
    setCacheStatus = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({
      supported: false,
      permission: 'default'
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    notificationStatus = _useState6[0],
    setNotificationStatus = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(navigator.onLine),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    offlineStatus = _useState8[0],
    setOfflineStatus = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState9, 2),
    testResults = _useState0[0],
    setTestResults = _useState0[1];
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    _checkServiceWorkerSupport();
    checkCacheAPI();
    checkNotificationSupport();

    // Listen for online/offline events
    var handleOnline = function handleOnline() {
      return setOfflineStatus(true);
    };
    var handleOffline = function handleOffline() {
      return setOfflineStatus(false);
    };
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    return function () {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  var _checkServiceWorkerSupport = /*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
      var registrations, registration, _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!('serviceWorker' in navigator)) {
              _context.next = 6;
              break;
            }
            _context.prev = 1;
            _context.next = 2;
            return navigator.serviceWorker.getRegistrations();
          case 2:
            registrations = _context.sent;
            _context.next = 3;
            return navigator.serviceWorker.getRegistration();
          case 3:
            registration = _context.sent;
            setSwStatus({
              supported: true,
              registered: registrations.length > 0,
              active: registration && registration.active !== null,
              installing: registration && registration.installing !== null,
              waiting: registration && registration.waiting !== null,
              error: null
            });

            // Listen for service worker updates
            if (registration) {
              registration.addEventListener('updatefound', function () {
                console.log('Service Worker update found');
                _checkServiceWorkerSupport(); // Refresh status
              });
            }
            _context.next = 5;
            break;
          case 4:
            _context.prev = 4;
            _t = _context["catch"](1);
            setSwStatus(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, {
                error: _t.message
              });
            });
          case 5:
            _context.next = 7;
            break;
          case 6:
            setSwStatus(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, {
                supported: false
              });
            });
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 4]]);
    }));
    return function checkServiceWorkerSupport() {
      return _ref.apply(this, arguments);
    };
  }();
  var checkCacheAPI = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3() {
      var cacheNames, totalSize, cacheDetails, _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            if (!('caches' in window)) {
              _context3.next = 5;
              break;
            }
            _context3.prev = 1;
            _context3.next = 2;
            return caches.keys();
          case 2:
            cacheNames = _context3.sent;
            totalSize = 0;
            _context3.next = 3;
            return Promise.all(cacheNames.map(/*#__PURE__*/function () {
              var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2(name) {
                var cache, keys;
                return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      _context2.next = 1;
                      return caches.open(name);
                    case 1:
                      cache = _context2.sent;
                      _context2.next = 2;
                      return cache.keys();
                    case 2:
                      keys = _context2.sent;
                      return _context2.abrupt("return", {
                        name: name,
                        itemCount: keys.length,
                        items: keys.slice(0, 5).map(function (req) {
                          return req.url;
                        }) // Show first 5 items
                      });
                    case 3:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x) {
                return _ref3.apply(this, arguments);
              };
            }()));
          case 3:
            cacheDetails = _context3.sent;
            setCacheStatus({
              available: true,
              caches: cacheDetails,
              totalSize: totalSize
            });
            _context3.next = 5;
            break;
          case 4:
            _context3.prev = 4;
            _t2 = _context3["catch"](1);
            console.error('Error checking cache:', _t2);
          case 5:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 4]]);
    }));
    return function checkCacheAPI() {
      return _ref2.apply(this, arguments);
    };
  }();
  var checkNotificationSupport = function checkNotificationSupport() {
    if ('Notification' in window) {
      setNotificationStatus({
        supported: true,
        permission: Notification.permission
      });
    }
  };
  var registerServiceWorker = /*#__PURE__*/function () {
    var _ref4 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee4() {
      var registration, _t3;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            _context4.prev = 0;
            _context4.next = 1;
            return navigator.serviceWorker.register('/service-worker.js');
          case 1:
            registration = _context4.sent;
            console.log('Service Worker registered:', registration);
            addTestResult('Service Worker Registration', 'success', 'Successfully registered service worker');
            _checkServiceWorkerSupport();
            _context4.next = 3;
            break;
          case 2:
            _context4.prev = 2;
            _t3 = _context4["catch"](0);
            console.error('Service Worker registration failed:', _t3);
            addTestResult('Service Worker Registration', 'error', "Registration failed: ".concat(_t3.message));
          case 3:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[0, 2]]);
    }));
    return function registerServiceWorker() {
      return _ref4.apply(this, arguments);
    };
  }();
  var unregisterServiceWorker = /*#__PURE__*/function () {
    var _ref5 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee5() {
      var registrations, _iterator, _step, registration, _t4, _t5;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            _context5.prev = 0;
            _context5.next = 1;
            return navigator.serviceWorker.getRegistrations();
          case 1:
            registrations = _context5.sent;
            _iterator = _createForOfIteratorHelper(registrations);
            _context5.prev = 2;
            _iterator.s();
          case 3:
            if ((_step = _iterator.n()).done) {
              _context5.next = 5;
              break;
            }
            registration = _step.value;
            _context5.next = 4;
            return registration.unregister();
          case 4:
            _context5.next = 3;
            break;
          case 5:
            _context5.next = 7;
            break;
          case 6:
            _context5.prev = 6;
            _t4 = _context5["catch"](2);
            _iterator.e(_t4);
          case 7:
            _context5.prev = 7;
            _iterator.f();
            return _context5.finish(7);
          case 8:
            addTestResult('Service Worker Unregistration', 'success', 'All service workers unregistered');
            _checkServiceWorkerSupport();
            _context5.next = 10;
            break;
          case 9:
            _context5.prev = 9;
            _t5 = _context5["catch"](0);
            addTestResult('Service Worker Unregistration', 'error', "Unregistration failed: ".concat(_t5.message));
          case 10:
          case "end":
            return _context5.stop();
        }
      }, _callee5, null, [[0, 9], [2, 6, 7, 8]]);
    }));
    return function unregisterServiceWorker() {
      return _ref5.apply(this, arguments);
    };
  }();
  var clearAllCaches = /*#__PURE__*/function () {
    var _ref6 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee6() {
      var cacheNames, _t6;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context6) {
        while (1) switch (_context6.prev = _context6.next) {
          case 0:
            _context6.prev = 0;
            _context6.next = 1;
            return caches.keys();
          case 1:
            cacheNames = _context6.sent;
            _context6.next = 2;
            return Promise.all(cacheNames.map(function (name) {
              return caches["delete"](name);
            }));
          case 2:
            addTestResult('Cache Clear', 'success', "Cleared ".concat(cacheNames.length, " caches"));
            checkCacheAPI();
            _context6.next = 4;
            break;
          case 3:
            _context6.prev = 3;
            _t6 = _context6["catch"](0);
            addTestResult('Cache Clear', 'error', "Failed to clear caches: ".concat(_t6.message));
          case 4:
          case "end":
            return _context6.stop();
        }
      }, _callee6, null, [[0, 3]]);
    }));
    return function clearAllCaches() {
      return _ref6.apply(this, arguments);
    };
  }();
  var testOfflineCapability = /*#__PURE__*/function () {
    var _ref7 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee7() {
      var response, _t7;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context7) {
        while (1) switch (_context7.prev = _context7.next) {
          case 0:
            _context7.prev = 0;
            _context7.next = 1;
            return fetch('/', {
              cache: 'only-if-cached',
              mode: 'same-origin'
            });
          case 1:
            response = _context7.sent;
            if (response.ok) {
              addTestResult('Offline Test', 'success', 'App is available offline');
            } else {
              addTestResult('Offline Test', 'warning', 'App may not work offline');
            }
            _context7.next = 3;
            break;
          case 2:
            _context7.prev = 2;
            _t7 = _context7["catch"](0);
            addTestResult('Offline Test', 'error', 'Offline capability test failed');
          case 3:
          case "end":
            return _context7.stop();
        }
      }, _callee7, null, [[0, 2]]);
    }));
    return function testOfflineCapability() {
      return _ref7.apply(this, arguments);
    };
  }();
  var requestNotificationPermission = /*#__PURE__*/function () {
    var _ref8 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee8() {
      var permission, _t8;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context8) {
        while (1) switch (_context8.prev = _context8.next) {
          case 0:
            _context8.prev = 0;
            _context8.next = 1;
            return Notification.requestPermission();
          case 1:
            permission = _context8.sent;
            setNotificationStatus(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, {
                permission: permission
              });
            });
            addTestResult('Notification Permission', 'success', "Permission: ".concat(permission));
            _context8.next = 3;
            break;
          case 2:
            _context8.prev = 2;
            _t8 = _context8["catch"](0);
            addTestResult('Notification Permission', 'error', "Failed: ".concat(_t8.message));
          case 3:
          case "end":
            return _context8.stop();
        }
      }, _callee8, null, [[0, 2]]);
    }));
    return function requestNotificationPermission() {
      return _ref8.apply(this, arguments);
    };
  }();
  var testPushNotification = function testPushNotification() {
    if (Notification.permission === 'granted') {
      new Notification('Test Notification', {
        body: 'This is a test notification from the App Builder',
        icon: '/logo192.png',
        badge: '/favicon.ico'
      });
      addTestResult('Push Notification', 'success', 'Test notification sent');
    } else {
      addTestResult('Push Notification', 'error', 'Notification permission not granted');
    }
  };
  var addTestResult = function addTestResult(test, status, message) {
    var result = {
      id: Date.now(),
      test: test,
      status: status,
      message: message,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(function (prev) {
      return [result].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.slice(0, 9)));
    }); // Keep last 10 results
  };
  var getStatusIcon = function getStatusIcon(status) {
    switch (status) {
      case 'success':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckCircleOutlined */ .hWy, {
          style: {
            color: '#52c41a'
          }
        });
      case 'error':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CloseCircleOutlined */ .bBN, {
          style: {
            color: '#ff4d4f'
          }
        });
      case 'warning':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SyncOutlined */ .OmY, {
          style: {
            color: '#faad14'
          }
        });
      default:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SyncOutlined */ .OmY, null);
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Title, {
    level: 2
  }, "Service Worker Testing Dashboard"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Test and monitor the service worker functionality, caching, and offline capabilities."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Alert */ .Fc, {
    message: "Connection Status: ".concat(offlineStatus ? 'Online' : 'Offline'),
    type: offlineStatus ? 'success' : 'warning',
    icon: offlineStatus ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .WifiOutlined */ ._bA, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CloudOutlined */ .gDm, null),
    style: {
      marginBottom: '24px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
      gap: '24px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: "Service Worker Status",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .SyncOutlined */ .OmY, {
      onClick: _checkServiceWorkerSupport
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    strong: true
  }, "Supported: "), swStatus.supported ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "green"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckCircleOutlined */ .hWy, null), " Yes") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "red"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CloseCircleOutlined */ .bBN, null), " No")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    strong: true
  }, "Registered: "), swStatus.registered ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "green"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckCircleOutlined */ .hWy, null), " Yes") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "orange"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CloseCircleOutlined */ .bBN, null), " No")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    strong: true
  }, "Active: "), swStatus.active ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "green"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckCircleOutlined */ .hWy, null), " Yes") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "orange"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CloseCircleOutlined */ .bBN, null), " No")), swStatus.error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Alert */ .Fc, {
    message: swStatus.error,
    type: "error",
    size: "small"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    type: "primary",
    onClick: registerServiceWorker,
    disabled: !swStatus.supported || swStatus.registered
  }, "Register SW"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    onClick: unregisterServiceWorker,
    disabled: !swStatus.supported || !swStatus.registered
  }, "Unregister SW")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: "Cache Status",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .ReloadOutlined */ .KF4, {
      onClick: checkCacheAPI
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    strong: true
  }, "Cache API Available: "), cacheStatus.available ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "green"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckCircleOutlined */ .hWy, null), " Yes") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "red"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CloseCircleOutlined */ .bBN, null), " No")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    strong: true
  }, "Active Caches: "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "blue"
  }, cacheStatus.caches.length)), cacheStatus.caches.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .List */ .B8, {
    size: "small",
    dataSource: cacheStatus.caches,
    renderItem: function renderItem(cache) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
        strong: true
      }, cache.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
        type: "secondary"
      }, cache.itemCount, " items")));
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .DeleteOutlined */ .SUY, null),
    onClick: clearAllCaches,
    disabled: cacheStatus.caches.length === 0
  }, "Clear All Caches"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    onClick: testOfflineCapability
  }, "Test Offline")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: "Notification Status"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    strong: true
  }, "Supported: "), notificationStatus.supported ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "green"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckCircleOutlined */ .hWy, null), " Yes") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: "red"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CloseCircleOutlined */ .bBN, null), " No")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    strong: true
  }, "Permission: "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
    color: notificationStatus.permission === 'granted' ? 'green' : 'orange'
  }, notificationStatus.permission)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .BellOutlined */ .Kbk, null),
    onClick: requestNotificationPermission,
    disabled: !notificationStatus.supported || notificationStatus.permission === 'granted'
  }, "Request Permission"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    onClick: testPushNotification,
    disabled: notificationStatus.permission !== 'granted'
  }, "Test Notification")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: "Test Results",
    style: {
      gridColumn: 'span 2'
    }
  }, testResults.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, {
    type: "secondary"
  }, "No tests run yet. Click the buttons above to start testing.") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .List */ .B8, {
    dataSource: testResults,
    renderItem: function renderItem(result) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .List */ .B8.Item.Meta, {
        avatar: getStatusIcon(result.status),
        title: result.test,
        description: "".concat(result.message, " - ").concat(result.timestamp)
      }));
    }
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ServiceWorkerTest);

/***/ })

}]);