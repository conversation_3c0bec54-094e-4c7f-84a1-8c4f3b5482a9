@import url(https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap);
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: #333;
  margin-top: 12px;
}

/* Animation for the spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.custom-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #2563EB;
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
:root {
  /* Primary colors */
  --color-primary-main: #2563EB;
  --color-primary-light: #DBEAFE;
  --color-primary-dark: #1E40AF;
  --color-primary-contrast: #FFFFFF;
  --primary-color: #2563EB;
  --primary-light: #DBEAFE;
  --primary-dark: #1E40AF;
  --primary-color-rgb: 37, 99, 235;

  /* Secondary colors */
  --color-secondary-main: #10B981;
  --color-secondary-light: #D1FAE5;
  --color-secondary-dark: #047857;
  --color-secondary-contrast: #FFFFFF;
  --secondary-color: #10B981;

  /* Accent colors */
  --color-accent-main: #8B5CF6;
  --color-accent-light: #EDE9FE;
  --color-accent-dark: #6D28D9;
  --color-accent-contrast: #FFFFFF;

  /* Neutral colors */
  --color-neutral-50: #F9FAFB;
  --color-neutral-100: #F3F4F6;
  --color-neutral-200: #E5E7EB;
  --color-neutral-300: #D1D5DB;
  --color-neutral-400: #9CA3AF;
  --color-neutral-500: #6B7280;
  --color-neutral-600: #4B5563;
  --color-neutral-700: #374151;
  --color-neutral-800: #1F2937;
  --color-neutral-900: #111827;

  /* Semantic colors */
  --color-success-main: #10B981;
  --color-success-light: #D1FAE5;
  --color-success-dark: #047857;
  --color-success-contrast: #FFFFFF;

  --color-warning-main: #F59E0B;
  --color-warning-light: #FEF3C7;
  --color-warning-dark: #B45309;
  --color-warning-contrast: #FFFFFF;

  --color-error-main: #EF4444;
  --color-error-light: #FEE2E2;
  --color-error-dark: #B91C1C;
  --color-error-contrast: #FFFFFF;

  --color-info-main: #3B82F6;
  --color-info-light: #DBEAFE;
  --color-info-dark: #1D4ED8;
  --color-info-contrast: #FFFFFF;

  /* Background colors */
  --color-background-default: #F9FAFB;
  --color-background-paper: #FFFFFF;
  --color-background-dark: #111827;
  --background-default: #F9FAFB;
  --background-paper: #FFFFFF;
  --background-secondary: #F9FAFB;

  /* Text colors */
  --color-text-primary: #111827;
  --color-text-secondary: #4B5563;
  --color-text-disabled: #9CA3AF;
  --color-text-hint: #6B7280;
  --color-text-white: #FFFFFF;

  /* Divider color */
  --color-divider: #E5E7EB;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  /* Font weights */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font sizes */
  --font-size-xs: 0.75rem;
  /* 12px */
  --font-size-sm: 0.875rem;
  /* 14px */
  --font-size-base: 1rem;
  /* 16px */
  --font-size-lg: 1.125rem;
  /* 18px */
  --font-size-xl: 1.25rem;
  /* 20px */
  --font-size-2xl: 1.5rem;
  /* 24px */
  --font-size-3xl: 1.875rem;
  /* 30px */
  --font-size-4xl: 2.25rem;
  /* 36px */
  --font-size-5xl: 3rem;
  /* 48px */

  /* Line heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Spacing */
  --spacing-0: 0;
  --spacing-1: 0.25rem;
  /* 4px */
  --spacing-2: 0.5rem;
  /* 8px */
  --spacing-3: 0.75rem;
  /* 12px */
  --spacing-4: 1rem;
  /* 16px */
  --spacing-5: 1.25rem;
  /* 20px */
  --spacing-6: 1.5rem;
  /* 24px */
  --spacing-8: 2rem;
  /* 32px */
  --spacing-10: 2.5rem;
  /* 40px */
  --spacing-12: 3rem;
  /* 48px */
  --spacing-16: 4rem;
  /* 64px */
  --spacing-20: 5rem;
  /* 80px */
  --spacing-24: 6rem;
  /* 96px */
  --spacing-32: 8rem;
  /* 128px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-none: none;

  /* Border radius */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;
  /* 2px */
  --border-radius-md: 0.375rem;
  /* 6px */
  --border-radius-lg: 0.5rem;
  /* 8px */
  --border-radius-xl: 0.75rem;
  /* 12px */
  --border-radius-2xl: 1rem;
  /* 16px */
  --border-radius-3xl: 1.5rem;
  /* 24px */
  --border-radius-full: 9999px;

  /* Transitions */
  --transition-default: all 0.2s ease-in-out;
  --transition-fast: all 0.1s ease-in-out;
  --transition-slow: all 0.3s ease-in-out;

  /* Z-index */
  --z-index-negative: -1;
  --z-index-0: 0;
  --z-index-10: 10;
  --z-index-20: 20;
  --z-index-30: 30;
  --z-index-40: 40;
  --z-index-50: 50;
  --z-index-auto: auto;
}
/* Import CSS variables */

/* Import Google Fonts */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--color-background-default);
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
  font-size: var(--font-size-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App container */
.App {
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Mobile-first approach */
@media (max-width: 768px) {
  .App {
    font-size: calc(14px + 0.5vmin);
    /* Slightly larger font on mobile */
  }
}

/* Header */
.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

@media (max-width: 768px) {
  .App-header {
    padding: var(--spacing-4);
  }

  .App-header h1 {
    font-size: 1.5rem;
  }

  .App-header .subtitle {
    font-size: 0.9rem;
  }
}

/* Main content */
main {
  flex: 1;
  padding: var(--spacing-6);
  background-color: var(--color-background-default);
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  overflow-x: hidden;
}

.App-main {
  flex: 1;
  padding: var(--spacing-6);
  background-color: var(--color-background-default);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Home components container */
.home-components-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  width: 100%;
}

@media (max-width: 768px) {
  .App-main {
    padding: var(--spacing-4);
  }

  /* Adjust grid layouts for mobile */
  .App-main .grid-container {
    grid-template-columns: 1fr !important;
  }

  /* Make tables responsive */
  .App-main table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Loading container */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  gap: var(--spacing-4);
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
}

.loading-spinner::after {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-neutral-200);
  border-top: 4px solid var(--color-primary-main);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Builder section */
.App-builder {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  height: 100%;
}

/* Preview area */
.App-preview {
  margin-top: 16px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  padding: 16px;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.App-preview-content {
  width: 100%;
  height: 100%;
}

/* Sidebar */
.App-sidebar {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  height: 100%;
}

/* Components section */
.App-components {
  margin-bottom: 24px;
}

/* Layouts section */
.App-layouts {
  margin-bottom: 24px;
}

/* AI section */
.App-ai {
  margin-bottom: 24px;
}

/* AI plugin */
.ai-plugin {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  margin-top: 8px;
}

.ai-plugin__input {
  margin-bottom: 16px;
}

.ai-plugin__input textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
}

.ai-plugin__error {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 4px;
}

.ai-plugin__button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
}

.ai-plugin__button:hover {
  background-color: #2980b9;
}

.ai-plugin__button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

/* Suggestions */
.App-suggestions {
  margin-top: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  background-color: #f9f9f9;
}

.App-suggestions ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.App-suggestions li {
  margin-bottom: 4px;
}

/* Footer */
.App-footer {
  background-color: var(--color-neutral-800);
  color: var(--color-text-white);
  padding: var(--spacing-4);
  text-align: center;
  box-shadow: var(--shadow-inner);
}

/* Loading state */
.App-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  font-size: 18px;
  color: #3498db;
}

/* Error state */
.App-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  font-size: 18px;
  color: #e74c3c;
}

/* Tutorial overlay */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-50);
}

.tutorial-content {
  background-color: var(--color-background-paper);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  max-width: 600px;
  width: 90%;
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

/* Lazy loading placeholders */
.loading-placeholder {
  background-color: var(--color-background-paper);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-4);
  margin: var(--spacing-2) 0;
  text-align: center;
  color: var(--color-text-secondary);
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-shadow: var(--shadow-sm);
  animation: pulse 1.5s infinite ease-in-out;
}

.interaction-placeholder {
  background-color: var(--color-background-paper);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-4);
  margin: var(--spacing-2) 0;
  text-align: center;
  color: var(--color-text-secondary);
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.interaction-placeholder:hover {
  background-color: var(--color-background-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

/* Accessibility improvements */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus {
  outline: 2px solid var(--color-primary-main);
  outline-offset: 2px;
}

/* App Navigation */
.App-nav {
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: var(--color-background-paper);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-sm);
}

.App-nav ul {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  justify-content: center;
  background-color: #f0f0f0;
}

.App-nav li {
  margin: 0 1rem;
  padding: 10px 20px;
}

.App-nav a {
  display: inline-block;
  padding: 0.5rem 1rem;
  color: var(--color-primary-main);
  text-decoration: none;
  font-weight: 500;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.App-nav a:hover {
  background-color: var(--color-background-hover);
}

.App-nav a:focus {
  outline: 2px solid var(--color-primary-main);
  outline-offset: 2px;
}

/* App Footer */
.App-footer {
  margin-top: 2rem;
  padding: 1rem;
  background-color: var(--color-background-paper);
  border-top: 1px solid var(--color-border);
  text-align: center;
}

/* Status Indicators */
.connection-status,
.api-status,
.backend-status,
.ws-server-status,
.mock-servers-status,
.theme-mode-status {
  position: fixed;
  padding: 5px 10px;
  font-size: 12px;
  z-index: 1000;
  color: white;
  border-bottom-left-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s ease;
}

/* WebSocket Connection Status */
.connection-status {
  top: 0;
  right: 0;
}

.connection-status.connected {
  background-color: #4caf50;
  /* Green */
}

.connection-status.connecting {
  background-color: #ff9800;
  /* Orange */
}

.connection-status.reconnecting {
  background-color: #ff9800;
  /* Orange */
  animation: pulse 1s infinite;
}

.connection-status.disconnected {
  background-color: #f44336;
  /* Red */
}

.connection-status.error {
  background-color: #f44336;
  /* Red */
}

.connection-status.initialized {
  background-color: #2196f3;
  /* Blue */
}

/* API Status */
.api-status {
  top: 25px;
  right: 0;
}

.api-status.available {
  background-color: #4caf50;
  /* Green */
}

.api-status.unavailable {
  background-color: #f44336;
  /* Red */
}

/* Backend Status */
.backend-status {
  top: 50px;
  right: 0;
}

.backend-status.available {
  background-color: #4caf50;
  /* Green */
}

.backend-status.unavailable {
  background-color: #f44336;
  /* Red */
}

/* WebSocket Server Status */
.ws-server-status {
  top: 75px;
  right: 0;
}

.ws-server-status.available {
  background-color: #4caf50;
  /* Green */
}

.ws-server-status.unavailable {
  background-color: #f44336;
  /* Red */
}

/* Mock Servers Status */
.mock-servers-status {
  top: 100px;
  right: 0;
  background-color: #722ed1;
  /* Purple */
}

/* Theme Mode Status */
.theme-mode-status {
  top: 125px;
  right: 0;
  transition: all 0.3s ease;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .App-nav ul {
    flex-direction: column;
    align-items: center;
  }

  .App-nav li {
    margin: 0.5rem 0;
  }

  .tutorial-content {
    padding: var(--spacing-4);
    width: 95%;
  }

  .loading-placeholder,
  .interaction-placeholder {
    padding: var(--spacing-3);
    min-height: 80px;
  }
}
/* stylelint-disable */
html,
body {
  width: 100%;
  height: 100%;
}
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
@-ms-viewport {
  width: device-width;
}
body {
  margin: 0;
}
[tabindex='-1']:focus {
  outline: none;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 500;
}
p {
  margin-top: 0;
  margin-bottom: 1em;
}
abbr[title],
abbr[data-original-title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline;
  text-decoration: underline dotted;
  border-bottom: 0;
  cursor: help;
}
address {
  margin-bottom: 1em;
  font-style: normal;
  line-height: inherit;
}
input[type='text'],
input[type='password'],
input[type='number'],
textarea {
  -webkit-appearance: none;
}
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1em;
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 500;
}
dd {
  margin-bottom: 0.5em;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1em;
}
dfn {
  font-style: italic;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
pre,
code,
kbd,
samp {
  font-size: 1em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}
pre {
  margin-top: 0;
  margin-bottom: 1em;
  overflow: auto;
}
figure {
  margin: 0 0 1em;
}
img {
  vertical-align: middle;
  border-style: none;
}
a,
area,
button,
[role='button'],
input:not([type='range']),
label,
select,
summary,
textarea {
  touch-action: manipulation;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75em;
  padding-bottom: 0.3em;
  text-align: left;
  caption-side: bottom;
}
input,
button,
select,
optgroup,
textarea {
  margin: 0;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html [type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type='radio'],
input[type='checkbox'] {
  box-sizing: border-box;
  padding: 0;
}
input[type='date'],
input[type='time'],
input[type='datetime-local'],
input[type='month'] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  margin: 0;
  padding: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  margin-bottom: 0.5em;
  padding: 0;
  color: inherit;
  font-size: 1.5em;
  line-height: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}
[type='search'] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type='search']::-webkit-search-cancel-button,
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
mark {
  padding: 0.2em;
  background-color: #feffe6;
}

