/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Helmet } from 'react-helmet';
import { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';
import ThemeProvider from './components/ThemeProvider';
import UserPreferencesProvider from './contexts/UserPreferencesContext';
import './App.css';

// Import only specific Ant Design components and styles
import { Typography } from 'antd/es/typography';
import { Button } from 'antd/es/button';
import { QuestionCircleOutlined } from '@ant-design/icons';
// Import only the CSS for components we actually use
import 'antd/es/typography/style/css';
import 'antd/es/button/style/css';

// Import components
import TestComponent from './TestComponent';
import Home from './components/Home';

// Import prefetching utility
import { initPrefetcher, cleanupPrefetcher } from './utils/prefetchLinks';

// Import Redux actions
import { wsConnect } from './redux/actions/websocket.actions';

// Import UI actions
import { setCurrentView } from './redux/reducers/uiReducer';

// Import additional components
import PWAPrompt from './components/PWAPrompt';
import ErrorBoundary from './components/ErrorBoundary';
import CriticalErrorBoundary from './components/CriticalErrorBoundary';
import LoadingState from './components/LoadingState'; // Used indirectly by lazyWithFallback
import { SkipLink, FocusHighlight, LiveRegion } from './components/AccessibilityHelpers';
import PerformanceMonitor from './components/performance/PerformanceMonitor';
import RenderTracker from './components/performance/RenderTracker';

// Import code splitting utilities
import { lazyWithFallback, lazyWithDelay, lazyOnInteraction } from './utils/codeSplitting';

// Lazy load pages for better performance with custom loading state
const AppBuilderEnhanced = lazyWithFallback(
  () => import('./pages/AppBuilderEnhanced'),
  {
    fallbackText: 'Loading App Builder...',
    fallbackDescription: 'Please wait while we load the application',
    size: 'large',
    fullPage: true
  }
);

// Import SimpleAppBuilder directly (no lazy loading)
import SimpleAppBuilder from './pages/SimpleAppBuilder';

// Lazy load WebSocket page
const WebSocketPage = lazyWithFallback(
  () => import('./pages/WebSocketPage'),
  {
    fallbackText: 'Loading WebSocket Manager...',
    fallbackDescription: 'Please wait while we load the WebSocket manager',
    size: 'large',
    fullPage: true
  }
);

// Lazy load non-critical components with delay
const SimpleWebSocketTestLazy = lazyWithDelay(
  () => import('./components/SimpleWebSocketTest'),
  {
    delay: 500,
    placeholder: <div className="loading-placeholder">Loading WebSocket Test...</div>
  }
);

// Lazy load components that are only needed on user interaction
const WebSocketHookExampleLazy = lazyOnInteraction(
  () => import('./components/WebSocketHookExample'),
  {
    placeholder: <div className="interaction-placeholder">Click to load WebSocket Hook Example</div>
  }
);

// Lazy load the Bootstrap Test page
const BootstrapTest = lazyWithFallback(
  () => import('./pages/BootstrapTest'),
  {
    fallbackText: 'Loading Bootstrap Test...',
    fallbackDescription: 'Please wait while we load the test suite',
    size: 'large',
    fullPage: true
  }
);

// Import plugins
import TutorialAIPlugin from './plugins/TutorialAIPlugin';

import { resourceHints } from './utils/resourceHintsManager';

const ComponentBuilder = lazyWithFallback(
  () => {
    // Preload required resources
    const resources = [
      { path: '/static/js/ComponentBuilder.chunk.js', as: 'script' },
      { path: '/static/css/ComponentBuilder.chunk.css', as: 'style' }
    ];

    Promise.all(
      resources.map(resource =>
        resourceHints.preload(resource.path, resource.as, {
          fetchPriority: 'low'
        })
      )
    ).catch(error => console.error('Preload error:', error));

    return import('./components/enhanced/ComponentBuilder');
  },
  {
    fallbackText: 'Loading Component Builder...',
    fallbackDescription: 'Please wait while we load the component builder',
    size: 'large',
    fullPage: false,
    timeout: 5000,
    retry: 2
  }
);

// Import tutorial components
import FirstTimeUserPrompt from './components/tutorial/FirstTimeUserPrompt';
import {
  TutorialProvider,
  TutorialRegistration,
  TutorialOverlay,
  ContextualHelp,
  TutorialTrigger,
  useTutorial
} from './components/tutorial';

// Create a separate component for the app content that uses tutorial hooks
function AppContent() {
  const { startTutorial } = useTutorial();
  const [showTutorial, setShowTutorial] = useState(true);

  useEffect(() => {
    const hasCompletedTutorial = localStorage.getItem('tutorialCompleted');
    if (hasCompletedTutorial) {
      setShowTutorial(false);
    }

    // Initialize UI state
    const dispatch = useDispatch();
    dispatch(setCurrentView('components'));
  }, []);

  const handleTutorialComplete = () => {
    localStorage.setItem('tutorialCompleted', 'true');
    setShowTutorial(false);
  };

  // Initialize WebSocket connection when the app loads
  useEffect(() => {
    // Initialize immediately instead of waiting
    initPrefetcher();
    const dispatch = useDispatch();
    dispatch(wsConnect());

    return () => {
      cleanupPrefetcher();
    };
  }, []);

  return (
    <>
      {/* Add a prominent tutorial button for new users */}
      <header className="App-header">
        <Typography.Title level={1} style={{ textAlign: 'center', color: 'white', margin: 0 }}>
          App Builder 201
        </Typography.Title>
        <Typography.Text style={{ textAlign: 'center', color: 'white', display: 'block' }}>
          Build your application with minimal setup
        </Typography.Text>
        <Button
          type="primary"
          icon={<QuestionCircleOutlined />}
          onClick={() => startTutorial('app_builder_intro')}
          className="tutorial-start-button"
        >
          Start Tutorial
        </Button>
      </header>

      <main id="main-content" className="App-main">
        {/* PWA installation prompt and offline indicator */}
        <PWAPrompt />

        {/* Navigation */}
        <nav className="App-nav">
          <ul>
            <li><Link to="/">Home</Link></li>
            <li><Link to="/app-builder">App Builder</Link></li>
            <li><Link to="/websocket">WebSocket Manager</Link></li>
            <li><Link to="/bootstrap-test">Bootstrap Test</Link></li>
          </ul>
        </nav>

        {/* Routes */}
        <Routes>
          <Route path="/bootstrap-test" element={
            <CriticalErrorBoundary componentName="BootstrapTest">
              <BootstrapTest />
            </CriticalErrorBoundary>
          } />
          <Route path="/websocket" element={
            <CriticalErrorBoundary componentName="WebSocketPage">
              <WebSocketPage />
            </CriticalErrorBoundary>
          } />
          <Route path="/" element={
            <CriticalErrorBoundary componentName="Home">
              <Home />
            </CriticalErrorBoundary>
          } />
          <Route path="/app-builder" element={
            <CriticalErrorBoundary componentName="AppBuilderEnhanced">
              <AppBuilderEnhanced />
            </CriticalErrorBoundary>
          } />
        </Routes>
      </main>

      <footer className="App-footer">
        <Typography.Text type="secondary" style={{ textAlign: 'center', display: 'block' }}>
          App Builder 201 - Build with ease
        </Typography.Text>
      </footer>

      {/* Performance Monitor */}
      <PerformanceMonitor />
    </>
  );
}

// Main App component
function App() {

  return (
    <UserPreferencesProvider>
      <ThemeProvider>
        <ErrorBoundary>
        <Router future={{ v7_startTransition: true }}>
        <Helmet>
          <link rel="preload" href="/static/js/bundle.js" as="script" />
          <link rel="preload" href="/static/css/main.css" as="style" />
          <link rel="preconnect" href="https://backend:8000" />
          <meta name="description" content="App Builder 201 - Build your application with minimal setup" />
          <meta name="theme-color" content="#2563EB" />
        </Helmet>
          <FocusHighlight>
            <LiveRegion id="app-announcements" ariaLive="polite">
              {/* Dynamic announcements will be inserted here */}
            </LiveRegion>
            <div className="App" role="application" aria-label="App Builder 201 Application">
              {/* Accessibility skip link */}
              <SkipLink targetId="main-content" />

              {/* Tutorial overlay */}
              <TutorialProvider userId="current-user">
                <AppContent />

                {/* Tutorial System Components */}
                <TutorialRegistration />
                <TutorialOverlay />
                <ContextualHelp />
                <TutorialTrigger />
                <FirstTimeUserPrompt />
              </TutorialProvider>
            </div>
          </FocusHighlight>
        </Router>
      </ErrorBoundary>
        </ThemeProvider>
      </UserPreferencesProvider>
  );
}

export default App;






