"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9180],{

/***/ 89180:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ demo_ResponsiveDemo)
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/prop-types/index.js
var prop_types = __webpack_require__(5556);
var prop_types_default = /*#__PURE__*/__webpack_require__.n(prop_types);
;// ./src/components/common/ResponsiveContainer.js



var _excluded = ["children", "className", "mobileClass", "tabletClass", "desktopClass", "style"],
  _excluded2 = ["children", "columns", "gap", "className"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



var useBreakpoint = es/* Grid */.xA.useBreakpoint;

/**
 * ResponsiveContainer component that applies responsive classes and provides
 * breakpoint information to child components
 */
var ResponsiveContainer = function ResponsiveContainer(_ref) {
  var children = _ref.children,
    _ref$className = _ref.className,
    className = _ref$className === void 0 ? '' : _ref$className,
    _ref$mobileClass = _ref.mobileClass,
    mobileClass = _ref$mobileClass === void 0 ? '' : _ref$mobileClass,
    _ref$tabletClass = _ref.tabletClass,
    tabletClass = _ref$tabletClass === void 0 ? '' : _ref$tabletClass,
    _ref$desktopClass = _ref.desktopClass,
    desktopClass = _ref$desktopClass === void 0 ? '' : _ref$desktopClass,
    _ref$style = _ref.style,
    style = _ref$style === void 0 ? {} : _ref$style,
    props = (0,objectWithoutProperties/* default */.A)(_ref, _excluded);
  var screens = useBreakpoint();

  // Determine current breakpoint
  var isMobile = !screens.md;
  var isTablet = screens.md && !screens.lg;
  var isDesktop = screens.lg;

  // Build responsive class names
  var getResponsiveClasses = function getResponsiveClasses() {
    var classes = ['responsive-container', className];
    if (isMobile && mobileClass) {
      classes.push(mobileClass);
    }
    if (isTablet && tabletClass) {
      classes.push(tabletClass);
    }
    if (isDesktop && desktopClass) {
      classes.push(desktopClass);
    }
    return classes.filter(Boolean).join(' ');
  };

  // Responsive style adjustments
  var getResponsiveStyles = function getResponsiveStyles() {
    var baseStyles = _objectSpread({
      width: '100%',
      maxWidth: '100%',
      overflowX: 'hidden'
    }, style);
    if (isMobile) {
      return _objectSpread(_objectSpread({}, baseStyles), {}, {
        padding: '8px',
        fontSize: '14px'
      });
    }
    if (isTablet) {
      return _objectSpread(_objectSpread({}, baseStyles), {}, {
        padding: '16px',
        fontSize: '15px'
      });
    }
    return _objectSpread(_objectSpread({}, baseStyles), {}, {
      padding: '24px',
      fontSize: '16px'
    });
  };
  return /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.A)({
    className: getResponsiveClasses(),
    style: getResponsiveStyles(),
    "data-breakpoint": isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop'
  }, props), typeof children === 'function' ? children({
    isMobile: isMobile,
    isTablet: isTablet,
    isDesktop: isDesktop,
    screens: screens
  }) : children);
};
ResponsiveContainer.propTypes = {
  children: prop_types_default().oneOfType([(prop_types_default()).node, (prop_types_default()).func]).isRequired,
  className: (prop_types_default()).string,
  mobileClass: (prop_types_default()).string,
  tabletClass: (prop_types_default()).string,
  desktopClass: (prop_types_default()).string,
  style: (prop_types_default()).object
};
/* harmony default export */ const common_ResponsiveContainer = (ResponsiveContainer);

/**
 * Hook for responsive breakpoint detection
 */
var useResponsive = function useResponsive() {
  var screens = useBreakpoint();
  return {
    isMobile: !screens.md,
    isTablet: screens.md && !screens.lg,
    isDesktop: screens.lg,
    screens: screens
  };
};

/**
 * Higher-order component for responsive behavior
 */
var withResponsive = function withResponsive(WrappedComponent) {
  var ResponsiveComponent = function ResponsiveComponent(props) {
    var responsive = useResponsive();
    return /*#__PURE__*/React.createElement(WrappedComponent, _extends({}, props, {
      responsive: responsive
    }));
  };
  ResponsiveComponent.displayName = "withResponsive(".concat(WrappedComponent.displayName || WrappedComponent.name, ")");
  return ResponsiveComponent;
};

/**
 * Responsive Grid component
 */
var ResponsiveGrid = function ResponsiveGrid(_ref2) {
  var children = _ref2.children,
    _ref2$columns = _ref2.columns,
    columns = _ref2$columns === void 0 ? {
      mobile: 1,
      tablet: 2,
      desktop: 3
    } : _ref2$columns,
    _ref2$gap = _ref2.gap,
    gap = _ref2$gap === void 0 ? {
      mobile: 12,
      tablet: 16,
      desktop: 24
    } : _ref2$gap,
    _ref2$className = _ref2.className,
    className = _ref2$className === void 0 ? '' : _ref2$className,
    props = (0,objectWithoutProperties/* default */.A)(_ref2, _excluded2);
  var _useResponsive = useResponsive(),
    isMobile = _useResponsive.isMobile,
    isTablet = _useResponsive.isTablet,
    isDesktop = _useResponsive.isDesktop;
  var getGridColumns = function getGridColumns() {
    if (isMobile) return columns.mobile;
    if (isTablet) return columns.tablet;
    return columns.desktop;
  };
  var getGridGap = function getGridGap() {
    if (isMobile) return gap.mobile;
    if (isTablet) return gap.tablet;
    return gap.desktop;
  };
  var gridStyle = {
    display: 'grid',
    gridTemplateColumns: "repeat(".concat(getGridColumns(), ", 1fr)"),
    gap: "".concat(getGridGap(), "px"),
    width: '100%'
  };
  return /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.A)({
    className: "responsive-grid ".concat(className),
    style: gridStyle
  }, props), children);
};
ResponsiveGrid.propTypes = {
  children: (prop_types_default()).node.isRequired,
  columns: prop_types_default().shape({
    mobile: (prop_types_default()).number,
    tablet: (prop_types_default()).number,
    desktop: (prop_types_default()).number
  }),
  gap: prop_types_default().shape({
    mobile: (prop_types_default()).number,
    tablet: (prop_types_default()).number,
    desktop: (prop_types_default()).number
  }),
  className: (prop_types_default()).string
};
;// ./src/components/demo/ResponsiveDemo.js




var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text;

/**
 * Demo component to showcase responsive design improvements
 */
var ResponsiveDemo = function ResponsiveDemo() {
  var _useResponsive = useResponsive(),
    isMobile = _useResponsive.isMobile,
    isTablet = _useResponsive.isTablet,
    isDesktop = _useResponsive.isDesktop,
    screens = _useResponsive.screens;

  // Sample data for demonstration
  var sampleData = [{
    key: '1',
    name: 'Component A',
    type: 'Button',
    status: 'Active'
  }, {
    key: '2',
    name: 'Component B',
    type: 'Input',
    status: 'Inactive'
  }, {
    key: '3',
    name: 'Component C',
    type: 'Card',
    status: 'Active'
  }, {
    key: '4',
    name: 'Component D',
    type: 'Table',
    status: 'Active'
  }];
  var columns = [{
    title: 'Name',
    dataIndex: 'name',
    key: 'name'
  }, {
    title: 'Type',
    dataIndex: 'type',
    key: 'type'
  }, {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: function render(status) {
      return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
        color: status === 'Active' ? 'green' : 'red'
      }, status);
    }
  }];
  var getCurrentBreakpointIcon = function getCurrentBreakpointIcon() {
    if (isMobile) return /*#__PURE__*/react.createElement(icons_es/* MobileOutlined */.jHj, {
      style: {
        color: '#1890ff'
      }
    });
    if (isTablet) return /*#__PURE__*/react.createElement(icons_es/* TabletOutlined */.pLH, {
      style: {
        color: '#52c41a'
      }
    });
    return /*#__PURE__*/react.createElement(icons_es/* DesktopOutlined */.zlw, {
      style: {
        color: '#722ed1'
      }
    });
  };
  var getCurrentBreakpointText = function getCurrentBreakpointText() {
    if (isMobile) return 'Mobile';
    if (isTablet) return 'Tablet';
    return 'Desktop';
  };
  return /*#__PURE__*/react.createElement(common_ResponsiveContainer, {
    className: "responsive-demo"
  }, /*#__PURE__*/react.createElement("div", {
    className: "dashboard-container"
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    className: "dashboard-card",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react.createElement("div", {
    className: "card-header"
  }, /*#__PURE__*/react.createElement(Title, {
    level: 2
  }, /*#__PURE__*/react.createElement(icons_es/* DashboardOutlined */.zpd, null), " Responsive Design Demo"), /*#__PURE__*/react.createElement(es/* Space */.$x, null, getCurrentBreakpointIcon(), /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Current: ", getCurrentBreakpointText())))), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    className: "dashboard-card",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react.createElement("div", {
    className: "card-header"
  }, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Breakpoint Information")), /*#__PURE__*/react.createElement("div", {
    className: "card-content"
  }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    sm: 8
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    align: "center"
  }, /*#__PURE__*/react.createElement(icons_es/* MobileOutlined */.jHj, {
    style: {
      fontSize: 24,
      color: isMobile ? '#1890ff' : '#d9d9d9'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    strong: isMobile
  }, "Mobile"), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "\u2264 768px")))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    sm: 8
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    align: "center"
  }, /*#__PURE__*/react.createElement(icons_es/* TabletOutlined */.pLH, {
    style: {
      fontSize: 24,
      color: isTablet ? '#52c41a' : '#d9d9d9'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    strong: isTablet
  }, "Tablet"), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "769px - 1024px")))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    xs: 24,
    sm: 8
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    align: "center"
  }, /*#__PURE__*/react.createElement(icons_es/* DesktopOutlined */.zlw, {
    style: {
      fontSize: 24,
      color: isDesktop ? '#722ed1' : '#d9d9d9'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    strong: isDesktop
  }, "Desktop"), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "\u2265 1025px"))))))), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    className: "dashboard-card",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react.createElement("div", {
    className: "card-header"
  }, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Responsive Grid Layout")), /*#__PURE__*/react.createElement("div", {
    className: "card-content"
  }, /*#__PURE__*/react.createElement(ResponsiveGrid, {
    columns: {
      mobile: 1,
      tablet: 2,
      desktop: 3
    },
    gap: {
      mobile: 8,
      tablet: 12,
      desktop: 16
    }
  }, [1, 2, 3, 4, 5, 6].map(function (num) {
    return /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      key: num,
      size: "small",
      className: "grid-item"
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
      direction: "vertical",
      align: "center"
    }, /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, {
      style: {
        fontSize: 20
      }
    }), /*#__PURE__*/react.createElement(Text, null, "Item ", num)));
  })))), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    className: "dashboard-card",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react.createElement("div", {
    className: "card-header"
  }, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Data Visualization")), /*#__PURE__*/react.createElement("div", {
    className: "card-content"
  }, /*#__PURE__*/react.createElement("div", {
    className: "data-visualization"
  }, /*#__PURE__*/react.createElement("div", {
    className: "chart-container",
    style: {
      height: isMobile ? 200 : isTablet ? 250 : 300,
      backgroundColor: '#f5f5f5',
      border: '1px dashed #d9d9d9',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 4
    }
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    align: "center"
  }, /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, {
    style: {
      fontSize: 48,
      color: '#d9d9d9'
    }
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Chart Placeholder"), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Height: ", isMobile ? '200px' : isTablet ? '250px' : '300px')))))), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    className: "dashboard-card",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react.createElement("div", {
    className: "card-header"
  }, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Responsive Table")), /*#__PURE__*/react.createElement("div", {
    className: "card-content"
  }, /*#__PURE__*/react.createElement(es/* Table */.XI, {
    dataSource: sampleData,
    columns: columns,
    pagination: false,
    scroll: {
      x: 400
    },
    size: isMobile ? 'small' : 'middle'
  }))), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    className: "dashboard-card"
  }, /*#__PURE__*/react.createElement("div", {
    className: "card-header"
  }, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Responsive Button Group")), /*#__PURE__*/react.createElement("div", {
    className: "card-content"
  }, /*#__PURE__*/react.createElement("div", {
    className: "button-group"
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary"
  }, "Primary Action"), /*#__PURE__*/react.createElement(es/* Button */.$n, null, "Secondary Action"), /*#__PURE__*/react.createElement(es/* Button */.$n, null, "Tertiary Action"))))));
};
/* harmony default export */ const demo_ResponsiveDemo = (ResponsiveDemo);

/***/ })

}]);