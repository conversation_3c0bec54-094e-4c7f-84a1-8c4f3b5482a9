"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4227],{

/***/ 983:
/***/ ((module) => {



module.exports = rego
rego.displayName = 'rego'
rego.aliases = []
function rego(Prism) {
  // https://www.openpolicyagent.org/docs/latest/policy-reference/
  Prism.languages.rego = {
    comment: /#.*/,
    property: {
      pattern:
        /(^|[^\\.])(?:"(?:\\.|[^\\"\r\n])*"|`[^`]*`|\b[a-z_]\w*\b)(?=\s*:(?!=))/i,
      lookbehind: true,
      greedy: true
    },
    string: {
      pattern: /(^|[^\\])"(?:\\.|[^\\"\r\n])*"|`[^`]*`/,
      lookbehind: true,
      greedy: true
    },
    keyword:
      /\b(?:as|default|else|import|not|null|package|set(?=\s*\()|some|with)\b/,
    boolean: /\b(?:false|true)\b/,
    function: {
      pattern: /\b[a-z_]\w*\b(?:\s*\.\s*\b[a-z_]\w*\b)*(?=\s*\()/i,
      inside: {
        namespace: /\b\w+\b(?=\s*\.)/,
        punctuation: /\./
      }
    },
    number: /-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,
    operator: /[-+*/%|&]|[<>:=]=?|!=|\b_\b/,
    punctuation: /[,;.\[\]{}()]/
  }
}


/***/ }),

/***/ 7068:
/***/ ((module) => {



module.exports = robotframework
robotframework.displayName = 'robotframework'
robotframework.aliases = []
function robotframework(Prism) {
  ;(function (Prism) {
    var comment = {
      pattern: /(^[ \t]*| {2}|\t)#.*/m,
      lookbehind: true,
      greedy: true
    }
    var variable = {
      pattern: /((?:^|[^\\])(?:\\{2})*)[$@&%]\{(?:[^{}\r\n]|\{[^{}\r\n]*\})*\}/,
      lookbehind: true,
      inside: {
        punctuation: /^[$@&%]\{|\}$/
      }
    }
    function createSection(name, inside) {
      var extendecInside = {}
      extendecInside['section-header'] = {
        pattern: /^ ?\*{3}.+?\*{3}/,
        alias: 'keyword'
      } // copy inside tokens
      for (var token in inside) {
        extendecInside[token] = inside[token]
      }
      extendecInside['tag'] = {
        pattern: /([\r\n](?: {2}|\t)[ \t]*)\[[-\w]+\]/,
        lookbehind: true,
        inside: {
          punctuation: /\[|\]/
        }
      }
      extendecInside['variable'] = variable
      extendecInside['comment'] = comment
      return {
        pattern: RegExp(
          /^ ?\*{3}[ \t]*<name>[ \t]*\*{3}(?:.|[\r\n](?!\*{3}))*/.source.replace(
            /<name>/g,
            function () {
              return name
            }
          ),
          'im'
        ),
        alias: 'section',
        inside: extendecInside
      }
    }
    var docTag = {
      pattern:
        /(\[Documentation\](?: {2}|\t)[ \t]*)(?![ \t]|#)(?:.|(?:\r\n?|\n)[ \t]*\.{3})+/,
      lookbehind: true,
      alias: 'string'
    }
    var testNameLike = {
      pattern: /([\r\n] ?)(?!#)(?:\S(?:[ \t]\S)*)+/,
      lookbehind: true,
      alias: 'function',
      inside: {
        variable: variable
      }
    }
    var testPropertyLike = {
      pattern: /([\r\n](?: {2}|\t)[ \t]*)(?!\[|\.{3}|#)(?:\S(?:[ \t]\S)*)+/,
      lookbehind: true,
      inside: {
        variable: variable
      }
    }
    Prism.languages['robotframework'] = {
      settings: createSection('Settings', {
        documentation: {
          pattern:
            /([\r\n] ?Documentation(?: {2}|\t)[ \t]*)(?![ \t]|#)(?:.|(?:\r\n?|\n)[ \t]*\.{3})+/,
          lookbehind: true,
          alias: 'string'
        },
        property: {
          pattern: /([\r\n] ?)(?!\.{3}|#)(?:\S(?:[ \t]\S)*)+/,
          lookbehind: true
        }
      }),
      variables: createSection('Variables'),
      'test-cases': createSection('Test Cases', {
        'test-name': testNameLike,
        documentation: docTag,
        property: testPropertyLike
      }),
      keywords: createSection('Keywords', {
        'keyword-name': testNameLike,
        documentation: docTag,
        property: testPropertyLike
      }),
      tasks: createSection('Tasks', {
        'task-name': testNameLike,
        documentation: docTag,
        property: testPropertyLike
      }),
      comment: comment
    }
    Prism.languages.robot = Prism.languages['robotframework']
  })(Prism)
}


/***/ }),

/***/ 16354:
/***/ ((module) => {



module.exports = sass
sass.displayName = 'sass'
sass.aliases = []
function sass(Prism) {
  ;(function (Prism) {
    Prism.languages.sass = Prism.languages.extend('css', {
      // Sass comments don't need to be closed, only indented
      comment: {
        pattern: /^([ \t]*)\/[\/*].*(?:(?:\r?\n|\r)\1[ \t].+)*/m,
        lookbehind: true,
        greedy: true
      }
    })
    Prism.languages.insertBefore('sass', 'atrule', {
      // We want to consume the whole line
      'atrule-line': {
        // Includes support for = and + shortcuts
        pattern: /^(?:[ \t]*)[@+=].+/m,
        greedy: true,
        inside: {
          atrule: /(?:@[\w-]+|[+=])/
        }
      }
    })
    delete Prism.languages.sass.atrule
    var variable = /\$[-\w]+|#\{\$[-\w]+\}/
    var operator = [
      /[+*\/%]|[=!]=|<=?|>=?|\b(?:and|not|or)\b/,
      {
        pattern: /(\s)-(?=\s)/,
        lookbehind: true
      }
    ]
    Prism.languages.insertBefore('sass', 'property', {
      // We want to consume the whole line
      'variable-line': {
        pattern: /^[ \t]*\$.+/m,
        greedy: true,
        inside: {
          punctuation: /:/,
          variable: variable,
          operator: operator
        }
      },
      // We want to consume the whole line
      'property-line': {
        pattern: /^[ \t]*(?:[^:\s]+ *:.*|:[^:\s].*)/m,
        greedy: true,
        inside: {
          property: [
            /[^:\s]+(?=\s*:)/,
            {
              pattern: /(:)[^:\s]+/,
              lookbehind: true
            }
          ],
          punctuation: /:/,
          variable: variable,
          operator: operator,
          important: Prism.languages.sass.important
        }
      }
    })
    delete Prism.languages.sass.property
    delete Prism.languages.sass.important // Now that whole lines for other patterns are consumed,
    // what's left should be selectors
    Prism.languages.insertBefore('sass', 'punctuation', {
      selector: {
        pattern:
          /^([ \t]*)\S(?:,[^,\r\n]+|[^,\r\n]*)(?:,[^,\r\n]+)*(?:,(?:\r?\n|\r)\1[ \t]+\S(?:,[^,\r\n]+|[^,\r\n]*)(?:,[^,\r\n]+)*)*/m,
        lookbehind: true,
        greedy: true
      }
    })
  })(Prism)
}


/***/ }),

/***/ 23588:
/***/ ((module) => {



module.exports = renpy
renpy.displayName = 'renpy'
renpy.aliases = ['rpy']
function renpy(Prism) {
  Prism.languages.renpy = {
    comment: {
      pattern: /(^|[^\\])#.+/,
      lookbehind: true
    },
    string: {
      pattern:
        /("""|''')[\s\S]+?\1|("|')(?:\\.|(?!\2)[^\\])*\2|(?:^#?(?:(?:[0-9a-fA-F]){3}|[0-9a-fA-F]{6})$)/m,
      greedy: true
    },
    function: /\b[a-z_]\w*(?=\()/i,
    property:
      /\b(?:Update|UpdateVersion|action|activate_sound|adv_nvl_transition|after_load_transition|align|alpha|alt|anchor|antialias|area|auto|background|bar_invert|bar_resizing|bar_vertical|black_color|bold|bottom_bar|bottom_gutter|bottom_margin|bottom_padding|box_reverse|box_wrap|can_update|caret|child|color|crop|default_afm_enable|default_afm_time|default_fullscreen|default_text_cps|developer|directory_name|drag_handle|drag_joined|drag_name|drag_raise|draggable|dragged|drop_shadow|drop_shadow_color|droppable|dropped|easein|easeout|edgescroll|end_game_transition|end_splash_transition|enter_replay_transition|enter_sound|enter_transition|enter_yesno_transition|executable_name|exit_replay_transition|exit_sound|exit_transition|exit_yesno_transition|fadein|fadeout|first_indent|first_spacing|fit_first|focus|focus_mask|font|foreground|game_main_transition|get_installed_packages|google_play_key|google_play_salt|ground|has_music|has_sound|has_voice|height|help|hinting|hover|hover_background|hover_color|hover_sound|hovered|hyperlink_functions|idle|idle_color|image_style|include_update|insensitive|insensitive_background|insensitive_color|inside|intra_transition|italic|justify|kerning|keyboard_focus|language|layer_clipping|layers|layout|left_bar|left_gutter|left_margin|left_padding|length|line_leading|line_overlap_split|line_spacing|linear|main_game_transition|main_menu_music|maximum|min_width|minimum|minwidth|modal|mouse|mousewheel|name|narrator_menu|newline_indent|nvl_adv_transition|offset|order_reverse|outlines|overlay_functions|pos|position|prefix|radius|range|rest_indent|right_bar|right_gutter|right_margin|right_padding|rotate|rotate_pad|ruby_style|sample_sound|save_directory|say_attribute_transition|screen_height|screen_width|scrollbars|selected_hover|selected_hover_color|selected_idle|selected_idle_color|selected_insensitive|show_side_image|show_two_window|side_spacing|side_xpos|side_ypos|size|size_group|slow_cps|slow_cps_multiplier|spacing|strikethrough|subpixel|text_align|text_style|text_xpos|text_y_fudge|text_ypos|thumb|thumb_offset|thumb_shadow|thumbnail_height|thumbnail_width|time|top_bar|top_gutter|top_margin|top_padding|translations|underline|unscrollable|update|value|version|version_name|version_tuple|vertical|width|window_hide_transition|window_icon|window_left_padding|window_show_transition|window_title|windows_icon|xadjustment|xalign|xanchor|xanchoraround|xaround|xcenter|xfill|xinitial|xmargin|xmaximum|xminimum|xoffset|xofsset|xpadding|xpos|xsize|xzoom|yadjustment|yalign|yanchor|yanchoraround|yaround|ycenter|yfill|yinitial|ymargin|ymaximum|yminimum|yoffset|ypadding|ypos|ysize|ysizexysize|yzoom|zoom|zorder)\b/,
    tag: /\b(?:bar|block|button|buttoscreenn|drag|draggroup|fixed|frame|grid|[hv]box|hotbar|hotspot|image|imagebutton|imagemap|input|key|label|menu|mm_menu_frame|mousearea|nvl|parallel|screen|self|side|tag|text|textbutton|timer|vbar|viewport|window)\b|\$/,
    keyword:
      /\b(?:None|add|adjustment|alignaround|allow|angle|animation|around|as|assert|behind|box_layout|break|build|cache|call|center|changed|child_size|choice|circles|class|clear|clicked|clipping|clockwise|config|contains|continue|corner1|corner2|counterclockwise|def|default|define|del|delay|disabled|disabled_text|dissolve|elif|else|event|except|exclude|exec|expression|fade|finally|for|from|function|global|gm_root|has|hide|id|if|import|in|init|is|jump|knot|lambda|left|less_rounded|mm_root|movie|music|null|on|onlayer|pass|pause|persistent|play|print|python|queue|raise|random|renpy|repeat|return|right|rounded_window|scene|scope|set|show|slow|slow_abortable|slow_done|sound|stop|store|style|style_group|substitute|suffix|theme|transform|transform_anchor|transpose|try|ui|unhovered|updater|use|voice|while|widget|widget_hover|widget_selected|widget_text|yield)\b/,
    boolean: /\b(?:[Ff]alse|[Tt]rue)\b/,
    number:
      /(?:\b(?:0[bo])?(?:(?:\d|0x[\da-f])[\da-f]*(?:\.\d*)?)|\B\.\d+)(?:e[+-]?\d+)?j?/i,
    operator:
      /[-+%=]=?|!=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]|\b(?:and|at|not|or|with)\b/,
    punctuation: /[{}[\];(),.:]/
  }
  Prism.languages.rpy = Prism.languages.renpy
}


/***/ }),

/***/ 29813:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


var refractorBash = __webpack_require__(43622)
module.exports = shellSession
shellSession.displayName = 'shellSession'
shellSession.aliases = []
function shellSession(Prism) {
  Prism.register(refractorBash)
  ;(function (Prism) {
    // CAREFUL!
    // The following patterns are concatenated, so the group referenced by a back reference is non-obvious!
    var strings = [
      // normal string
      /"(?:\\[\s\S]|\$\([^)]+\)|\$(?!\()|`[^`]+`|[^"\\`$])*"/.source,
      /'[^']*'/.source,
      /\$'(?:[^'\\]|\\[\s\S])*'/.source, // here doc
      // 2 capturing groups
      /<<-?\s*(["']?)(\w+)\1\s[\s\S]*?[\r\n]\2/.source
    ].join('|')
    Prism.languages['shell-session'] = {
      command: {
        pattern: RegExp(
          // user info
          /^/.source +
            '(?:' + // <user> ":" ( <path> )?
            (/[^\s@:$#%*!/\\]+@[^\r\n@:$#%*!/\\]+(?::[^\0-\x1F$#%*?"<>:;|]+)?/
              .source +
              '|' + // <path>
              // Since the path pattern is quite general, we will require it to start with a special character to
              // prevent false positives.
              /[/~.][^\0-\x1F$#%*?"<>@:;|]*/.source) +
            ')?' + // shell symbol
            /[$#%](?=\s)/.source + // bash command
            /(?:[^\\\r\n \t'"<$]|[ \t](?:(?!#)|#.*$)|\\(?:[^\r]|\r\n?)|\$(?!')|<(?!<)|<<str>>)+/.source.replace(
              /<<str>>/g,
              function () {
                return strings
              }
            ),
          'm'
        ),
        greedy: true,
        inside: {
          info: {
            // foo@bar:~/files$ exit
            // foo@bar$ exit
            // ~/files$ exit
            pattern: /^[^#$%]+/,
            alias: 'punctuation',
            inside: {
              user: /^[^\s@:$#%*!/\\]+@[^\r\n@:$#%*!/\\]+/,
              punctuation: /:/,
              path: /[\s\S]+/
            }
          },
          bash: {
            pattern: /(^[$#%]\s*)\S[\s\S]*/,
            lookbehind: true,
            alias: 'language-bash',
            inside: Prism.languages.bash
          },
          'shell-symbol': {
            pattern: /^[$#%]/,
            alias: 'important'
          }
        }
      },
      output: /.(?:.*(?:[\r\n]|.$))*/
    }
    Prism.languages['sh-session'] = Prism.languages['shellsession'] =
      Prism.languages['shell-session']
  })(Prism)
}


/***/ }),

/***/ 30230:
/***/ ((module) => {



module.exports = solutionFile
solutionFile.displayName = 'solutionFile'
solutionFile.aliases = []
function solutionFile(Prism) {
  ;(function (Prism) {
    var guid = {
      // https://en.wikipedia.org/wiki/Universally_unique_identifier#Format
      pattern: /\{[\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12}\}/i,
      alias: 'constant',
      inside: {
        punctuation: /[{}]/
      }
    }
    Prism.languages['solution-file'] = {
      comment: {
        pattern: /#.*/,
        greedy: true
      },
      string: {
        pattern: /"[^"\r\n]*"|'[^'\r\n]*'/,
        greedy: true,
        inside: {
          guid: guid
        }
      },
      object: {
        // Foo
        //   Bar("abs") = 9
        //   EndBar
        //   Prop = TRUE
        // EndFoo
        pattern:
          /^([ \t]*)(?:([A-Z]\w*)\b(?=.*(?:\r\n?|\n)(?:\1[ \t].*(?:\r\n?|\n))*\1End\2(?=[ \t]*$))|End[A-Z]\w*(?=[ \t]*$))/m,
        lookbehind: true,
        greedy: true,
        alias: 'keyword'
      },
      property: {
        pattern: /^([ \t]*)(?!\s)[^\r\n"#=()]*[^\s"#=()](?=\s*=)/m,
        lookbehind: true,
        inside: {
          guid: guid
        }
      },
      guid: guid,
      number: /\b\d+(?:\.\d+)*\b/,
      boolean: /\b(?:FALSE|TRUE)\b/,
      operator: /=/,
      punctuation: /[(),]/
    }
    Prism.languages['sln'] = Prism.languages['solution-file']
  })(Prism)
}


/***/ }),

/***/ 41318:
/***/ ((module) => {



module.exports = rust
rust.displayName = 'rust'
rust.aliases = []
function rust(Prism) {
  ;(function (Prism) {
    var multilineComment = /\/\*(?:[^*/]|\*(?!\/)|\/(?!\*)|<self>)*\*\//.source
    for (var i = 0; i < 2; i++) {
      // support 4 levels of nested comments
      multilineComment = multilineComment.replace(/<self>/g, function () {
        return multilineComment
      })
    }
    multilineComment = multilineComment.replace(/<self>/g, function () {
      return /[^\s\S]/.source
    })
    Prism.languages.rust = {
      comment: [
        {
          pattern: RegExp(/(^|[^\\])/.source + multilineComment),
          lookbehind: true,
          greedy: true
        },
        {
          pattern: /(^|[^\\:])\/\/.*/,
          lookbehind: true,
          greedy: true
        }
      ],
      string: {
        pattern: /b?"(?:\\[\s\S]|[^\\"])*"|b?r(#*)"(?:[^"]|"(?!\1))*"\1/,
        greedy: true
      },
      char: {
        pattern:
          /b?'(?:\\(?:x[0-7][\da-fA-F]|u\{(?:[\da-fA-F]_*){1,6}\}|.)|[^\\\r\n\t'])'/,
        greedy: true
      },
      attribute: {
        pattern: /#!?\[(?:[^\[\]"]|"(?:\\[\s\S]|[^\\"])*")*\]/,
        greedy: true,
        alias: 'attr-name',
        inside: {
          string: null // see below
        }
      },
      // Closure params should not be confused with bitwise OR |
      'closure-params': {
        pattern: /([=(,:]\s*|\bmove\s*)\|[^|]*\||\|[^|]*\|(?=\s*(?:\{|->))/,
        lookbehind: true,
        greedy: true,
        inside: {
          'closure-punctuation': {
            pattern: /^\||\|$/,
            alias: 'punctuation'
          },
          rest: null // see below
        }
      },
      'lifetime-annotation': {
        pattern: /'\w+/,
        alias: 'symbol'
      },
      'fragment-specifier': {
        pattern: /(\$\w+:)[a-z]+/,
        lookbehind: true,
        alias: 'punctuation'
      },
      variable: /\$\w+/,
      'function-definition': {
        pattern: /(\bfn\s+)\w+/,
        lookbehind: true,
        alias: 'function'
      },
      'type-definition': {
        pattern: /(\b(?:enum|struct|trait|type|union)\s+)\w+/,
        lookbehind: true,
        alias: 'class-name'
      },
      'module-declaration': [
        {
          pattern: /(\b(?:crate|mod)\s+)[a-z][a-z_\d]*/,
          lookbehind: true,
          alias: 'namespace'
        },
        {
          pattern:
            /(\b(?:crate|self|super)\s*)::\s*[a-z][a-z_\d]*\b(?:\s*::(?:\s*[a-z][a-z_\d]*\s*::)*)?/,
          lookbehind: true,
          alias: 'namespace',
          inside: {
            punctuation: /::/
          }
        }
      ],
      keyword: [
        // https://github.com/rust-lang/reference/blob/master/src/keywords.md
        /\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\b/, // primitives and str
        // https://doc.rust-lang.org/stable/rust-by-example/primitives.html
        /\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\b/
      ],
      // functions can technically start with an upper-case letter, but this will introduce a lot of false positives
      // and Rust's naming conventions recommend snake_case anyway.
      // https://doc.rust-lang.org/1.0.0/style/style/naming/README.html
      function: /\b[a-z_]\w*(?=\s*(?:::\s*<|\())/,
      macro: {
        pattern: /\b\w+!/,
        alias: 'property'
      },
      constant: /\b[A-Z_][A-Z_\d]+\b/,
      'class-name': /\b[A-Z]\w*\b/,
      namespace: {
        pattern: /(?:\b[a-z][a-z_\d]*\s*::\s*)*\b[a-z][a-z_\d]*\s*::(?!\s*<)/,
        inside: {
          punctuation: /::/
        }
      },
      // Hex, oct, bin, dec numbers with visual separators and type suffix
      number:
        /\b(?:0x[\dA-Fa-f](?:_?[\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\d(?:_?\d)*)?\.)?\d(?:_?\d)*(?:[Ee][+-]?\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\b/,
      boolean: /\b(?:false|true)\b/,
      punctuation: /->|\.\.=|\.{1,3}|::|[{}[\];(),:]/,
      operator: /[-+*\/%!^]=?|=[=>]?|&[&=]?|\|[|=]?|<<?=?|>>?=?|[@?]/
    }
    Prism.languages.rust['closure-params'].inside.rest = Prism.languages.rust
    Prism.languages.rust['attribute'].inside['string'] =
      Prism.languages.rust['string']
  })(Prism)
}


/***/ }),

/***/ 42770:
/***/ ((module) => {



module.exports = sml
sml.displayName = 'sml'
sml.aliases = ['smlnj']
function sml(Prism) {
  // https://smlfamily.github.io/sml97-defn.pdf
  // https://people.mpi-sws.org/~rossberg/sml.html
  ;(function (Prism) {
    var keywords =
      /\b(?:abstype|and|andalso|as|case|datatype|do|else|end|eqtype|exception|fn|fun|functor|handle|if|in|include|infix|infixr|let|local|nonfix|of|op|open|orelse|raise|rec|sharing|sig|signature|struct|structure|then|type|val|where|while|with|withtype)\b/i
    Prism.languages.sml = {
      // allow one level of nesting
      comment:
        /\(\*(?:[^*(]|\*(?!\))|\((?!\*)|\(\*(?:[^*(]|\*(?!\))|\((?!\*))*\*\))*\*\)/,
      string: {
        pattern: /#?"(?:[^"\\]|\\.)*"/,
        greedy: true
      },
      'class-name': [
        {
          // This is only an approximation since the real grammar is context-free
          //
          // Why the main loop so complex?
          // The main loop is approximately the same as /(?:\s*(?:[*,]|->)\s*<TERMINAL>)*/ which is, obviously, a lot
          // simpler. The difference is that if a comma is the last iteration of the loop, then the terminal must be
          // followed by a long identifier.
          pattern: RegExp(
            /((?:^|[^:]):\s*)<TERMINAL>(?:\s*(?:(?:\*|->)\s*<TERMINAL>|,\s*<TERMINAL>(?:(?=<NOT-LAST>)|(?!<NOT-LAST>)\s+<LONG-ID>)))*/.source
              .replace(/<NOT-LAST>/g, function () {
                return /\s*(?:[*,]|->)/.source
              })
              .replace(/<TERMINAL>/g, function () {
                return /(?:'[\w']*|<LONG-ID>|\((?:[^()]|\([^()]*\))*\)|\{(?:[^{}]|\{[^{}]*\})*\})(?:\s+<LONG-ID>)*/
                  .source
              })
              .replace(/<LONG-ID>/g, function () {
                return /(?!<KEYWORD>)[a-z\d_][\w'.]*/.source
              })
              .replace(/<KEYWORD>/g, function () {
                return keywords.source
              }),
            'i'
          ),
          lookbehind: true,
          greedy: true,
          inside: null // see below
        },
        {
          pattern:
            /((?:^|[^\w'])(?:datatype|exception|functor|signature|structure|type)\s+)[a-z_][\w'.]*/i,
          lookbehind: true
        }
      ],
      function: {
        pattern: /((?:^|[^\w'])fun\s+)[a-z_][\w'.]*/i,
        lookbehind: true
      },
      keyword: keywords,
      variable: {
        pattern: /(^|[^\w'])'[\w']*/,
        lookbehind: true
      },
      number: /~?\b(?:\d+(?:\.\d+)?(?:e~?\d+)?|0x[\da-f]+)\b/i,
      word: {
        pattern: /\b0w(?:\d+|x[\da-f]+)\b/i,
        alias: 'constant'
      },
      boolean: /\b(?:false|true)\b/i,
      operator: /\.\.\.|:[>=:]|=>?|->|[<>]=?|[!+\-*/^#|@~]/,
      punctuation: /[(){}\[\].:,;]/
    }
    Prism.languages.sml['class-name'][0].inside = Prism.languages.sml
    Prism.languages.smlnj = Prism.languages.sml
  })(Prism)
}


/***/ }),

/***/ 43808:
/***/ ((module) => {



module.exports = r
r.displayName = 'r'
r.aliases = []
function r(Prism) {
  Prism.languages.r = {
    comment: /#.*/,
    string: {
      pattern: /(['"])(?:\\.|(?!\1)[^\\\r\n])*\1/,
      greedy: true
    },
    'percent-operator': {
      // Includes user-defined operators
      // and %%, %*%, %/%, %in%, %o%, %x%
      pattern: /%[^%\s]*%/,
      alias: 'operator'
    },
    boolean: /\b(?:FALSE|TRUE)\b/,
    ellipsis: /\.\.(?:\.|\d+)/,
    number: [
      /\b(?:Inf|NaN)\b/,
      /(?:\b0x[\dA-Fa-f]+(?:\.\d*)?|\b\d+(?:\.\d*)?|\B\.\d+)(?:[EePp][+-]?\d+)?[iL]?/
    ],
    keyword:
      /\b(?:NA|NA_character_|NA_complex_|NA_integer_|NA_real_|NULL|break|else|for|function|if|in|next|repeat|while)\b/,
    operator: /->?>?|<(?:=|<?-)?|[>=!]=?|::?|&&?|\|\|?|[+*\/^$@~]/,
    punctuation: /[(){}\[\],;]/
  }
}


/***/ }),

/***/ 44853:
/***/ ((module) => {



module.exports = rip
rip.displayName = 'rip'
rip.aliases = []
function rip(Prism) {
  Prism.languages.rip = {
    comment: {
      pattern: /#.*/,
      greedy: true
    },
    char: {
      pattern: /\B`[^\s`'",.:;#\/\\()<>\[\]{}]\b/,
      greedy: true
    },
    string: {
      pattern: /("|')(?:\\.|(?!\1)[^\\\r\n])*\1/,
      greedy: true
    },
    regex: {
      pattern:
        /(^|[^/])\/(?!\/)(?:\[[^\n\r\]]*\]|\\.|[^/\\\r\n\[])+\/(?=\s*(?:$|[\r\n,.;})]))/,
      lookbehind: true,
      greedy: true
    },
    keyword:
      /(?:=>|->)|\b(?:case|catch|class|else|exit|finally|if|raise|return|switch|try)\b/,
    builtin: /@|\bSystem\b/,
    boolean: /\b(?:false|true)\b/,
    date: /\b\d{4}-\d{2}-\d{2}\b/,
    time: /\b\d{2}:\d{2}:\d{2}\b/,
    datetime: /\b\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\b/,
    symbol: /:[^\d\s`'",.:;#\/\\()<>\[\]{}][^\s`'",.:;#\/\\()<>\[\]{}]*/,
    number: /[+-]?\b(?:\d+\.\d+|\d+)\b/,
    punctuation: /(?:\.{2,3})|[`,.:;=\/\\()<>\[\]{}]/,
    reference: /[^\d\s`'",.:;#\/\\()<>\[\]{}][^\s`'",.:;#\/\\()<>\[\]{}]*/
  }
}


/***/ }),

/***/ 45087:
/***/ ((module) => {



module.exports = scheme
scheme.displayName = 'scheme'
scheme.aliases = []
function scheme(Prism) {
  ;(function (Prism) {
    Prism.languages.scheme = {
      // this supports "normal" single-line comments:
      //   ; comment
      // and (potentially nested) multiline comments:
      //   #| comment #| nested |# still comment |#
      // (only 1 level of nesting is supported)
      comment:
        /;.*|#;\s*(?:\((?:[^()]|\([^()]*\))*\)|\[(?:[^\[\]]|\[[^\[\]]*\])*\])|#\|(?:[^#|]|#(?!\|)|\|(?!#)|#\|(?:[^#|]|#(?!\|)|\|(?!#))*\|#)*\|#/,
      string: {
        pattern: /"(?:[^"\\]|\\.)*"/,
        greedy: true
      },
      symbol: {
        pattern: /'[^()\[\]#'\s]+/,
        greedy: true
      },
      char: {
        pattern:
          /#\\(?:[ux][a-fA-F\d]+\b|[-a-zA-Z]+\b|[\uD800-\uDBFF][\uDC00-\uDFFF]|\S)/,
        greedy: true
      },
      'lambda-parameter': [
        // https://www.cs.cmu.edu/Groups/AI/html/r4rs/r4rs_6.html#SEC30
        {
          pattern:
            /((?:^|[^'`#])[(\[]lambda\s+)(?:[^|()\[\]'\s]+|\|(?:[^\\|]|\\.)*\|)/,
          lookbehind: true
        },
        {
          pattern: /((?:^|[^'`#])[(\[]lambda\s+[(\[])[^()\[\]']+/,
          lookbehind: true
        }
      ],
      keyword: {
        pattern:
          /((?:^|[^'`#])[(\[])(?:begin|case(?:-lambda)?|cond(?:-expand)?|define(?:-library|-macro|-record-type|-syntax|-values)?|defmacro|delay(?:-force)?|do|else|except|export|guard|if|import|include(?:-ci|-library-declarations)?|lambda|let(?:rec)?(?:-syntax|-values|\*)?|let\*-values|only|parameterize|prefix|(?:quasi-?)?quote|rename|set!|syntax-(?:case|rules)|unless|unquote(?:-splicing)?|when)(?=[()\[\]\s]|$)/,
        lookbehind: true
      },
      builtin: {
        // all functions of the base library of R7RS plus some of built-ins of R5Rs
        pattern:
          /((?:^|[^'`#])[(\[])(?:abs|and|append|apply|assoc|ass[qv]|binary-port\?|boolean=?\?|bytevector(?:-append|-copy|-copy!|-length|-u8-ref|-u8-set!|\?)?|caar|cadr|call-with-(?:current-continuation|port|values)|call\/cc|car|cdar|cddr|cdr|ceiling|char(?:->integer|-ready\?|\?|<\?|<=\?|=\?|>\?|>=\?)|close-(?:input-port|output-port|port)|complex\?|cons|current-(?:error|input|output)-port|denominator|dynamic-wind|eof-object\??|eq\?|equal\?|eqv\?|error|error-object(?:-irritants|-message|\?)|eval|even\?|exact(?:-integer-sqrt|-integer\?|\?)?|expt|features|file-error\?|floor(?:-quotient|-remainder|\/)?|flush-output-port|for-each|gcd|get-output-(?:bytevector|string)|inexact\??|input-port(?:-open\?|\?)|integer(?:->char|\?)|lcm|length|list(?:->string|->vector|-copy|-ref|-set!|-tail|\?)?|make-(?:bytevector|list|parameter|string|vector)|map|max|member|memq|memv|min|modulo|negative\?|newline|not|null\?|number(?:->string|\?)|numerator|odd\?|open-(?:input|output)-(?:bytevector|string)|or|output-port(?:-open\?|\?)|pair\?|peek-char|peek-u8|port\?|positive\?|procedure\?|quotient|raise|raise-continuable|rational\?|rationalize|read-(?:bytevector|bytevector!|char|error\?|line|string|u8)|real\?|remainder|reverse|round|set-c[ad]r!|square|string(?:->list|->number|->symbol|->utf8|->vector|-append|-copy|-copy!|-fill!|-for-each|-length|-map|-ref|-set!|\?|<\?|<=\?|=\?|>\?|>=\?)?|substring|symbol(?:->string|\?|=\?)|syntax-error|textual-port\?|truncate(?:-quotient|-remainder|\/)?|u8-ready\?|utf8->string|values|vector(?:->list|->string|-append|-copy|-copy!|-fill!|-for-each|-length|-map|-ref|-set!|\?)?|with-exception-handler|write-(?:bytevector|char|string|u8)|zero\?)(?=[()\[\]\s]|$)/,
        lookbehind: true
      },
      operator: {
        pattern: /((?:^|[^'`#])[(\[])(?:[-+*%/]|[<>]=?|=>?)(?=[()\[\]\s]|$)/,
        lookbehind: true
      },
      number: {
        // The number pattern from [the R7RS spec](https://small.r7rs.org/attachment/r7rs.pdf).
        //
        // <number>      := <num 2>|<num 8>|<num 10>|<num 16>
        // <num R>       := <prefix R><complex R>
        // <complex R>   := <real R>(?:@<real R>|<imaginary R>)?|<imaginary R>
        // <imaginary R> := [+-](?:<ureal R>|(?:inf|nan)\.0)?i
        // <real R>      := [+-]?<ureal R>|[+-](?:inf|nan)\.0
        // <ureal R>     := <uint R>(?:\/<uint R>)?
        //                | <decimal R>
        //
        // <decimal 10>  := (?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?
        // <uint R>      := <digit R>+
        // <prefix R>    := <radix R>(?:#[ei])?|(?:#[ei])?<radix R>
        // <radix 2>     := #b
        // <radix 8>     := #o
        // <radix 10>    := (?:#d)?
        // <radix 16>    := #x
        // <digit 2>     := [01]
        // <digit 8>     := [0-7]
        // <digit 10>    := \d
        // <digit 16>    := [0-9a-f]
        //
        // The problem with this grammar is that the resulting regex is way to complex, so we simplify by grouping all
        // non-decimal bases together. This results in a decimal (dec) and combined binary, octal, and hexadecimal (box)
        // pattern:
        pattern: RegExp(
          SortedBNF({
            '<ureal dec>':
              /\d+(?:\/\d+)|(?:\d+(?:\.\d*)?|\.\d+)(?:[esfdl][+-]?\d+)?/.source,
            '<real dec>': /[+-]?<ureal dec>|[+-](?:inf|nan)\.0/.source,
            '<imaginary dec>': /[+-](?:<ureal dec>|(?:inf|nan)\.0)?i/.source,
            '<complex dec>':
              /<real dec>(?:@<real dec>|<imaginary dec>)?|<imaginary dec>/
                .source,
            '<num dec>': /(?:#d(?:#[ei])?|#[ei](?:#d)?)?<complex dec>/.source,
            '<ureal box>': /[0-9a-f]+(?:\/[0-9a-f]+)?/.source,
            '<real box>': /[+-]?<ureal box>|[+-](?:inf|nan)\.0/.source,
            '<imaginary box>': /[+-](?:<ureal box>|(?:inf|nan)\.0)?i/.source,
            '<complex box>':
              /<real box>(?:@<real box>|<imaginary box>)?|<imaginary box>/
                .source,
            '<num box>': /#[box](?:#[ei])?|(?:#[ei])?#[box]<complex box>/
              .source,
            '<number>': /(^|[()\[\]\s])(?:<num dec>|<num box>)(?=[()\[\]\s]|$)/
              .source
          }),
          'i'
        ),
        lookbehind: true
      },
      boolean: {
        pattern: /(^|[()\[\]\s])#(?:[ft]|false|true)(?=[()\[\]\s]|$)/,
        lookbehind: true
      },
      function: {
        pattern:
          /((?:^|[^'`#])[(\[])(?:[^|()\[\]'\s]+|\|(?:[^\\|]|\\.)*\|)(?=[()\[\]\s]|$)/,
        lookbehind: true
      },
      identifier: {
        pattern: /(^|[()\[\]\s])\|(?:[^\\|]|\\.)*\|(?=[()\[\]\s]|$)/,
        lookbehind: true,
        greedy: true
      },
      punctuation: /[()\[\]']/
    }
    /**
     * Given a topologically sorted BNF grammar, this will return the RegExp source of last rule of the grammar.
     *
     * @param {Record<string, string>} grammar
     * @returns {string}
     */
    function SortedBNF(grammar) {
      for (var key in grammar) {
        grammar[key] = grammar[key].replace(/<[\w\s]+>/g, function (key) {
          return '(?:' + grammar[key].trim() + ')'
        })
      } // return the last item
      return grammar[key]
    }
  })(Prism)
}


/***/ }),

/***/ 45223:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


var refractorJava = __webpack_require__(38072)
module.exports = scala
scala.displayName = 'scala'
scala.aliases = []
function scala(Prism) {
  Prism.register(refractorJava)
  Prism.languages.scala = Prism.languages.extend('java', {
    'triple-quoted-string': {
      pattern: /"""[\s\S]*?"""/,
      greedy: true,
      alias: 'string'
    },
    string: {
      pattern: /("|')(?:\\.|(?!\1)[^\\\r\n])*\1/,
      greedy: true
    },
    keyword:
      /<-|=>|\b(?:abstract|case|catch|class|def|do|else|extends|final|finally|for|forSome|if|implicit|import|lazy|match|new|null|object|override|package|private|protected|return|sealed|self|super|this|throw|trait|try|type|val|var|while|with|yield)\b/,
    number:
      /\b0x(?:[\da-f]*\.)?[\da-f]+|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e\d+)?[dfl]?/i,
    builtin:
      /\b(?:Any|AnyRef|AnyVal|Boolean|Byte|Char|Double|Float|Int|Long|Nothing|Short|String|Unit)\b/,
    symbol: /'[^\d\s\\]\w*/
  })
  Prism.languages.insertBefore('scala', 'triple-quoted-string', {
    'string-interpolation': {
      pattern:
        /\b[a-z]\w*(?:"""(?:[^$]|\$(?:[^{]|\{(?:[^{}]|\{[^{}]*\})*\}))*?"""|"(?:[^$"\r\n]|\$(?:[^{]|\{(?:[^{}]|\{[^{}]*\})*\}))*")/i,
      greedy: true,
      inside: {
        id: {
          pattern: /^\w+/,
          greedy: true,
          alias: 'function'
        },
        escape: {
          pattern: /\\\$"|\$[$"]/,
          greedy: true,
          alias: 'symbol'
        },
        interpolation: {
          pattern: /\$(?:\w+|\{(?:[^{}]|\{[^{}]*\})*\})/,
          greedy: true,
          inside: {
            punctuation: /^\$\{?|\}$/,
            expression: {
              pattern: /[\s\S]+/,
              inside: Prism.languages.scala
            }
          }
        },
        string: /[\s\S]+/
      }
    }
  })
  delete Prism.languages.scala['class-name']
  delete Prism.languages.scala['function']
}


/***/ }),

/***/ 45986:
/***/ ((module) => {



module.exports = regex
regex.displayName = 'regex'
regex.aliases = []
function regex(Prism) {
  ;(function (Prism) {
    var specialEscape = {
      pattern: /\\[\\(){}[\]^$+*?|.]/,
      alias: 'escape'
    }
    var escape =
      /\\(?:x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]+\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/
    var charSet = {
      pattern: /\.|\\[wsd]|\\p\{[^{}]+\}/i,
      alias: 'class-name'
    }
    var charSetWithoutDot = {
      pattern: /\\[wsd]|\\p\{[^{}]+\}/i,
      alias: 'class-name'
    }
    var rangeChar = '(?:[^\\\\-]|' + escape.source + ')'
    var range = RegExp(rangeChar + '-' + rangeChar) // the name of a capturing group
    var groupName = {
      pattern: /(<|')[^<>']+(?=[>']$)/,
      lookbehind: true,
      alias: 'variable'
    }
    Prism.languages.regex = {
      'char-class': {
        pattern: /((?:^|[^\\])(?:\\\\)*)\[(?:[^\\\]]|\\[\s\S])*\]/,
        lookbehind: true,
        inside: {
          'char-class-negation': {
            pattern: /(^\[)\^/,
            lookbehind: true,
            alias: 'operator'
          },
          'char-class-punctuation': {
            pattern: /^\[|\]$/,
            alias: 'punctuation'
          },
          range: {
            pattern: range,
            inside: {
              escape: escape,
              'range-punctuation': {
                pattern: /-/,
                alias: 'operator'
              }
            }
          },
          'special-escape': specialEscape,
          'char-set': charSetWithoutDot,
          escape: escape
        }
      },
      'special-escape': specialEscape,
      'char-set': charSet,
      backreference: [
        {
          // a backreference which is not an octal escape
          pattern: /\\(?![123][0-7]{2})[1-9]/,
          alias: 'keyword'
        },
        {
          pattern: /\\k<[^<>']+>/,
          alias: 'keyword',
          inside: {
            'group-name': groupName
          }
        }
      ],
      anchor: {
        pattern: /[$^]|\\[ABbGZz]/,
        alias: 'function'
      },
      escape: escape,
      group: [
        {
          // https://docs.oracle.com/javase/10/docs/api/java/util/regex/Pattern.html
          // https://docs.microsoft.com/en-us/dotnet/standard/base-types/regular-expression-language-quick-reference?view=netframework-4.7.2#grouping-constructs
          // (), (?<name>), (?'name'), (?>), (?:), (?=), (?!), (?<=), (?<!), (?is-m), (?i-m:)
          pattern:
            /\((?:\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,
          alias: 'punctuation',
          inside: {
            'group-name': groupName
          }
        },
        {
          pattern: /\)/,
          alias: 'punctuation'
        }
      ],
      quantifier: {
        pattern: /(?:[+*?]|\{\d+(?:,\d*)?\})[?+]?/,
        alias: 'number'
      },
      alternation: {
        pattern: /\|/,
        alias: 'keyword'
      }
    }
  })(Prism)
}


/***/ }),

/***/ 51315:
/***/ ((module) => {



module.exports = smalltalk
smalltalk.displayName = 'smalltalk'
smalltalk.aliases = []
function smalltalk(Prism) {
  Prism.languages.smalltalk = {
    comment: {
      pattern: /"(?:""|[^"])*"/,
      greedy: true
    },
    char: {
      pattern: /\$./,
      greedy: true
    },
    string: {
      pattern: /'(?:''|[^'])*'/,
      greedy: true
    },
    symbol: /#[\da-z]+|#(?:-|([+\/\\*~<>=@%|&?!])\1?)|#(?=\()/i,
    'block-arguments': {
      pattern: /(\[\s*):[^\[|]*\|/,
      lookbehind: true,
      inside: {
        variable: /:[\da-z]+/i,
        punctuation: /\|/
      }
    },
    'temporary-variables': {
      pattern: /\|[^|]+\|/,
      inside: {
        variable: /[\da-z]+/i,
        punctuation: /\|/
      }
    },
    keyword: /\b(?:new|nil|self|super)\b/,
    boolean: /\b(?:false|true)\b/,
    number: [
      /\d+r-?[\dA-Z]+(?:\.[\dA-Z]+)?(?:e-?\d+)?/,
      /\b\d+(?:\.\d+)?(?:e-?\d+)?/
    ],
    operator: /[<=]=?|:=|~[~=]|\/\/?|\\\\|>[>=]?|[!^+\-*&|,@]/,
    punctuation: /[.;:?\[\](){}]/
  }
}


/***/ }),

/***/ 52746:
/***/ ((module) => {



module.exports = roboconf
roboconf.displayName = 'roboconf'
roboconf.aliases = []
function roboconf(Prism) {
  Prism.languages.roboconf = {
    comment: /#.*/,
    keyword: {
      pattern:
        /(^|\s)(?:(?:external|import)\b|(?:facet|instance of)(?=[ \t]+[\w-]+[ \t]*\{))/,
      lookbehind: true
    },
    component: {
      pattern: /[\w-]+(?=[ \t]*\{)/,
      alias: 'variable'
    },
    property: /[\w.-]+(?=[ \t]*:)/,
    value: {
      pattern: /(=[ \t]*(?![ \t]))[^,;]+/,
      lookbehind: true,
      alias: 'attr-value'
    },
    optional: {
      pattern: /\(optional\)/,
      alias: 'builtin'
    },
    wildcard: {
      pattern: /(\.)\*/,
      lookbehind: true,
      alias: 'operator'
    },
    punctuation: /[{},.;:=]/
  }
}


/***/ }),

/***/ 54264:
/***/ ((module) => {



module.exports = ruby
ruby.displayName = 'ruby'
ruby.aliases = ['rb']
function ruby(Prism) {
  /**
   * Original by Samuel Flores
   *
   * Adds the following new token classes:
   *     constant, builtin, variable, symbol, regex
   */
  ;(function (Prism) {
    Prism.languages.ruby = Prism.languages.extend('clike', {
      comment: {
        pattern: /#.*|^=begin\s[\s\S]*?^=end/m,
        greedy: true
      },
      'class-name': {
        pattern:
          /(\b(?:class|module)\s+|\bcatch\s+\()[\w.\\]+|\b[A-Z_]\w*(?=\s*\.\s*new\b)/,
        lookbehind: true,
        inside: {
          punctuation: /[.\\]/
        }
      },
      keyword:
        /\b(?:BEGIN|END|alias|and|begin|break|case|class|def|define_method|defined|do|each|else|elsif|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|private|protected|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\b/,
      operator:
        /\.{2,3}|&\.|===|<?=>|[!=]?~|(?:&&|\|\||<<|>>|\*\*|[+\-*/%<>!^&|=])=?|[?:]/,
      punctuation: /[(){}[\].,;]/
    })
    Prism.languages.insertBefore('ruby', 'operator', {
      'double-colon': {
        pattern: /::/,
        alias: 'punctuation'
      }
    })
    var interpolation = {
      pattern: /((?:^|[^\\])(?:\\{2})*)#\{(?:[^{}]|\{[^{}]*\})*\}/,
      lookbehind: true,
      inside: {
        content: {
          pattern: /^(#\{)[\s\S]+(?=\}$)/,
          lookbehind: true,
          inside: Prism.languages.ruby
        },
        delimiter: {
          pattern: /^#\{|\}$/,
          alias: 'punctuation'
        }
      }
    }
    delete Prism.languages.ruby.function
    var percentExpression =
      '(?:' +
      [
        /([^a-zA-Z0-9\s{(\[<=])(?:(?!\1)[^\\]|\\[\s\S])*\1/.source,
        /\((?:[^()\\]|\\[\s\S]|\((?:[^()\\]|\\[\s\S])*\))*\)/.source,
        /\{(?:[^{}\\]|\\[\s\S]|\{(?:[^{}\\]|\\[\s\S])*\})*\}/.source,
        /\[(?:[^\[\]\\]|\\[\s\S]|\[(?:[^\[\]\\]|\\[\s\S])*\])*\]/.source,
        /<(?:[^<>\\]|\\[\s\S]|<(?:[^<>\\]|\\[\s\S])*>)*>/.source
      ].join('|') +
      ')'
    var symbolName =
      /(?:"(?:\\.|[^"\\\r\n])*"|(?:\b[a-zA-Z_]\w*|[^\s\0-\x7F]+)[?!]?|\$.)/
        .source
    Prism.languages.insertBefore('ruby', 'keyword', {
      'regex-literal': [
        {
          pattern: RegExp(
            /%r/.source + percentExpression + /[egimnosux]{0,6}/.source
          ),
          greedy: true,
          inside: {
            interpolation: interpolation,
            regex: /[\s\S]+/
          }
        },
        {
          pattern:
            /(^|[^/])\/(?!\/)(?:\[[^\r\n\]]+\]|\\.|[^[/\\\r\n])+\/[egimnosux]{0,6}(?=\s*(?:$|[\r\n,.;})#]))/,
          lookbehind: true,
          greedy: true,
          inside: {
            interpolation: interpolation,
            regex: /[\s\S]+/
          }
        }
      ],
      variable: /[@$]+[a-zA-Z_]\w*(?:[?!]|\b)/,
      symbol: [
        {
          pattern: RegExp(/(^|[^:]):/.source + symbolName),
          lookbehind: true,
          greedy: true
        },
        {
          pattern: RegExp(
            /([\r\n{(,][ \t]*)/.source + symbolName + /(?=:(?!:))/.source
          ),
          lookbehind: true,
          greedy: true
        }
      ],
      'method-definition': {
        pattern: /(\bdef\s+)\w+(?:\s*\.\s*\w+)?/,
        lookbehind: true,
        inside: {
          function: /\b\w+$/,
          keyword: /^self\b/,
          'class-name': /^\w+/,
          punctuation: /\./
        }
      }
    })
    Prism.languages.insertBefore('ruby', 'string', {
      'string-literal': [
        {
          pattern: RegExp(/%[qQiIwWs]?/.source + percentExpression),
          greedy: true,
          inside: {
            interpolation: interpolation,
            string: /[\s\S]+/
          }
        },
        {
          pattern:
            /("|')(?:#\{[^}]+\}|#(?!\{)|\\(?:\r\n|[\s\S])|(?!\1)[^\\#\r\n])*\1/,
          greedy: true,
          inside: {
            interpolation: interpolation,
            string: /[\s\S]+/
          }
        },
        {
          pattern: /<<[-~]?([a-z_]\w*)[\r\n](?:.*[\r\n])*?[\t ]*\1/i,
          alias: 'heredoc-string',
          greedy: true,
          inside: {
            delimiter: {
              pattern: /^<<[-~]?[a-z_]\w*|\b[a-z_]\w*$/i,
              inside: {
                symbol: /\b\w+/,
                punctuation: /^<<[-~]?/
              }
            },
            interpolation: interpolation,
            string: /[\s\S]+/
          }
        },
        {
          pattern: /<<[-~]?'([a-z_]\w*)'[\r\n](?:.*[\r\n])*?[\t ]*\1/i,
          alias: 'heredoc-string',
          greedy: true,
          inside: {
            delimiter: {
              pattern: /^<<[-~]?'[a-z_]\w*'|\b[a-z_]\w*$/i,
              inside: {
                symbol: /\b\w+/,
                punctuation: /^<<[-~]?'|'$/
              }
            },
            string: /[\s\S]+/
          }
        }
      ],
      'command-literal': [
        {
          pattern: RegExp(/%x/.source + percentExpression),
          greedy: true,
          inside: {
            interpolation: interpolation,
            command: {
              pattern: /[\s\S]+/,
              alias: 'string'
            }
          }
        },
        {
          pattern: /`(?:#\{[^}]+\}|#(?!\{)|\\(?:\r\n|[\s\S])|[^\\`#\r\n])*`/,
          greedy: true,
          inside: {
            interpolation: interpolation,
            command: {
              pattern: /[\s\S]+/,
              alias: 'string'
            }
          }
        }
      ]
    })
    delete Prism.languages.ruby.string
    Prism.languages.insertBefore('ruby', 'number', {
      builtin:
        /\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Fixnum|Float|Hash|IO|Integer|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|Stat|String|Struct|Symbol|TMS|Thread|ThreadGroup|Time|TrueClass)\b/,
      constant: /\b[A-Z][A-Z0-9_]*(?:[?!]|\b)/
    })
    Prism.languages.rb = Prism.languages.ruby
  })(Prism)
}


/***/ }),

/***/ 56278:
/***/ ((module) => {



module.exports = rest
rest.displayName = 'rest'
rest.aliases = []
function rest(Prism) {
  Prism.languages.rest = {
    table: [
      {
        pattern:
          /(^[\t ]*)(?:\+[=-]+)+\+(?:\r?\n|\r)(?:\1[+|].+[+|](?:\r?\n|\r))+\1(?:\+[=-]+)+\+/m,
        lookbehind: true,
        inside: {
          punctuation: /\||(?:\+[=-]+)+\+/
        }
      },
      {
        pattern:
          /(^[\t ]*)=+ [ =]*=(?:(?:\r?\n|\r)\1.+)+(?:\r?\n|\r)\1=+ [ =]*=(?=(?:\r?\n|\r){2}|\s*$)/m,
        lookbehind: true,
        inside: {
          punctuation: /[=-]+/
        }
      }
    ],
    // Directive-like patterns
    'substitution-def': {
      pattern: /(^[\t ]*\.\. )\|(?:[^|\s](?:[^|]*[^|\s])?)\| [^:]+::/m,
      lookbehind: true,
      inside: {
        substitution: {
          pattern: /^\|(?:[^|\s]|[^|\s][^|]*[^|\s])\|/,
          alias: 'attr-value',
          inside: {
            punctuation: /^\||\|$/
          }
        },
        directive: {
          pattern: /( )(?! )[^:]+::/,
          lookbehind: true,
          alias: 'function',
          inside: {
            punctuation: /::$/
          }
        }
      }
    },
    'link-target': [
      {
        pattern: /(^[\t ]*\.\. )\[[^\]]+\]/m,
        lookbehind: true,
        alias: 'string',
        inside: {
          punctuation: /^\[|\]$/
        }
      },
      {
        pattern: /(^[\t ]*\.\. )_(?:`[^`]+`|(?:[^:\\]|\\.)+):/m,
        lookbehind: true,
        alias: 'string',
        inside: {
          punctuation: /^_|:$/
        }
      }
    ],
    directive: {
      pattern: /(^[\t ]*\.\. )[^:]+::/m,
      lookbehind: true,
      alias: 'function',
      inside: {
        punctuation: /::$/
      }
    },
    comment: {
      // The two alternatives try to prevent highlighting of blank comments
      pattern:
        /(^[\t ]*\.\.)(?:(?: .+)?(?:(?:\r?\n|\r).+)+| .+)(?=(?:\r?\n|\r){2}|$)/m,
      lookbehind: true
    },
    title: [
      // Overlined and underlined
      {
        pattern:
          /^(([!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~])\2+)(?:\r?\n|\r).+(?:\r?\n|\r)\1$/m,
        inside: {
          punctuation:
            /^[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~]+|[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~]+$/,
          important: /.+/
        }
      }, // Underlined only
      {
        pattern:
          /(^|(?:\r?\n|\r){2}).+(?:\r?\n|\r)([!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~])\2+(?=\r?\n|\r|$)/,
        lookbehind: true,
        inside: {
          punctuation: /[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~]+$/,
          important: /.+/
        }
      }
    ],
    hr: {
      pattern:
        /((?:\r?\n|\r){2})([!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~])\2{3,}(?=(?:\r?\n|\r){2})/,
      lookbehind: true,
      alias: 'punctuation'
    },
    field: {
      pattern: /(^[\t ]*):[^:\r\n]+:(?= )/m,
      lookbehind: true,
      alias: 'attr-name'
    },
    'command-line-option': {
      pattern:
        /(^[\t ]*)(?:[+-][a-z\d]|(?:--|\/)[a-z\d-]+)(?:[ =](?:[a-z][\w-]*|<[^<>]+>))?(?:, (?:[+-][a-z\d]|(?:--|\/)[a-z\d-]+)(?:[ =](?:[a-z][\w-]*|<[^<>]+>))?)*(?=(?:\r?\n|\r)? {2,}\S)/im,
      lookbehind: true,
      alias: 'symbol'
    },
    'literal-block': {
      pattern: /::(?:\r?\n|\r){2}([ \t]+)(?![ \t]).+(?:(?:\r?\n|\r)\1.+)*/,
      inside: {
        'literal-block-punctuation': {
          pattern: /^::/,
          alias: 'punctuation'
        }
      }
    },
    'quoted-literal-block': {
      pattern:
        /::(?:\r?\n|\r){2}([!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~]).*(?:(?:\r?\n|\r)\1.*)*/,
      inside: {
        'literal-block-punctuation': {
          pattern: /^(?:::|([!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~])\1*)/m,
          alias: 'punctuation'
        }
      }
    },
    'list-bullet': {
      pattern:
        /(^[\t ]*)(?:[*+\-•‣⁃]|\(?(?:\d+|[a-z]|[ivxdclm]+)\)|(?:\d+|[a-z]|[ivxdclm]+)\.)(?= )/im,
      lookbehind: true,
      alias: 'punctuation'
    },
    'doctest-block': {
      pattern: /(^[\t ]*)>>> .+(?:(?:\r?\n|\r).+)*/m,
      lookbehind: true,
      inside: {
        punctuation: /^>>>/
      }
    },
    inline: [
      {
        pattern:
          /(^|[\s\-:\/'"<(\[{])(?::[^:]+:`.*?`|`.*?`:[^:]+:|(\*\*?|``?|\|)(?!\s)(?:(?!\2).)*\S\2(?=[\s\-.,:;!?\\\/'")\]}]|$))/m,
        lookbehind: true,
        inside: {
          bold: {
            pattern: /(^\*\*).+(?=\*\*$)/,
            lookbehind: true
          },
          italic: {
            pattern: /(^\*).+(?=\*$)/,
            lookbehind: true
          },
          'inline-literal': {
            pattern: /(^``).+(?=``$)/,
            lookbehind: true,
            alias: 'symbol'
          },
          role: {
            pattern: /^:[^:]+:|:[^:]+:$/,
            alias: 'function',
            inside: {
              punctuation: /^:|:$/
            }
          },
          'interpreted-text': {
            pattern: /(^`).+(?=`$)/,
            lookbehind: true,
            alias: 'attr-value'
          },
          substitution: {
            pattern: /(^\|).+(?=\|$)/,
            lookbehind: true,
            alias: 'attr-value'
          },
          punctuation: /\*\*?|``?|\|/
        }
      }
    ],
    link: [
      {
        pattern: /\[[^\[\]]+\]_(?=[\s\-.,:;!?\\\/'")\]}]|$)/,
        alias: 'string',
        inside: {
          punctuation: /^\[|\]_$/
        }
      },
      {
        pattern:
          /(?:\b[a-z\d]+(?:[_.:+][a-z\d]+)*_?_|`[^`]+`_?_|_`[^`]+`)(?=[\s\-.,:;!?\\\/'")\]}]|$)/i,
        alias: 'string',
        inside: {
          punctuation: /^_?`|`$|`?_?_$/
        }
      }
    ],
    // Line block start,
    // quote attribution,
    // explicit markup start,
    // and anonymous hyperlink target shortcut (__)
    punctuation: {
      pattern: /(^[\t ]*)(?:\|(?= |$)|(?:---?|—|\.\.|__)(?= )|\.\.$)/m,
      lookbehind: true
    }
  }
}


/***/ }),

/***/ 65175:
/***/ ((module) => {



module.exports = sas
sas.displayName = 'sas'
sas.aliases = []
function sas(Prism) {
  ;(function (Prism) {
    var stringPattern = /(?:"(?:""|[^"])*"(?!")|'(?:''|[^'])*'(?!'))/.source
    var number = /\b(?:\d[\da-f]*x|\d+(?:\.\d+)?(?:e[+-]?\d+)?)\b/i
    var numericConstant = {
      pattern: RegExp(stringPattern + '[bx]'),
      alias: 'number'
    }
    var macroVariable = {
      pattern: /&[a-z_]\w*/i
    }
    var macroKeyword = {
      pattern:
        /((?:^|\s|=|\())%(?:ABORT|BY|CMS|COPY|DISPLAY|DO|ELSE|END|EVAL|GLOBAL|GO|GOTO|IF|INC|INCLUDE|INDEX|INPUT|KTRIM|LENGTH|LET|LIST|LOCAL|PUT|QKTRIM|QSCAN|QSUBSTR|QSYSFUNC|QUPCASE|RETURN|RUN|SCAN|SUBSTR|SUPERQ|SYMDEL|SYMEXIST|SYMGLOBL|SYMLOCAL|SYSCALL|SYSEVALF|SYSEXEC|SYSFUNC|SYSGET|SYSRPUT|THEN|TO|TSO|UNQUOTE|UNTIL|UPCASE|WHILE|WINDOW)\b/i,
      lookbehind: true,
      alias: 'keyword'
    }
    var step = {
      pattern: /(^|\s)(?:proc\s+\w+|data(?!=)|quit|run)\b/i,
      alias: 'keyword',
      lookbehind: true
    }
    var comment = [
      /\/\*[\s\S]*?\*\//,
      {
        pattern: /(^[ \t]*|;\s*)\*[^;]*;/m,
        lookbehind: true
      }
    ]
    var string = {
      pattern: RegExp(stringPattern),
      greedy: true
    }
    var punctuation = /[$%@.(){}\[\];,\\]/
    var func = {
      pattern: /%?\b\w+(?=\()/,
      alias: 'keyword'
    }
    var args = {
      function: func,
      'arg-value': {
        pattern: /(=\s*)[A-Z\.]+/i,
        lookbehind: true
      },
      operator: /=/,
      'macro-variable': macroVariable,
      arg: {
        pattern: /[A-Z]+/i,
        alias: 'keyword'
      },
      number: number,
      'numeric-constant': numericConstant,
      punctuation: punctuation,
      string: string
    }
    var format = {
      pattern: /\b(?:format|put)\b=?[\w'$.]+/i,
      inside: {
        keyword: /^(?:format|put)(?==)/i,
        equals: /=/,
        format: {
          pattern: /(?:\w|\$\d)+\.\d?/,
          alias: 'number'
        }
      }
    }
    var altformat = {
      pattern: /\b(?:format|put)\s+[\w']+(?:\s+[$.\w]+)+(?=;)/i,
      inside: {
        keyword: /^(?:format|put)/i,
        format: {
          pattern: /[\w$]+\.\d?/,
          alias: 'number'
        }
      }
    }
    var globalStatements = {
      pattern:
        /((?:^|\s)=?)(?:catname|checkpoint execute_always|dm|endsas|filename|footnote|%include|libname|%list|lock|missing|options|page|resetline|%run|sasfile|skip|sysecho|title\d?)\b/i,
      lookbehind: true,
      alias: 'keyword'
    }
    var submitStatement = {
      pattern: /(^|\s)(?:submit(?:\s+(?:load|norun|parseonly))?|endsubmit)\b/i,
      lookbehind: true,
      alias: 'keyword'
    }
    var actionSets =
      /aStore|accessControl|aggregation|audio|autotune|bayesianNetClassifier|bioMedImage|boolRule|builtins|cardinality|cdm|clustering|conditionalRandomFields|configuration|copula|countreg|dataDiscovery|dataPreprocess|dataSciencePilot|dataStep|decisionTree|deduplication|deepLearn|deepNeural|deepRnn|ds2|ecm|entityRes|espCluster|explainModel|factmac|fastKnn|fcmpact|fedSql|freqTab|gVarCluster|gam|gleam|graphSemiSupLearn|hiddenMarkovModel|hyperGroup|ica|image|iml|kernalPca|langModel|ldaTopic|loadStreams|mbc|mixed|mlTools|modelPublishing|network|neuralNet|nmf|nonParametricBayes|nonlinear|optNetwork|optimization|panel|pca|percentile|phreg|pls|qkb|qlim|quantreg|recommend|regression|reinforcementLearn|robustPca|ruleMining|sampling|sandwich|sccasl|search(?:Analytics)?|sentimentAnalysis|sequence|session(?:Prop)?|severity|simSystem|simple|smartData|sparkEmbeddedProcess|sparseML|spatialreg|spc|stabilityMonitoring|svDataDescription|svm|table|text(?:Filters|Frequency|Mining|Parse|Rule(?:Develop|Score)|Topic|Util)|timeData|transpose|tsInfo|tsReconcile|uniTimeSeries|varReduce/
        .source
    var casActions = {
      pattern: RegExp(
        /(^|\s)(?:action\s+)?(?:<act>)\.[a-z]+\b[^;]+/.source.replace(
          /<act>/g,
          function () {
            return actionSets
          }
        ),
        'i'
      ),
      lookbehind: true,
      inside: {
        keyword: RegExp(
          /(?:<act>)\.[a-z]+\b/.source.replace(/<act>/g, function () {
            return actionSets
          }),
          'i'
        ),
        action: {
          pattern: /(?:action)/i,
          alias: 'keyword'
        },
        comment: comment,
        function: func,
        'arg-value': args['arg-value'],
        operator: args.operator,
        argument: args.arg,
        number: number,
        'numeric-constant': numericConstant,
        punctuation: punctuation,
        string: string
      }
    }
    var keywords = {
      pattern:
        /((?:^|\s)=?)(?:after|analysis|and|array|barchart|barwidth|begingraph|by|call|cas|cbarline|cfill|class(?:lev)?|close|column|computed?|contains|continue|data(?==)|define|delete|describe|document|do\s+over|do|dol|drop|dul|else|end(?:comp|source)?|entryTitle|eval(?:uate)?|exec(?:ute)?|exit|file(?:name)?|fill(?:attrs)?|flist|fnc|function(?:list)?|global|goto|group(?:by)?|headline|headskip|histogram|if|infile|keep|keylabel|keyword|label|layout|leave|legendlabel|length|libname|loadactionset|merge|midpoints|_?null_|name|noobs|nowd|ods|options|or|otherwise|out(?:put)?|over(?:lay)?|plot|print|put|raise|ranexp|rannor|rbreak|retain|return|select|session|sessref|set|source|statgraph|sum|summarize|table|temp|terminate|then\s+do|then|title\d?|to|var|when|where|xaxisopts|y2axisopts|yaxisopts)\b/i,
      lookbehind: true
    }
    Prism.languages.sas = {
      datalines: {
        pattern: /^([ \t]*)(?:cards|(?:data)?lines);[\s\S]+?^[ \t]*;/im,
        lookbehind: true,
        alias: 'string',
        inside: {
          keyword: {
            pattern: /^(?:cards|(?:data)?lines)/i
          },
          punctuation: /;/
        }
      },
      'proc-sql': {
        pattern:
          /(^proc\s+(?:fed)?sql(?:\s+[\w|=]+)?;)[\s\S]+?(?=^(?:proc\s+\w+|data|quit|run);|(?![\s\S]))/im,
        lookbehind: true,
        inside: {
          sql: {
            pattern: RegExp(
              /^[ \t]*(?:select|alter\s+table|(?:create|describe|drop)\s+(?:index|table(?:\s+constraints)?|view)|create\s+unique\s+index|insert\s+into|update)(?:<str>|[^;"'])+;/.source.replace(
                /<str>/g,
                function () {
                  return stringPattern
                }
              ),
              'im'
            ),
            alias: 'language-sql',
            inside: Prism.languages.sql
          },
          'global-statements': globalStatements,
          'sql-statements': {
            pattern:
              /(^|\s)(?:disconnect\s+from|begin|commit|exec(?:ute)?|reset|rollback|validate)\b/i,
            lookbehind: true,
            alias: 'keyword'
          },
          number: number,
          'numeric-constant': numericConstant,
          punctuation: punctuation,
          string: string
        }
      },
      'proc-groovy': {
        pattern:
          /(^proc\s+groovy(?:\s+[\w|=]+)?;)[\s\S]+?(?=^(?:proc\s+\w+|data|quit|run);|(?![\s\S]))/im,
        lookbehind: true,
        inside: {
          comment: comment,
          groovy: {
            pattern: RegExp(
              /(^[ \t]*submit(?:\s+(?:load|norun|parseonly))?)(?:<str>|[^"'])+?(?=endsubmit;)/.source.replace(
                /<str>/g,
                function () {
                  return stringPattern
                }
              ),
              'im'
            ),
            lookbehind: true,
            alias: 'language-groovy',
            inside: Prism.languages.groovy
          },
          keyword: keywords,
          'submit-statement': submitStatement,
          'global-statements': globalStatements,
          number: number,
          'numeric-constant': numericConstant,
          punctuation: punctuation,
          string: string
        }
      },
      'proc-lua': {
        pattern:
          /(^proc\s+lua(?:\s+[\w|=]+)?;)[\s\S]+?(?=^(?:proc\s+\w+|data|quit|run);|(?![\s\S]))/im,
        lookbehind: true,
        inside: {
          comment: comment,
          lua: {
            pattern: RegExp(
              /(^[ \t]*submit(?:\s+(?:load|norun|parseonly))?)(?:<str>|[^"'])+?(?=endsubmit;)/.source.replace(
                /<str>/g,
                function () {
                  return stringPattern
                }
              ),
              'im'
            ),
            lookbehind: true,
            alias: 'language-lua',
            inside: Prism.languages.lua
          },
          keyword: keywords,
          'submit-statement': submitStatement,
          'global-statements': globalStatements,
          number: number,
          'numeric-constant': numericConstant,
          punctuation: punctuation,
          string: string
        }
      },
      'proc-cas': {
        pattern:
          /(^proc\s+cas(?:\s+[\w|=]+)?;)[\s\S]+?(?=^(?:proc\s+\w+|quit|data);|(?![\s\S]))/im,
        lookbehind: true,
        inside: {
          comment: comment,
          'statement-var': {
            pattern: /((?:^|\s)=?)saveresult\s[^;]+/im,
            lookbehind: true,
            inside: {
              statement: {
                pattern: /^saveresult\s+\S+/i,
                inside: {
                  keyword: /^(?:saveresult)/i
                }
              },
              rest: args
            }
          },
          'cas-actions': casActions,
          statement: {
            pattern:
              /((?:^|\s)=?)(?:default|(?:un)?set|on|output|upload)[^;]+/im,
            lookbehind: true,
            inside: args
          },
          step: step,
          keyword: keywords,
          function: func,
          format: format,
          altformat: altformat,
          'global-statements': globalStatements,
          number: number,
          'numeric-constant': numericConstant,
          punctuation: punctuation,
          string: string
        }
      },
      'proc-args': {
        pattern: RegExp(
          /(^proc\s+\w+\s+)(?!\s)(?:[^;"']|<str>)+;/.source.replace(
            /<str>/g,
            function () {
              return stringPattern
            }
          ),
          'im'
        ),
        lookbehind: true,
        inside: args
      },
      /*Special keywords within macros*/
      'macro-keyword': macroKeyword,
      'macro-variable': macroVariable,
      'macro-string-functions': {
        pattern:
          /((?:^|\s|=))%(?:BQUOTE|NRBQUOTE|NRQUOTE|NRSTR|QUOTE|STR)\(.*?(?:[^%]\))/i,
        lookbehind: true,
        inside: {
          function: {
            pattern: /%(?:BQUOTE|NRBQUOTE|NRQUOTE|NRSTR|QUOTE|STR)/i,
            alias: 'keyword'
          },
          'macro-keyword': macroKeyword,
          'macro-variable': macroVariable,
          'escaped-char': {
            pattern: /%['"()<>=¬^~;,#]/
          },
          punctuation: punctuation
        }
      },
      'macro-declaration': {
        pattern: /^%macro[^;]+(?=;)/im,
        inside: {
          keyword: /%macro/i
        }
      },
      'macro-end': {
        pattern: /^%mend[^;]+(?=;)/im,
        inside: {
          keyword: /%mend/i
        }
      },
      /*%_zscore(headcir, _lhc, _mhc, _shc, headcz, headcpct, _Fheadcz); */
      macro: {
        pattern: /%_\w+(?=\()/,
        alias: 'keyword'
      },
      input: {
        pattern: /\binput\s[-\w\s/*.$&]+;/i,
        inside: {
          input: {
            alias: 'keyword',
            pattern: /^input/i
          },
          comment: comment,
          number: number,
          'numeric-constant': numericConstant
        }
      },
      'options-args': {
        pattern: /(^options)[-'"|/\\<>*+=:()\w\s]*(?=;)/im,
        lookbehind: true,
        inside: args
      },
      'cas-actions': casActions,
      comment: comment,
      function: func,
      format: format,
      altformat: altformat,
      'numeric-constant': numericConstant,
      datetime: {
        // '1jan2013'd, '9:25:19pm't, '18jan2003:9:27:05am'dt
        pattern: RegExp(stringPattern + '(?:dt?|t)'),
        alias: 'number'
      },
      string: string,
      step: step,
      keyword: keywords,
      // In SAS Studio syntax highlighting, these operators are styled like keywords
      'operator-keyword': {
        pattern: /\b(?:eq|ge|gt|in|le|lt|ne|not)\b/i,
        alias: 'operator'
      },
      // Decimal (1.2e23), hexadecimal (0c1x)
      number: number,
      operator: /\*\*?|\|\|?|!!?|¦¦?|<[>=]?|>[<=]?|[-+\/=&]|[~¬^]=?/,
      punctuation: punctuation
    }
  })(Prism)
}


/***/ }),

/***/ 65868:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


var refractorMarkupTemplating = __webpack_require__(56876)
module.exports = smarty
smarty.displayName = 'smarty'
smarty.aliases = []
function smarty(Prism) {
  Prism.register(refractorMarkupTemplating)
  ;(function (Prism) {
    Prism.languages.smarty = {
      comment: {
        pattern: /^\{\*[\s\S]*?\*\}/,
        greedy: true
      },
      'embedded-php': {
        pattern: /^\{php\}[\s\S]*?\{\/php\}/,
        greedy: true,
        inside: {
          smarty: {
            pattern: /^\{php\}|\{\/php\}$/,
            inside: null // see below
          },
          php: {
            pattern: /[\s\S]+/,
            alias: 'language-php',
            inside: Prism.languages.php
          }
        }
      },
      string: [
        {
          pattern: /"(?:\\.|[^"\\\r\n])*"/,
          greedy: true,
          inside: {
            interpolation: {
              pattern: /\{[^{}]*\}|`[^`]*`/,
              inside: {
                'interpolation-punctuation': {
                  pattern: /^[{`]|[`}]$/,
                  alias: 'punctuation'
                },
                expression: {
                  pattern: /[\s\S]+/,
                  inside: null // see below
                }
              }
            },
            variable: /\$\w+/
          }
        },
        {
          pattern: /'(?:\\.|[^'\\\r\n])*'/,
          greedy: true
        }
      ],
      keyword: {
        pattern: /(^\{\/?)[a-z_]\w*\b(?!\()/i,
        lookbehind: true,
        greedy: true
      },
      delimiter: {
        pattern: /^\{\/?|\}$/,
        greedy: true,
        alias: 'punctuation'
      },
      number: /\b0x[\dA-Fa-f]+|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[Ee][-+]?\d+)?/,
      variable: [
        /\$(?!\d)\w+/,
        /#(?!\d)\w+#/,
        {
          pattern: /(\.|->|\w\s*=)(?!\d)\w+\b(?!\()/,
          lookbehind: true
        },
        {
          pattern: /(\[)(?!\d)\w+(?=\])/,
          lookbehind: true
        }
      ],
      function: {
        pattern: /(\|\s*)@?[a-z_]\w*|\b[a-z_]\w*(?=\()/i,
        lookbehind: true
      },
      'attr-name': /\b[a-z_]\w*(?=\s*=)/i,
      boolean: /\b(?:false|no|off|on|true|yes)\b/,
      punctuation: /[\[\](){}.,:`]|->/,
      operator: [
        /[+\-*\/%]|==?=?|[!<>]=?|&&|\|\|?/,
        /\bis\s+(?:not\s+)?(?:div|even|odd)(?:\s+by)?\b/,
        /\b(?:and|eq|gt?e|gt|lt?e|lt|mod|neq?|not|or)\b/
      ]
    }
    Prism.languages.smarty['embedded-php'].inside.smarty.inside =
      Prism.languages.smarty
    Prism.languages.smarty.string[0].inside.interpolation.inside.expression.inside =
      Prism.languages.smarty
    var string = /"(?:\\.|[^"\\\r\n])*"|'(?:\\.|[^'\\\r\n])*'/
    var smartyPattern = RegExp(
      // comments
      /\{\*[\s\S]*?\*\}/.source +
        '|' + // php tags
        /\{php\}[\s\S]*?\{\/php\}/.source +
        '|' + // smarty blocks
        /\{(?:[^{}"']|<str>|\{(?:[^{}"']|<str>|\{(?:[^{}"']|<str>)*\})*\})*\}/.source.replace(
          /<str>/g,
          function () {
            return string.source
          }
        ),
      'g'
    ) // Tokenize all inline Smarty expressions
    Prism.hooks.add('before-tokenize', function (env) {
      var smartyLiteralStart = '{literal}'
      var smartyLiteralEnd = '{/literal}'
      var smartyLiteralMode = false
      Prism.languages['markup-templating'].buildPlaceholders(
        env,
        'smarty',
        smartyPattern,
        function (match) {
          // Smarty tags inside {literal} block are ignored
          if (match === smartyLiteralEnd) {
            smartyLiteralMode = false
          }
          if (!smartyLiteralMode) {
            if (match === smartyLiteralStart) {
              smartyLiteralMode = true
            }
            return true
          }
          return false
        }
      )
    }) // Re-insert the tokens after tokenizing
    Prism.hooks.add('after-tokenize', function (env) {
      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'smarty')
    })
  })(Prism)
}


/***/ }),

/***/ 72388:
/***/ ((module) => {



module.exports = scss
scss.displayName = 'scss'
scss.aliases = []
function scss(Prism) {
  Prism.languages.scss = Prism.languages.extend('css', {
    comment: {
      pattern: /(^|[^\\])(?:\/\*[\s\S]*?\*\/|\/\/.*)/,
      lookbehind: true
    },
    atrule: {
      pattern: /@[\w-](?:\([^()]+\)|[^()\s]|\s+(?!\s))*?(?=\s+[{;])/,
      inside: {
        rule: /@[\w-]+/ // See rest below
      }
    },
    // url, compassified
    url: /(?:[-a-z]+-)?url(?=\()/i,
    // CSS selector regex is not appropriate for Sass
    // since there can be lot more things (var, @ directive, nesting..)
    // a selector must start at the end of a property or after a brace (end of other rules or nesting)
    // it can contain some characters that aren't used for defining rules or end of selector, & (parent selector), or interpolated variable
    // the end of a selector is found when there is no rules in it ( {} or {\s}) or if there is a property (because an interpolated var
    // can "pass" as a selector- e.g: proper#{$erty})
    // this one was hard to do, so please be careful if you edit this one :)
    selector: {
      // Initial look-ahead is used to prevent matching of blank selectors
      pattern:
        /(?=\S)[^@;{}()]?(?:[^@;{}()\s]|\s+(?!\s)|#\{\$[-\w]+\})+(?=\s*\{(?:\}|\s|[^}][^:{}]*[:{][^}]))/,
      inside: {
        parent: {
          pattern: /&/,
          alias: 'important'
        },
        placeholder: /%[-\w]+/,
        variable: /\$[-\w]+|#\{\$[-\w]+\}/
      }
    },
    property: {
      pattern: /(?:[-\w]|\$[-\w]|#\{\$[-\w]+\})+(?=\s*:)/,
      inside: {
        variable: /\$[-\w]+|#\{\$[-\w]+\}/
      }
    }
  })
  Prism.languages.insertBefore('scss', 'atrule', {
    keyword: [
      /@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\b/i,
      {
        pattern: /( )(?:from|through)(?= )/,
        lookbehind: true
      }
    ]
  })
  Prism.languages.insertBefore('scss', 'important', {
    // var and interpolated vars
    variable: /\$[-\w]+|#\{\$[-\w]+\}/
  })
  Prism.languages.insertBefore('scss', 'function', {
    'module-modifier': {
      pattern: /\b(?:as|hide|show|with)\b/i,
      alias: 'keyword'
    },
    placeholder: {
      pattern: /%[-\w]+/,
      alias: 'selector'
    },
    statement: {
      pattern: /\B!(?:default|optional)\b/i,
      alias: 'keyword'
    },
    boolean: /\b(?:false|true)\b/,
    null: {
      pattern: /\bnull\b/,
      alias: 'keyword'
    },
    operator: {
      pattern: /(\s)(?:[-+*\/%]|[=!]=|<=?|>=?|and|not|or)(?=\s)/,
      lookbehind: true
    }
  })
  Prism.languages.scss['atrule'].inside.rest = Prism.languages.scss
}


/***/ }),

/***/ 74394:
/***/ ((module) => {



module.exports = smali
smali.displayName = 'smali'
smali.aliases = []
function smali(Prism) {
  // Test files for the parser itself:
  // https://github.com/JesusFreke/smali/tree/master/smali/src/test/resources/LexerTest
  Prism.languages.smali = {
    comment: /#.*/,
    string: {
      pattern: /"(?:[^\r\n\\"]|\\.)*"|'(?:[^\r\n\\']|\\(?:.|u[\da-fA-F]{4}))'/,
      greedy: true
    },
    'class-name': {
      pattern:
        /(^|[^L])L(?:(?:\w+|`[^`\r\n]*`)\/)*(?:[\w$]+|`[^`\r\n]*`)(?=\s*;)/,
      lookbehind: true,
      inside: {
        'class-name': {
          pattern: /(^L|\/)(?:[\w$]+|`[^`\r\n]*`)$/,
          lookbehind: true
        },
        namespace: {
          pattern: /^(L)(?:(?:\w+|`[^`\r\n]*`)\/)+/,
          lookbehind: true,
          inside: {
            punctuation: /\//
          }
        },
        builtin: /^L/
      }
    },
    builtin: [
      {
        // Reference: https://github.com/JesusFreke/smali/wiki/TypesMethodsAndFields#types
        pattern: /([();\[])[BCDFIJSVZ]+/,
        lookbehind: true
      },
      {
        // e.g. .field mWifiOnUid:I
        pattern: /([\w$>]:)[BCDFIJSVZ]/,
        lookbehind: true
      }
    ],
    keyword: [
      {
        pattern: /(\.end\s+)[\w-]+/,
        lookbehind: true
      },
      {
        pattern: /(^|[^\w.-])\.(?!\d)[\w-]+/,
        lookbehind: true
      },
      {
        pattern:
          /(^|[^\w.-])(?:abstract|annotation|bridge|constructor|enum|final|interface|private|protected|public|runtime|static|synthetic|system|transient)(?![\w.-])/,
        lookbehind: true
      }
    ],
    function: {
      pattern: /(^|[^\w.-])(?:\w+|<[\w$-]+>)(?=\()/,
      lookbehind: true
    },
    field: {
      pattern: /[\w$]+(?=:)/,
      alias: 'variable'
    },
    register: {
      pattern: /(^|[^\w.-])[vp]\d(?![\w.-])/,
      lookbehind: true,
      alias: 'variable'
    },
    boolean: {
      pattern: /(^|[^\w.-])(?:false|true)(?![\w.-])/,
      lookbehind: true
    },
    number: {
      pattern:
        /(^|[^/\w.-])-?(?:NAN|INFINITY|0x(?:[\dA-F]+(?:\.[\dA-F]*)?|\.[\dA-F]+)(?:p[+-]?[\dA-F]+)?|(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?)[dflst]?(?![\w.-])/i,
      lookbehind: true
    },
    label: {
      pattern: /(:)\w+/,
      lookbehind: true,
      alias: 'property'
    },
    operator: /->|\.\.|[\[=]/,
    punctuation: /[{}(),;:]/
  }
}


/***/ }),

/***/ 90969:
/***/ ((module) => {



module.exports = solidity
solidity.displayName = 'solidity'
solidity.aliases = ['sol']
function solidity(Prism) {
  Prism.languages.solidity = Prism.languages.extend('clike', {
    'class-name': {
      pattern:
        /(\b(?:contract|enum|interface|library|new|struct|using)\s+)(?!\d)[\w$]+/,
      lookbehind: true
    },
    keyword:
      /\b(?:_|anonymous|as|assembly|assert|break|calldata|case|constant|constructor|continue|contract|default|delete|do|else|emit|enum|event|external|for|from|function|if|import|indexed|inherited|interface|internal|is|let|library|mapping|memory|modifier|new|payable|pragma|private|public|pure|require|returns?|revert|selfdestruct|solidity|storage|struct|suicide|switch|this|throw|using|var|view|while)\b/,
    operator: /=>|->|:=|=:|\*\*|\+\+|--|\|\||&&|<<=?|>>=?|[-+*/%^&|<>!=]=?|[~?]/
  })
  Prism.languages.insertBefore('solidity', 'keyword', {
    builtin:
      /\b(?:address|bool|byte|u?int(?:8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?|string|bytes(?:[1-9]|[12]\d|3[0-2])?)\b/
  })
  Prism.languages.insertBefore('solidity', 'number', {
    version: {
      pattern: /([<>]=?|\^)\d+\.\d+\.\d+\b/,
      lookbehind: true,
      alias: 'number'
    }
  })
  Prism.languages.sol = Prism.languages.solidity
}


/***/ }),

/***/ 92684:
/***/ ((module) => {



module.exports = reason
reason.displayName = 'reason'
reason.aliases = []
function reason(Prism) {
  Prism.languages.reason = Prism.languages.extend('clike', {
    string: {
      pattern: /"(?:\\(?:\r\n|[\s\S])|[^\\\r\n"])*"/,
      greedy: true
    },
    // 'class-name' must be matched *after* 'constructor' defined below
    'class-name': /\b[A-Z]\w*/,
    keyword:
      /\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\b/,
    operator:
      /\.{3}|:[:=]|\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\-*\/]\.?|\b(?:asr|land|lor|lsl|lsr|lxor|mod)\b/
  })
  Prism.languages.insertBefore('reason', 'class-name', {
    char: {
      pattern: /'(?:\\x[\da-f]{2}|\\o[0-3][0-7][0-7]|\\\d{3}|\\.|[^'\\\r\n])'/,
      greedy: true
    },
    // Negative look-ahead prevents from matching things like String.capitalize
    constructor: /\b[A-Z]\w*\b(?!\s*\.)/,
    label: {
      pattern: /\b[a-z]\w*(?=::)/,
      alias: 'symbol'
    }
  }) // We can't match functions property, so let's not even try.
  delete Prism.languages.reason.function
}


/***/ }),

/***/ 95518:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


var refractorScheme = __webpack_require__(45087)
module.exports = racket
racket.displayName = 'racket'
racket.aliases = ['rkt']
function racket(Prism) {
  Prism.register(refractorScheme)
  Prism.languages.racket = Prism.languages.extend('scheme', {
    'lambda-parameter': {
      // the racket lambda syntax is a lot more complex, so we won't even attempt to capture it.
      // this will just prevent false positives of the `function` pattern
      pattern: /([(\[]lambda\s+[(\[])[^()\[\]'\s]+/,
      lookbehind: true
    }
  })
  Prism.languages.insertBefore('racket', 'string', {
    lang: {
      pattern: /^#lang.+/m,
      greedy: true,
      alias: 'keyword'
    }
  })
  Prism.languages.rkt = Prism.languages.racket
}


/***/ })

}]);