"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4651],{

/***/ 64651:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_AppBuilderWithTheme)
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./src/contexts/EnhancedThemeContext.js
var EnhancedThemeContext = __webpack_require__(82569);
// EXTERNAL MODULE: ./src/pages/AppBuilderEnhanced.js
var AppBuilderEnhanced = __webpack_require__(98302);
;// ./src/styles/enhanced-theme.css
// extracted by mini-css-extract-plugin

;// ./src/pages/AppBuilderWithTheme.js





/**
 * App Builder wrapper component with enhanced theme support
 * This component wraps the main App Builder with the enhanced theme provider
 */
var AppBuilderWithTheme = function AppBuilderWithTheme() {
  return /*#__PURE__*/react.createElement(EnhancedThemeContext/* EnhancedThemeProvider */.fx, null, /*#__PURE__*/react.createElement(AppBuilderEnhanced["default"], null));
};
/* harmony default export */ const pages_AppBuilderWithTheme = (AppBuilderWithTheme);

/***/ })

}]);