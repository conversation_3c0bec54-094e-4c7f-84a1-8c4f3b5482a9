"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[64],{

/***/ 12237:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ EnhancedKeyboardShortcuts)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _hooks_useKeyboardShortcuts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(31960);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
/**
 * Enhanced Keyboard Shortcuts System
 * 
 * Comprehensive keyboard shortcuts with visual feedback, customization,
 * and quick action toolbars for improved productivity.
 */







var Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU.TabPane;

// Enhanced styled components
var ShortcutContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n  backdrop-filter: blur(4px);\n"])));
var ShortcutPanel = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  max-width: 800px;\n  max-height: 80vh;\n  overflow-y: auto;\n  padding: ", ";\n  margin: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.xl, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[6], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4]);
var QuickActionBar = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  background: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  padding: ", ";\n  display: flex;\n  gap: ", ";\n  z-index: 1000;\n  border: 1px solid ", ";\n  backdrop-filter: blur(8px);\n  \n  &.hidden {\n    transform: translateY(-100%);\n    opacity: 0;\n    pointer-events: none;\n  }\n  \n  transition: all 0.3s ease;\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light);
var ShortcutKey = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.span(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  padding: 2px 6px;\n  font-family: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n  margin: 0 2px;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[100], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontFamily.mono, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.primary);
var ShortcutFeedback = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: ", ";\n  color: white;\n  padding: ", " ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  z-index: 10000;\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  font-weight: ", ";\n  animation: shortcutFeedback 0.8s ease-out;\n  \n  @keyframes shortcutFeedback {\n    0% {\n      opacity: 0;\n      transform: translate(-50%, -50%) scale(0.8);\n    }\n    20% {\n      opacity: 1;\n      transform: translate(-50%, -50%) scale(1.05);\n    }\n    100% {\n      opacity: 0;\n      transform: translate(-50%, -50%) scale(1);\n    }\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.xl, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.semibold);

// Default keyboard shortcuts configuration
var DEFAULT_SHORTCUTS = {
  // File operations
  'ctrl+s': {
    action: 'save',
    label: 'Save Project',
    category: 'File',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SaveOutlined */ .ylI, null)
  },
  'ctrl+n': {
    action: 'new',
    label: 'New Component',
    category: 'File',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FileAddOutlined */ .iUk, null)
  },
  // Edit operations
  'ctrl+c': {
    action: 'copy',
    label: 'Copy Component',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CopyOutlined */ .wq3, null)
  },
  'ctrl+v': {
    action: 'paste',
    label: 'Paste Component',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ScissorOutlined */ .p9w, null)
  },
  'ctrl+x': {
    action: 'cut',
    label: 'Cut Component',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ScissorOutlined */ .p9w, null)
  },
  'ctrl+z': {
    action: 'undo',
    label: 'Undo',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .UndoOutlined */ .Xrf, null)
  },
  'ctrl+y': {
    action: 'redo',
    label: 'Redo',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .RedoOutlined */ .zYO, null)
  },
  'delete': {
    action: 'delete',
    label: 'Delete Component',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DeleteOutlined */ .SUY, null)
  },
  // View operations
  'f11': {
    action: 'fullscreen',
    label: 'Toggle Fullscreen',
    category: 'View',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenOutlined */ .KrH, null)
  },
  'ctrl+shift+p': {
    action: 'preview',
    label: 'Toggle Preview',
    category: 'View',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .EyeOutlined */ .Om2, null)
  },
  'ctrl+f': {
    action: 'search',
    label: 'Search Components',
    category: 'View',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SearchOutlined */ .VrN, null)
  },
  // Navigation
  'alt+1': {
    action: 'focusPalette',
    label: 'Focus Component Palette',
    category: 'Navigation',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  },
  'alt+2': {
    action: 'focusCanvas',
    label: 'Focus Canvas',
    category: 'Navigation',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  },
  'alt+3': {
    action: 'focusProperties',
    label: 'Focus Properties',
    category: 'Navigation',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  },
  // Help
  'f1': {
    action: 'help',
    label: 'Show Help',
    category: 'Help',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .QuestionCircleOutlined */ .faO, null)
  },
  'ctrl+shift+k': {
    action: 'shortcuts',
    label: 'Show Shortcuts',
    category: 'Help',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ControlOutlined */ .aM7, null)
  },
  // Quick actions
  'ctrl+space': {
    action: 'quickActions',
    label: 'Quick Actions',
    category: 'Quick',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  },
  'escape': {
    action: 'escape',
    label: 'Cancel/Close',
    category: 'Quick',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseOutlined */ .r$3, null)
  }
};
function EnhancedKeyboardShortcuts(_ref) {
  var onAction = _ref.onAction,
    _ref$showQuickActions = _ref.showQuickActions,
    showQuickActions = _ref$showQuickActions === void 0 ? true : _ref$showQuickActions,
    _ref$enableCustomizat = _ref.enableCustomization,
    enableCustomization = _ref$enableCustomizat === void 0 ? true : _ref$enableCustomizat,
    _ref$enableFeedback = _ref.enableFeedback,
    enableFeedback = _ref$enableFeedback === void 0 ? true : _ref$enableFeedback;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    showShortcutsPanel = _useState2[0],
    setShowShortcutsPanel = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(showQuickActions),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    showQuickActionBar = _useState4[0],
    setShowQuickActionBar = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(DEFAULT_SHORTCUTS),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    shortcuts = _useState6[0],
    setShortcuts = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    feedbackMessage = _useState8[0],
    setFeedbackMessage = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    searchTerm = _useState0[0],
    setSearchTerm = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('All'),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    activeCategory = _useState10[0],
    setActiveCategory = _useState10[1];
  var feedbackTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);

  // Get categories for filtering
  var categories = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var cats = ['All'].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(new Set(Object.values(shortcuts).map(function (s) {
      return s.category;
    }))));
    return cats;
  }, [shortcuts]);

  // Filter shortcuts based on search and category
  var filteredShortcuts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return Object.entries(shortcuts).filter(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref2, 2),
        key = _ref3[0],
        shortcut = _ref3[1];
      var matchesSearch = !searchTerm || shortcut.label.toLowerCase().includes(searchTerm.toLowerCase()) || key.toLowerCase().includes(searchTerm.toLowerCase());
      var matchesCategory = activeCategory === 'All' || shortcut.category === activeCategory;
      return matchesSearch && matchesCategory;
    });
  }, [shortcuts, searchTerm, activeCategory]);

  // Show feedback message
  var showFeedback = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (message, icon) {
    if (!enableFeedback) return;
    setFeedbackMessage({
      text: message,
      icon: icon
    });
    if (feedbackTimeoutRef.current) {
      clearTimeout(feedbackTimeoutRef.current);
    }
    feedbackTimeoutRef.current = setTimeout(function () {
      setFeedbackMessage('');
    }, 800);
  }, [enableFeedback]);

  // Handle shortcut action
  var handleShortcutAction = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (action, shortcutKey) {
    var shortcut = shortcuts[shortcutKey];
    if (onAction) {
      onAction(action, shortcutKey);
    }

    // Show feedback
    if (shortcut) {
      showFeedback(shortcut.label, shortcut.icon);
    }

    // Handle built-in actions
    switch (action) {
      case 'shortcuts':
        setShowShortcutsPanel(true);
        break;
      case 'escape':
        setShowShortcutsPanel(false);
        break;
      default:
        break;
    }
  }, [shortcuts, onAction, showFeedback]);

  // Create shortcuts object for the hook
  var shortcutHandlers = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var handlers = {};
    Object.entries(shortcuts).forEach(function (_ref4) {
      var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref4, 2),
        key = _ref5[0],
        shortcut = _ref5[1];
      handlers[key] = function () {
        return handleShortcutAction(shortcut.action, key);
      };
    });
    return handlers;
  }, [shortcuts, handleShortcutAction]);

  // Register keyboard shortcuts
  (0,_hooks_useKeyboardShortcuts__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)(shortcutHandlers);

  // Quick action buttons
  var quickActions = [{
    action: 'save',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SaveOutlined */ .ylI, null),
    tooltip: 'Save (Ctrl+S)',
    color: '#52c41a'
  }, {
    action: 'undo',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .UndoOutlined */ .Xrf, null),
    tooltip: 'Undo (Ctrl+Z)',
    color: '#1890ff'
  }, {
    action: 'redo',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .RedoOutlined */ .zYO, null),
    tooltip: 'Redo (Ctrl+Y)',
    color: '#1890ff'
  }, {
    action: 'copy',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CopyOutlined */ .wq3, null),
    tooltip: 'Copy (Ctrl+C)',
    color: '#722ed1'
  }, {
    action: 'paste',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ScissorOutlined */ .p9w, null),
    tooltip: 'Paste (Ctrl+V)',
    color: '#722ed1'
  }, {
    action: 'preview',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .EyeOutlined */ .Om2, null),
    tooltip: 'Preview (Ctrl+Shift+P)',
    color: '#fa8c16'
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, showQuickActionBar && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(QuickActionBar, {
    className: showQuickActionBar ? '' : 'hidden'
  }, quickActions.map(function (action, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      key: index,
      title: action.tooltip,
      placement: "bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      type: "text",
      icon: action.icon,
      size: "small",
      onClick: function onClick() {
        return handleShortcutAction(action.action);
      },
      style: {
        color: action.color,
        border: "1px solid ".concat(action.color, "20"),
        background: "".concat(action.color, "10")
      }
    }));
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, {
    type: "vertical",
    style: {
      margin: '0 4px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
    title: "Keyboard Shortcuts (Ctrl+Shift+K)",
    placement: "bottom"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ControlOutlined */ .aM7, null),
    size: "small",
    onClick: function onClick() {
      return setShowShortcutsPanel(true);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
    title: "Hide Quick Actions",
    placement: "bottom"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseOutlined */ .r$3, null),
    size: "small",
    onClick: function onClick() {
      return setShowQuickActionBar(false);
    }
  }))), !showQuickActionBar && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .FloatButton */ .ff, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null),
    tooltip: "Show Quick Actions",
    onClick: function onClick() {
      return setShowQuickActionBar(true);
    },
    style: {
      right: 24,
      top: 24
    }
  }), showShortcutsPanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ShortcutContainer, {
    onClick: function onClick() {
      return setShowShortcutsPanel(false);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ShortcutPanel, {
    onClick: function onClick(e) {
      return e.stopPropagation();
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 3,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ControlOutlined */ .aM7, null), " Keyboard Shortcuts"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseOutlined */ .r$3, null),
    onClick: function onClick() {
      return setShowShortcutsPanel(false);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
    direction: "vertical",
    size: "large",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      display: 'flex',
      gap: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "Search shortcuts...",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SearchOutlined */ .VrN, null),
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    allowClear: true,
    style: {
      flex: 1
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU, {
    activeKey: activeCategory,
    onChange: setActiveCategory
  }, categories.map(function (category) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
      tab: category,
      key: category
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        maxHeight: '400px',
        overflowY: 'auto'
      }
    }, filteredShortcuts.filter(function (_ref6) {
      var _ref7 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref6, 2),
        shortcut = _ref7[1];
      return activeCategory === 'All' || shortcut.category === activeCategory;
    }).map(function (_ref8) {
      var _ref9 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref8, 2),
        key = _ref9[0],
        shortcut = _ref9[1];
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
        key: key,
        size: "small",
        style: {
          marginBottom: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2]
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, shortcut.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, shortcut.label)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, key.split('+').map(function (k, i) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ShortcutKey, {
          key: i
        }, k.toUpperCase());
      }))));
    })));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Alert */ .Fc, {
    message: "Pro Tip",
    description: "Press Ctrl+Shift+K anytime to view this shortcuts panel. Most shortcuts work globally throughout the App Builder.",
    type: "info",
    showIcon: true
  })))), feedbackMessage && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ShortcutFeedback, null, feedbackMessage.icon, feedbackMessage.text));
}

/***/ }),

/***/ 60064:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ IntegratedAppBuilder)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1807);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(71468);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(70572);






var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
var _excluded = ["children"],
  _excluded2 = ["children"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/**
 * Integrated App Builder
 * 
 * Main App Builder component that integrates all UI/UX improvements
 * with existing features including WebSocket collaboration, template system,
 * code export, tutorial assistant, and AI design suggestions.
 */






// Enhanced UI/UX Components - with fallbacks
var ResponsiveAppLayout, AccessibleComponent, EnhancedComponentPaletteFixed, UXEnhancedPropertyEditor, UXEnhancedPreviewArea, EnhancedKeyboardShortcuts, DragDropProvider;

// New Feature Components
var TestingTools, DataManagementTools, PerformanceTools, EnhancedCodeExporter, IntegratedTutorialAssistant;
try {
  ResponsiveAppLayout = (__webpack_require__(32361)/* ["default"] */ .A);
} catch (error) {
  console.warn('ResponsiveAppLayout not available, using fallback');
  ResponsiveAppLayout = function ResponsiveAppLayout(_ref) {
    var children = _ref.children,
      headerContent = _ref.headerContent,
      leftPanel = _ref.leftPanel,
      rightPanel = _ref.rightPanel;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE, {
      style: {
        height: '100vh'
      }
    }, headerContent && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Header, {
      style: {
        background: '#fff',
        padding: '0 24px'
      }
    }, headerContent), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE, null, leftPanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Sider, {
      width: 250,
      style: {
        background: '#fff'
      }
    }, leftPanel), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Content, {
      style: {
        padding: '24px'
      }
    }, children), rightPanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Sider, {
      width: 250,
      style: {
        background: '#fff'
      }
    }, rightPanel)));
  };
}
try {
  AccessibleComponent = (__webpack_require__(12576)/* ["default"] */ .A);
} catch (error) {
  console.warn('AccessibleComponent not available, using fallback');
  AccessibleComponent = function AccessibleComponent(_ref2) {
    var children = _ref2.children,
      props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(_ref2, _excluded);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", props, children);
  };
}
try {
  EnhancedComponentPaletteFixed = (__webpack_require__(90985)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedComponentPaletteFixed not available, using fallback');
  EnhancedComponentPaletteFixed = function EnhancedComponentPaletteFixed(_ref3) {
    var onAddComponent = _ref3.onAddComponent;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        padding: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h3", null, "Components"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      onClick: function onClick() {
        return onAddComponent('button');
      },
      block: true,
      style: {
        marginBottom: '8px'
      }
    }, "Add Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      onClick: function onClick() {
        return onAddComponent('text');
      },
      block: true,
      style: {
        marginBottom: '8px'
      }
    }, "Add Text"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      onClick: function onClick() {
        return onAddComponent('input');
      },
      block: true
    }, "Add Input"));
  };
}
try {
  UXEnhancedPropertyEditor = (__webpack_require__(37812)/* ["default"] */ .A);
} catch (error) {
  console.warn('UXEnhancedPropertyEditor not available, using fallback');
  UXEnhancedPropertyEditor = function UXEnhancedPropertyEditor(_ref4) {
    var component = _ref4.component;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        padding: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h3", null, "Properties"), component ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("strong", null, "Type:"), " ", component.type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("strong", null, "ID:"), " ", component.id)) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, "Select a component to edit properties"));
  };
}
try {
  UXEnhancedPreviewArea = (__webpack_require__(79647)/* ["default"] */ .A);
} catch (error) {
  console.warn('UXEnhancedPreviewArea not available, using fallback');
  UXEnhancedPreviewArea = function UXEnhancedPreviewArea(_ref5) {
    var components = _ref5.components,
      onSelectComponent = _ref5.onSelectComponent,
      onDrop = _ref5.onDrop;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        minHeight: '400px',
        border: '2px dashed #ccc',
        borderRadius: '8px',
        padding: '20px',
        textAlign: 'center',
        background: '#fafafa'
      }
    }, components.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h3", null, "Canvas Area"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, "Drag components here to start building")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h3", null, "Preview"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, components.length, " component(s) added")));
  };
}
try {
  EnhancedKeyboardShortcuts = (__webpack_require__(12237)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedKeyboardShortcuts not available, using fallback');
  EnhancedKeyboardShortcuts = function EnhancedKeyboardShortcuts() {
    return null;
  };
}
try {
  var dragDropModule = __webpack_require__(51311);
  DragDropProvider = dragDropModule.DragDropProvider;
} catch (error) {
  console.warn('DragDropProvider not available, using fallback');
  DragDropProvider = function DragDropProvider(_ref6) {
    var children = _ref6.children;
    return children;
  };
}

// Progressive Loading - with fallback
var useProgressiveLoading, ProgressiveWrapper;
try {
  var progressiveModule = __webpack_require__(92939);
  useProgressiveLoading = progressiveModule.useProgressiveLoading;
  ProgressiveWrapper = progressiveModule.ProgressiveWrapper;
} catch (error) {
  console.warn('Progressive loading not available, using fallback');
  useProgressiveLoading = function useProgressiveLoading() {
    return {
      isLoaded: true,
      loadComponent: function loadComponent() {}
    };
  };
  ProgressiveWrapper = function ProgressiveWrapper(_ref7) {
    var children = _ref7.children;
    return children;
  };
}

// Lazy-loaded Feature Components - with fallbacks
var TutorialAssistant, AIDesignSuggestions, TemplateManager, CodeExporter, CollaborationIndicator;
try {
  var lazyComponents = __webpack_require__(38787);
  TutorialAssistant = lazyComponents.TutorialAssistant;
  AIDesignSuggestions = lazyComponents.AIDesignSuggestions;
  TemplateManager = lazyComponents.TemplateManager;
  CodeExporter = lazyComponents.CodeExporter;
  CollaborationIndicator = lazyComponents.CollaborationIndicator;
} catch (error) {
  console.warn('Lazy components not available, using fallbacks');
  var FallbackComponent = function FallbackComponent(_ref8) {
    var children = _ref8.children,
      props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(_ref8, _excluded2);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        padding: '10px',
        border: '1px dashed #ccc',
        borderRadius: '4px'
      }
    }, "Feature not available");
  };
  TutorialAssistant = FallbackComponent;
  AIDesignSuggestions = FallbackComponent;
  TemplateManager = FallbackComponent;
  CodeExporter = FallbackComponent;
  CollaborationIndicator = FallbackComponent;
}

// Import new components - with fallbacks
var AppBuilderExamples, IntegratedTutorialSystem;
try {
  AppBuilderExamples = (__webpack_require__(32150)/* ["default"] */ .A);
} catch (error) {
  console.warn('AppBuilderExamples not available');
  AppBuilderExamples = function AppBuilderExamples() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Examples not available");
  };
}
try {
  IntegratedTutorialSystem = (__webpack_require__(39446)/* ["default"] */ .A);
} catch (error) {
  console.warn('IntegratedTutorialSystem not available');
  IntegratedTutorialSystem = function IntegratedTutorialSystem() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Tutorial system not available");
  };
}

// Design System - with fallbacks
var theme, visualHierarchy;
try {
  var designSystem = __webpack_require__(79146);
  theme = designSystem.theme;
  visualHierarchy = designSystem.visualHierarchy;
} catch (error) {
  console.warn('Design system not available, using fallback theme');
  theme = {
    colors: {
      primary: '#1890ff',
      secondary: '#666'
    },
    spacing: {
      sm: '8px',
      md: '16px',
      lg: '24px'
    },
    borderRadius: {
      sm: '4px',
      md: '8px'
    }
  };
  visualHierarchy = {
    headings: {
      h1: {
        fontSize: '24px'
      },
      h2: {
        fontSize: '20px'
      }
    }
  };
}

// Load new feature components with fallbacks
try {
  TestingTools = (__webpack_require__(11937)/* ["default"] */ .A);
} catch (error) {
  console.warn('TestingTools not available, using fallback');
  TestingTools = function TestingTools() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Testing Tools not available");
  };
}
try {
  DataManagementTools = (__webpack_require__(27954)/* ["default"] */ .A);
} catch (error) {
  console.warn('DataManagementTools not available, using fallback');
  DataManagementTools = function DataManagementTools() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Data Management Tools not available");
  };
}
try {
  PerformanceTools = (__webpack_require__(19361)/* ["default"] */ .A);
} catch (error) {
  console.warn('PerformanceTools not available, using fallback');
  PerformanceTools = function PerformanceTools() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Performance Tools not available");
  };
}
try {
  EnhancedCodeExporter = (__webpack_require__(4090)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedCodeExporter not available, using fallback');
  EnhancedCodeExporter = function EnhancedCodeExporter() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Enhanced Code Exporter not available");
  };
}
try {
  IntegratedTutorialAssistant = (__webpack_require__(9771)/* ["default"] */ .A);
} catch (error) {
  console.warn('IntegratedTutorialAssistant not available, using fallback');
  IntegratedTutorialAssistant = function IntegratedTutorialAssistant() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Tutorial Assistant not available");
  };
}

// Hooks and Services - with fallbacks
var useWebSocket, useAppBuilder, useTutorial, useAIDesignSuggestions, useTemplates, useCodeExport, useCollaboration;
try {
  useWebSocket = (__webpack_require__(97787)/* ["default"] */ .A);
} catch (error) {
  console.warn('useWebSocket not available, using fallback');
  useWebSocket = function useWebSocket() {
    return {
      isConnected: false,
      collaborators: [],
      sendUpdate: function sendUpdate() {},
      sendCursor: function sendCursor() {}
    };
  };
}
try {
  useAppBuilder = (__webpack_require__(20364)/* ["default"] */ .A);
} catch (error) {
  console.warn('useAppBuilder not available, using fallback');
  useAppBuilder = function useAppBuilder() {
    return {
      components: [],
      addComponent: function addComponent() {},
      updateComponent: function updateComponent() {},
      deleteComponent: function deleteComponent() {},
      moveComponent: function moveComponent() {},
      duplicateComponent: function duplicateComponent() {},
      undoAction: function undoAction() {},
      redoAction: function redoAction() {},
      canUndo: false,
      canRedo: false,
      saveProject: function saveProject() {},
      loadProject: function loadProject() {},
      isModified: false
    };
  };
}
try {
  useTutorial = (__webpack_require__(52648)/* ["default"] */ .A);
} catch (error) {
  console.warn('useTutorial not available, using fallback');
  useTutorial = function useTutorial() {
    return {
      isActive: false,
      currentStep: 0,
      totalSteps: 0,
      nextStep: function nextStep() {},
      previousStep: function previousStep() {},
      skipTutorial: function skipTutorial() {},
      startTutorial: function startTutorial() {}
    };
  };
}
try {
  useAIDesignSuggestions = (__webpack_require__(87169)/* ["default"] */ .A);
} catch (error) {
  console.warn('useAIDesignSuggestions not available, using fallback');
  useAIDesignSuggestions = function useAIDesignSuggestions() {
    return {
      suggestions: [],
      loading: false,
      generateSuggestions: function generateSuggestions() {},
      applySuggestion: function applySuggestion() {},
      dismissSuggestion: function dismissSuggestion() {}
    };
  };
}
try {
  useTemplates = (__webpack_require__(2643)/* ["default"] */ .A);
} catch (error) {
  console.warn('useTemplates not available, using fallback');
  useTemplates = function useTemplates() {
    return {
      templates: [],
      loading: false,
      saveAsTemplate: function saveAsTemplate() {},
      loadTemplate: function loadTemplate() {},
      deleteTemplate: function deleteTemplate() {}
    };
  };
}
try {
  useCodeExport = (__webpack_require__(66337)/* ["default"] */ .A);
} catch (error) {
  console.warn('useCodeExport not available, using fallback');
  useCodeExport = function useCodeExport() {
    return {
      exportFormats: ['React', 'Vue', 'Angular'],
      loading: false,
      exportCode: function exportCode() {},
      downloadCode: function downloadCode() {}
    };
  };
}
try {
  useCollaboration = (__webpack_require__(7805)/* ["default"] */ .A);
} catch (error) {
  console.warn('useCollaboration not available, using fallback');
  useCollaboration = function useCollaboration() {
    return {
      activeUsers: [],
      comments: [],
      addComment: function addComment() {},
      resolveComment: function resolveComment() {}
    };
  };
}
var IntegratedContainer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: 100vh;\n  background: ", ";\n  position: relative;\n  overflow: hidden;\n\n  /* Ensure proper stacking context */\n  z-index: 0;\n"])), function (props) {
  var _props$theme;
  return ((_props$theme = props.theme) === null || _props$theme === void 0 || (_props$theme = _props$theme.colors) === null || _props$theme === void 0 || (_props$theme = _props$theme.background) === null || _props$theme === void 0 ? void 0 : _props$theme["default"]) || '#f5f5f5';
});
var HeaderContent = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  flex: 1;\n\n  .app-title {\n    font-size: ", ";\n    font-weight: ", ";\n    margin: 0;\n    color: ", ";\n  }\n\n  .project-name {\n    font-size: ", ";\n    color: ", ";\n    margin-left: ", ";\n  }\n"])), function (props) {
  var _props$theme2;
  return ((_props$theme2 = props.theme) === null || _props$theme2 === void 0 || (_props$theme2 = _props$theme2.spacing) === null || _props$theme2 === void 0 ? void 0 : _props$theme2[4]) || '16px';
}, function (props) {
  var _props$theme3;
  return ((_props$theme3 = props.theme) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.visualHierarchy) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.heading) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.h4) === null || _props$theme3 === void 0 ? void 0 : _props$theme3.fontSize) || '18px';
}, function (props) {
  var _props$theme4;
  return ((_props$theme4 = props.theme) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.visualHierarchy) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.heading) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.h4) === null || _props$theme4 === void 0 ? void 0 : _props$theme4.fontWeight) || '600';
}, function (props) {
  var _props$theme5;
  return ((_props$theme5 = props.theme) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.colors) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.text) === null || _props$theme5 === void 0 ? void 0 : _props$theme5.primary) || '#333';
}, function (props) {
  var _props$theme6;
  return ((_props$theme6 = props.theme) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.visualHierarchy) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.body) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.small) === null || _props$theme6 === void 0 ? void 0 : _props$theme6.fontSize) || '12px';
}, function (props) {
  var _props$theme7;
  return ((_props$theme7 = props.theme) === null || _props$theme7 === void 0 || (_props$theme7 = _props$theme7.colors) === null || _props$theme7 === void 0 || (_props$theme7 = _props$theme7.text) === null || _props$theme7 === void 0 ? void 0 : _props$theme7.secondary) || '#666';
}, function (props) {
  var _props$theme8;
  return ((_props$theme8 = props.theme) === null || _props$theme8 === void 0 || (_props$theme8 = _props$theme8.spacing) === null || _props$theme8 === void 0 ? void 0 : _props$theme8[2]) || '8px';
});
var FeatureToggles = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n\n  @media (max-width: 768px) {\n    gap: ", ";\n  }\n"])), function (props) {
  var _props$theme9;
  return ((_props$theme9 = props.theme) === null || _props$theme9 === void 0 || (_props$theme9 = _props$theme9.spacing) === null || _props$theme9 === void 0 ? void 0 : _props$theme9[2]) || '8px';
}, function (props) {
  var _props$theme0;
  return ((_props$theme0 = props.theme) === null || _props$theme0 === void 0 || (_props$theme0 = _props$theme0.spacing) === null || _props$theme0 === void 0 ? void 0 : _props$theme0[1]) || '4px';
});
var LoadingOverlay = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: ", ";\n\n  .loading-text {\n    margin-top: ", ";\n    font-size: ", ";\n    color: ", ";\n  }\n"])), function (props) {
  var _props$theme1;
  return ((_props$theme1 = props.theme) === null || _props$theme1 === void 0 || (_props$theme1 = _props$theme1.zIndex) === null || _props$theme1 === void 0 ? void 0 : _props$theme1.modal) || 1000;
}, function (props) {
  var _props$theme10;
  return ((_props$theme10 = props.theme) === null || _props$theme10 === void 0 || (_props$theme10 = _props$theme10.spacing) === null || _props$theme10 === void 0 ? void 0 : _props$theme10[4]) || '16px';
}, function (props) {
  var _props$theme11;
  return ((_props$theme11 = props.theme) === null || _props$theme11 === void 0 || (_props$theme11 = _props$theme11.visualHierarchy) === null || _props$theme11 === void 0 || (_props$theme11 = _props$theme11.body) === null || _props$theme11 === void 0 || (_props$theme11 = _props$theme11.medium) === null || _props$theme11 === void 0 ? void 0 : _props$theme11.fontSize) || '14px';
}, function (props) {
  var _props$theme12;
  return ((_props$theme12 = props.theme) === null || _props$theme12 === void 0 || (_props$theme12 = _props$theme12.colors) === null || _props$theme12 === void 0 || (_props$theme12 = _props$theme12.text) === null || _props$theme12 === void 0 ? void 0 : _props$theme12.secondary) || '#666';
});
var ErrorBoundary = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  padding: ", ";\n  text-align: center;\n\n  .error-title {\n    font-size: ", ";\n    font-weight: ", ";\n    color: ", ";\n    margin-bottom: ", ";\n  }\n\n  .error-message {\n    font-size: ", ";\n    color: ", ";\n    margin-bottom: ", ";\n  }\n"])), function (props) {
  var _props$theme13;
  return ((_props$theme13 = props.theme) === null || _props$theme13 === void 0 || (_props$theme13 = _props$theme13.spacing) === null || _props$theme13 === void 0 ? void 0 : _props$theme13[8]) || '32px';
}, function (props) {
  var _props$theme14;
  return ((_props$theme14 = props.theme) === null || _props$theme14 === void 0 || (_props$theme14 = _props$theme14.visualHierarchy) === null || _props$theme14 === void 0 || (_props$theme14 = _props$theme14.heading) === null || _props$theme14 === void 0 || (_props$theme14 = _props$theme14.h3) === null || _props$theme14 === void 0 ? void 0 : _props$theme14.fontSize) || '20px';
}, function (props) {
  var _props$theme15;
  return ((_props$theme15 = props.theme) === null || _props$theme15 === void 0 || (_props$theme15 = _props$theme15.visualHierarchy) === null || _props$theme15 === void 0 || (_props$theme15 = _props$theme15.heading) === null || _props$theme15 === void 0 || (_props$theme15 = _props$theme15.h3) === null || _props$theme15 === void 0 ? void 0 : _props$theme15.fontWeight) || '600';
}, function (props) {
  var _props$theme16;
  return ((_props$theme16 = props.theme) === null || _props$theme16 === void 0 || (_props$theme16 = _props$theme16.colors) === null || _props$theme16 === void 0 || (_props$theme16 = _props$theme16.error) === null || _props$theme16 === void 0 ? void 0 : _props$theme16.main) || '#ff4d4f';
}, function (props) {
  var _props$theme17;
  return ((_props$theme17 = props.theme) === null || _props$theme17 === void 0 || (_props$theme17 = _props$theme17.spacing) === null || _props$theme17 === void 0 ? void 0 : _props$theme17[4]) || '16px';
}, function (props) {
  var _props$theme18;
  return ((_props$theme18 = props.theme) === null || _props$theme18 === void 0 || (_props$theme18 = _props$theme18.visualHierarchy) === null || _props$theme18 === void 0 || (_props$theme18 = _props$theme18.body) === null || _props$theme18 === void 0 || (_props$theme18 = _props$theme18.medium) === null || _props$theme18 === void 0 ? void 0 : _props$theme18.fontSize) || '14px';
}, function (props) {
  var _props$theme19;
  return ((_props$theme19 = props.theme) === null || _props$theme19 === void 0 || (_props$theme19 = _props$theme19.colors) === null || _props$theme19 === void 0 || (_props$theme19 = _props$theme19.text) === null || _props$theme19 === void 0 ? void 0 : _props$theme19.secondary) || '#666';
}, function (props) {
  var _props$theme20;
  return ((_props$theme20 = props.theme) === null || _props$theme20 === void 0 || (_props$theme20 = _props$theme20.spacing) === null || _props$theme20 === void 0 ? void 0 : _props$theme20[6]) || '24px';
});
function IntegratedAppBuilder(_ref9) {
  var projectId = _ref9.projectId,
    _ref9$initialComponen = _ref9.initialComponents,
    initialComponents = _ref9$initialComponen === void 0 ? [] : _ref9$initialComponen,
    _ref9$enableFeatures = _ref9.enableFeatures,
    enableFeatures = _ref9$enableFeatures === void 0 ? {
      websocket: true,
      tutorial: true,
      aiSuggestions: true,
      templates: true,
      codeExport: true,
      collaboration: true,
      testing: true,
      dataManagement: true,
      performanceMonitoring: true,
      enhancedExport: true,
      tutorialAssistant: true
    } : _ref9$enableFeatures,
    onSave = _ref9.onSave,
    onLoad = _ref9.onLoad,
    _onError = _ref9.onError;
  // Redux state
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__/* .useDispatch */ .wA)();
  var user = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__/* .useSelector */ .d4)(function (state) {
    var _state$auth;
    return (_state$auth = state.auth) === null || _state$auth === void 0 ? void 0 : _state$auth.user;
  });
  var project = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__/* .useSelector */ .d4)(function (state) {
    var _state$projects;
    return (_state$projects = state.projects) === null || _state$projects === void 0 ? void 0 : _state$projects.current;
  });

  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    selectedComponent = _useState6[0],
    setSelectedComponent = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    previewMode = _useState8[0],
    setPreviewMode = _useState8[1];

  // Clipboard for copy/paste operations
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState9, 2),
    clipboard = _useState0[0],
    setClipboard = _useState0[1];

  // New feature states
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState1, 2),
    activeFeaturePanel = _useState10[0],
    setActiveFeaturePanel = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({}),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState11, 2),
    testResults = _useState12[0],
    setTestResults = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({
      coreWebVitals: {},
      bundleSize: {
        total: 0
      },
      renderMetrics: {},
      memoryUsage: {
        used: 0,
        total: 100
      }
    }),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState13, 2),
    performanceData = _useState14[0],
    setPerformanceData = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)([]),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState15, 2),
    dataBindings = _useState16[0],
    setDataBindings = _useState16[1];
  var _useState17 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({
      framework: 'react',
      typescript: true,
      includeTests: false,
      includeStyles: true,
      codeQuality: {
        prettier: true,
        eslint: true,
        components: true
      }
    }),
    _useState18 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState17, 2),
    exportSettings = _useState18[0],
    setExportSettings = _useState18[1];
  var _useState19 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({
      currentTutorial: null,
      completedTutorials: new Set(),
      isActive: false
    }),
    _useState20 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState19, 2),
    tutorialProgress = _useState20[0],
    setTutorialProgress = _useState20[1];

  // Feature states
  var _useState21 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState22 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState21, 2),
    showTutorial = _useState22[0],
    setShowTutorial = _useState22[1];
  var _useState23 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState24 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState23, 2),
    showAISuggestions = _useState24[0],
    setShowAISuggestions = _useState24[1];
  var _useState25 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState26 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState25, 2),
    showTemplates = _useState26[0],
    setShowTemplates = _useState26[1];
  var _useState27 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState28 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState27, 2),
    showCodeExport = _useState28[0],
    setShowCodeExport = _useState28[1];
  var _useState29 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState30 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState29, 2),
    showExamples = _useState30[0],
    setShowExamples = _useState30[1];
  var _useState31 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState32 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState31, 2),
    showTestingTools = _useState32[0],
    setShowTestingTools = _useState32[1];
  var _useState33 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState34 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState33, 2),
    showPerformanceMonitor = _useState34[0],
    setShowPerformanceMonitor = _useState34[1];

  // Progressive loading for feature components
  var progressiveLoading = useProgressiveLoading({
    strategy: 'priority',
    features: Object.keys(enableFeatures).filter(function (key) {
      return enableFeatures[key];
    }),
    autoStart: true,
    delay: 1000 // Start loading after initial render
  });

  // App Builder hook with enhanced features
  var _useAppBuilder = useAppBuilder({
      projectId: projectId,
      initialComponents: initialComponents,
      autoSave: true,
      onSave: onSave,
      onLoad: onLoad,
      onError: function onError(err) {
        setError(err);
        if (_onError) _onError(err);
      }
    }),
    components = _useAppBuilder.components,
    addComponent = _useAppBuilder.addComponent,
    updateComponent = _useAppBuilder.updateComponent,
    deleteComponent = _useAppBuilder.deleteComponent,
    moveComponent = _useAppBuilder.moveComponent,
    duplicateComponent = _useAppBuilder.duplicateComponent,
    undoAction = _useAppBuilder.undoAction,
    redoAction = _useAppBuilder.redoAction,
    canUndo = _useAppBuilder.canUndo,
    canRedo = _useAppBuilder.canRedo,
    saveProject = _useAppBuilder.saveProject,
    loadProject = _useAppBuilder.loadProject,
    isModified = _useAppBuilder.isModified;

  // WebSocket collaboration
  var _useWebSocket = useWebSocket({
      enabled: enableFeatures.websocket,
      projectId: projectId,
      userId: user === null || user === void 0 ? void 0 : user.id,
      onComponentUpdate: updateComponent,
      onComponentAdd: addComponent,
      onComponentDelete: deleteComponent,
      onComponentMove: moveComponent
    }),
    websocketConnected = _useWebSocket.isConnected,
    collaborators = _useWebSocket.collaborators,
    sendUpdate = _useWebSocket.sendUpdate,
    sendCursor = _useWebSocket.sendCursor;

  // Tutorial system
  var _useTutorial = useTutorial({
      enabled: enableFeatures.tutorial,
      autoStart: !(user !== null && user !== void 0 && user.hasCompletedTutorial)
    }),
    tutorialActive = _useTutorial.isActive,
    currentStep = _useTutorial.currentStep,
    totalSteps = _useTutorial.totalSteps,
    nextStep = _useTutorial.nextStep,
    previousStep = _useTutorial.previousStep,
    skipTutorial = _useTutorial.skipTutorial,
    startTutorial = _useTutorial.startTutorial;

  // AI Design Suggestions
  var _useAIDesignSuggestio = useAIDesignSuggestions({
      enabled: enableFeatures.aiSuggestions,
      components: components,
      selectedComponent: selectedComponent,
      autoRefresh: true
    }),
    suggestions = _useAIDesignSuggestio.suggestions,
    aiLoading = _useAIDesignSuggestio.loading,
    generateSuggestions = _useAIDesignSuggestio.generateSuggestions,
    applySuggestion = _useAIDesignSuggestio.applySuggestion,
    dismissSuggestion = _useAIDesignSuggestio.dismissSuggestion;

  // Template system
  var _useTemplates = useTemplates({
      enabled: enableFeatures.templates,
      projectId: projectId
    }),
    templates = _useTemplates.templates,
    templatesLoading = _useTemplates.loading,
    saveAsTemplate = _useTemplates.saveAsTemplate,
    loadTemplate = _useTemplates.loadTemplate,
    deleteTemplate = _useTemplates.deleteTemplate;

  // Code export
  var _useCodeExport = useCodeExport({
      enabled: enableFeatures.codeExport,
      components: components,
      projectSettings: project === null || project === void 0 ? void 0 : project.settings
    }),
    exportCode = _useCodeExport.exportCode,
    exportFormats = _useCodeExport.exportFormats,
    exportLoading = _useCodeExport.loading,
    downloadCode = _useCodeExport.downloadCode;

  // Collaboration features
  var _useCollaboration = useCollaboration({
      enabled: enableFeatures.collaboration,
      projectId: projectId,
      websocketConnected: websocketConnected
    }),
    activeUsers = _useCollaboration.activeUsers,
    comments = _useCollaboration.comments,
    addComment = _useCollaboration.addComment,
    resolveComment = _useCollaboration.resolveComment,
    shareProject = _useCollaboration.shareProject;

  // Initialize app
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    var initializeApp = /*#__PURE__*/function () {
      var _ref0 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee() {
        var _t;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              setLoading(true);
              if (!projectId) {
                _context.next = 1;
                break;
              }
              _context.next = 1;
              return loadProject(projectId);
            case 1:
              if (!enableFeatures.aiSuggestions) {
                _context.next = 2;
                break;
              }
              _context.next = 2;
              return generateSuggestions();
            case 2:
              setLoading(false);
              _context.next = 4;
              break;
            case 3:
              _context.prev = 3;
              _t = _context["catch"](0);
              setError(_t);
              setLoading(false);
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 3]]);
      }));
      return function initializeApp() {
        return _ref0.apply(this, arguments);
      };
    }();
    initializeApp();
  }, [projectId, enableFeatures.aiSuggestions]); // Removed function dependencies to prevent infinite re-renders

  // Handle incoming WebSocket events for new features
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    if (!websocketConnected) return;
    var handleWebSocketMessage = function handleWebSocketMessage(event) {
      try {
        var data = JSON.parse(event.data);
        switch (data.type) {
          case 'test_results':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " completed ").concat(data.testType, " tests: ").concat(data.results.passed, "/").concat(data.results.total, " passed"));
            }
            break;
          case 'optimization_applied':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " applied optimization: ").concat(data.suggestion));
            }
            break;
          case 'data_binding_created':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              setDataBindings(function (prev) {
                return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [data.binding]);
              });
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " created a data binding"));
            }
            break;
          case 'data_binding_updated':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              setDataBindings(function (prev) {
                return prev.map(function (b) {
                  return b.id === data.binding.id ? data.binding : b;
                });
              });
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " updated a data binding"));
            }
            break;
          case 'data_binding_deleted':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              setDataBindings(function (prev) {
                return prev.filter(function (b) {
                  return b.id !== data.bindingId;
                });
              });
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " deleted a data binding"));
            }
            break;
          case 'code_exported':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " exported code for ").concat(data.framework));
            }
            break;
          case 'tutorial_completed':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " completed a tutorial"));
            }
            break;
          default:
            // Handle other WebSocket events
            break;
        }
      } catch (error) {
        console.warn('Failed to parse WebSocket message:', error);
      }
    };

    // Note: WebSocket message handling is integrated through the useWebSocket hook
    console.log('WebSocket event handling for new features is ready');
    return function () {
      // Cleanup if needed
    };
  }, [websocketConnected, user === null || user === void 0 ? void 0 : user.id]);

  // Persist feature panel state and settings
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    var savedSettings = localStorage.getItem("app-builder-settings-".concat(projectId));
    if (savedSettings) {
      try {
        var settings = JSON.parse(savedSettings);
        if (settings.exportSettings) {
          setExportSettings(function (prev) {
            return _objectSpread(_objectSpread({}, prev), settings.exportSettings);
          });
        }
        if (settings.dataBindings) {
          setDataBindings(settings.dataBindings);
        }
        if (settings.tutorialProgress) {
          setTutorialProgress(function (prev) {
            return _objectSpread(_objectSpread({}, prev), settings.tutorialProgress);
          });
        }
      } catch (error) {
        console.warn('Failed to load saved settings:', error);
      }
    }
  }, [projectId]);

  // Save settings when they change
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    var settings = {
      exportSettings: exportSettings,
      dataBindings: dataBindings,
      tutorialProgress: tutorialProgress,
      lastUpdated: Date.now()
    };
    localStorage.setItem("app-builder-settings-".concat(projectId), JSON.stringify(settings));
  }, [projectId, exportSettings, dataBindings, tutorialProgress]);

  // Handle component selection with collaboration
  var handleSelectComponent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (component) {
    setSelectedComponent(component);

    // Send cursor position for collaboration
    if (websocketConnected && component) {
      sendCursor({
        componentId: component.id,
        action: 'select',
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendCursor]);

  // Handle component updates with real-time sync
  var handleUpdateComponent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (componentId, updates) {
    updateComponent(componentId, updates);

    // Send update to collaborators
    if (websocketConnected) {
      sendUpdate({
        type: 'component_update',
        componentId: componentId,
        updates: updates,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [updateComponent, websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Handle component addition with AI suggestions
  var handleAddComponent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(/*#__PURE__*/function () {
    var _ref1 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee2(componentType, position) {
      var newComponent;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 1;
            return addComponent(componentType, position);
          case 1:
            newComponent = _context2.sent;
            // Generate AI suggestions for the new component
            if (enableFeatures.aiSuggestions && newComponent) {
              setTimeout(function () {
                return generateSuggestions();
              }, 500);
            }

            // Send update to collaborators
            if (websocketConnected) {
              sendUpdate({
                type: 'component_add',
                component: newComponent,
                userId: user === null || user === void 0 ? void 0 : user.id,
                timestamp: Date.now()
              });
            }
            return _context2.abrupt("return", newComponent);
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function (_x, _x2) {
      return _ref1.apply(this, arguments);
    };
  }(), [addComponent, enableFeatures.aiSuggestions, generateSuggestions, websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Handle component deletion with confirmation
  var handleDeleteComponent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (componentId) {
    var component = components.find(function (c) {
      return c.id === componentId;
    });
    if (component) {
      deleteComponent(componentId);

      // Clear selection if deleted component was selected
      if ((selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.id) === componentId) {
        setSelectedComponent(null);
      }

      // Send update to collaborators
      if (websocketConnected) {
        sendUpdate({
          type: 'component_delete',
          componentId: componentId,
          userId: user === null || user === void 0 ? void 0 : user.id,
          timestamp: Date.now()
        });
      }
      antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Deleted ".concat(component.type, " component"));
    }
  }, [components, deleteComponent, selectedComponent, websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Enhanced keyboard shortcut handlers
  var handleKeyboardAction = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (action, shortcutKey) {
    switch (action) {
      case 'save':
        if (isModified) {
          saveProject();
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Project saved successfully');
        }
        break;
      case 'copy':
        if (selectedComponent) {
          setClipboard(_objectSpread(_objectSpread({}, selectedComponent), {}, {
            id: undefined
          }));
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Component copied to clipboard');
        }
        break;
      case 'paste':
        if (clipboard) {
          var _clipboard$position, _clipboard$position2;
          var newComponent = _objectSpread(_objectSpread({}, clipboard), {}, {
            id: "component_".concat(Date.now()),
            position: {
              x: (((_clipboard$position = clipboard.position) === null || _clipboard$position === void 0 ? void 0 : _clipboard$position.x) || 0) + 20,
              y: (((_clipboard$position2 = clipboard.position) === null || _clipboard$position2 === void 0 ? void 0 : _clipboard$position2.y) || 0) + 20
            }
          });
          handleAddComponent(newComponent.type, newComponent.position);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Component pasted from clipboard');
        }
        break;
      case 'cut':
        if (selectedComponent) {
          setClipboard(_objectSpread(_objectSpread({}, selectedComponent), {}, {
            id: undefined
          }));
          handleDeleteComponent(selectedComponent.id);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Component cut to clipboard');
        }
        break;
      case 'undo':
        if (canUndo) {
          undoAction();
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Action undone');
        }
        break;
      case 'redo':
        if (canRedo) {
          redoAction();
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Action redone');
        }
        break;
      case 'delete':
        if (selectedComponent) {
          handleDeleteComponent(selectedComponent.id);
        }
        break;
      case 'preview':
        setPreviewMode(function (prev) {
          return !prev;
        });
        antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Preview mode ".concat(!previewMode ? 'enabled' : 'disabled'));
        break;
      case 'new':
        handleAddComponent('text', {
          x: 100,
          y: 100
        });
        break;
      case 'fullscreen':
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          document.documentElement.requestFullscreen();
        }
        break;

      // New feature shortcuts
      case 'toggle_testing':
        if (enableFeatures.testing) {
          handleFeaturePanelToggle('testing');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Testing panel toggled');
        }
        break;
      case 'toggle_performance':
        if (enableFeatures.performanceMonitoring) {
          handleFeaturePanelToggle('performance');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Performance panel toggled');
        }
        break;
      case 'toggle_data':
        if (enableFeatures.dataManagement) {
          handleFeaturePanelToggle('data');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Data management panel toggled');
        }
        break;
      case 'toggle_export':
        if (enableFeatures.enhancedExport) {
          handleFeaturePanelToggle('export');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Export panel toggled');
        }
        break;
      case 'close_panels':
        if (activeFeaturePanel) {
          setActiveFeaturePanel(null);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('All panels closed');
        }
        break;
      default:
        console.log("Unhandled keyboard action: ".concat(action));
    }
  }, [selectedComponent, clipboard, isModified, canUndo, canRedo, previewMode, saveProject, undoAction, redoAction, handleAddComponent, handleDeleteComponent, enableFeatures, handleFeaturePanelToggle, activeFeaturePanel, setActiveFeaturePanel]);

  // New Feature Handlers

  // Testing Tools Handlers
  var handleTestComplete = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (testType, results) {
    setTestResults(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, results));
    });
    var passedTests = results.filter(function (r) {
      return r.status === 'passed';
    }).length;
    var totalTests = results.length;
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("".concat(testType, " tests completed: ").concat(passedTests, "/").concat(totalTests, " passed"));

    // Send test results via WebSocket for collaboration
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'test_results',
        testType: testType,
        results: {
          passed: passedTests,
          total: totalTests
        },
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleTestStart = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (testType) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("Starting ".concat(testType, " tests..."));
    setTestResults(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, []));
    });
  }, []);

  // Performance Monitoring Handlers
  var handleOptimizationApply = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (suggestion) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Applied optimization: ".concat(suggestion.title));
    setPerformanceData(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        optimizations: [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev.optimizations || []), [_objectSpread(_objectSpread({}, suggestion), {}, {
          appliedAt: Date.now()
        })])
      });
    });
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'optimization_applied',
        suggestion: suggestion.title,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Data Management Handlers
  var handleDataChange = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (data) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Data updated successfully');
    if (selectedComponent && data.componentId === selectedComponent.id) {
      handleUpdateComponent(selectedComponent.id, {
        data: data
      });
    }
  }, [selectedComponent, handleUpdateComponent]);
  var handleBindingCreate = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (binding) {
    setDataBindings(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [binding]);
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Data binding created');
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_created',
        binding: binding,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleBindingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (binding) {
    setDataBindings(function (prev) {
      return prev.map(function (b) {
        return b.id === binding.id ? binding : b;
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Data binding updated');
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_updated',
        binding: binding,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleBindingDelete = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (bindingId) {
    setDataBindings(function (prev) {
      return prev.filter(function (b) {
        return b.id !== bindingId;
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Data binding deleted');
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_deleted',
        bindingId: bindingId,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Enhanced Export Handlers
  var handleEnhancedExport = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (exportData) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Code exported for ".concat(exportData.framework));
    setExportSettings(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        lastExport: {
          framework: exportData.framework,
          timestamp: Date.now(),
          settings: exportData.settings
        }
      });
    });
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'code_exported',
        framework: exportData.framework,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleCodePreview = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (code) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Code preview generated');
  }, []);

  // Tutorial Assistant Handlers
  var handleTutorialComplete = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (tutorial) {
    setTutorialProgress(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        completedTutorials: new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev.completedTutorials), [tutorial.id])),
        currentTutorial: null,
        isActive: false
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Tutorial \"".concat(tutorial.title, "\" completed! \uD83C\uDF89"));
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'tutorial_completed',
        tutorialId: tutorial.id,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleTutorialSkip = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (tutorial) {
    setTutorialProgress(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        currentTutorial: null,
        isActive: false
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("Tutorial \"".concat(tutorial.title, "\" skipped."));
  }, []);

  // Feature Panel Management
  var handleFeaturePanelToggle = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (panelType) {
    setActiveFeaturePanel(function (prev) {
      return prev === panelType ? null : panelType;
    });
  }, []);

  // Memoized header content
  var headerContent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(HeaderContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h1", {
      className: "app-title"
    }, "App Builder"), (project === null || project === void 0 ? void 0 : project.name) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", {
      className: "project-name"
    }, project.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        marginTop: '4px',
        fontSize: '12px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '2px 8px',
        borderRadius: '12px',
        background: websocketConnected ? '#f6ffed' : '#fff2f0',
        border: "1px solid ".concat(websocketConnected ? '#b7eb8f' : '#ffccc7'),
        color: websocketConnected ? '#52c41a' : '#ff4d4f'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        background: websocketConnected ? '#52c41a' : '#ff4d4f'
      }
    }), websocketConnected ? 'Connected' : 'Offline'), isModified && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '2px 8px',
        borderRadius: '12px',
        background: '#fff7e6',
        border: '1px solid #ffd591',
        color: '#fa8c16'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        background: '#fa8c16'
      }
    }), "Unsaved Changes"), activeFeaturePanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '2px 8px',
        borderRadius: '12px',
        background: '#e6f7ff',
        border: '1px solid #91d5ff',
        color: '#1890ff'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        background: '#1890ff'
      }
    }), activeFeaturePanel.charAt(0).toUpperCase() + activeFeaturePanel.slice(1), " Panel"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '2px 8px',
        borderRadius: '12px',
        background: '#f0f5ff',
        border: '1px solid #adc6ff',
        color: '#1890ff'
      }
    }, components.length, " Component", components.length !== 1 ? 's' : ''))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(FeatureToggles, null, enableFeatures.collaboration && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ProgressiveWrapper, {
      componentName: "CollaborationIndicator",
      strategy: "viewport",
      fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          width: 32,
          height: 32
        }
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(CollaborationIndicator, {
      connected: websocketConnected,
      collaborators: collaborators,
      activeUsers: activeUsers
    })), enableFeatures.aiSuggestions && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ProgressiveWrapper, {
      componentName: "AIDesignSuggestions",
      strategy: "interaction",
      fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          width: 120,
          height: 32
        }
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(AIDesignSuggestions, {
      suggestions: suggestions,
      loading: aiLoading,
      onApply: applySuggestion,
      onDismiss: dismissSuggestion,
      compact: true
    })), enableFeatures.templates && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      "data-tutorial": "theme-manager"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ProgressiveWrapper, {
      componentName: "TemplateManager",
      strategy: "interaction",
      fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          width: 100,
          height: 32
        }
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TemplateManager, {
      templates: templates,
      loading: templatesLoading,
      onSave: saveAsTemplate,
      onLoad: loadTemplate,
      onDelete: deleteTemplate,
      compact: true
    }))), enableFeatures.codeExport && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ProgressiveWrapper, {
      componentName: "CodeExporter",
      strategy: "interaction",
      fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          width: 100,
          height: 32
        }
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(CodeExporter, {
      formats: exportFormats,
      loading: exportLoading,
      onExport: exportCode,
      onDownload: downloadCode,
      compact: true
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "preview-mode",
      onClick: function onClick() {
        return setPreviewMode(function (prev) {
          return !prev;
        });
      },
      style: {
        background: previewMode ? '#52c41a' : '#f0f0f0',
        color: previewMode ? 'white' : '#333',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      title: previewMode ? 'Exit preview mode' : 'Enter preview mode'
    }, "\uD83D\uDC41\uFE0F ", previewMode ? 'Exit Preview' : 'Preview'), enableFeatures.tutorial && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      onClick: function onClick() {
        return startTutorial();
      },
      style: {
        background: tutorialActive ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: tutorialActive ? 'Tutorial is active' : 'Start interactive tutorial'
    }, "\uD83C\uDF93 ", tutorialActive ? 'Tutorial Active' : 'Start Tutorial'), enableFeatures.testing && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "testing-tools",
      onClick: function onClick() {
        return handleFeaturePanelToggle('testing');
      },
      style: {
        background: activeFeaturePanel === 'testing' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Open testing and debugging tools"
    }, "\uD83E\uDDEA ", activeFeaturePanel === 'testing' ? 'Close Testing' : 'Testing'), enableFeatures.performanceMonitoring && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "performance-monitor",
      onClick: function onClick() {
        return handleFeaturePanelToggle('performance');
      },
      style: {
        background: activeFeaturePanel === 'performance' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #722ed1 0%, #531dab 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Open performance monitoring dashboard"
    }, "\u26A1 ", activeFeaturePanel === 'performance' ? 'Close Performance' : 'Performance'), enableFeatures.dataManagement && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "data-management",
      onClick: function onClick() {
        return handleFeaturePanelToggle('data');
      },
      style: {
        background: activeFeaturePanel === 'data' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #13c2c2 0%, #08979c 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Open data management tools"
    }, "\uD83D\uDCCA ", activeFeaturePanel === 'data' ? 'Close Data' : 'Data'), enableFeatures.enhancedExport && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "code-export",
      onClick: function onClick() {
        return handleFeaturePanelToggle('export');
      },
      style: {
        background: activeFeaturePanel === 'export' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #eb2f96 0%, #c41d7f 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Enhanced code export with multiple frameworks"
    }, "\uD83D\uDCBB ", activeFeaturePanel === 'export' ? 'Close Export' : 'Export'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      onClick: function onClick() {
        return setShowExamples(true);
      },
      style: {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "View examples and tutorials"
    }, "\uD83D\uDCDA Examples")));
  }, [project === null || project === void 0 ? void 0 : project.name, enableFeatures, websocketConnected, collaborators, activeUsers, suggestions, aiLoading, applySuggestion, dismissSuggestion, templates, templatesLoading, saveAsTemplate, loadTemplate, deleteTemplate, exportFormats, exportLoading, exportCode, downloadCode, setShowExamples, previewMode, setPreviewMode, components.length, isModified, tutorialActive, startTutorial, activeFeaturePanel, handleFeaturePanelToggle]);

  // Error boundary
  if (error) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ErrorBoundary, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      className: "error-title"
    }, "Something went wrong"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      className: "error-message"
    }, error.message), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      onClick: function onClick() {
        return window.location.reload();
      }
    }, "Reload Application")));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DragDropProvider, {
    showOverlay: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(AccessibleComponent, {
    role: "application",
    ariaLabel: "App Builder application for creating user interfaces"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ResponsiveAppLayout, {
    headerContent: headerContent,
    leftPanel: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      "data-tutorial": "component-palette"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(EnhancedComponentPaletteFixed, {
      onAddComponent: handleAddComponent,
      selectedComponent: selectedComponent,
      showAISuggestions: enableFeatures.aiSuggestions,
      loading: loading
    })),
    rightPanel: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      "data-tutorial": "property-editor"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(UXEnhancedPropertyEditor, {
      component: selectedComponent,
      onUpdateComponent: handleUpdateComponent,
      loading: loading
    })),
    showBreakpointIndicator: "production" === 'development',
    enablePanelResize: true,
    persistLayout: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    "data-tutorial": "canvas-area"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(UXEnhancedPreviewArea, {
    components: components,
    selectedComponentId: selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.id,
    onSelectComponent: handleSelectComponent,
    onDeleteComponent: handleDeleteComponent,
    onUpdateComponent: handleUpdateComponent,
    onMoveComponent: moveComponent,
    previewMode: previewMode,
    websocketConnected: websocketConnected,
    loading: loading,
    onDrop: handleAddComponent
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(EnhancedKeyboardShortcuts, {
    onAction: handleKeyboardAction,
    showQuickActions: true,
    enableCustomization: true,
    showFeedback: true
  }), enableFeatures.tutorial && tutorialActive && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TutorialAssistant, {
    currentStep: currentStep,
    totalSteps: totalSteps,
    onNext: nextStep,
    onPrevious: previousStep,
    onSkip: skipTutorial,
    onComplete: skipTutorial
  }), enableFeatures.tutorial && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedTutorialSystem, {
    onTutorialComplete: function onTutorialComplete() {
      antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Tutorial completed! You\'re ready to build amazing apps.');
    }
  }), enableFeatures.tutorialAssistant && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedTutorialAssistant, {
    enableAutoStart: !(user !== null && user !== void 0 && user.hasCompletedTutorial),
    showContextualHelp: true,
    onTutorialComplete: handleTutorialComplete,
    onTutorialSkip: handleTutorialSkip,
    features: Object.keys(enableFeatures).filter(function (key) {
      return enableFeatures[key];
    })
  }), showExamples && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "App Builder Examples & Tutorials",
    visible: showExamples,
    onCancel: function onCancel() {
      return setShowExamples(false);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(AppBuilderExamples, null)), activeFeaturePanel === 'testing' && enableFeatures.testing && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Testing Tools",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TestingTools, {
    components: components,
    onTestComplete: handleTestComplete,
    onTestStart: handleTestStart,
    enabledTests: ['component', 'layout', 'accessibility', 'performance'],
    autoRun: false,
    showMetrics: true
  }))), activeFeaturePanel === 'performance' && enableFeatures.performanceMonitoring && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Performance Monitor",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(PerformanceTools, {
    components: components,
    onOptimizationApply: handleOptimizationApply,
    realTimeMonitoring: true,
    showSuggestions: true
  }))), activeFeaturePanel === 'data' && enableFeatures.dataManagement && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Data Management",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DataManagementTools, {
    components: components,
    onDataChange: handleDataChange,
    onBindingCreate: handleBindingCreate,
    onBindingUpdate: handleBindingUpdate,
    onBindingDelete: handleBindingDelete,
    realTimeUpdates: true,
    showVisualization: true
  }))), activeFeaturePanel === 'export' && enableFeatures.enhancedExport && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Enhanced Code Export",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(EnhancedCodeExporter, {
    components: components,
    layouts: [],
    theme: theme,
    onExport: handleEnhancedExport,
    onPreview: handleCodePreview
  }))), loading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(LoadingOverlay, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    className: "loading-text"
  }, "Loading App Builder...")),  false && /*#__PURE__*/0);
}

/***/ }),

/***/ 90985:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ EnhancedComponentPaletteFixed)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79146);
/* harmony import */ var _hooks_useAIDesignSuggestions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(87169);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Enhanced Component Palette with improved visual hierarchy and categorization
 */







var Search = antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.Search;
var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title;
var Panel = antd__WEBPACK_IMPORTED_MODULE_5__/* .Collapse */ .SD.Panel;

// Enhanced styled components
var PaletteContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  background: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.lg, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light);
var PaletteHeader = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", ";\n  background: linear-gradient(135deg, ", " 0%, ", " 100%);\n  color: ", ";\n  border-bottom: 1px solid ", ";\n  \n  .ant-typography {\n    color: ", " !important;\n    margin-bottom: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.accent.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]);
var SearchContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", " ", ";\n  background: ", ";\n  border-bottom: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light);
var FilterControls = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: ", ";\n  flex-wrap: wrap;\n  gap: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]);
var ComponentGrid = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));\n  gap: ", ";\n  padding: ", ";\n  \n  ", " {\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n    gap: ", ";\n    padding: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]);
var ComponentCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  cursor: grab;\n  transition: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  position: relative;\n  min-height: 140px;\n  overflow: hidden;\n  background: ", ";\n  \n  /* Enhanced visual hierarchy */\n  box-shadow: ", ";\n  \n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: ", ";\n    border-color: ", ";\n    background: ", "05;\n    \n    .component-icon {\n      transform: scale(1.15);\n      background: ", "20;\n    }\n    \n    .component-label {\n      color: ", ";\n      font-weight: ", ";\n    }\n  }\n  \n  &:active {\n    cursor: grabbing;\n    transform: scale(0.95) translateY(-1px);\n  }\n  \n  &:focus-visible {\n    outline: 2px solid ", ";\n    outline-offset: 2px;\n  }\n  \n  /* Complexity indicator */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: ", ";\n    opacity: 0.8;\n  }\n  \n  .ant-card-body {\n    padding: ", ";\n    text-align: center;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md, function (props) {
  return props.isDragging ? _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.light : _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper;
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.sm, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.xl, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.semibold, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, function (props) {
  switch (props.complexity) {
    case 'simple':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main;
    case 'medium':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.main;
    case 'complex':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.main;
    default:
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.neutral[300];
  }
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3]);
var ComponentIcon = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-size: 28px;\n  color: ", ";\n  margin-bottom: ", ";\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 48px;\n  height: 48px;\n  border-radius: ", ";\n  background: ", ";\n  margin: 0 auto ", ";\n  transition: ", ";\n  position: relative;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  \n  @media (prefers-contrast: high) {\n    border: 2px solid currentColor;\n    background: transparent;\n  }\n"])), function (props) {
  return props.color || _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main;
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.lg, function (props) {
  return props.gradient || "linear-gradient(135deg, ".concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, "15 0%, ").concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, "25 100%)");
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"]);
var ComponentLabel = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n  margin-bottom: ", ";\n  line-height: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.sm, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.primary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.lineHeight.tight);
var ComponentDescription = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-size: ", ";\n  color: ", ";\n  line-height: ", ";\n  text-align: center;\n  margin-top: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.lineHeight.normal, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1]);
var ComponentPreview = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  width: 24px;\n  height: 16px;\n  background: ", ";\n  border: 1px solid ", ";\n  border-radius: 2px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: bold;\n  color: ", ";\n  opacity: 0.7;\n  transition: ", ";\n  \n  ", ":hover & {\n    opacity: 1;\n    transform: scale(1.1);\n    background: ", ";\n    box-shadow: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.neutral[100], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"], ComponentCard, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.sm);
var ComplexityBadge = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ", ";\n  opacity: 0.8;\n  transition: ", ";\n  \n  ", ":hover & {\n    opacity: 1;\n    transform: scale(1.2);\n  }\n"])), function (props) {
  switch (props.complexity) {
    case 'simple':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main;
    case 'medium':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.main;
    case 'complex':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.main;
    default:
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.neutral[300];
  }
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"], ComponentCard);
var CategoryHeader = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject10 || (_templateObject10 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ", " ", ";\n  background: ", ";\n  border-bottom: 1px solid ", ";\n  font-weight: ", ";\n  color: ", ";\n  cursor: pointer;\n  transition: ", ";\n  \n  &:hover {\n    background: ", ";\n  }\n  \n  &:focus {\n    ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.semibold, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.primary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.interactive.hover, _design_system__WEBPACK_IMPORTED_MODULE_8__.a11yUtils.focusRing());
var ScrollableContent = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject11 || (_templateObject11 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  \n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: ", ";\n    border-radius: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb:hover {\n    background: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.full, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.dark);

// Component data with enhanced metadata
var COMPONENT_GROUPS = [{
  id: 'layout',
  title: 'Layout',
  description: 'Structural components for organizing content',
  color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main,
  icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LayoutOutlined */ .hy2, null),
  components: [{
    type: 'header',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FontSizeOutlined */ .ld1, null),
    label: 'Header',
    description: 'Page or section header with title and navigation',
    usage: 'Use for page titles, navigation bars, or section headers',
    tags: ['layout', 'navigation', 'title'],
    complexity: 'simple',
    preview: '═══'
  }, {
    type: 'section',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LayoutOutlined */ .hy2, null),
    label: 'Section',
    description: 'Container for grouping related content',
    usage: 'Organize content into logical sections',
    tags: ['layout', 'container', 'organization'],
    complexity: 'simple',
    preview: '▢'
  }, {
    type: 'card',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CreditCardOutlined */ .wN, null),
    label: 'Card',
    description: 'Flexible content container with optional header and footer',
    usage: 'Display content in a clean, contained format',
    tags: ['layout', 'container', 'content'],
    complexity: 'medium',
    preview: '▢'
  }]
}, {
  id: 'basic',
  title: 'Basic Components',
  description: 'Essential UI elements for content and interaction',
  color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main,
  icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .AppstoreOutlined */ .rS9, null),
  components: [{
    type: 'text',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FileTextOutlined */ .y9H, null),
    label: 'Text',
    description: 'Formatted text content with typography options',
    usage: 'Display paragraphs, headings, and formatted text',
    tags: ['content', 'text', 'typography'],
    complexity: 'simple',
    preview: 'Aa'
  }, {
    type: 'button',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .AppstoreOutlined */ .rS9, null),
    label: 'Button',
    description: 'Interactive button for user actions',
    usage: 'Trigger actions, submit forms, or navigate',
    tags: ['interaction', 'action', 'click'],
    complexity: 'simple',
    preview: '▢'
  }, {
    type: 'image',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PictureOutlined */ .JZT, null),
    label: 'Image',
    description: 'Display images with responsive sizing',
    usage: 'Show photos, illustrations, or graphics',
    tags: ['media', 'visual', 'content'],
    complexity: 'simple',
    preview: '🖼'
  }]
}];
function EnhancedComponentPaletteFixed(_ref) {
  var onAddComponent = _ref.onAddComponent,
    onDragStart = _ref.onDragStart,
    onDragEnd = _ref.onDragEnd,
    _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$selectedComponen = _ref.selectedComponent,
    selectedComponent = _ref$selectedComponen === void 0 ? null : _ref$selectedComponen,
    _ref$showAISuggestion = _ref.showAISuggestions,
    showAISuggestions = _ref$showAISuggestion === void 0 ? true : _ref$showAISuggestion,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    searchTerm = _useState2[0],
    setSearchTerm = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(['layout', 'basic']),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    expandedCategories = _useState4[0],
    setExpandedCategories = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    showDescriptions = _useState6[0],
    setShowDescriptions = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    showTags = _useState8[0],
    setShowTags = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('all'),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    filterBy = _useState0[0],
    setFilterBy = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('name'),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    sortBy = _useState10[0],
    setSortBy = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    draggedComponent = _useState12[0],
    setDraggedComponent = _useState12[1];
  var dragPreviewRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);

  // AI suggestions hook
  var _useAIDesignSuggestio = (0,_hooks_useAIDesignSuggestions__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A)({
      autoRefresh: true,
      context: {
        selectedComponent: selectedComponent
      }
    }),
    suggestions = _useAIDesignSuggestio.suggestions,
    aiLoading = _useAIDesignSuggestio.loading,
    applyComponentCombination = _useAIDesignSuggestio.applyComponentCombination,
    hasLayoutSuggestions = _useAIDesignSuggestio.hasLayoutSuggestions,
    hasCombinationSuggestions = _useAIDesignSuggestio.hasCombinationSuggestions;

  // Filter and sort components
  var filteredGroups = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    var groups = [].concat(COMPONENT_GROUPS);

    // Add AI suggestions group if enabled
    if (showAISuggestions && hasCombinationSuggestions) {
      groups.unshift({
        id: 'ai',
        title: 'AI Suggestions',
        description: 'Smart component recommendations based on your current app',
        color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.accent.main,
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RobotOutlined */ .J_h, null),
        isAI: true,
        components: [] // Will be populated with AI suggestions
      });
    }

    // Filter by search term
    if (searchTerm) {
      groups = groups.map(function (group) {
        return _objectSpread(_objectSpread({}, group), {}, {
          components: group.components.filter(function (component) {
            return component.label.toLowerCase().includes(searchTerm.toLowerCase()) || component.description.toLowerCase().includes(searchTerm.toLowerCase()) || component.tags.some(function (tag) {
              return tag.toLowerCase().includes(searchTerm.toLowerCase());
            });
          })
        });
      }).filter(function (group) {
        return group.components.length > 0 || group.isAI;
      });
    }

    // Filter by complexity
    if (filterBy !== 'all') {
      groups = groups.map(function (group) {
        return _objectSpread(_objectSpread({}, group), {}, {
          components: group.components.filter(function (component) {
            return component.complexity === filterBy;
          })
        });
      }).filter(function (group) {
        return group.components.length > 0 || group.isAI;
      });
    }

    // Sort components within each group
    groups = groups.map(function (group) {
      return _objectSpread(_objectSpread({}, group), {}, {
        components: (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(group.components).sort(function (a, b) {
          switch (sortBy) {
            case 'name':
              return a.label.localeCompare(b.label);
            case 'complexity':
              var complexityOrder = {
                simple: 0,
                medium: 1,
                complex: 2
              };
              return complexityOrder[a.complexity] - complexityOrder[b.complexity];
            default:
              return 0;
          }
        })
      });
    });
    return groups;
  }, [searchTerm, filterBy, sortBy, showAISuggestions, hasCombinationSuggestions]);
  var handleDragStart = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e, component) {
    setDraggedComponent(component);

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: component.type,
      label: component.label,
      source: 'palette'
    }));
    e.dataTransfer.effectAllowed = 'copy';
    if (onDragStart) {
      onDragStart(component);
    }
  }, [onDragStart]);
  var handleDragEnd = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e) {
    setDraggedComponent(null);
    if (onDragEnd) {
      onDragEnd();
    }
  }, [onDragEnd]);
  var handleCategoryToggle = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (categoryId) {
    setExpandedCategories(function (prev) {
      return prev.includes(categoryId) ? prev.filter(function (cat) {
        return cat !== categoryId;
      }) : [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [categoryId]);
    });
  }, []);
  var handleComponentClick = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (component) {
    if (onAddComponent) {
      onAddComponent(component.type);
    }
  }, [onAddComponent]);

  // Filter menu for dropdown
  var filterMenu = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1, {
    selectedKeys: [filterBy],
    onClick: function onClick(_ref2) {
      var key = _ref2.key;
      return setFilterBy(key);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "all"
  }, "All Components"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "simple"
  }, "Simple"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "medium"
  }, "Medium"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "complex"
  }, "Complex"));

  // Sort menu for dropdown
  var sortMenu = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1, {
    selectedKeys: [sortBy],
    onClick: function onClick(_ref3) {
      var key = _ref3.key;
      return setSortBy(key);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "name"
  }, "Sort by Name"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "complexity"
  }, "Sort by Complexity"));
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PaletteContainer, {
    role: "region",
    "aria-label": "Component Palette"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PaletteHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 5,
    style: {
      margin: 0,
      color: 'white'
    }
  }, "Component Palette"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      color: 'rgba(255, 255, 255, 0.8)'
    }
  }, "Drag components to the canvas or click to add")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SearchContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Search, {
    placeholder: "Search components...",
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SearchOutlined */ .VrN, null),
    allowClear: true,
    style: {
      marginBottom: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
    },
    "aria-label": "Search components"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FilterControls, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Dropdown */ .ms, {
    overlay: filterMenu,
    trigger: ['click']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FilterOutlined */ .Lxx, null)
  }, "Filter ", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownOutlined */ .lHd, null))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Dropdown */ .ms, {
    overlay: sortMenu,
    trigger: ['click']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    size: "small"
  }, "Sort ", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownOutlined */ .lHd, null)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      fontSize: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs
    }
  }, "Descriptions:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    size: "small",
    checked: showDescriptions,
    onChange: setShowDescriptions
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ScrollableContent, null, !loading && filteredGroups.map(function (group) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      key: group.id
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(CategoryHeader, {
      onClick: function onClick() {
        return handleCategoryToggle(group.id);
      },
      tabIndex: 0,
      role: "button",
      "aria-expanded": expandedCategories.includes(group.id),
      "aria-controls": "category-".concat(group.id)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        width: 12,
        height: 12,
        borderRadius: '50%',
        backgroundColor: group.color
      }
    }), group.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", null, group.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
      count: group.components.length,
      size: "small"
    }), group.isAI && aiLoading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
      status: "processing"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownOutlined */ .lHd, {
      style: {
        transform: expandedCategories.includes(group.id) ? 'rotate(180deg)' : 'rotate(0deg)',
        transition: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"]
      }
    })), expandedCategories.includes(group.id) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentGrid, {
      id: "category-".concat(group.id)
    }, group.components.map(function (component) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentCard, {
        key: component.type,
        size: "small",
        hoverable: true,
        complexity: component.complexity,
        isDragging: (draggedComponent === null || draggedComponent === void 0 ? void 0 : draggedComponent.type) === component.type,
        draggable: true,
        onDragStart: function onDragStart(e) {
          return handleDragStart(e, component);
        },
        onDragEnd: handleDragEnd,
        onClick: function onClick() {
          return handleComponentClick(component);
        },
        tabIndex: 0,
        role: "button",
        "aria-label": "Add ".concat(component.label, " component")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentIcon, {
        className: "component-icon",
        color: group.color
      }, component.icon), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentLabel, {
        className: "component-label"
      }, component.label), showDescriptions && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentDescription, null, component.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentPreview, null, component.preview), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComplexityBadge, {
        complexity: component.complexity
      }), showTags && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          marginTop: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1]
        }
      }, component.tags.slice(0, 2).map(function (tag) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tag */ .vw, {
          key: tag,
          size: "small",
          style: {
            fontSize: '10px'
          }
        }, tag);
      })));
    })));
  })));
}

/***/ })

}]);