"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[7056],{

/***/ 11980:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   F4: () => (/* binding */ triggerFocus),
/* harmony export */   OL: () => (/* binding */ hasPrefixSuffix),
/* harmony export */   bk: () => (/* binding */ hasAddon),
/* harmony export */   gS: () => (/* binding */ resolveOnChange)
/* harmony export */ });
function hasAddon(props) {
  return !!(props.addonBefore || props.addonAfter);
}
function hasPrefixSuffix(props) {
  return !!(props.prefix || props.suffix || props.allowClear);
}

// TODO: It's better to use `Proxy` replace the `element.value`. But we still need support IE11.
function cloneEvent(event, target, value) {
  // A bug report filed on WebKit's Bugzilla tracker, dating back to 2009, specifically addresses the issue of cloneNode() not copying files of <input type="file"> elements.
  // As of the last update, this bug was still marked as "NEW," indicating that it might not have been resolved yet​​.
  // https://bugs.webkit.org/show_bug.cgi?id=28123
  var currentTarget = target.cloneNode(true);

  // click clear icon
  var newEvent = Object.create(event, {
    target: {
      value: currentTarget
    },
    currentTarget: {
      value: currentTarget
    }
  });

  // Fill data
  currentTarget.value = value;

  // Fill selection. Some type like `email` not support selection
  // https://github.com/ant-design/ant-design/issues/47833
  if (typeof target.selectionStart === 'number' && typeof target.selectionEnd === 'number') {
    currentTarget.selectionStart = target.selectionStart;
    currentTarget.selectionEnd = target.selectionEnd;
  }
  currentTarget.setSelectionRange = function () {
    target.setSelectionRange.apply(target, arguments);
  };
  return newEvent;
}
function resolveOnChange(target, e, onChange, targetValue) {
  if (!onChange) {
    return;
  }
  var event = e;
  if (e.type === 'click') {
    // Clone a new target for event.
    // Avoid the following usage, the setQuery method gets the original value.
    //
    // const [query, setQuery] = React.useState('');
    // <Input
    //   allowClear
    //   value={query}
    //   onChange={(e)=> {
    //     setQuery((prevStatus) => e.target.value);
    //   }}
    // />

    event = cloneEvent(e, target, '');
    onChange(event);
    return;
  }

  // Trigger by composition event, this means we need force change the input value
  // https://github.com/ant-design/ant-design/issues/45737
  // https://github.com/ant-design/ant-design/issues/46598
  if (target.type !== 'file' && targetValue !== undefined) {
    event = cloneEvent(e, target, targetValue);
    onChange(event);
    return;
  }
  onChange(event);
}
function triggerFocus(element, option) {
  if (!element) return;
  element.focus(option);

  // Selection content
  var _ref = option || {},
    cursor = _ref.cursor;
  if (cursor) {
    var len = element.value.length;
    switch (cursor) {
      case 'start':
        element.setSelectionRange(0, 0);
        break;
      case 'end':
        element.setSelectionRange(len, len);
        break;
      default:
        element.setSelectionRange(0, len);
    }
  }
}

/***/ }),

/***/ 22489:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ useCount)
/* harmony export */ });
/* unused harmony export inCountRange */
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89379);
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(82284);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);



var _excluded = ["show"];

/**
 * Cut `value` by the `count.max` prop.
 */
function inCountRange(value, countConfig) {
  if (!countConfig.max) {
    return true;
  }
  var count = countConfig.strategy(value);
  return count <= countConfig.max;
}
function useCount(count, showCount) {
  return react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function () {
    var mergedConfig = {};
    if (showCount) {
      mergedConfig.show = (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(showCount) === 'object' && showCount.formatter ? showCount.formatter : !!showCount;
    }
    mergedConfig = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, mergedConfig), count);
    var _ref = mergedConfig,
      show = _ref.show,
      rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref, _excluded);
    return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, rest), {}, {
      show: !!show,
      showFormatter: typeof show === 'function' ? show : undefined,
      strategy: rest.strategy || function (value) {
        return value.length;
      }
    });
  }, [count, showCount]);
}

/***/ }),

/***/ 48491:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  a: () => (/* reexport */ es_BaseInput),
  A: () => (/* binding */ es)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(89379);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/rc-input/es/utils/commonUtils.js
var commonUtils = __webpack_require__(11980);
;// ./node_modules/rc-input/es/BaseInput.js







var BaseInput = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var _props, _props2, _props3;
  var inputEl = props.inputElement,
    children = props.children,
    prefixCls = props.prefixCls,
    prefix = props.prefix,
    suffix = props.suffix,
    addonBefore = props.addonBefore,
    addonAfter = props.addonAfter,
    className = props.className,
    style = props.style,
    disabled = props.disabled,
    readOnly = props.readOnly,
    focused = props.focused,
    triggerFocus = props.triggerFocus,
    allowClear = props.allowClear,
    value = props.value,
    handleReset = props.handleReset,
    hidden = props.hidden,
    classes = props.classes,
    classNames = props.classNames,
    dataAttrs = props.dataAttrs,
    styles = props.styles,
    components = props.components,
    onClear = props.onClear;
  var inputElement = children !== null && children !== void 0 ? children : inputEl;
  var AffixWrapperComponent = (components === null || components === void 0 ? void 0 : components.affixWrapper) || 'span';
  var GroupWrapperComponent = (components === null || components === void 0 ? void 0 : components.groupWrapper) || 'span';
  var WrapperComponent = (components === null || components === void 0 ? void 0 : components.wrapper) || 'span';
  var GroupAddonComponent = (components === null || components === void 0 ? void 0 : components.groupAddon) || 'span';
  var containerRef = (0,react.useRef)(null);
  var onInputClick = function onInputClick(e) {
    var _containerRef$current;
    if ((_containerRef$current = containerRef.current) !== null && _containerRef$current !== void 0 && _containerRef$current.contains(e.target)) {
      triggerFocus === null || triggerFocus === void 0 || triggerFocus();
    }
  };
  var hasAffix = (0,commonUtils/* hasPrefixSuffix */.OL)(props);
  var element = /*#__PURE__*/(0,react.cloneElement)(inputElement, {
    value: value,
    className: classnames_default()((_props = inputElement.props) === null || _props === void 0 ? void 0 : _props.className, !hasAffix && (classNames === null || classNames === void 0 ? void 0 : classNames.variant)) || null
  });

  // ======================== Ref ======================== //
  var groupRef = (0,react.useRef)(null);
  react.useImperativeHandle(ref, function () {
    return {
      nativeElement: groupRef.current || containerRef.current
    };
  });

  // ================== Prefix & Suffix ================== //
  if (hasAffix) {
    // ================== Clear Icon ================== //
    var clearIcon = null;
    if (allowClear) {
      var needClear = !disabled && !readOnly && value;
      var clearIconCls = "".concat(prefixCls, "-clear-icon");
      var iconNode = (0,esm_typeof/* default */.A)(allowClear) === 'object' && allowClear !== null && allowClear !== void 0 && allowClear.clearIcon ? allowClear.clearIcon : '✖';
      clearIcon = /*#__PURE__*/react.createElement("button", {
        type: "button",
        tabIndex: -1,
        onClick: function onClick(event) {
          handleReset === null || handleReset === void 0 || handleReset(event);
          onClear === null || onClear === void 0 || onClear();
        }
        // Do not trigger onBlur when clear input
        // https://github.com/ant-design/ant-design/issues/31200
        ,
        onMouseDown: function onMouseDown(e) {
          return e.preventDefault();
        },
        className: classnames_default()(clearIconCls, (0,defineProperty/* default */.A)((0,defineProperty/* default */.A)({}, "".concat(clearIconCls, "-hidden"), !needClear), "".concat(clearIconCls, "-has-suffix"), !!suffix))
      }, iconNode);
    }
    var affixWrapperPrefixCls = "".concat(prefixCls, "-affix-wrapper");
    var affixWrapperCls = classnames_default()(affixWrapperPrefixCls, (0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-disabled"), disabled), "".concat(affixWrapperPrefixCls, "-disabled"), disabled), "".concat(affixWrapperPrefixCls, "-focused"), focused), "".concat(affixWrapperPrefixCls, "-readonly"), readOnly), "".concat(affixWrapperPrefixCls, "-input-with-clear-btn"), suffix && allowClear && value), classes === null || classes === void 0 ? void 0 : classes.affixWrapper, classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, classNames === null || classNames === void 0 ? void 0 : classNames.variant);
    var suffixNode = (suffix || allowClear) && /*#__PURE__*/react.createElement("span", {
      className: classnames_default()("".concat(prefixCls, "-suffix"), classNames === null || classNames === void 0 ? void 0 : classNames.suffix),
      style: styles === null || styles === void 0 ? void 0 : styles.suffix
    }, clearIcon, suffix);
    element = /*#__PURE__*/react.createElement(AffixWrapperComponent, (0,esm_extends/* default */.A)({
      className: affixWrapperCls,
      style: styles === null || styles === void 0 ? void 0 : styles.affixWrapper,
      onClick: onInputClick
    }, dataAttrs === null || dataAttrs === void 0 ? void 0 : dataAttrs.affixWrapper, {
      ref: containerRef
    }), prefix && /*#__PURE__*/react.createElement("span", {
      className: classnames_default()("".concat(prefixCls, "-prefix"), classNames === null || classNames === void 0 ? void 0 : classNames.prefix),
      style: styles === null || styles === void 0 ? void 0 : styles.prefix
    }, prefix), element, suffixNode);
  }

  // ================== Addon ================== //
  if ((0,commonUtils/* hasAddon */.bk)(props)) {
    var wrapperCls = "".concat(prefixCls, "-group");
    var addonCls = "".concat(wrapperCls, "-addon");
    var groupWrapperCls = "".concat(wrapperCls, "-wrapper");
    var mergedWrapperClassName = classnames_default()("".concat(prefixCls, "-wrapper"), wrapperCls, classes === null || classes === void 0 ? void 0 : classes.wrapper, classNames === null || classNames === void 0 ? void 0 : classNames.wrapper);
    var mergedGroupClassName = classnames_default()(groupWrapperCls, (0,defineProperty/* default */.A)({}, "".concat(groupWrapperCls, "-disabled"), disabled), classes === null || classes === void 0 ? void 0 : classes.group, classNames === null || classNames === void 0 ? void 0 : classNames.groupWrapper);

    // Need another wrapper for changing display:table to display:inline-block
    // and put style prop in wrapper
    element = /*#__PURE__*/react.createElement(GroupWrapperComponent, {
      className: mergedGroupClassName,
      ref: groupRef
    }, /*#__PURE__*/react.createElement(WrapperComponent, {
      className: mergedWrapperClassName
    }, addonBefore && /*#__PURE__*/react.createElement(GroupAddonComponent, {
      className: addonCls
    }, addonBefore), element, addonAfter && /*#__PURE__*/react.createElement(GroupAddonComponent, {
      className: addonCls
    }, addonAfter)));
  }

  // `className` and `style` are always on the root element
  return /*#__PURE__*/react.cloneElement(element, {
    className: classnames_default()((_props2 = element.props) === null || _props2 === void 0 ? void 0 : _props2.className, className) || null,
    style: (0,objectSpread2/* default */.A)((0,objectSpread2/* default */.A)({}, (_props3 = element.props) === null || _props3 === void 0 ? void 0 : _props3.style), style),
    hidden: hidden
  });
});
/* harmony default export */ const es_BaseInput = (BaseInput);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(12533);
// EXTERNAL MODULE: ./node_modules/rc-util/es/omit.js
var omit = __webpack_require__(19853);
// EXTERNAL MODULE: ./node_modules/rc-input/es/hooks/useCount.js
var useCount = __webpack_require__(22489);
;// ./node_modules/rc-input/es/Input.js






var _excluded = ["autoComplete", "onChange", "onFocus", "onBlur", "onPressEnter", "onKeyDown", "onKeyUp", "prefixCls", "disabled", "htmlSize", "className", "maxLength", "suffix", "showCount", "count", "type", "classes", "classNames", "styles", "onCompositionStart", "onCompositionEnd"];







var Input = /*#__PURE__*/(0,react.forwardRef)(function (props, ref) {
  var autoComplete = props.autoComplete,
    onChange = props.onChange,
    onFocus = props.onFocus,
    onBlur = props.onBlur,
    onPressEnter = props.onPressEnter,
    onKeyDown = props.onKeyDown,
    onKeyUp = props.onKeyUp,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-input' : _props$prefixCls,
    disabled = props.disabled,
    htmlSize = props.htmlSize,
    className = props.className,
    maxLength = props.maxLength,
    suffix = props.suffix,
    showCount = props.showCount,
    count = props.count,
    _props$type = props.type,
    type = _props$type === void 0 ? 'text' : _props$type,
    classes = props.classes,
    classNames = props.classNames,
    styles = props.styles,
    _onCompositionStart = props.onCompositionStart,
    onCompositionEnd = props.onCompositionEnd,
    rest = (0,objectWithoutProperties/* default */.A)(props, _excluded);
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    focused = _useState2[0],
    setFocused = _useState2[1];
  var compositionRef = (0,react.useRef)(false);
  var keyLockRef = (0,react.useRef)(false);
  var inputRef = (0,react.useRef)(null);
  var holderRef = (0,react.useRef)(null);
  var focus = function focus(option) {
    if (inputRef.current) {
      (0,commonUtils/* triggerFocus */.F4)(inputRef.current, option);
    }
  };

  // ====================== Value =======================
  var _useMergedState = (0,useMergedState/* default */.A)(props.defaultValue, {
      value: props.value
    }),
    _useMergedState2 = (0,slicedToArray/* default */.A)(_useMergedState, 2),
    value = _useMergedState2[0],
    setValue = _useMergedState2[1];
  var formatValue = value === undefined || value === null ? '' : String(value);

  // =================== Select Range ===================
  var _useState3 = (0,react.useState)(null),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    selection = _useState4[0],
    setSelection = _useState4[1];

  // ====================== Count =======================
  var countConfig = (0,useCount/* default */.A)(count, showCount);
  var mergedMax = countConfig.max || maxLength;
  var valueLength = countConfig.strategy(formatValue);
  var isOutOfRange = !!mergedMax && valueLength > mergedMax;

  // ======================= Ref ========================
  (0,react.useImperativeHandle)(ref, function () {
    var _holderRef$current;
    return {
      focus: focus,
      blur: function blur() {
        var _inputRef$current;
        (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 || _inputRef$current.blur();
      },
      setSelectionRange: function setSelectionRange(start, end, direction) {
        var _inputRef$current2;
        (_inputRef$current2 = inputRef.current) === null || _inputRef$current2 === void 0 || _inputRef$current2.setSelectionRange(start, end, direction);
      },
      select: function select() {
        var _inputRef$current3;
        (_inputRef$current3 = inputRef.current) === null || _inputRef$current3 === void 0 || _inputRef$current3.select();
      },
      input: inputRef.current,
      nativeElement: ((_holderRef$current = holderRef.current) === null || _holderRef$current === void 0 ? void 0 : _holderRef$current.nativeElement) || inputRef.current
    };
  });
  (0,react.useEffect)(function () {
    if (keyLockRef.current) {
      keyLockRef.current = false;
    }
    setFocused(function (prev) {
      return prev && disabled ? false : prev;
    });
  }, [disabled]);
  var triggerChange = function triggerChange(e, currentValue, info) {
    var cutValue = currentValue;
    if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {
      cutValue = countConfig.exceedFormatter(currentValue, {
        max: countConfig.max
      });
      if (currentValue !== cutValue) {
        var _inputRef$current4, _inputRef$current5;
        setSelection([((_inputRef$current4 = inputRef.current) === null || _inputRef$current4 === void 0 ? void 0 : _inputRef$current4.selectionStart) || 0, ((_inputRef$current5 = inputRef.current) === null || _inputRef$current5 === void 0 ? void 0 : _inputRef$current5.selectionEnd) || 0]);
      }
    } else if (info.source === 'compositionEnd') {
      // Avoid triggering twice
      // https://github.com/ant-design/ant-design/issues/46587
      return;
    }
    setValue(cutValue);
    if (inputRef.current) {
      (0,commonUtils/* resolveOnChange */.gS)(inputRef.current, e, onChange, cutValue);
    }
  };
  (0,react.useEffect)(function () {
    if (selection) {
      var _inputRef$current6;
      (_inputRef$current6 = inputRef.current) === null || _inputRef$current6 === void 0 || _inputRef$current6.setSelectionRange.apply(_inputRef$current6, (0,toConsumableArray/* default */.A)(selection));
    }
  }, [selection]);
  var onInternalChange = function onInternalChange(e) {
    triggerChange(e, e.target.value, {
      source: 'change'
    });
  };
  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {
    compositionRef.current = false;
    triggerChange(e, e.currentTarget.value, {
      source: 'compositionEnd'
    });
    onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);
  };
  var handleKeyDown = function handleKeyDown(e) {
    if (onPressEnter && e.key === 'Enter' && !keyLockRef.current) {
      keyLockRef.current = true;
      onPressEnter(e);
    }
    onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);
  };
  var handleKeyUp = function handleKeyUp(e) {
    if (e.key === 'Enter') {
      keyLockRef.current = false;
    }
    onKeyUp === null || onKeyUp === void 0 || onKeyUp(e);
  };
  var handleFocus = function handleFocus(e) {
    setFocused(true);
    onFocus === null || onFocus === void 0 || onFocus(e);
  };
  var handleBlur = function handleBlur(e) {
    if (keyLockRef.current) {
      keyLockRef.current = false;
    }
    setFocused(false);
    onBlur === null || onBlur === void 0 || onBlur(e);
  };
  var handleReset = function handleReset(e) {
    setValue('');
    focus();
    if (inputRef.current) {
      (0,commonUtils/* resolveOnChange */.gS)(inputRef.current, e, onChange);
    }
  };

  // ====================== Input =======================
  var outOfRangeCls = isOutOfRange && "".concat(prefixCls, "-out-of-range");
  var getInputElement = function getInputElement() {
    // Fix https://fb.me/react-unknown-prop
    var otherProps = (0,omit/* default */.A)(props, ['prefixCls', 'onPressEnter', 'addonBefore', 'addonAfter', 'prefix', 'suffix', 'allowClear',
    // Input elements must be either controlled or uncontrolled,
    // specify either the value prop, or the defaultValue prop, but not both.
    'defaultValue', 'showCount', 'count', 'classes', 'htmlSize', 'styles', 'classNames', 'onClear']);
    return /*#__PURE__*/react.createElement("input", (0,esm_extends/* default */.A)({
      autoComplete: autoComplete
    }, otherProps, {
      onChange: onInternalChange,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onKeyDown: handleKeyDown,
      onKeyUp: handleKeyUp,
      className: classnames_default()(prefixCls, (0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-disabled"), disabled), classNames === null || classNames === void 0 ? void 0 : classNames.input),
      style: styles === null || styles === void 0 ? void 0 : styles.input,
      ref: inputRef,
      size: htmlSize,
      type: type,
      onCompositionStart: function onCompositionStart(e) {
        compositionRef.current = true;
        _onCompositionStart === null || _onCompositionStart === void 0 || _onCompositionStart(e);
      },
      onCompositionEnd: onInternalCompositionEnd
    }));
  };
  var getSuffix = function getSuffix() {
    // Max length value
    var hasMaxLength = Number(mergedMax) > 0;
    if (suffix || countConfig.show) {
      var dataCount = countConfig.showFormatter ? countConfig.showFormatter({
        value: formatValue,
        count: valueLength,
        maxLength: mergedMax
      }) : "".concat(valueLength).concat(hasMaxLength ? " / ".concat(mergedMax) : '');
      return /*#__PURE__*/react.createElement(react.Fragment, null, countConfig.show && /*#__PURE__*/react.createElement("span", {
        className: classnames_default()("".concat(prefixCls, "-show-count-suffix"), (0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-show-count-has-suffix"), !!suffix), classNames === null || classNames === void 0 ? void 0 : classNames.count),
        style: (0,objectSpread2/* default */.A)({}, styles === null || styles === void 0 ? void 0 : styles.count)
      }, dataCount), suffix);
    }
    return null;
  };

  // ====================== Render ======================
  return /*#__PURE__*/react.createElement(es_BaseInput, (0,esm_extends/* default */.A)({}, rest, {
    prefixCls: prefixCls,
    className: classnames_default()(className, outOfRangeCls),
    handleReset: handleReset,
    value: formatValue,
    focused: focused,
    triggerFocus: focus,
    suffix: getSuffix(),
    disabled: disabled,
    classes: classes,
    classNames: classNames,
    styles: styles,
    ref: holderRef
  }), getInputElement());
});
/* harmony default export */ const es_Input = (Input);
;// ./node_modules/rc-input/es/index.js



/* harmony default export */ const es = (es_Input);

/***/ }),

/***/ 57056:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ rc_input_number_es)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@rc-component/mini-decimal/es/index.js + 5 modules
var es = __webpack_require__(84032);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(46942);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-input/es/index.js + 2 modules
var rc_input_es = __webpack_require__(48491);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useLayoutEffect.js
var useLayoutEffect = __webpack_require__(30981);
// EXTERNAL MODULE: ./node_modules/rc-util/es/proxyObject.js
var proxyObject = __webpack_require__(86401);
// EXTERNAL MODULE: ./node_modules/rc-util/es/ref.js
var es_ref = __webpack_require__(8719);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(68210);
;// ./node_modules/rc-input-number/es/hooks/useCursor.js


/**
 * Keep input cursor in the correct position if possible.
 * Is this necessary since we have `formatter` which may mass the content?
 */
function useCursor(input, focused) {
  var selectionRef = (0,react.useRef)(null);
  function recordCursor() {
    // Record position
    try {
      var start = input.selectionStart,
        end = input.selectionEnd,
        value = input.value;
      var beforeTxt = value.substring(0, start);
      var afterTxt = value.substring(end);
      selectionRef.current = {
        start: start,
        end: end,
        value: value,
        beforeTxt: beforeTxt,
        afterTxt: afterTxt
      };
    } catch (e) {
      // Fix error in Chrome:
      // Failed to read the 'selectionStart' property from 'HTMLInputElement'
      // http://stackoverflow.com/q/21177489/3040605
    }
  }

  /**
   * Restore logic:
   *  1. back string same
   *  2. start string same
   */
  function restoreCursor() {
    if (input && selectionRef.current && focused) {
      try {
        var value = input.value;
        var _selectionRef$current = selectionRef.current,
          beforeTxt = _selectionRef$current.beforeTxt,
          afterTxt = _selectionRef$current.afterTxt,
          start = _selectionRef$current.start;
        var startPos = value.length;
        if (value.startsWith(beforeTxt)) {
          startPos = beforeTxt.length;
        } else if (value.endsWith(afterTxt)) {
          startPos = value.length - selectionRef.current.afterTxt.length;
        } else {
          var beforeLastChar = beforeTxt[start - 1];
          var newIndex = value.indexOf(beforeLastChar, start - 1);
          if (newIndex !== -1) {
            startPos = newIndex + 1;
          }
        }
        input.setSelectionRange(startPos, startPos);
      } catch (e) {
        (0,warning/* default */.Ay)(false, "Something warning of cursor restore. Please fire issue about this: ".concat(e.message));
      }
    }
  }
  return [recordCursor, restoreCursor];
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMobile.js
var useMobile = __webpack_require__(38372);
// EXTERNAL MODULE: ./node_modules/rc-util/es/raf.js
var raf = __webpack_require__(25371);
;// ./node_modules/rc-input-number/es/StepHandler.js


/* eslint-disable react/no-unknown-property */





/**
 * When click and hold on a button - the speed of auto changing the value.
 */
var STEP_INTERVAL = 200;

/**
 * When click and hold on a button - the delay before auto changing the value.
 */
var STEP_DELAY = 600;
function StepHandler(_ref) {
  var prefixCls = _ref.prefixCls,
    upNode = _ref.upNode,
    downNode = _ref.downNode,
    upDisabled = _ref.upDisabled,
    downDisabled = _ref.downDisabled,
    onStep = _ref.onStep;
  // ======================== Step ========================
  var stepTimeoutRef = react.useRef();
  var frameIds = react.useRef([]);
  var onStepRef = react.useRef();
  onStepRef.current = onStep;
  var onStopStep = function onStopStep() {
    clearTimeout(stepTimeoutRef.current);
  };

  // We will interval update step when hold mouse down
  var onStepMouseDown = function onStepMouseDown(e, up) {
    e.preventDefault();
    onStopStep();
    onStepRef.current(up);

    // Loop step for interval
    function loopStep() {
      onStepRef.current(up);
      stepTimeoutRef.current = setTimeout(loopStep, STEP_INTERVAL);
    }

    // First time press will wait some time to trigger loop step update
    stepTimeoutRef.current = setTimeout(loopStep, STEP_DELAY);
  };
  react.useEffect(function () {
    return function () {
      onStopStep();
      frameIds.current.forEach(function (id) {
        return raf/* default */.A.cancel(id);
      });
    };
  }, []);

  // ======================= Render =======================
  var isMobile = (0,useMobile/* default */.A)();
  if (isMobile) {
    return null;
  }
  var handlerClassName = "".concat(prefixCls, "-handler");
  var upClassName = classnames_default()(handlerClassName, "".concat(handlerClassName, "-up"), (0,defineProperty/* default */.A)({}, "".concat(handlerClassName, "-up-disabled"), upDisabled));
  var downClassName = classnames_default()(handlerClassName, "".concat(handlerClassName, "-down"), (0,defineProperty/* default */.A)({}, "".concat(handlerClassName, "-down-disabled"), downDisabled));

  // fix: https://github.com/ant-design/ant-design/issues/43088
  // In Safari, When we fire onmousedown and onmouseup events in quick succession, 
  // there may be a problem that the onmouseup events are executed first, 
  // resulting in a disordered program execution.
  // So, we need to use requestAnimationFrame to ensure that the onmouseup event is executed after the onmousedown event.
  var safeOnStopStep = function safeOnStopStep() {
    return frameIds.current.push((0,raf/* default */.A)(onStopStep));
  };
  var sharedHandlerProps = {
    unselectable: 'on',
    role: 'button',
    onMouseUp: safeOnStopStep,
    onMouseLeave: safeOnStopStep
  };
  return /*#__PURE__*/react.createElement("div", {
    className: "".concat(handlerClassName, "-wrap")
  }, /*#__PURE__*/react.createElement("span", (0,esm_extends/* default */.A)({}, sharedHandlerProps, {
    onMouseDown: function onMouseDown(e) {
      onStepMouseDown(e, true);
    },
    "aria-label": "Increase Value",
    "aria-disabled": upDisabled,
    className: upClassName
  }), upNode || /*#__PURE__*/react.createElement("span", {
    unselectable: "on",
    className: "".concat(prefixCls, "-handler-up-inner")
  })), /*#__PURE__*/react.createElement("span", (0,esm_extends/* default */.A)({}, sharedHandlerProps, {
    onMouseDown: function onMouseDown(e) {
      onStepMouseDown(e, false);
    },
    "aria-label": "Decrease Value",
    "aria-disabled": downDisabled,
    className: downClassName
  }), downNode || /*#__PURE__*/react.createElement("span", {
    unselectable: "on",
    className: "".concat(prefixCls, "-handler-down-inner")
  })));
}
;// ./node_modules/rc-input-number/es/utils/numberUtil.js

function getDecupleSteps(step) {
  var stepStr = typeof step === 'number' ? (0,es/* num2str */.yH)(step) : (0,es/* trimNumber */.OM)(step).fullStr;
  var hasPoint = stepStr.includes('.');
  if (!hasPoint) {
    return step + '0';
  }
  return (0,es/* trimNumber */.OM)(stepStr.replace(/(\d)\.(\d)/g, '$1$2.')).fullStr;
}
// EXTERNAL MODULE: ./node_modules/rc-input/es/utils/commonUtils.js
var commonUtils = __webpack_require__(11980);
;// ./node_modules/rc-input-number/es/hooks/useFrame.js



/**
 * Always trigger latest once when call multiple time
 */
/* harmony default export */ const useFrame = (function () {
  var idRef = (0,react.useRef)(0);
  var cleanUp = function cleanUp() {
    raf/* default */.A.cancel(idRef.current);
  };
  (0,react.useEffect)(function () {
    return cleanUp;
  }, []);
  return function (callback) {
    cleanUp();
    idRef.current = (0,raf/* default */.A)(function () {
      callback();
    });
  };
});
;// ./node_modules/rc-input-number/es/InputNumber.js





var _excluded = ["prefixCls", "className", "style", "min", "max", "step", "defaultValue", "value", "disabled", "readOnly", "upHandler", "downHandler", "keyboard", "changeOnWheel", "controls", "classNames", "stringMode", "parser", "formatter", "precision", "decimalSeparator", "onChange", "onInput", "onPressEnter", "onStep", "changeOnBlur", "domRef"],
  _excluded2 = ["disabled", "style", "prefixCls", "value", "prefix", "suffix", "addonBefore", "addonAfter", "className", "classNames"];












/**
 * We support `stringMode` which need handle correct type when user call in onChange
 * format max or min value
 * 1. if isInvalid return null
 * 2. if precision is undefined, return decimal
 * 3. format with precision
 *    I. if max > 0, round down with precision. Example: max= 3.5, precision=0  afterFormat: 3
 *    II. if max < 0, round up with precision. Example: max= -3.5, precision=0  afterFormat: -4
 *    III. if min > 0, round up with precision. Example: min= 3.5, precision=0  afterFormat: 4
 *    IV. if min < 0, round down with precision. Example: max= -3.5, precision=0  afterFormat: -3
 */
var getDecimalValue = function getDecimalValue(stringMode, decimalValue) {
  if (stringMode || decimalValue.isEmpty()) {
    return decimalValue.toString();
  }
  return decimalValue.toNumber();
};
var getDecimalIfValidate = function getDecimalIfValidate(value) {
  var decimal = (0,es/* default */.Ay)(value);
  return decimal.isInvalidate() ? null : decimal;
};
var InternalInputNumber = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var prefixCls = props.prefixCls,
    className = props.className,
    style = props.style,
    min = props.min,
    max = props.max,
    _props$step = props.step,
    step = _props$step === void 0 ? 1 : _props$step,
    defaultValue = props.defaultValue,
    value = props.value,
    disabled = props.disabled,
    readOnly = props.readOnly,
    upHandler = props.upHandler,
    downHandler = props.downHandler,
    keyboard = props.keyboard,
    _props$changeOnWheel = props.changeOnWheel,
    changeOnWheel = _props$changeOnWheel === void 0 ? false : _props$changeOnWheel,
    _props$controls = props.controls,
    controls = _props$controls === void 0 ? true : _props$controls,
    classNames = props.classNames,
    stringMode = props.stringMode,
    parser = props.parser,
    formatter = props.formatter,
    precision = props.precision,
    decimalSeparator = props.decimalSeparator,
    onChange = props.onChange,
    onInput = props.onInput,
    onPressEnter = props.onPressEnter,
    onStep = props.onStep,
    _props$changeOnBlur = props.changeOnBlur,
    changeOnBlur = _props$changeOnBlur === void 0 ? true : _props$changeOnBlur,
    domRef = props.domRef,
    inputProps = (0,objectWithoutProperties/* default */.A)(props, _excluded);
  var inputClassName = "".concat(prefixCls, "-input");
  var inputRef = react.useRef(null);
  var _React$useState = react.useState(false),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    focus = _React$useState2[0],
    setFocus = _React$useState2[1];
  var userTypingRef = react.useRef(false);
  var compositionRef = react.useRef(false);
  var shiftKeyRef = react.useRef(false);

  // ============================ Value =============================
  // Real value control
  var _React$useState3 = react.useState(function () {
      return (0,es/* default */.Ay)(value !== null && value !== void 0 ? value : defaultValue);
    }),
    _React$useState4 = (0,slicedToArray/* default */.A)(_React$useState3, 2),
    decimalValue = _React$useState4[0],
    setDecimalValue = _React$useState4[1];
  function setUncontrolledDecimalValue(newDecimal) {
    if (value === undefined) {
      setDecimalValue(newDecimal);
    }
  }

  // ====================== Parser & Formatter ======================
  /**
   * `precision` is used for formatter & onChange.
   * It will auto generate by `value` & `step`.
   * But it will not block user typing.
   *
   * Note: Auto generate `precision` is used for legacy logic.
   * We should remove this since we already support high precision with BigInt.
   *
   * @param number  Provide which number should calculate precision
   * @param userTyping  Change by user typing
   */
  var getPrecision = react.useCallback(function (numStr, userTyping) {
    if (userTyping) {
      return undefined;
    }
    if (precision >= 0) {
      return precision;
    }
    return Math.max((0,es/* getNumberPrecision */.i5)(numStr), (0,es/* getNumberPrecision */.i5)(step));
  }, [precision, step]);

  // >>> Parser
  var mergedParser = react.useCallback(function (num) {
    var numStr = String(num);
    if (parser) {
      return parser(numStr);
    }
    var parsedStr = numStr;
    if (decimalSeparator) {
      parsedStr = parsedStr.replace(decimalSeparator, '.');
    }

    // [Legacy] We still support auto convert `$ 123,456` to `123456`
    return parsedStr.replace(/[^\w.-]+/g, '');
  }, [parser, decimalSeparator]);

  // >>> Formatter
  var inputValueRef = react.useRef('');
  var mergedFormatter = react.useCallback(function (number, userTyping) {
    if (formatter) {
      return formatter(number, {
        userTyping: userTyping,
        input: String(inputValueRef.current)
      });
    }
    var str = typeof number === 'number' ? (0,es/* num2str */.yH)(number) : number;

    // User typing will not auto format with precision directly
    if (!userTyping) {
      var mergedPrecision = getPrecision(str, userTyping);
      if ((0,es/* validateNumber */.yb)(str) && (decimalSeparator || mergedPrecision >= 0)) {
        // Separator
        var separatorStr = decimalSeparator || '.';
        str = (0,es/* toFixed */.Mg)(str, separatorStr, mergedPrecision);
      }
    }
    return str;
  }, [formatter, getPrecision, decimalSeparator]);

  // ========================== InputValue ==========================
  /**
   * Input text value control
   *
   * User can not update input content directly. It updates with follow rules by priority:
   *  1. controlled `value` changed
   *    * [SPECIAL] Typing like `1.` should not immediately convert to `1`
   *  2. User typing with format (not precision)
   *  3. Blur or Enter trigger revalidate
   */
  var _React$useState5 = react.useState(function () {
      var initValue = defaultValue !== null && defaultValue !== void 0 ? defaultValue : value;
      if (decimalValue.isInvalidate() && ['string', 'number'].includes((0,esm_typeof/* default */.A)(initValue))) {
        return Number.isNaN(initValue) ? '' : initValue;
      }
      return mergedFormatter(decimalValue.toString(), false);
    }),
    _React$useState6 = (0,slicedToArray/* default */.A)(_React$useState5, 2),
    inputValue = _React$useState6[0],
    setInternalInputValue = _React$useState6[1];
  inputValueRef.current = inputValue;

  // Should always be string
  function setInputValue(newValue, userTyping) {
    setInternalInputValue(mergedFormatter(
    // Invalidate number is sometime passed by external control, we should let it go
    // Otherwise is controlled by internal interactive logic which check by userTyping
    // You can ref 'show limited value when input is not focused' test for more info.
    newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping), userTyping));
  }

  // >>> Max & Min limit
  var maxDecimal = react.useMemo(function () {
    return getDecimalIfValidate(max);
  }, [max, precision]);
  var minDecimal = react.useMemo(function () {
    return getDecimalIfValidate(min);
  }, [min, precision]);
  var upDisabled = react.useMemo(function () {
    if (!maxDecimal || !decimalValue || decimalValue.isInvalidate()) {
      return false;
    }
    return maxDecimal.lessEquals(decimalValue);
  }, [maxDecimal, decimalValue]);
  var downDisabled = react.useMemo(function () {
    if (!minDecimal || !decimalValue || decimalValue.isInvalidate()) {
      return false;
    }
    return decimalValue.lessEquals(minDecimal);
  }, [minDecimal, decimalValue]);

  // Cursor controller
  var _useCursor = useCursor(inputRef.current, focus),
    _useCursor2 = (0,slicedToArray/* default */.A)(_useCursor, 2),
    recordCursor = _useCursor2[0],
    restoreCursor = _useCursor2[1];

  // ============================= Data =============================
  /**
   * Find target value closet within range.
   * e.g. [11, 28]:
   *    3  => 11
   *    23 => 23
   *    99 => 28
   */
  var getRangeValue = function getRangeValue(target) {
    // target > max
    if (maxDecimal && !target.lessEquals(maxDecimal)) {
      return maxDecimal;
    }

    // target < min
    if (minDecimal && !minDecimal.lessEquals(target)) {
      return minDecimal;
    }
    return null;
  };

  /**
   * Check value is in [min, max] range
   */
  var isInRange = function isInRange(target) {
    return !getRangeValue(target);
  };

  /**
   * Trigger `onChange` if value validated and not equals of origin.
   * Return the value that re-align in range.
   */
  var triggerValueUpdate = function triggerValueUpdate(newValue, userTyping) {
    var updateValue = newValue;
    var isRangeValidate = isInRange(updateValue) || updateValue.isEmpty();

    // Skip align value when trigger value is empty.
    // We just trigger onChange(null)
    // This should not block user typing
    if (!updateValue.isEmpty() && !userTyping) {
      // Revert value in range if needed
      updateValue = getRangeValue(updateValue) || updateValue;
      isRangeValidate = true;
    }
    if (!readOnly && !disabled && isRangeValidate) {
      var numStr = updateValue.toString();
      var mergedPrecision = getPrecision(numStr, userTyping);
      if (mergedPrecision >= 0) {
        updateValue = (0,es/* default */.Ay)((0,es/* toFixed */.Mg)(numStr, '.', mergedPrecision));

        // When to fixed. The value may out of min & max range.
        // 4 in [0, 3.8] => 3.8 => 4 (toFixed)
        if (!isInRange(updateValue)) {
          updateValue = (0,es/* default */.Ay)((0,es/* toFixed */.Mg)(numStr, '.', mergedPrecision, true));
        }
      }

      // Trigger event
      if (!updateValue.equals(decimalValue)) {
        setUncontrolledDecimalValue(updateValue);
        onChange === null || onChange === void 0 || onChange(updateValue.isEmpty() ? null : getDecimalValue(stringMode, updateValue));

        // Reformat input if value is not controlled
        if (value === undefined) {
          setInputValue(updateValue, userTyping);
        }
      }
      return updateValue;
    }
    return decimalValue;
  };

  // ========================== User Input ==========================
  var onNextPromise = useFrame();

  // >>> Collect input value
  var collectInputValue = function collectInputValue(inputStr) {
    recordCursor();

    // Update inputValue in case input can not parse as number
    // Refresh ref value immediately since it may used by formatter
    inputValueRef.current = inputStr;
    setInternalInputValue(inputStr);

    // Parse number
    if (!compositionRef.current) {
      var finalValue = mergedParser(inputStr);
      var finalDecimal = (0,es/* default */.Ay)(finalValue);
      if (!finalDecimal.isNaN()) {
        triggerValueUpdate(finalDecimal, true);
      }
    }

    // Trigger onInput later to let user customize value if they want to handle something after onChange
    onInput === null || onInput === void 0 || onInput(inputStr);

    // optimize for chinese input experience
    // https://github.com/ant-design/ant-design/issues/8196
    onNextPromise(function () {
      var nextInputStr = inputStr;
      if (!parser) {
        nextInputStr = inputStr.replace(/。/g, '.');
      }
      if (nextInputStr !== inputStr) {
        collectInputValue(nextInputStr);
      }
    });
  };

  // >>> Composition
  var onCompositionStart = function onCompositionStart() {
    compositionRef.current = true;
  };
  var onCompositionEnd = function onCompositionEnd() {
    compositionRef.current = false;
    collectInputValue(inputRef.current.value);
  };

  // >>> Input
  var onInternalInput = function onInternalInput(e) {
    collectInputValue(e.target.value);
  };

  // ============================= Step =============================
  var onInternalStep = function onInternalStep(up) {
    var _inputRef$current;
    // Ignore step since out of range
    if (up && upDisabled || !up && downDisabled) {
      return;
    }

    // Clear typing status since it may be caused by up & down key.
    // We should sync with input value.
    userTypingRef.current = false;
    var stepDecimal = (0,es/* default */.Ay)(shiftKeyRef.current ? getDecupleSteps(step) : step);
    if (!up) {
      stepDecimal = stepDecimal.negate();
    }
    var target = (decimalValue || (0,es/* default */.Ay)(0)).add(stepDecimal.toString());
    var updatedValue = triggerValueUpdate(target, false);
    onStep === null || onStep === void 0 || onStep(getDecimalValue(stringMode, updatedValue), {
      offset: shiftKeyRef.current ? getDecupleSteps(step) : step,
      type: up ? 'up' : 'down'
    });
    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 || _inputRef$current.focus();
  };

  // ============================ Flush =============================
  /**
   * Flush current input content to trigger value change & re-formatter input if needed.
   * This will always flush input value for update.
   * If it's invalidate, will fallback to last validate value.
   */
  var flushInputValue = function flushInputValue(userTyping) {
    var parsedValue = (0,es/* default */.Ay)(mergedParser(inputValue));
    var formatValue;
    if (!parsedValue.isNaN()) {
      // Only validate value or empty value can be re-fill to inputValue
      // Reassign the formatValue within ranged of trigger control
      formatValue = triggerValueUpdate(parsedValue, userTyping);
    } else {
      formatValue = triggerValueUpdate(decimalValue, userTyping);
    }
    if (value !== undefined) {
      // Reset back with controlled value first
      setInputValue(decimalValue, false);
    } else if (!formatValue.isNaN()) {
      // Reset input back since no validate value
      setInputValue(formatValue, false);
    }
  };

  // Solve the issue of the event triggering sequence when entering numbers in chinese input (Safari)
  var onBeforeInput = function onBeforeInput() {
    userTypingRef.current = true;
  };
  var onKeyDown = function onKeyDown(event) {
    var key = event.key,
      shiftKey = event.shiftKey;
    userTypingRef.current = true;
    shiftKeyRef.current = shiftKey;
    if (key === 'Enter') {
      if (!compositionRef.current) {
        userTypingRef.current = false;
      }
      flushInputValue(false);
      onPressEnter === null || onPressEnter === void 0 || onPressEnter(event);
    }
    if (keyboard === false) {
      return;
    }

    // Do step
    if (!compositionRef.current && ['Up', 'ArrowUp', 'Down', 'ArrowDown'].includes(key)) {
      onInternalStep(key === 'Up' || key === 'ArrowUp');
      event.preventDefault();
    }
  };
  var onKeyUp = function onKeyUp() {
    userTypingRef.current = false;
    shiftKeyRef.current = false;
  };
  react.useEffect(function () {
    if (changeOnWheel && focus) {
      var onWheel = function onWheel(event) {
        // moving mouse wheel rises wheel event with deltaY < 0
        // scroll value grows from top to bottom, as screen Y coordinate
        onInternalStep(event.deltaY < 0);
        event.preventDefault();
      };
      var input = inputRef.current;
      if (input) {
        // React onWheel is passive and we can't preventDefault() in it.
        // That's why we should subscribe with DOM listener
        // https://stackoverflow.com/questions/63663025/react-onwheel-handler-cant-preventdefault-because-its-a-passive-event-listenev
        input.addEventListener('wheel', onWheel, {
          passive: false
        });
        return function () {
          return input.removeEventListener('wheel', onWheel);
        };
      }
    }
  });

  // >>> Focus & Blur
  var onBlur = function onBlur() {
    if (changeOnBlur) {
      flushInputValue(false);
    }
    setFocus(false);
    userTypingRef.current = false;
  };

  // ========================== Controlled ==========================
  // Input by precision & formatter
  (0,useLayoutEffect/* useLayoutUpdateEffect */.o)(function () {
    if (!decimalValue.isInvalidate()) {
      setInputValue(decimalValue, false);
    }
  }, [precision, formatter]);

  // Input by value
  (0,useLayoutEffect/* useLayoutUpdateEffect */.o)(function () {
    var newValue = (0,es/* default */.Ay)(value);
    setDecimalValue(newValue);
    var currentParsedValue = (0,es/* default */.Ay)(mergedParser(inputValue));

    // When user typing from `1.2` to `1.`, we should not convert to `1` immediately.
    // But let it go if user set `formatter`
    if (!newValue.equals(currentParsedValue) || !userTypingRef.current || formatter) {
      // Update value as effect
      setInputValue(newValue, userTypingRef.current);
    }
  }, [value]);

  // ============================ Cursor ============================
  (0,useLayoutEffect/* useLayoutUpdateEffect */.o)(function () {
    if (formatter) {
      restoreCursor();
    }
  }, [inputValue]);

  // ============================ Render ============================
  return /*#__PURE__*/react.createElement("div", {
    ref: domRef,
    className: classnames_default()(prefixCls, className, (0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)({}, "".concat(prefixCls, "-focused"), focus), "".concat(prefixCls, "-disabled"), disabled), "".concat(prefixCls, "-readonly"), readOnly), "".concat(prefixCls, "-not-a-number"), decimalValue.isNaN()), "".concat(prefixCls, "-out-of-range"), !decimalValue.isInvalidate() && !isInRange(decimalValue))),
    style: style,
    onFocus: function onFocus() {
      setFocus(true);
    },
    onBlur: onBlur,
    onKeyDown: onKeyDown,
    onKeyUp: onKeyUp,
    onCompositionStart: onCompositionStart,
    onCompositionEnd: onCompositionEnd,
    onBeforeInput: onBeforeInput
  }, controls && /*#__PURE__*/react.createElement(StepHandler, {
    prefixCls: prefixCls,
    upNode: upHandler,
    downNode: downHandler,
    upDisabled: upDisabled,
    downDisabled: downDisabled,
    onStep: onInternalStep
  }), /*#__PURE__*/react.createElement("div", {
    className: "".concat(inputClassName, "-wrap")
  }, /*#__PURE__*/react.createElement("input", (0,esm_extends/* default */.A)({
    autoComplete: "off",
    role: "spinbutton",
    "aria-valuemin": min,
    "aria-valuemax": max,
    "aria-valuenow": decimalValue.isInvalidate() ? null : decimalValue.toString(),
    step: step
  }, inputProps, {
    ref: (0,es_ref/* composeRef */.K4)(inputRef, ref),
    className: inputClassName,
    value: inputValue,
    onChange: onInternalInput,
    disabled: disabled,
    readOnly: readOnly
  }))));
});
var InputNumber = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var disabled = props.disabled,
    style = props.style,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-input-number' : _props$prefixCls,
    value = props.value,
    prefix = props.prefix,
    suffix = props.suffix,
    addonBefore = props.addonBefore,
    addonAfter = props.addonAfter,
    className = props.className,
    classNames = props.classNames,
    rest = (0,objectWithoutProperties/* default */.A)(props, _excluded2);
  var holderRef = react.useRef(null);
  var inputNumberDomRef = react.useRef(null);
  var inputFocusRef = react.useRef(null);
  var focus = function focus(option) {
    if (inputFocusRef.current) {
      (0,commonUtils/* triggerFocus */.F4)(inputFocusRef.current, option);
    }
  };
  react.useImperativeHandle(ref, function () {
    return (0,proxyObject/* default */.A)(inputFocusRef.current, {
      focus: focus,
      nativeElement: holderRef.current.nativeElement || inputNumberDomRef.current
    });
  });
  return /*#__PURE__*/react.createElement(rc_input_es/* BaseInput */.a, {
    className: className,
    triggerFocus: focus,
    prefixCls: prefixCls,
    value: value,
    disabled: disabled,
    style: style,
    prefix: prefix,
    suffix: suffix,
    addonAfter: addonAfter,
    addonBefore: addonBefore,
    classNames: classNames,
    components: {
      affixWrapper: 'div',
      groupWrapper: 'div',
      wrapper: 'div',
      groupAddon: 'div'
    },
    ref: holderRef
  }, /*#__PURE__*/react.createElement(InternalInputNumber, (0,esm_extends/* default */.A)({
    prefixCls: prefixCls,
    disabled: disabled,
    ref: inputFocusRef,
    domRef: inputNumberDomRef,
    className: classNames === null || classNames === void 0 ? void 0 : classNames.input
  }, rest)));
});
if (false) {}
/* harmony default export */ const es_InputNumber = (InputNumber);
;// ./node_modules/rc-input-number/es/index.js

/* harmony default export */ const rc_input_number_es = (es_InputNumber);

/***/ })

}]);