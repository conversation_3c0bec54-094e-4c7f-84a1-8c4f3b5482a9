"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[473],{

/***/ 16050:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*=\"language-\"]": {
    "color": "white",
    "background": "none",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontSize": "1em",
    "textAlign": "left",
    "textShadow": "0 -.1em .2em black",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "white",
    "background": "hsl(0, 0%, 8%)",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontSize": "1em",
    "textAlign": "left",
    "textShadow": "0 -.1em .2em black",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "borderRadius": ".5em",
    "border": ".3em solid hsl(0, 0%, 33%)",
    "boxShadow": "1px 1px .5em black inset",
    "margin": ".5em 0",
    "overflow": "auto",
    "padding": "1em"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "background": "hsl(0, 0%, 8%)",
    "borderRadius": ".3em",
    "border": ".13em solid hsl(0, 0%, 33%)",
    "boxShadow": "1px 1px .3em -.1em black inset",
    "padding": ".15em .2em .05em",
    "whiteSpace": "normal"
  },
  "pre[class*=\"language-\"]::-moz-selection": {
    "background": "hsla(0, 0%, 93%, 0.15)",
    "textShadow": "none"
  },
  "pre[class*=\"language-\"]::selection": {
    "background": "hsla(0, 0%, 93%, 0.15)",
    "textShadow": "none"
  },
  "pre[class*=\"language-\"] ::-moz-selection": {
    "textShadow": "none",
    "background": "hsla(0, 0%, 93%, 0.15)"
  },
  "code[class*=\"language-\"]::-moz-selection": {
    "textShadow": "none",
    "background": "hsla(0, 0%, 93%, 0.15)"
  },
  "code[class*=\"language-\"] ::-moz-selection": {
    "textShadow": "none",
    "background": "hsla(0, 0%, 93%, 0.15)"
  },
  "pre[class*=\"language-\"] ::selection": {
    "textShadow": "none",
    "background": "hsla(0, 0%, 93%, 0.15)"
  },
  "code[class*=\"language-\"]::selection": {
    "textShadow": "none",
    "background": "hsla(0, 0%, 93%, 0.15)"
  },
  "code[class*=\"language-\"] ::selection": {
    "textShadow": "none",
    "background": "hsla(0, 0%, 93%, 0.15)"
  },
  "comment": {
    "color": "hsl(0, 0%, 47%)"
  },
  "prolog": {
    "color": "hsl(0, 0%, 47%)"
  },
  "doctype": {
    "color": "hsl(0, 0%, 47%)"
  },
  "cdata": {
    "color": "hsl(0, 0%, 47%)"
  },
  "punctuation": {
    "Opacity": ".7"
  },
  "namespace": {
    "Opacity": ".7"
  },
  "tag": {
    "color": "hsl(14, 58%, 55%)"
  },
  "boolean": {
    "color": "hsl(14, 58%, 55%)"
  },
  "number": {
    "color": "hsl(14, 58%, 55%)"
  },
  "deleted": {
    "color": "hsl(14, 58%, 55%)"
  },
  "keyword": {
    "color": "hsl(53, 89%, 79%)"
  },
  "property": {
    "color": "hsl(53, 89%, 79%)"
  },
  "selector": {
    "color": "hsl(53, 89%, 79%)"
  },
  "constant": {
    "color": "hsl(53, 89%, 79%)"
  },
  "symbol": {
    "color": "hsl(53, 89%, 79%)"
  },
  "builtin": {
    "color": "hsl(53, 89%, 79%)"
  },
  "attr-name": {
    "color": "hsl(76, 21%, 52%)"
  },
  "attr-value": {
    "color": "hsl(76, 21%, 52%)"
  },
  "string": {
    "color": "hsl(76, 21%, 52%)"
  },
  "char": {
    "color": "hsl(76, 21%, 52%)"
  },
  "operator": {
    "color": "hsl(76, 21%, 52%)"
  },
  "entity": {
    "color": "hsl(76, 21%, 52%)",
    "cursor": "help"
  },
  "url": {
    "color": "hsl(76, 21%, 52%)"
  },
  ".language-css .token.string": {
    "color": "hsl(76, 21%, 52%)"
  },
  ".style .token.string": {
    "color": "hsl(76, 21%, 52%)"
  },
  "variable": {
    "color": "hsl(76, 21%, 52%)"
  },
  "inserted": {
    "color": "hsl(76, 21%, 52%)"
  },
  "atrule": {
    "color": "hsl(218, 22%, 55%)"
  },
  "regex": {
    "color": "hsl(42, 75%, 65%)"
  },
  "important": {
    "color": "hsl(42, 75%, 65%)",
    "fontWeight": "bold"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  },
  ".language-markup .token.tag": {
    "color": "hsl(33, 33%, 52%)"
  },
  ".language-markup .token.attr-name": {
    "color": "hsl(33, 33%, 52%)"
  },
  ".language-markup .token.punctuation": {
    "color": "hsl(33, 33%, 52%)"
  },
  "": {
    "position": "relative",
    "zIndex": "1"
  },
  ".line-highlight.line-highlight": {
    "background": "linear-gradient(to right, hsla(0, 0%, 33%, .1) 70%, hsla(0, 0%, 33%, 0))",
    "borderBottom": "1px dashed hsl(0, 0%, 33%)",
    "borderTop": "1px dashed hsl(0, 0%, 33%)",
    "marginTop": "0.75em",
    "zIndex": "0"
  },
  ".line-highlight.line-highlight:before": {
    "backgroundColor": "hsl(215, 15%, 59%)",
    "color": "hsl(24, 20%, 95%)"
  },
  ".line-highlight.line-highlight[data-end]:after": {
    "backgroundColor": "hsl(215, 15%, 59%)",
    "color": "hsl(24, 20%, 95%)"
  }
});

/***/ }),

/***/ 19337:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*=\"language-\"]": {
    "color": "#393A34",
    "fontFamily": "\"Consolas\", \"Bitstream Vera Sans Mono\", \"Courier New\", Courier, monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "fontSize": ".9em",
    "lineHeight": "1.2em",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#393A34",
    "fontFamily": "\"Consolas\", \"Bitstream Vera Sans Mono\", \"Courier New\", Courier, monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "fontSize": ".9em",
    "lineHeight": "1.2em",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "border": "1px solid #dddddd",
    "backgroundColor": "white"
  },
  "pre > code[class*=\"language-\"]": {
    "fontSize": "1em"
  },
  "pre[class*=\"language-\"]::-moz-selection": {
    "background": "#C1DEF1"
  },
  "pre[class*=\"language-\"] ::-moz-selection": {
    "background": "#C1DEF1"
  },
  "code[class*=\"language-\"]::-moz-selection": {
    "background": "#C1DEF1"
  },
  "code[class*=\"language-\"] ::-moz-selection": {
    "background": "#C1DEF1"
  },
  "pre[class*=\"language-\"]::selection": {
    "background": "#C1DEF1"
  },
  "pre[class*=\"language-\"] ::selection": {
    "background": "#C1DEF1"
  },
  "code[class*=\"language-\"]::selection": {
    "background": "#C1DEF1"
  },
  "code[class*=\"language-\"] ::selection": {
    "background": "#C1DEF1"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "padding": ".2em",
    "paddingTop": "1px",
    "paddingBottom": "1px",
    "background": "#f8f8f8",
    "border": "1px solid #dddddd"
  },
  "comment": {
    "color": "#008000",
    "fontStyle": "italic"
  },
  "prolog": {
    "color": "#008000",
    "fontStyle": "italic"
  },
  "doctype": {
    "color": "#008000",
    "fontStyle": "italic"
  },
  "cdata": {
    "color": "#008000",
    "fontStyle": "italic"
  },
  "namespace": {
    "Opacity": ".7"
  },
  "string": {
    "color": "#A31515"
  },
  "punctuation": {
    "color": "#393A34"
  },
  "operator": {
    "color": "#393A34"
  },
  "url": {
    "color": "#36acaa"
  },
  "symbol": {
    "color": "#36acaa"
  },
  "number": {
    "color": "#36acaa"
  },
  "boolean": {
    "color": "#36acaa"
  },
  "variable": {
    "color": "#36acaa"
  },
  "constant": {
    "color": "#36acaa"
  },
  "inserted": {
    "color": "#36acaa"
  },
  "atrule": {
    "color": "#0000ff"
  },
  "keyword": {
    "color": "#0000ff"
  },
  "attr-value": {
    "color": "#0000ff"
  },
  ".language-autohotkey .token.selector": {
    "color": "#0000ff"
  },
  ".language-json .token.boolean": {
    "color": "#0000ff"
  },
  ".language-json .token.number": {
    "color": "#0000ff"
  },
  "code[class*=\"language-css\"]": {
    "color": "#0000ff"
  },
  "function": {
    "color": "#393A34"
  },
  "deleted": {
    "color": "#9a050f"
  },
  ".language-autohotkey .token.tag": {
    "color": "#9a050f"
  },
  "selector": {
    "color": "#800000"
  },
  ".language-autohotkey .token.keyword": {
    "color": "#00009f"
  },
  "important": {
    "color": "#e90",
    "fontWeight": "bold"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "class-name": {
    "color": "#2B91AF"
  },
  ".language-json .token.property": {
    "color": "#2B91AF"
  },
  "tag": {
    "color": "#800000"
  },
  "attr-name": {
    "color": "#ff0000"
  },
  "property": {
    "color": "#ff0000"
  },
  "regex": {
    "color": "#ff0000"
  },
  "entity": {
    "color": "#ff0000"
  },
  "directive.tag.tag": {
    "background": "#ffff00",
    "color": "#393A34"
  },
  ".line-numbers.line-numbers .line-numbers-rows": {
    "borderRightColor": "#a5a5a5"
  },
  ".line-numbers .line-numbers-rows > span:before": {
    "color": "#2B91AF"
  },
  ".line-highlight.line-highlight": {
    "background": "linear-gradient(to right, rgba(193, 222, 241, 0.2) 70%, rgba(221, 222, 241, 0))"
  }
});

/***/ }),

/***/ 30222:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  "pre[class*=\"language-\"]": {
    "color": "#d4d4d4",
    "fontSize": "13px",
    "textShadow": "none",
    "fontFamily": "Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "background": "#1e1e1e"
  },
  "code[class*=\"language-\"]": {
    "color": "#d4d4d4",
    "fontSize": "13px",
    "textShadow": "none",
    "fontFamily": "Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]::selection": {
    "textShadow": "none",
    "background": "#264F78"
  },
  "code[class*=\"language-\"]::selection": {
    "textShadow": "none",
    "background": "#264F78"
  },
  "pre[class*=\"language-\"] *::selection": {
    "textShadow": "none",
    "background": "#264F78"
  },
  "code[class*=\"language-\"] *::selection": {
    "textShadow": "none",
    "background": "#264F78"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "padding": ".1em .3em",
    "borderRadius": ".3em",
    "color": "#db4c69",
    "background": "#1e1e1e"
  },
  ".namespace": {
    "Opacity": ".7"
  },
  "doctype.doctype-tag": {
    "color": "#569CD6"
  },
  "doctype.name": {
    "color": "#9cdcfe"
  },
  "comment": {
    "color": "#6a9955"
  },
  "prolog": {
    "color": "#6a9955"
  },
  "punctuation": {
    "color": "#d4d4d4"
  },
  ".language-html .language-css .token.punctuation": {
    "color": "#d4d4d4"
  },
  ".language-html .language-javascript .token.punctuation": {
    "color": "#d4d4d4"
  },
  "property": {
    "color": "#9cdcfe"
  },
  "tag": {
    "color": "#569cd6"
  },
  "boolean": {
    "color": "#569cd6"
  },
  "number": {
    "color": "#b5cea8"
  },
  "constant": {
    "color": "#9cdcfe"
  },
  "symbol": {
    "color": "#b5cea8"
  },
  "inserted": {
    "color": "#b5cea8"
  },
  "unit": {
    "color": "#b5cea8"
  },
  "selector": {
    "color": "#d7ba7d"
  },
  "attr-name": {
    "color": "#9cdcfe"
  },
  "string": {
    "color": "#ce9178"
  },
  "char": {
    "color": "#ce9178"
  },
  "builtin": {
    "color": "#ce9178"
  },
  "deleted": {
    "color": "#ce9178"
  },
  ".language-css .token.string.url": {
    "textDecoration": "underline"
  },
  "operator": {
    "color": "#d4d4d4"
  },
  "entity": {
    "color": "#569cd6"
  },
  "operator.arrow": {
    "color": "#569CD6"
  },
  "atrule": {
    "color": "#ce9178"
  },
  "atrule.rule": {
    "color": "#c586c0"
  },
  "atrule.url": {
    "color": "#9cdcfe"
  },
  "atrule.url.function": {
    "color": "#dcdcaa"
  },
  "atrule.url.punctuation": {
    "color": "#d4d4d4"
  },
  "keyword": {
    "color": "#569CD6"
  },
  "keyword.module": {
    "color": "#c586c0"
  },
  "keyword.control-flow": {
    "color": "#c586c0"
  },
  "function": {
    "color": "#dcdcaa"
  },
  "function.maybe-class-name": {
    "color": "#dcdcaa"
  },
  "regex": {
    "color": "#d16969"
  },
  "important": {
    "color": "#569cd6"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "class-name": {
    "color": "#4ec9b0"
  },
  "maybe-class-name": {
    "color": "#4ec9b0"
  },
  "console": {
    "color": "#9cdcfe"
  },
  "parameter": {
    "color": "#9cdcfe"
  },
  "interpolation": {
    "color": "#9cdcfe"
  },
  "punctuation.interpolation-punctuation": {
    "color": "#569cd6"
  },
  "variable": {
    "color": "#9cdcfe"
  },
  "imports.maybe-class-name": {
    "color": "#9cdcfe"
  },
  "exports.maybe-class-name": {
    "color": "#9cdcfe"
  },
  "escape": {
    "color": "#d7ba7d"
  },
  "tag.punctuation": {
    "color": "#808080"
  },
  "cdata": {
    "color": "#808080"
  },
  "attr-value": {
    "color": "#ce9178"
  },
  "attr-value.punctuation": {
    "color": "#ce9178"
  },
  "attr-value.punctuation.attr-equals": {
    "color": "#d4d4d4"
  },
  "namespace": {
    "color": "#4ec9b0"
  },
  "pre[class*=\"language-javascript\"]": {
    "color": "#9cdcfe"
  },
  "code[class*=\"language-javascript\"]": {
    "color": "#9cdcfe"
  },
  "pre[class*=\"language-jsx\"]": {
    "color": "#9cdcfe"
  },
  "code[class*=\"language-jsx\"]": {
    "color": "#9cdcfe"
  },
  "pre[class*=\"language-typescript\"]": {
    "color": "#9cdcfe"
  },
  "code[class*=\"language-typescript\"]": {
    "color": "#9cdcfe"
  },
  "pre[class*=\"language-tsx\"]": {
    "color": "#9cdcfe"
  },
  "code[class*=\"language-tsx\"]": {
    "color": "#9cdcfe"
  },
  "pre[class*=\"language-css\"]": {
    "color": "#ce9178"
  },
  "code[class*=\"language-css\"]": {
    "color": "#ce9178"
  },
  "pre[class*=\"language-html\"]": {
    "color": "#d4d4d4"
  },
  "code[class*=\"language-html\"]": {
    "color": "#d4d4d4"
  },
  ".language-regex .token.anchor": {
    "color": "#dcdcaa"
  },
  ".language-html .token.punctuation": {
    "color": "#808080"
  },
  "pre[class*=\"language-\"] > code[class*=\"language-\"]": {
    "position": "relative",
    "zIndex": "1"
  },
  ".line-highlight.line-highlight": {
    "background": "#f7ebc6",
    "boxShadow": "inset 5px 0 0 #f7d87c",
    "zIndex": "0"
  }
});

/***/ }),

/***/ 30767:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*='language-']": {
    "color": "#9efeff",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "fontFamily": "'Operator Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontWeight": "400",
    "fontSize": "17px",
    "lineHeight": "25px",
    "letterSpacing": "0.5px",
    "textShadow": "0 1px #222245"
  },
  "pre[class*='language-']": {
    "color": "#9efeff",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "fontFamily": "'Operator Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontWeight": "400",
    "fontSize": "17px",
    "lineHeight": "25px",
    "letterSpacing": "0.5px",
    "textShadow": "0 1px #222245",
    "padding": "2em",
    "margin": "0.5em 0",
    "overflow": "auto",
    "background": "#1e1e3f"
  },
  "pre[class*='language-']::-moz-selection": {
    "color": "inherit",
    "background": "#a599e9"
  },
  "pre[class*='language-'] ::-moz-selection": {
    "color": "inherit",
    "background": "#a599e9"
  },
  "code[class*='language-']::-moz-selection": {
    "color": "inherit",
    "background": "#a599e9"
  },
  "code[class*='language-'] ::-moz-selection": {
    "color": "inherit",
    "background": "#a599e9"
  },
  "pre[class*='language-']::selection": {
    "color": "inherit",
    "background": "#a599e9"
  },
  "pre[class*='language-'] ::selection": {
    "color": "inherit",
    "background": "#a599e9"
  },
  "code[class*='language-']::selection": {
    "color": "inherit",
    "background": "#a599e9"
  },
  "code[class*='language-'] ::selection": {
    "color": "inherit",
    "background": "#a599e9"
  },
  ":not(pre) > code[class*='language-']": {
    "background": "#1e1e3f",
    "padding": "0.1em",
    "borderRadius": "0.3em"
  },
  "": {
    "fontWeight": "400"
  },
  "comment": {
    "color": "#b362ff"
  },
  "prolog": {
    "color": "#b362ff"
  },
  "cdata": {
    "color": "#b362ff"
  },
  "delimiter": {
    "color": "#ff9d00"
  },
  "keyword": {
    "color": "#ff9d00"
  },
  "selector": {
    "color": "#ff9d00"
  },
  "important": {
    "color": "#ff9d00"
  },
  "atrule": {
    "color": "#ff9d00"
  },
  "operator": {
    "color": "rgb(255, 180, 84)",
    "background": "none"
  },
  "attr-name": {
    "color": "rgb(255, 180, 84)"
  },
  "punctuation": {
    "color": "#ffffff"
  },
  "boolean": {
    "color": "rgb(255, 98, 140)"
  },
  "tag": {
    "color": "rgb(255, 157, 0)"
  },
  "tag.punctuation": {
    "color": "rgb(255, 157, 0)"
  },
  "doctype": {
    "color": "rgb(255, 157, 0)"
  },
  "builtin": {
    "color": "rgb(255, 157, 0)"
  },
  "entity": {
    "color": "#6897bb",
    "background": "none"
  },
  "symbol": {
    "color": "#6897bb"
  },
  "number": {
    "color": "#ff628c"
  },
  "property": {
    "color": "#ff628c"
  },
  "constant": {
    "color": "#ff628c"
  },
  "variable": {
    "color": "#ff628c"
  },
  "string": {
    "color": "#a5ff90"
  },
  "char": {
    "color": "#a5ff90"
  },
  "attr-value": {
    "color": "#a5c261"
  },
  "attr-value.punctuation": {
    "color": "#a5c261"
  },
  "attr-value.punctuation:first-child": {
    "color": "#a9b7c6"
  },
  "url": {
    "color": "#287bde",
    "textDecoration": "underline",
    "background": "none"
  },
  "function": {
    "color": "rgb(250, 208, 0)"
  },
  "regex": {
    "background": "#364135"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "inserted": {
    "background": "#00ff00"
  },
  "deleted": {
    "background": "#ff000d"
  },
  "code.language-css .token.property": {
    "color": "#a9b7c6"
  },
  "code.language-css .token.property + .token.punctuation": {
    "color": "#a9b7c6"
  },
  "code.language-css .token.id": {
    "color": "#ffc66d"
  },
  "code.language-css .token.selector > .token.class": {
    "color": "#ffc66d"
  },
  "code.language-css .token.selector > .token.attribute": {
    "color": "#ffc66d"
  },
  "code.language-css .token.selector > .token.pseudo-class": {
    "color": "#ffc66d"
  },
  "code.language-css .token.selector > .token.pseudo-element": {
    "color": "#ffc66d"
  },
  "class-name": {
    "color": "#fb94ff"
  },
  ".language-css .token.string": {
    "background": "none"
  },
  ".style .token.string": {
    "background": "none"
  },
  ".line-highlight.line-highlight": {
    "marginTop": "36px",
    "background": "linear-gradient(to right, rgba(179, 98, 255, 0.17), transparent)"
  },
  ".line-highlight.line-highlight:before": {
    "content": "''"
  },
  ".line-highlight.line-highlight[data-end]:after": {
    "content": "''"
  }
});

/***/ }),

/***/ 38635:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*=\"language-\"]": {
    "color": "#f92aad",
    "textShadow": "0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3",
    "background": "none",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontSize": "1em",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#f92aad",
    "textShadow": "0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3",
    "background": "none",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontSize": "1em",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "backgroundColor": "transparent !important",
    "backgroundImage": "linear-gradient(to bottom, #2a2139 75%, #34294f)"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "backgroundColor": "transparent !important",
    "backgroundImage": "linear-gradient(to bottom, #2a2139 75%, #34294f)",
    "padding": ".1em",
    "borderRadius": ".3em",
    "whiteSpace": "normal"
  },
  "comment": {
    "color": "#8e8e8e"
  },
  "block-comment": {
    "color": "#8e8e8e"
  },
  "prolog": {
    "color": "#8e8e8e"
  },
  "doctype": {
    "color": "#8e8e8e"
  },
  "cdata": {
    "color": "#8e8e8e"
  },
  "punctuation": {
    "color": "#ccc"
  },
  "tag": {
    "color": "#e2777a"
  },
  "attr-name": {
    "color": "#e2777a"
  },
  "namespace": {
    "color": "#e2777a"
  },
  "number": {
    "color": "#e2777a"
  },
  "unit": {
    "color": "#e2777a"
  },
  "hexcode": {
    "color": "#e2777a"
  },
  "deleted": {
    "color": "#e2777a"
  },
  "property": {
    "color": "#72f1b8",
    "textShadow": "0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475"
  },
  "selector": {
    "color": "#72f1b8",
    "textShadow": "0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475"
  },
  "function-name": {
    "color": "#6196cc"
  },
  "boolean": {
    "color": "#fdfdfd",
    "textShadow": "0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"
  },
  "selector.id": {
    "color": "#fdfdfd",
    "textShadow": "0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"
  },
  "function": {
    "color": "#fdfdfd",
    "textShadow": "0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"
  },
  "class-name": {
    "color": "#fff5f6",
    "textShadow": "0 0 2px #000, 0 0 10px #fc1f2c75, 0 0 5px #fc1f2c75, 0 0 25px #fc1f2c75"
  },
  "constant": {
    "color": "#f92aad",
    "textShadow": "0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"
  },
  "symbol": {
    "color": "#f92aad",
    "textShadow": "0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"
  },
  "important": {
    "color": "#f4eee4",
    "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575",
    "fontWeight": "bold"
  },
  "atrule": {
    "color": "#f4eee4",
    "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"
  },
  "keyword": {
    "color": "#f4eee4",
    "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"
  },
  "selector.class": {
    "color": "#f4eee4",
    "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"
  },
  "builtin": {
    "color": "#f4eee4",
    "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"
  },
  "string": {
    "color": "#f87c32"
  },
  "char": {
    "color": "#f87c32"
  },
  "attr-value": {
    "color": "#f87c32"
  },
  "regex": {
    "color": "#f87c32"
  },
  "variable": {
    "color": "#f87c32"
  },
  "operator": {
    "color": "#67cdcc"
  },
  "entity": {
    "color": "#67cdcc",
    "cursor": "help"
  },
  "url": {
    "color": "#67cdcc"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "inserted": {
    "color": "green"
  }
});

/***/ }),

/***/ 48292:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*=\"language-\"]": {
    "color": "#839496",
    "textShadow": "0 1px rgba(0, 0, 0, 0.3)",
    "fontFamily": "Inconsolata, Monaco, Consolas, 'Courier New', Courier, monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#839496",
    "textShadow": "0 1px rgba(0, 0, 0, 0.3)",
    "fontFamily": "Inconsolata, Monaco, Consolas, 'Courier New', Courier, monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "borderRadius": "0.3em",
    "background": "#002b36"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "background": "#002b36",
    "padding": ".1em",
    "borderRadius": ".3em"
  },
  "comment": {
    "color": "#586e75"
  },
  "prolog": {
    "color": "#586e75"
  },
  "doctype": {
    "color": "#586e75"
  },
  "cdata": {
    "color": "#586e75"
  },
  "punctuation": {
    "color": "#93a1a1"
  },
  ".namespace": {
    "Opacity": ".7"
  },
  "property": {
    "color": "#268bd2"
  },
  "keyword": {
    "color": "#268bd2"
  },
  "tag": {
    "color": "#268bd2"
  },
  "class-name": {
    "color": "#FFFFB6",
    "textDecoration": "underline"
  },
  "boolean": {
    "color": "#b58900"
  },
  "constant": {
    "color": "#b58900"
  },
  "symbol": {
    "color": "#dc322f"
  },
  "deleted": {
    "color": "#dc322f"
  },
  "number": {
    "color": "#859900"
  },
  "selector": {
    "color": "#859900"
  },
  "attr-name": {
    "color": "#859900"
  },
  "string": {
    "color": "#859900"
  },
  "char": {
    "color": "#859900"
  },
  "builtin": {
    "color": "#859900"
  },
  "inserted": {
    "color": "#859900"
  },
  "variable": {
    "color": "#268bd2"
  },
  "operator": {
    "color": "#EDEDED"
  },
  "function": {
    "color": "#268bd2"
  },
  "regex": {
    "color": "#E9C062"
  },
  "important": {
    "color": "#fd971f",
    "fontWeight": "bold"
  },
  "entity": {
    "color": "#FFFFB6",
    "cursor": "help"
  },
  "url": {
    "color": "#96CBFE"
  },
  ".language-css .token.string": {
    "color": "#87C38A"
  },
  ".style .token.string": {
    "color": "#87C38A"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "atrule": {
    "color": "#F9EE98"
  },
  "attr-value": {
    "color": "#F9EE98"
  }
});

/***/ }),

/***/ 53401:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*=\"language-\"]": {
    "color": "#ccc",
    "background": "none",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontSize": "1em",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#ccc",
    "background": "#2d2d2d",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontSize": "1em",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "background": "#2d2d2d",
    "padding": ".1em",
    "borderRadius": ".3em",
    "whiteSpace": "normal"
  },
  "comment": {
    "color": "#999"
  },
  "block-comment": {
    "color": "#999"
  },
  "prolog": {
    "color": "#999"
  },
  "doctype": {
    "color": "#999"
  },
  "cdata": {
    "color": "#999"
  },
  "punctuation": {
    "color": "#ccc"
  },
  "tag": {
    "color": "#e2777a"
  },
  "attr-name": {
    "color": "#e2777a"
  },
  "namespace": {
    "color": "#e2777a"
  },
  "deleted": {
    "color": "#e2777a"
  },
  "function-name": {
    "color": "#6196cc"
  },
  "boolean": {
    "color": "#f08d49"
  },
  "number": {
    "color": "#f08d49"
  },
  "function": {
    "color": "#f08d49"
  },
  "property": {
    "color": "#f8c555"
  },
  "class-name": {
    "color": "#f8c555"
  },
  "constant": {
    "color": "#f8c555"
  },
  "symbol": {
    "color": "#f8c555"
  },
  "selector": {
    "color": "#cc99cd"
  },
  "important": {
    "color": "#cc99cd",
    "fontWeight": "bold"
  },
  "atrule": {
    "color": "#cc99cd"
  },
  "keyword": {
    "color": "#cc99cd"
  },
  "builtin": {
    "color": "#cc99cd"
  },
  "string": {
    "color": "#7ec699"
  },
  "char": {
    "color": "#7ec699"
  },
  "attr-value": {
    "color": "#7ec699"
  },
  "regex": {
    "color": "#7ec699"
  },
  "variable": {
    "color": "#7ec699"
  },
  "operator": {
    "color": "#67cdcc"
  },
  "entity": {
    "color": "#67cdcc",
    "cursor": "help"
  },
  "url": {
    "color": "#67cdcc"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "inserted": {
    "color": "green"
  }
});

/***/ }),

/***/ 60116:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*=\"language-\"]": {
    "color": "#22da17",
    "fontFamily": "monospace",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "lineHeight": "25px",
    "fontSize": "18px",
    "margin": "5px 0"
  },
  "pre[class*=\"language-\"]": {
    "color": "white",
    "fontFamily": "monospace",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "lineHeight": "25px",
    "fontSize": "18px",
    "margin": "0.5em 0",
    "background": "#0a143c",
    "padding": "1em",
    "overflow": "auto"
  },
  "pre[class*=\"language-\"] *": {
    "fontFamily": "monospace"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "color": "white",
    "background": "#0a143c",
    "padding": "0.1em",
    "borderRadius": "0.3em",
    "whiteSpace": "normal"
  },
  "pre[class*=\"language-\"]::-moz-selection": {
    "textShadow": "none",
    "background": "rgba(29, 59, 83, 0.99)"
  },
  "pre[class*=\"language-\"] ::-moz-selection": {
    "textShadow": "none",
    "background": "rgba(29, 59, 83, 0.99)"
  },
  "code[class*=\"language-\"]::-moz-selection": {
    "textShadow": "none",
    "background": "rgba(29, 59, 83, 0.99)"
  },
  "code[class*=\"language-\"] ::-moz-selection": {
    "textShadow": "none",
    "background": "rgba(29, 59, 83, 0.99)"
  },
  "pre[class*=\"language-\"]::selection": {
    "textShadow": "none",
    "background": "rgba(29, 59, 83, 0.99)"
  },
  "pre[class*=\"language-\"] ::selection": {
    "textShadow": "none",
    "background": "rgba(29, 59, 83, 0.99)"
  },
  "code[class*=\"language-\"]::selection": {
    "textShadow": "none",
    "background": "rgba(29, 59, 83, 0.99)"
  },
  "code[class*=\"language-\"] ::selection": {
    "textShadow": "none",
    "background": "rgba(29, 59, 83, 0.99)"
  },
  "comment": {
    "color": "rgb(99, 119, 119)",
    "fontStyle": "italic"
  },
  "prolog": {
    "color": "rgb(99, 119, 119)",
    "fontStyle": "italic"
  },
  "cdata": {
    "color": "rgb(99, 119, 119)",
    "fontStyle": "italic"
  },
  "punctuation": {
    "color": "rgb(199, 146, 234)"
  },
  ".namespace": {
    "color": "rgb(178, 204, 214)"
  },
  "deleted": {
    "color": "rgba(239, 83, 80, 0.56)",
    "fontStyle": "italic"
  },
  "symbol": {
    "color": "rgb(128, 203, 196)"
  },
  "property": {
    "color": "rgb(128, 203, 196)"
  },
  "tag": {
    "color": "rgb(127, 219, 202)"
  },
  "operator": {
    "color": "rgb(127, 219, 202)"
  },
  "keyword": {
    "color": "rgb(127, 219, 202)"
  },
  "boolean": {
    "color": "rgb(255, 88, 116)"
  },
  "number": {
    "color": "rgb(247, 140, 108)"
  },
  "constant": {
    "color": "rgb(34 183 199)"
  },
  "function": {
    "color": "rgb(34 183 199)"
  },
  "builtin": {
    "color": "rgb(34 183 199)"
  },
  "char": {
    "color": "rgb(34 183 199)"
  },
  "selector": {
    "color": "rgb(199, 146, 234)",
    "fontStyle": "italic"
  },
  "doctype": {
    "color": "rgb(199, 146, 234)",
    "fontStyle": "italic"
  },
  "attr-name": {
    "color": "rgb(173, 219, 103)",
    "fontStyle": "italic"
  },
  "inserted": {
    "color": "rgb(173, 219, 103)",
    "fontStyle": "italic"
  },
  "string": {
    "color": "rgb(173, 219, 103)"
  },
  "url": {
    "color": "rgb(173, 219, 103)"
  },
  "entity": {
    "color": "rgb(173, 219, 103)"
  },
  ".language-css .token.string": {
    "color": "rgb(173, 219, 103)"
  },
  ".style .token.string": {
    "color": "rgb(173, 219, 103)"
  },
  "class-name": {
    "color": "rgb(255, 203, 139)"
  },
  "atrule": {
    "color": "rgb(255, 203, 139)"
  },
  "attr-value": {
    "color": "rgb(255, 203, 139)"
  },
  "regex": {
    "color": "rgb(214, 222, 235)"
  },
  "important": {
    "color": "rgb(214, 222, 235)",
    "fontWeight": "bold"
  },
  "variable": {
    "color": "rgb(214, 222, 235)"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  }
});

/***/ }),

/***/ 73340:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*=\"language-\"]": {
    "color": "#657b83",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontSize": "1em",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#657b83",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "fontSize": "1em",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "borderRadius": "0.3em",
    "backgroundColor": "#fdf6e3"
  },
  "pre[class*=\"language-\"]::-moz-selection": {
    "background": "#073642"
  },
  "pre[class*=\"language-\"] ::-moz-selection": {
    "background": "#073642"
  },
  "code[class*=\"language-\"]::-moz-selection": {
    "background": "#073642"
  },
  "code[class*=\"language-\"] ::-moz-selection": {
    "background": "#073642"
  },
  "pre[class*=\"language-\"]::selection": {
    "background": "#073642"
  },
  "pre[class*=\"language-\"] ::selection": {
    "background": "#073642"
  },
  "code[class*=\"language-\"]::selection": {
    "background": "#073642"
  },
  "code[class*=\"language-\"] ::selection": {
    "background": "#073642"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "backgroundColor": "#fdf6e3",
    "padding": ".1em",
    "borderRadius": ".3em"
  },
  "comment": {
    "color": "#93a1a1"
  },
  "prolog": {
    "color": "#93a1a1"
  },
  "doctype": {
    "color": "#93a1a1"
  },
  "cdata": {
    "color": "#93a1a1"
  },
  "punctuation": {
    "color": "#586e75"
  },
  "namespace": {
    "Opacity": ".7"
  },
  "property": {
    "color": "#268bd2"
  },
  "tag": {
    "color": "#268bd2"
  },
  "boolean": {
    "color": "#268bd2"
  },
  "number": {
    "color": "#268bd2"
  },
  "constant": {
    "color": "#268bd2"
  },
  "symbol": {
    "color": "#268bd2"
  },
  "deleted": {
    "color": "#268bd2"
  },
  "selector": {
    "color": "#2aa198"
  },
  "attr-name": {
    "color": "#2aa198"
  },
  "string": {
    "color": "#2aa198"
  },
  "char": {
    "color": "#2aa198"
  },
  "builtin": {
    "color": "#2aa198"
  },
  "url": {
    "color": "#2aa198"
  },
  "inserted": {
    "color": "#2aa198"
  },
  "entity": {
    "color": "#657b83",
    "background": "#eee8d5",
    "cursor": "help"
  },
  "atrule": {
    "color": "#859900"
  },
  "attr-value": {
    "color": "#859900"
  },
  "keyword": {
    "color": "#859900"
  },
  "function": {
    "color": "#b58900"
  },
  "class-name": {
    "color": "#b58900"
  },
  "regex": {
    "color": "#cb4b16"
  },
  "important": {
    "color": "#cb4b16",
    "fontWeight": "bold"
  },
  "variable": {
    "color": "#cb4b16"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  }
});

/***/ }),

/***/ 88685:
/***/ (() => {

/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  "code[class*=\"language-\"]": {
    "MozTabSize": "2",
    "OTabSize": "2",
    "tabSize": "2",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "whiteSpace": "pre-wrap",
    "wordWrap": "normal",
    "fontFamily": "Menlo, Monaco, \"Courier New\", monospace",
    "fontSize": "14px",
    "color": "#76d9e6",
    "textShadow": "none"
  },
  "pre[class*=\"language-\"]": {
    "MozTabSize": "2",
    "OTabSize": "2",
    "tabSize": "2",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "whiteSpace": "pre-wrap",
    "wordWrap": "normal",
    "fontFamily": "Menlo, Monaco, \"Courier New\", monospace",
    "fontSize": "14px",
    "color": "#76d9e6",
    "textShadow": "none",
    "background": "#2a2a2a",
    "padding": "15px",
    "borderRadius": "4px",
    "border": "1px solid #e1e1e8",
    "overflow": "auto",
    "position": "relative"
  },
  "pre > code[class*=\"language-\"]": {
    "fontSize": "1em"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "background": "#2a2a2a",
    "padding": "0.15em 0.2em 0.05em",
    "borderRadius": ".3em",
    "border": "0.13em solid #7a6652",
    "boxShadow": "1px 1px 0.3em -0.1em #000 inset"
  },
  "pre[class*=\"language-\"] code": {
    "whiteSpace": "pre",
    "display": "block"
  },
  "namespace": {
    "Opacity": ".7"
  },
  "comment": {
    "color": "#6f705e"
  },
  "prolog": {
    "color": "#6f705e"
  },
  "doctype": {
    "color": "#6f705e"
  },
  "cdata": {
    "color": "#6f705e"
  },
  "operator": {
    "color": "#a77afe"
  },
  "boolean": {
    "color": "#a77afe"
  },
  "number": {
    "color": "#a77afe"
  },
  "attr-name": {
    "color": "#e6d06c"
  },
  "string": {
    "color": "#e6d06c"
  },
  "entity": {
    "color": "#e6d06c",
    "cursor": "help"
  },
  "url": {
    "color": "#e6d06c"
  },
  ".language-css .token.string": {
    "color": "#e6d06c"
  },
  ".style .token.string": {
    "color": "#e6d06c"
  },
  "selector": {
    "color": "#a6e22d"
  },
  "inserted": {
    "color": "#a6e22d"
  },
  "atrule": {
    "color": "#ef3b7d"
  },
  "attr-value": {
    "color": "#ef3b7d"
  },
  "keyword": {
    "color": "#ef3b7d"
  },
  "important": {
    "color": "#ef3b7d",
    "fontWeight": "bold"
  },
  "deleted": {
    "color": "#ef3b7d"
  },
  "regex": {
    "color": "#76d9e6"
  },
  "statement": {
    "color": "#76d9e6",
    "fontWeight": "bold"
  },
  "placeholder": {
    "color": "#fff"
  },
  "variable": {
    "color": "#fff"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "punctuation": {
    "color": "#bebec5"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "code.language-markup": {
    "color": "#f9f9f9"
  },
  "code.language-markup .token.tag": {
    "color": "#ef3b7d"
  },
  "code.language-markup .token.attr-name": {
    "color": "#a6e22d"
  },
  "code.language-markup .token.attr-value": {
    "color": "#e6d06c"
  },
  "code.language-markup .token.style": {
    "color": "#76d9e6"
  },
  "code.language-markup .token.script": {
    "color": "#76d9e6"
  },
  "code.language-markup .token.script .token.keyword": {
    "color": "#76d9e6"
  },
  ".line-highlight.line-highlight": {
    "padding": "0",
    "background": "rgba(255, 255, 255, 0.08)"
  },
  ".line-highlight.line-highlight:before": {
    "padding": "0.2em 0.5em",
    "backgroundColor": "rgba(255, 255, 255, 0.4)",
    "color": "black",
    "height": "1em",
    "lineHeight": "1em",
    "boxShadow": "0 1px 1px rgba(255, 255, 255, 0.7)"
  },
  ".line-highlight.line-highlight[data-end]:after": {
    "padding": "0.2em 0.5em",
    "backgroundColor": "rgba(255, 255, 255, 0.4)",
    "color": "black",
    "height": "1em",
    "lineHeight": "1em",
    "boxShadow": "0 1px 1px rgba(255, 255, 255, 0.7)"
  }
});

/***/ })

}]);