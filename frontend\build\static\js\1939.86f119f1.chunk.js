"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1939],{

/***/ 41939:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;
var PreviewContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: 100%;\n  height: 100%;\n  background: #f0f2f5;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n"])));
var ControlBar = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 12px 16px;\n  background: white;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  gap: 8px;\n"])));
var DeviceFrame = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  overflow: auto;\n"])));
var DeviceContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: ", ";\n  border-radius: ", ";\n  padding: ", ";\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n  transform: scale(", ") ", ";\n  position: relative;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: ", ";\n    left: 50%;\n    transform: translateX(-50%);\n    width: ", ";\n    height: ", ";\n    background: #999;\n    border-radius: 2px;\n    display: ", ";\n  }\n"])), function (props) {
  return props.deviceType === 'mobile' ? '#333' : '#666';
}, function (props) {
  return props.deviceType === 'mobile' ? '25px' : '8px';
}, function (props) {
  return props.deviceType === 'mobile' ? '20px 10px' : '15px';
}, function (props) {
  return props.scale;
}, function (props) {
  return props.rotated ? 'rotate(90deg)' : '';
}, function (props) {
  return props.deviceType === 'mobile' ? '8px' : '6px';
}, function (props) {
  return props.deviceType === 'mobile' ? '60px' : '80px';
}, function (props) {
  return props.deviceType === 'mobile' ? '4px' : '6px';
}, function (props) {
  return props.deviceType === 'desktop' ? 'none' : 'block';
});
var Screen = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: ", "px;\n  height: ", "px;\n  background: white;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  box-shadow: inset 0 0 0 1px #e8e8e8;\n"])), function (props) {
  return props.width;
}, function (props) {
  return props.height;
});
var PreviewContent = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: 100%;\n  height: 100%;\n  overflow: auto;\n  position: relative;\n"])));
var DeviceInfo = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: -30px;\n  left: 0;\n  right: 0;\n  text-align: center;\n  color: #666;\n  font-size: 12px;\n  font-weight: 500;\n"])));

// Device presets
var DEVICE_PRESETS = {
  mobile: {
    'iPhone 14': {
      width: 390,
      height: 844
    },
    'iPhone 14 Plus': {
      width: 428,
      height: 926
    },
    'iPhone SE': {
      width: 375,
      height: 667
    },
    'Samsung Galaxy S23': {
      width: 360,
      height: 780
    },
    'Google Pixel 7': {
      width: 412,
      height: 915
    }
  },
  tablet: {
    'iPad': {
      width: 768,
      height: 1024
    },
    'iPad Pro 11"': {
      width: 834,
      height: 1194
    },
    'iPad Pro 12.9"': {
      width: 1024,
      height: 1366
    },
    'Samsung Galaxy Tab': {
      width: 800,
      height: 1280
    },
    'Surface Pro': {
      width: 912,
      height: 1368
    }
  },
  desktop: {
    'MacBook Air': {
      width: 1280,
      height: 800
    },
    'MacBook Pro 14"': {
      width: 1512,
      height: 982
    },
    'iMac 24"': {
      width: 1920,
      height: 1080
    },
    'Full HD': {
      width: 1920,
      height: 1080
    },
    'Ultra Wide': {
      width: 2560,
      height: 1080
    }
  }
};
var MultiDevicePreview = function MultiDevicePreview(_ref) {
  var children = _ref.children,
    _ref$onDeviceChange = _ref.onDeviceChange,
    onDeviceChange = _ref$onDeviceChange === void 0 ? function () {} : _ref$onDeviceChange,
    _ref$showControls = _ref.showControls,
    showControls = _ref$showControls === void 0 ? true : _ref$showControls,
    _ref$defaultDevice = _ref.defaultDevice,
    defaultDevice = _ref$defaultDevice === void 0 ? 'desktop' : _ref$defaultDevice,
    _ref$defaultPreset = _ref.defaultPreset,
    defaultPreset = _ref$defaultPreset === void 0 ? 'Full HD' : _ref$defaultPreset;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(defaultDevice),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    deviceType = _useState2[0],
    setDeviceType = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(defaultPreset),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    devicePreset = _useState4[0],
    setDevicePreset = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
      width: 1920,
      height: 1080
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    customSize = _useState6[0],
    setCustomSize = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0.5),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    scale = _useState8[0],
    setScale = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    rotated = _useState0[0],
    setRotated = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    fullscreen = _useState10[0],
    setFullscreen = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState11, 2),
    showRuler = _useState12[0],
    setShowRuler = _useState12[1];
  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);

  // Get current device dimensions
  var getCurrentDimensions = function getCurrentDimensions() {
    if (devicePreset === 'Custom') {
      return customSize;
    }
    return DEVICE_PRESETS[deviceType][devicePreset] || {
      width: 1920,
      height: 1080
    };
  };
  var dimensions = getCurrentDimensions();
  var actualDimensions = rotated ? {
    width: dimensions.height,
    height: dimensions.width
  } : dimensions;

  // Handle device type change
  var handleDeviceTypeChange = function handleDeviceTypeChange(newType) {
    setDeviceType(newType);
    setRotated(false);

    // Set default preset for device type
    var presets = Object.keys(DEVICE_PRESETS[newType]);
    var defaultPreset = presets[0];
    setDevicePreset(defaultPreset);
    onDeviceChange(newType, defaultPreset);
  };

  // Handle preset change
  var handlePresetChange = function handlePresetChange(preset) {
    setDevicePreset(preset);
    onDeviceChange(deviceType, preset);
  };

  // Auto-adjust scale based on container size
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    var adjustScale = function adjustScale() {
      if (!containerRef.current) return;
      var container = containerRef.current;
      var containerWidth = container.clientWidth - 40; // padding
      var containerHeight = container.clientHeight - 40;
      var scaleX = containerWidth / actualDimensions.width;
      var scaleY = containerHeight / actualDimensions.height;
      var autoScale = Math.min(scaleX, scaleY, 1);
      setScale(autoScale);
    };
    adjustScale();
    window.addEventListener('resize', adjustScale);
    return function () {
      return window.removeEventListener('resize', adjustScale);
    };
  }, [actualDimensions]);
  var renderControls = function renderControls() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ControlBar, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
      wrap: true
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Device:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6, {
      value: deviceType,
      onChange: handleDeviceTypeChange,
      style: {
        width: 120
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
      value: "mobile"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MobileOutlined */ .jHj, null), " Mobile"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
      value: "tablet"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .TabletOutlined */ .pLH, null), " Tablet"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
      value: "desktop"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DesktopOutlined */ .zlw, null), " Desktop"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Preset:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6, {
      value: devicePreset,
      onChange: handlePresetChange,
      style: {
        width: 150
      }
    }, Object.keys(DEVICE_PRESETS[deviceType]).map(function (preset) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
        key: preset,
        value: preset
      }, preset);
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
      value: "Custom"
    }, "Custom Size"))), devicePreset === 'Custom' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, "W:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("input", {
      type: "number",
      value: customSize.width,
      onChange: function onChange(e) {
        return setCustomSize(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            width: parseInt(e.target.value) || 0
          });
        });
      },
      style: {
        width: 80,
        padding: '4px 8px',
        border: '1px solid #d9d9d9',
        borderRadius: '4px'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, "H:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("input", {
      type: "number",
      value: customSize.height,
      onChange: function onChange(e) {
        return setCustomSize(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            height: parseInt(e.target.value) || 0
          });
        });
      },
      style: {
        width: 80,
        padding: '4px 8px',
        border: '1px solid #d9d9d9',
        borderRadius: '4px'
      }
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
      wrap: true
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, "Scale:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Slider */ .Ap, {
      min: 0.1,
      max: 1,
      step: 0.1,
      value: scale,
      onChange: setScale,
      style: {
        width: 100
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, Math.round(scale * 100), "%")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: "Rotate Device"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      type: rotated ? 'primary' : 'default',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .RotateRightOutlined */ .sCq, null),
      onClick: function onClick() {
        return setRotated(!rotated);
      },
      disabled: deviceType === 'desktop'
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: "Toggle Ruler"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      type: showRuler ? 'primary' : 'default',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .EyeOutlined */ .Om2, null),
      onClick: function onClick() {
        return setShowRuler(!showRuler);
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: "Fullscreen"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenOutlined */ .KrH, null),
      onClick: function onClick() {
        return setFullscreen(!fullscreen);
      }
    }))));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PreviewContainer, null, showControls && renderControls(), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceFrame, {
    ref: containerRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      position: 'relative'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceInfo, null, devicePreset, " - ", actualDimensions.width, " \xD7 ", actualDimensions.height, rotated && ' (Rotated)'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceContainer, {
    deviceType: deviceType,
    scale: scale,
    rotated: rotated
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Screen, {
    width: actualDimensions.width,
    height: actualDimensions.height
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PreviewContent, null, children))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MultiDevicePreview);

/***/ })

}]);