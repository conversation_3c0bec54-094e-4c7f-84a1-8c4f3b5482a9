"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6254],{

/***/ 4617:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  r3: () => (/* reexport */ PerformanceMonitor["default"])
});

// UNUSED EXPORTS: LazyLoadComponent, MemoizedComponent, VirtualList, usePerformanceOptimization

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
;// ./src/components/performance/MemoizedComponent.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/**
 * MemoizedComponent higher-order component
 * Wraps a component with React.memo and provides optimized props
 * 
 * @param {React.ComponentType} Component - The component to memoize
 * @param {Object} options - Options for memoization
 * @param {Function} options.areEqual - Custom comparison function for React.memo
 * @param {Array} options.memoizedProps - List of prop names to memoize
 * @param {Array} options.callbackProps - List of prop names to wrap with useCallback
 * @returns {React.MemoExoticComponent} Memoized component
 */
var MemoizedComponent = function MemoizedComponent(Component) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var areEqual = options.areEqual,
    _options$memoizedProp = options.memoizedProps,
    memoizedProps = _options$memoizedProp === void 0 ? [] : _options$memoizedProp,
    _options$callbackProp = options.callbackProps,
    callbackProps = _options$callbackProp === void 0 ? [] : _options$callbackProp;

  // Create memoized component with custom comparison function
  var MemoComponent = /*#__PURE__*/memo(Component, areEqual);

  // Return a wrapper component that memoizes props
  return function (props) {
    // Memoize specified props
    var memoizedPropValues = useMemo(function () {
      var result = {};
      memoizedProps.forEach(function (propName) {
        if (props[propName] !== undefined) {
          result[propName] = props[propName];
        }
      });
      return result;
    }, [props].concat(_toConsumableArray(memoizedProps.map(function (propName) {
      return props[propName];
    }))));

    // Wrap callback props with useCallback
    var callbackPropValues = {};
    callbackProps.forEach(function (propName) {
      if (typeof props[propName] === 'function') {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        callbackPropValues[propName] = useCallback(props[propName], [props[propName]]);
      }
    });

    // Combine all props
    var optimizedProps = _objectSpread(_objectSpread(_objectSpread({}, props), memoizedPropValues), callbackPropValues);
    return /*#__PURE__*/React.createElement(MemoComponent, optimizedProps);
  };
};
/* harmony default export */ const performance_MemoizedComponent = ((/* unused pure expression or super */ null && (MemoizedComponent)));
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
;// ./src/components/performance/LazyLoadComponent.js




var _excluded = (/* unused pure expression or super */ null && (["importFunc", "fallback", "minHeight", "visibilityThreshold", "loadImmediately"]));
var _templateObject, _templateObject2, _templateObject3;





// Styled components
var LoadingContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: ", ";\n  width: 100%;\n"])), function (props) {
  return props.minHeight || '200px';
});
var ErrorContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: ", ";\n  width: 100%;\n  color: ", ";\n  text-align: center;\n  padding: 16px;\n"])), function (props) {
  return props.minHeight || '200px';
}, function (props) {
  return props.theme.colorPalette.error;
});
var RetryButton = styled_components_browser_esm/* default */.Ay.button(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-top: 16px;\n  padding: 8px 16px;\n  background-color: ", ";\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  \n  &:hover {\n    background-color: ", ";\n  }\n"])), function (props) {
  return props.theme.colorPalette.primary;
}, function (props) {
  return props.theme.colorPalette.primaryDark;
});

/**
 * LazyLoadComponent
 * A component that lazily loads another component when it becomes visible in the viewport
 * 
 * @param {Object} props - Component props
 * @param {Function} props.importFunc - Function that returns a dynamic import (e.g., () => import('./MyComponent'))
 * @param {React.ReactNode} props.fallback - Fallback UI to show while loading
 * @param {string} props.minHeight - Minimum height of the loading container
 * @param {boolean} props.visibilityThreshold - Visibility threshold for intersection observer (0-1)
 * @param {boolean} props.loadImmediately - Whether to load the component immediately without waiting for visibility
 * @returns {React.ReactElement} Lazy loaded component
 */
var LazyLoadComponent = function LazyLoadComponent(_ref) {
  var importFunc = _ref.importFunc,
    _ref$fallback = _ref.fallback,
    fallback = _ref$fallback === void 0 ? null : _ref$fallback,
    _ref$minHeight = _ref.minHeight,
    minHeight = _ref$minHeight === void 0 ? '200px' : _ref$minHeight,
    _ref$visibilityThresh = _ref.visibilityThreshold,
    visibilityThreshold = _ref$visibilityThresh === void 0 ? 0.1 : _ref$visibilityThresh,
    _ref$loadImmediately = _ref.loadImmediately,
    loadImmediately = _ref$loadImmediately === void 0 ? false : _ref$loadImmediately,
    props = _objectWithoutProperties(_ref, _excluded);
  var _useState = useState(loadImmediately),
    _useState2 = _slicedToArray(_useState, 2),
    shouldLoad = _useState2[0],
    setShouldLoad = _useState2[1];
  var _useState3 = useState(null),
    _useState4 = _slicedToArray(_useState3, 2),
    Component = _useState4[0],
    setComponent = _useState4[1];
  var _useState5 = useState(null),
    _useState6 = _slicedToArray(_useState5, 2),
    error = _useState6[0],
    setError = _useState6[1];
  var _useState7 = useState(0),
    _useState8 = _slicedToArray(_useState7, 2),
    retryCount = _useState8[0],
    setRetryCount = _useState8[1];

  // Create ref for the container element
  var containerRef = React.useRef(null);

  // Set up intersection observer to detect when component is visible
  useEffect(function () {
    if (loadImmediately) return;
    var observer = new IntersectionObserver(function (_ref2) {
      var _ref3 = _slicedToArray(_ref2, 1),
        entry = _ref3[0];
      if (entry.isIntersecting) {
        setShouldLoad(true);
        observer.disconnect();
      }
    }, {
      threshold: visibilityThreshold
    });
    if (containerRef.current) {
      observer.observe(containerRef.current);
    }
    return function () {
      observer.disconnect();
    };
  }, [loadImmediately, visibilityThreshold]);

  // Load the component when it should be loaded
  useEffect(function () {
    if (!shouldLoad) return;
    var isMounted = true;
    var loadComponent = /*#__PURE__*/function () {
      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {
        var module, LoadedComponent, _t;
        return _regeneratorRuntime.wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 1;
              return importFunc();
            case 1:
              module = _context.sent;
              LoadedComponent = module["default"] || module;
              if (isMounted) {
                setComponent(function () {
                  return LoadedComponent;
                });
                setError(null);
              }
              _context.next = 3;
              break;
            case 2:
              _context.prev = 2;
              _t = _context["catch"](0);
              if (isMounted) {
                console.error('Error loading component:', _t);
                setError(_t);
              }
            case 3:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 2]]);
      }));
      return function loadComponent() {
        return _ref4.apply(this, arguments);
      };
    }();
    loadComponent();
    return function () {
      isMounted = false;
    };
  }, [importFunc, shouldLoad, retryCount]);

  // Handle retry
  var handleRetry = function handleRetry() {
    setError(null);
    setRetryCount(function (count) {
      return count + 1;
    });
  };

  // If the component shouldn't load yet, show a placeholder
  if (!shouldLoad) {
    return /*#__PURE__*/React.createElement("div", {
      ref: containerRef,
      style: {
        minHeight: minHeight
      }
    }, fallback || /*#__PURE__*/React.createElement(LoadingContainer, {
      minHeight: minHeight
    }, /*#__PURE__*/React.createElement(Spin, {
      size: "large"
    })));
  }

  // If there was an error loading the component, show an error message
  if (error) {
    return /*#__PURE__*/React.createElement(ErrorContainer, {
      minHeight: minHeight
    }, /*#__PURE__*/React.createElement("div", null, "Failed to load component"), /*#__PURE__*/React.createElement(RetryButton, {
      onClick: handleRetry
    }, "Retry"));
  }

  // If the component is still loading, show a loading indicator
  if (!Component) {
    return /*#__PURE__*/React.createElement(LoadingContainer, {
      minHeight: minHeight
    }, /*#__PURE__*/React.createElement(Spin, {
      size: "large"
    }));
  }

  // Render the loaded component
  return /*#__PURE__*/React.createElement(Component, props);
};
/* harmony default export */ const performance_LazyLoadComponent = ((/* unused pure expression or super */ null && (LazyLoadComponent)));
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
;// ./src/components/performance/VirtualList.js





var VirtualList_excluded = (/* unused pure expression or super */ null && (["items", "renderItem", "itemHeight", "height", "overscan"]));
var VirtualList_templateObject, VirtualList_templateObject2;
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }



// Styled components
var VirtualListContainer = styled_components_browser_esm/* default */.Ay.div(VirtualList_templateObject || (VirtualList_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  overflow-y: auto;\n  width: 100%;\n  height: ", ";\n"])), function (props) {
  return props.height || '400px';
});
var VirtualListContent = styled_components_browser_esm/* default */.Ay.div(VirtualList_templateObject2 || (VirtualList_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n"])));

/**
 * VirtualList component
 * Efficiently renders large lists by only rendering items that are visible in the viewport
 *
 * @param {Object} props - Component props
 * @param {Array} props.items - Array of items to render
 * @param {Function} props.renderItem - Function to render each item
 * @param {number} props.itemHeight - Height of each item in pixels
 * @param {string} props.height - Height of the list container
 * @param {number} props.overscan - Number of items to render outside of the visible area
 * @returns {React.ReactElement} Virtual list component
 */
var VirtualList = function VirtualList(_ref) {
  var _ref$items = _ref.items,
    items = _ref$items === void 0 ? [] : _ref$items,
    renderItem = _ref.renderItem,
    _ref$itemHeight = _ref.itemHeight,
    itemHeight = _ref$itemHeight === void 0 ? 50 : _ref$itemHeight,
    _ref$height = _ref.height,
    height = _ref$height === void 0 ? '400px' : _ref$height,
    _ref$overscan = _ref.overscan,
    overscan = _ref$overscan === void 0 ? 5 : _ref$overscan,
    props = _objectWithoutProperties(_ref, VirtualList_excluded);
  var _useState = useState(0),
    _useState2 = _slicedToArray(_useState, 2),
    scrollTop = _useState2[0],
    setScrollTop = _useState2[1];
  var _useState3 = useState(0),
    _useState4 = _slicedToArray(_useState3, 2),
    containerHeight = _useState4[0],
    setContainerHeight = _useState4[1];
  var containerRef = useRef(null);

  // Calculate total height of all items
  var totalHeight = items.length * itemHeight;

  // Calculate range of visible items
  var startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  var endIndex = Math.min(items.length - 1, Math.floor((scrollTop + containerHeight) / itemHeight) + overscan);

  // Get visible items
  var visibleItems = items.slice(startIndex, endIndex + 1);

  // Handle scroll event
  var handleScroll = useCallback(function () {
    if (containerRef.current) {
      setScrollTop(containerRef.current.scrollTop);
    }
  }, []);

  // Measure container height on mount and resize
  useEffect(function () {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.clientHeight);
      var resizeObserver = new ResizeObserver(function (entries) {
        var _iterator = _createForOfIteratorHelper(entries),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var entry = _step.value;
            setContainerHeight(entry.contentRect.height);
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      });
      resizeObserver.observe(containerRef.current);
      return function () {
        resizeObserver.disconnect();
      };
    }
  }, []);

  // Add scroll event listener
  useEffect(function () {
    var container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return function () {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);
  return /*#__PURE__*/React.createElement(VirtualListContainer, _extends({
    ref: containerRef,
    height: height
  }, props), /*#__PURE__*/React.createElement(VirtualListContent, {
    style: {
      height: "".concat(totalHeight, "px"),
      pointerEvents: 'none'
    }
  }, /*#__PURE__*/React.createElement("div", {
    style: {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      transform: "translateY(".concat(startIndex * itemHeight, "px)"),
      pointerEvents: 'auto'
    }
  }, visibleItems.map(function (item, index) {
    // Generate a more unique key using item properties if available, or a combination of indices
    var itemKey = item.id || (_typeof(item) === 'object' && item !== null && 'key' in item ? item.key : null) || "item-".concat(startIndex + index, "-").concat(_typeof(item) === 'object' ? JSON.stringify(item).slice(0, 20) : item);
    return /*#__PURE__*/React.createElement("div", {
      key: itemKey,
      style: {
        height: "".concat(itemHeight, "px")
      }
    }, renderItem(item, startIndex + index));
  }))));
};
/* harmony default export */ const performance_VirtualList = ((/* unused pure expression or super */ null && (VirtualList)));
// EXTERNAL MODULE: ./src/components/performance/PerformanceMonitor.js + 1 modules
var PerformanceMonitor = __webpack_require__(2356);
// EXTERNAL MODULE: ./src/hooks/usePerformanceOptimization.js
var usePerformanceOptimization = __webpack_require__(81415);
;// ./src/components/performance/index.js







/***/ }),

/***/ 70405:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   c: () => (/* binding */ DEVICE_PRESETS)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;

// Device configurations with detailed specifications
var DEVICE_PRESETS = {
  mobile: {
    name: 'Mobile',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MobileOutlined */ .jHj, null),
    variants: {
      'iphone-se': {
        name: 'iPhone SE',
        width: 375,
        height: 667,
        scale: 0.8
      },
      'iphone-12': {
        name: 'iPhone 12',
        width: 390,
        height: 844,
        scale: 0.7
      },
      'pixel-5': {
        name: 'Pixel 5',
        width: 393,
        height: 851,
        scale: 0.7
      },
      'samsung-s21': {
        name: 'Samsung S21',
        width: 384,
        height: 854,
        scale: 0.7
      }
    },
    defaultVariant: 'iphone-12',
    frame: true,
    category: 'mobile'
  },
  tablet: {
    name: 'Tablet',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .TabletOutlined */ .pLH, null),
    variants: {
      'ipad': {
        name: 'iPad',
        width: 768,
        height: 1024,
        scale: 0.6
      },
      'ipad-pro': {
        name: 'iPad Pro',
        width: 1024,
        height: 1366,
        scale: 0.5
      },
      'surface': {
        name: 'Surface Pro',
        width: 912,
        height: 1368,
        scale: 0.5
      },
      'galaxy-tab': {
        name: 'Galaxy Tab',
        width: 800,
        height: 1280,
        scale: 0.6
      }
    },
    defaultVariant: 'ipad',
    frame: true,
    category: 'tablet'
  },
  desktop: {
    name: 'Desktop',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DesktopOutlined */ .zlw, null),
    variants: {
      'laptop': {
        name: 'Laptop',
        width: 1366,
        height: 768,
        scale: 0.7
      },
      'desktop': {
        name: 'Desktop',
        width: 1920,
        height: 1080,
        scale: 0.5
      },
      'ultrawide': {
        name: 'Ultrawide',
        width: 2560,
        height: 1080,
        scale: 0.4
      },
      'custom': {
        name: 'Custom',
        width: 1200,
        height: 800,
        scale: 0.8
      }
    },
    defaultVariant: 'laptop',
    frame: false,
    category: 'desktop'
  }
};

// Styled components
var DeviceFrameContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  margin: 20px auto;\n  transition: all 0.3s ease;\n  transform: ", ";\n"])), function (props) {
  return props.orientation === 'landscape' ? 'rotate(0deg)' : 'rotate(0deg)';
});
var DeviceFrame = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  background: ", ";\n  border-radius: ", ";\n  padding: ", ";\n  box-shadow: ", ";\n  transition: all 0.3s ease;\n  \n  ", "\n  \n  ", "\n"])), function (props) {
  if (props.category === 'mobile') return '#333';
  if (props.category === 'tablet') return '#444';
  return 'transparent';
}, function (props) {
  if (props.category === 'mobile') return '25px';
  if (props.category === 'tablet') return '15px';
  return '8px';
}, function (props) {
  if (props.category === 'mobile') return '20px 10px';
  if (props.category === 'tablet') return '15px';
  return '0';
}, function (props) {
  return props.frame ? '0 8px 32px rgba(0, 0, 0, 0.3)' : 'none';
}, function (props) {
  return props.category === 'mobile' && "\n    &::before {\n      content: '';\n      position: absolute;\n      top: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n    }\n    \n    &::after {\n      content: '';\n      position: absolute;\n      bottom: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  ";
}, function (props) {
  return props.category === 'tablet' && "\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: 6px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  ";
});
var DeviceScreen = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: ", "px;\n  height: ", "px;\n  max-width: 100%;\n  max-height: 100%;\n  background: white;\n  border-radius: ", ";\n  overflow: auto;\n  position: relative;\n  transform: scale(", ");\n  transform-origin: top center;\n  transition: all 0.3s ease;\n  \n  @media (max-width: 1200px) {\n    transform: scale(", ");\n  }\n  \n  @media (max-width: 768px) {\n    transform: scale(", ");\n  }\n"])), function (props) {
  return props.orientation === 'landscape' ? props.height : props.width;
}, function (props) {
  return props.orientation === 'landscape' ? props.width : props.height;
}, function (props) {
  if (props.category === 'mobile') return '8px';
  if (props.category === 'tablet') return '6px';
  return '4px';
}, function (props) {
  return props.scale;
}, function (props) {
  return Math.min(props.scale, 0.8);
}, function (props) {
  return Math.min(props.scale, 0.6);
});
var DeviceControls = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n"])));
var DeviceInfo = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: -30px;\n  left: 0;\n  font-size: 12px;\n  color: #666;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 4px 8px;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n"])));

/**
 * DevicePreviewFrame Component
 * Provides device-specific preview frames with orientation controls
 */
var DevicePreviewFrame = function DevicePreviewFrame(_ref) {
  var children = _ref.children,
    onDeviceChange = _ref.onDeviceChange,
    _ref$initialDevice = _ref.initialDevice,
    initialDevice = _ref$initialDevice === void 0 ? 'desktop' : _ref$initialDevice,
    _ref$initialVariant = _ref.initialVariant,
    initialVariant = _ref$initialVariant === void 0 ? null : _ref$initialVariant,
    _ref$showControls = _ref.showControls,
    showControls = _ref$showControls === void 0 ? true : _ref$showControls,
    _ref$showInfo = _ref.showInfo,
    showInfo = _ref$showInfo === void 0 ? true : _ref$showInfo,
    className = _ref.className;
  var _useState = useState(initialDevice),
    _useState2 = _slicedToArray(_useState, 2),
    currentDevice = _useState2[0],
    setCurrentDevice = _useState2[1];
  var _useState3 = useState(initialVariant || DEVICE_PRESETS[initialDevice].defaultVariant),
    _useState4 = _slicedToArray(_useState3, 2),
    currentVariant = _useState4[0],
    setCurrentVariant = _useState4[1];
  var _useState5 = useState('portrait'),
    _useState6 = _slicedToArray(_useState5, 2),
    orientation = _useState6[0],
    setOrientation = _useState6[1];
  var _useState7 = useState(false),
    _useState8 = _slicedToArray(_useState7, 2),
    isFullscreen = _useState8[0],
    setIsFullscreen = _useState8[1];

  // Get current device configuration
  var deviceConfig = useMemo(function () {
    var device = DEVICE_PRESETS[currentDevice];
    var variant = device.variants[currentVariant];
    return _objectSpread(_objectSpread(_objectSpread({}, device), variant), {}, {
      category: device.category
    });
  }, [currentDevice, currentVariant]);

  // Handle device type change
  var handleDeviceChange = useCallback(function (newDevice) {
    setCurrentDevice(newDevice);
    var newVariant = DEVICE_PRESETS[newDevice].defaultVariant;
    setCurrentVariant(newVariant);
    if (onDeviceChange) {
      onDeviceChange({
        device: newDevice,
        variant: newVariant,
        config: _objectSpread(_objectSpread({}, DEVICE_PRESETS[newDevice]), DEVICE_PRESETS[newDevice].variants[newVariant])
      });
    }
  }, [onDeviceChange]);

  // Handle variant change
  var handleVariantChange = useCallback(function (newVariant) {
    setCurrentVariant(newVariant);
    if (onDeviceChange) {
      onDeviceChange({
        device: currentDevice,
        variant: newVariant,
        config: _objectSpread(_objectSpread({}, DEVICE_PRESETS[currentDevice]), DEVICE_PRESETS[currentDevice].variants[newVariant])
      });
    }
  }, [currentDevice, onDeviceChange]);

  // Handle orientation change
  var handleOrientationChange = useCallback(function () {
    var newOrientation = orientation === 'portrait' ? 'landscape' : 'portrait';
    setOrientation(newOrientation);
  }, [orientation]);

  // Handle fullscreen toggle
  var handleFullscreenToggle = useCallback(function () {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);
  return /*#__PURE__*/React.createElement("div", {
    className: className
  }, showControls && /*#__PURE__*/React.createElement(DeviceControls, null, /*#__PURE__*/React.createElement(Space, null, Object.entries(DEVICE_PRESETS).map(function (_ref2) {
    var _ref3 = _slicedToArray(_ref2, 2),
      key = _ref3[0],
      device = _ref3[1];
    return /*#__PURE__*/React.createElement(Button, {
      key: key,
      type: currentDevice === key ? 'primary' : 'default',
      icon: device.icon,
      size: "small",
      onClick: function onClick() {
        return handleDeviceChange(key);
      }
    }, device.name);
  })), /*#__PURE__*/React.createElement(Select, {
    value: currentVariant,
    onChange: handleVariantChange,
    size: "small",
    style: {
      minWidth: 120
    }
  }, Object.entries(deviceConfig.variants || {}).map(function (_ref4) {
    var _ref5 = _slicedToArray(_ref4, 2),
      key = _ref5[0],
      variant = _ref5[1];
    return /*#__PURE__*/React.createElement(Option, {
      key: key,
      value: key
    }, variant.name);
  })), deviceConfig.category !== 'desktop' && /*#__PURE__*/React.createElement(Tooltip, {
    title: "Switch to ".concat(orientation === 'portrait' ? 'landscape' : 'portrait')
  }, /*#__PURE__*/React.createElement(Button, {
    icon: orientation === 'portrait' ? /*#__PURE__*/React.createElement(RotateRightOutlined, null) : /*#__PURE__*/React.createElement(RotateLeftOutlined, null),
    size: "small",
    onClick: handleOrientationChange
  })), /*#__PURE__*/React.createElement(Tooltip, {
    title: "Toggle Fullscreen"
  }, /*#__PURE__*/React.createElement(Button, {
    icon: /*#__PURE__*/React.createElement(FullscreenOutlined, null),
    size: "small",
    onClick: handleFullscreenToggle,
    type: isFullscreen ? 'primary' : 'default'
  })), /*#__PURE__*/React.createElement(Badge, {
    count: "".concat(deviceConfig.width, "\xD7").concat(deviceConfig.height),
    style: {
      backgroundColor: '#108ee9'
    }
  })), /*#__PURE__*/React.createElement(DeviceFrameContainer, {
    orientation: orientation
  }, showInfo && /*#__PURE__*/React.createElement(DeviceInfo, null, deviceConfig.name, " - ", deviceConfig.width, "\xD7", deviceConfig.height, orientation === 'landscape' && ' (Landscape)'), /*#__PURE__*/React.createElement(DeviceFrame, {
    category: deviceConfig.category,
    frame: deviceConfig.frame
  }, /*#__PURE__*/React.createElement(DeviceScreen, {
    width: deviceConfig.width,
    height: deviceConfig.height,
    scale: deviceConfig.scale,
    orientation: orientation,
    category: deviceConfig.category
  }, children))));
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (DevicePreviewFrame)));

/***/ }),

/***/ 88167:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(56822);
/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(53954);
/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85501);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);





function _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }



/**
 * Safe wrapper component that catches errors and provides fallback UI
 */
var SafeComponentWrapper = /*#__PURE__*/function (_React$Component) {
  function SafeComponentWrapper(props) {
    var _this;
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this, SafeComponentWrapper);
    _this = _callSuper(this, SafeComponentWrapper, [props]);
    _this.state = {
      hasError: false,
      error: null
    };
    return _this;
  }
  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(SafeComponentWrapper, _React$Component);
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(SafeComponentWrapper, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      console.error('Component error caught by SafeComponentWrapper:', error, errorInfo);
    }
  }, {
    key: "render",
    value: function render() {
      var _this2 = this;
      if (this.state.hasError) {
        var _this$state$error;
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Alert */ .Fc, {
          message: "Component Loading Error",
          description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", null, "This component failed to load properly. This might be due to missing dependencies or configuration issues."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("strong", null, "Error:"), " ", ((_this$state$error = this.state.error) === null || _this$state$error === void 0 ? void 0 : _this$state$error.message) || 'Unknown error'), this.props.fallback && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
            style: {
              marginTop: '16px'
            }
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", null, "Using fallback component:"), this.props.fallback)),
          type: "warning",
          showIcon: true,
          action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
            size: "small",
            onClick: function onClick() {
              return _this2.setState({
                hasError: false,
                error: null
              });
            }
          }, "Retry")
        }));
      }
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      return {
        hasError: true,
        error: error
      };
    }
  }]);
}(react__WEBPACK_IMPORTED_MODULE_5__.Component);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeComponentWrapper);

/***/ }),

/***/ 99160:
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {

/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(70572);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(82569);
/* harmony import */ var _EnhancedHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(6827);

var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;






var Content = antd__WEBPACK_IMPORTED_MODULE_2__/* .Layout */ .PE.Content,
  Footer = antd__WEBPACK_IMPORTED_MODULE_2__/* .Layout */ .PE.Footer;

// Styled components
var StyledLayout = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Layout */ .PE)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  min-height: 100vh;\n  background-color: var(--color-background);\n  transition: background-color 0.3s ease;\n"])));
var StyledContent = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay)(Content)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: 24px;\n  background-color: var(--color-background);\n  min-height: calc(100vh - 64px - 70px);\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    padding: 16px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 12px;\n  }\n"])));
var StyledFooter = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay)(Footer)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  text-align: center;\n  padding: 24px;\n  background-color: var(--color-surface);\n  border-top: 1px solid var(--color-border-light);\n  color: var(--color-text-secondary);\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  .footer-content {\n    max-width: 1200px;\n    margin: 0 auto;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-wrap: wrap;\n    gap: 16px;\n\n    @media (max-width: 768px) {\n      flex-direction: column;\n      text-align: center;\n    }\n  }\n\n  .footer-links {\n    display: flex;\n    gap: 24px;\n    align-items: center;\n\n    @media (max-width: 768px) {\n      gap: 16px;\n    }\n\n    a {\n      color: var(--color-text-secondary);\n      text-decoration: none;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: var(--color-primary);\n      }\n    }\n  }\n\n  .footer-info {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: var(--color-text-tertiary);\n    font-size: 12px;\n  }\n"])));
var BackToTopButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .BackTop */ .XT)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  .ant-back-top-content {\n    background-color: var(--color-primary);\n    color: white;\n    border-radius: 50%;\n    width: 48px;\n    height: 48px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: var(--shadow-lg);\n    transition: all 0.3s ease;\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      transform: scale(1.1);\n    }\n\n    .anticon {\n      font-size: 16px;\n    }\n  }\n"])));
var MainContainer = styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n"])));
var ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  background-color: var(--color-background);\n  border-radius: var(--border-radius-lg);\n  overflow: hidden;\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    border-radius: var(--border-radius-md);\n  }\n"])));
var EnhancedLayout = function EnhancedLayout(_ref) {
  var children = _ref.children,
    headerTitle = _ref.headerTitle,
    _ref$showStatus = _ref.showStatus,
    showStatus = _ref$showStatus === void 0 ? true : _ref$showStatus,
    onLogoClick = _ref.onLogoClick,
    headerActions = _ref.headerActions,
    _ref$footerLinks = _ref.footerLinks,
    footerLinks = _ref$footerLinks === void 0 ? [] : _ref$footerLinks,
    _ref$showBackToTop = _ref.showBackToTop,
    showBackToTop = _ref$showBackToTop === void 0 ? true : _ref$showBackToTop;
  var _useEnhancedTheme = useEnhancedTheme(),
    isDarkMode = _useEnhancedTheme.isDarkMode;
  var defaultFooterLinks = [{
    href: '/about',
    label: 'About'
  }, {
    href: '/docs',
    label: 'Documentation'
  }, {
    href: '/support',
    label: 'Support'
  }, {
    href: '/privacy',
    label: 'Privacy'
  }];
  var links = footerLinks.length > 0 ? footerLinks : defaultFooterLinks;
  return /*#__PURE__*/React.createElement(StyledLayout, null, /*#__PURE__*/React.createElement(EnhancedHeader, {
    title: headerTitle,
    showStatus: showStatus,
    onLogoClick: onLogoClick
  }, headerActions), /*#__PURE__*/React.createElement(StyledContent, {
    id: "main-content"
  }, /*#__PURE__*/React.createElement(MainContainer, null, /*#__PURE__*/React.createElement(ContentWrapper, null, children))), /*#__PURE__*/React.createElement(StyledFooter, null, /*#__PURE__*/React.createElement("div", {
    className: "footer-content"
  }, /*#__PURE__*/React.createElement("div", null, "App Builder \xA9", new Date().getFullYear(), " - Build with ease"), /*#__PURE__*/React.createElement("div", {
    className: "footer-links"
  }, links.map(function (link, index) {
    return /*#__PURE__*/React.createElement("a", {
      key: index,
      href: link.href,
      target: link.external ? '_blank' : undefined,
      rel: link.external ? 'noopener noreferrer' : undefined
    }, link.label);
  })), /*#__PURE__*/React.createElement("div", {
    className: "footer-info"
  }, /*#__PURE__*/React.createElement("span", null, "Theme: ", isDarkMode ? 'Dark' : 'Light')))), showBackToTop && /*#__PURE__*/React.createElement(BackToTopButton, null, /*#__PURE__*/React.createElement("div", {
    className: "ant-back-top-content"
  }, /*#__PURE__*/React.createElement(UpOutlined, null))));
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (EnhancedLayout)));

/***/ })

}]);