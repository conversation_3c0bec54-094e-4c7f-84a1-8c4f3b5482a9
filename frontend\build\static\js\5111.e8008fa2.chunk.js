"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5111],{

/***/ 17492:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* unused harmony exports CollaborationProvider, useCollaboration */
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(83590);





function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




// Create the collaboration context
var CollaborationContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.createContext)({
  // Session state
  currentSession: null,
  isConnected: false,
  isJoining: false,
  // Participants
  participants: [],
  activeParticipants: [],
  // Comments
  comments: [],
  // Operations
  pendingOperations: [],
  // Actions
  joinSession: function joinSession() {},
  leaveSession: function leaveSession() {},
  createComment: function createComment() {},
  updateComment: function updateComment() {},
  resolveComment: function resolveComment() {},
  sendOperation: function sendOperation() {},
  updatePresence: function updatePresence() {}
});

// User colors for visual identification
var USER_COLORS = (/* unused pure expression or super */ null && (['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9']));
var CollaborationProvider = function CollaborationProvider(_ref) {
  var children = _ref.children;
  var _useAuth = useAuth(),
    user = _useAuth.user,
    isAuthenticated = _useAuth.isAuthenticated;
  var _useState = useState(null),
    _useState2 = _slicedToArray(_useState, 2),
    currentSession = _useState2[0],
    setCurrentSession = _useState2[1];
  var _useState3 = useState(false),
    _useState4 = _slicedToArray(_useState3, 2),
    isConnected = _useState4[0],
    setIsConnected = _useState4[1];
  var _useState5 = useState(false),
    _useState6 = _slicedToArray(_useState5, 2),
    isJoining = _useState6[0],
    setIsJoining = _useState6[1];
  var _useState7 = useState([]),
    _useState8 = _slicedToArray(_useState7, 2),
    participants = _useState8[0],
    setParticipants = _useState8[1];
  var _useState9 = useState([]),
    _useState0 = _slicedToArray(_useState9, 2),
    comments = _useState0[0],
    setComments = _useState0[1];
  var _useState1 = useState([]),
    _useState10 = _slicedToArray(_useState1, 2),
    pendingOperations = _useState10[0],
    setPendingOperations = _useState10[1];

  // WebSocket connection
  var wsRef = useRef(null);
  var reconnectTimeoutRef = useRef(null);
  var heartbeatIntervalRef = useRef(null);

  // User presence tracking
  var _useState11 = useState({}),
    _useState12 = _slicedToArray(_useState11, 2),
    userPresence = _useState12[0],
    setUserPresence = _useState12[1];
  var presenceUpdateTimeoutRef = useRef(null);

  // Get active participants (those who have been active recently)
  var activeParticipants = participants.filter(function (p) {
    var lastSeen = new Date(p.last_seen);
    var now = new Date();
    var timeDiff = now - lastSeen;
    return timeDiff < 5 * 60 * 1000; // Active if seen within 5 minutes
  });

  // Assign colors to participants
  var getParticipantColor = useCallback(function (userId) {
    var index = participants.findIndex(function (p) {
      return p.user_id === userId;
    });
    return USER_COLORS[index % USER_COLORS.length];
  }, [participants]);

  // WebSocket connection management
  var connectWebSocket = useCallback(function (sessionId) {
    if (!isAuthenticated || !user) {
      console.warn('Cannot connect to collaboration WebSocket: user not authenticated');
      return;
    }
    try {
      var protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      var wsUrl = "".concat(protocol, "//").concat(window.location.host, "/ws/collaboration/").concat(sessionId, "/");
      wsRef.current = new WebSocket(wsUrl);
      wsRef.current.onopen = function () {
        console.log('Collaboration WebSocket connected');
        setIsConnected(true);

        // Start heartbeat
        heartbeatIntervalRef.current = setInterval(function () {
          var _wsRef$current;
          if (((_wsRef$current = wsRef.current) === null || _wsRef$current === void 0 ? void 0 : _wsRef$current.readyState) === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
              type: 'ping',
              timestamp: Date.now()
            }));
          }
        }, 30000);

        // Send join session message
        wsRef.current.send(JSON.stringify({
          type: 'join_session',
          session_id: sessionId
        }));
      };
      wsRef.current.onmessage = function (event) {
        try {
          var data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      wsRef.current.onclose = function (event) {
        console.log('Collaboration WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);

        // Clear heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }

        // Attempt to reconnect if not a clean close
        if (event.code !== 1000 && currentSession) {
          scheduleReconnect(sessionId);
        }
      };
      wsRef.current.onerror = function (error) {
        console.error('Collaboration WebSocket error:', error);
        message.error('Connection error occurred');
      };
    } catch (error) {
      console.error('Error connecting to collaboration WebSocket:', error);
      message.error('Failed to connect to collaboration service');
    }
  }, [isAuthenticated, user, currentSession]);
  var scheduleReconnect = useCallback(function (sessionId) {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    reconnectTimeoutRef.current = setTimeout(function () {
      console.log('Attempting to reconnect to collaboration WebSocket...');
      connectWebSocket(sessionId);
    }, 3000);
  }, [connectWebSocket]);
  var disconnectWebSocket = useCallback(function () {
    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected');
      wsRef.current = null;
    }
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    setIsConnected(false);
  }, []);

  // Handle WebSocket messages
  var handleWebSocketMessage = useCallback(function (data) {
    switch (data.type) {
      case 'session_joined':
        setCurrentSession(data.data);
        setParticipants(data.data.participants || []);
        setComments(data.data.comments || []);
        setIsJoining(false);
        message.success('Joined collaboration session');
        break;
      case 'user_joined':
        setParticipants(function (prev) {
          var existing = prev.find(function (p) {
            return p.user_id === data.user_id;
          });
          if (existing) {
            return prev.map(function (p) {
              return p.user_id === data.user_id ? _objectSpread(_objectSpread({}, p), {}, {
                is_active: true,
                last_seen: data.timestamp
              }) : p;
            });
          } else {
            return [].concat(_toConsumableArray(prev), [{
              user_id: data.user_id,
              username: data.username,
              is_active: true,
              last_seen: data.timestamp,
              role: 'editor'
            }]);
          }
        });
        if (data.user_id !== (user === null || user === void 0 ? void 0 : user.id)) {
          message.info("".concat(data.username, " joined the session"));
        }
        break;
      case 'user_left':
        setParticipants(function (prev) {
          return prev.map(function (p) {
            return p.user_id === data.user_id ? _objectSpread(_objectSpread({}, p), {}, {
              is_active: false
            }) : p;
          });
        });
        if (data.user_id !== (user === null || user === void 0 ? void 0 : user.id)) {
          message.info("".concat(data.username, " left the session"));
        }
        break;
      case 'cursor_position':
        if (data.user_id !== (user === null || user === void 0 ? void 0 : user.id)) {
          setUserPresence(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, _defineProperty({}, data.user_id, _objectSpread(_objectSpread({}, prev[data.user_id]), {}, {
              cursor_position: data.position,
              username: data.username,
              timestamp: data.timestamp
            })));
          });
        }
        break;
      case 'selection_change':
        if (data.user_id !== (user === null || user === void 0 ? void 0 : user.id)) {
          setUserPresence(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, _defineProperty({}, data.user_id, _objectSpread(_objectSpread({}, prev[data.user_id]), {}, {
              selection: data.selection,
              username: data.username,
              timestamp: data.timestamp
            })));
          });
        }
        break;
      case 'comment_created':
        setComments(function (prev) {
          return [data.comment].concat(_toConsumableArray(prev));
        });
        if (data.comment.author !== (user === null || user === void 0 ? void 0 : user.username)) {
          message.info('New comment added');
        }
        break;
      case 'comment_updated':
        setComments(function (prev) {
          return prev.map(function (c) {
            return c.id === data.comment.id ? data.comment : c;
          });
        });
        break;
      case 'comment_resolved':
        setComments(function (prev) {
          return prev.map(function (c) {
            return c.id === data.comment.id ? _objectSpread(_objectSpread({}, c), {}, {
              status: data.comment.status,
              resolved_at: data.comment.resolved_at
            }) : c;
          });
        });
        break;
      case 'component_operation':
        // Handle real-time component operations
        if (data.operation.user_id !== (user === null || user === void 0 ? void 0 : user.id)) {
          // This would trigger a callback to update the app state
          // The actual implementation would depend on the app's state management
          console.log('Received component operation:', data.operation);
        }
        break;
      case 'error':
        console.error('Collaboration error:', data.message);
        message.error(data.message);
        break;
      default:
        console.log('Unknown collaboration message type:', data.type);
    }
  }, [user]);

  // Join a collaboration session
  var joinSession = useCallback(/*#__PURE__*/function () {
    var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee(sessionId) {
      var _t;
      return _regeneratorRuntime.wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (isAuthenticated) {
              _context.next = 1;
              break;
            }
            message.error('You must be logged in to join a collaboration session');
            return _context.abrupt("return", false);
          case 1:
            setIsJoining(true);
            _context.prev = 2;
            // Connect to WebSocket
            connectWebSocket(sessionId);
            return _context.abrupt("return", true);
          case 3:
            _context.prev = 3;
            _t = _context["catch"](2);
            console.error('Error joining session:', _t);
            message.error('Failed to join collaboration session');
            setIsJoining(false);
            return _context.abrupt("return", false);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[2, 3]]);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), [isAuthenticated, connectWebSocket]);

  // Leave the current session
  var leaveSession = useCallback(function () {
    if (wsRef.current && currentSession) {
      wsRef.current.send(JSON.stringify({
        type: 'leave_session'
      }));
    }
    disconnectWebSocket();
    setCurrentSession(null);
    setParticipants([]);
    setComments([]);
    setUserPresence({});
    setPendingOperations([]);
    setIsJoining(false);
  }, [currentSession, disconnectWebSocket]);

  // Create a comment
  var createComment = useCallback(function (content) {
    var componentId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    var canvasPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
    var parentId = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
    if (!wsRef.current || !isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }
    wsRef.current.send(JSON.stringify({
      type: 'create_comment',
      content: content,
      component_id: componentId,
      canvas_position: canvasPosition,
      parent_id: parentId
    }));
  }, [isConnected]);

  // Update a comment
  var updateComment = useCallback(function (commentId, content) {
    if (!wsRef.current || !isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }
    wsRef.current.send(JSON.stringify({
      type: 'update_comment',
      comment_id: commentId,
      content: content
    }));
  }, [isConnected]);

  // Resolve a comment
  var resolveComment = useCallback(function (commentId) {
    if (!wsRef.current || !isConnected) {
      message.error('Not connected to collaboration session');
      return;
    }
    wsRef.current.send(JSON.stringify({
      type: 'resolve_comment',
      comment_id: commentId
    }));
  }, [isConnected]);

  // Send a component operation
  var sendOperation = useCallback(function (operationType, targetId, operationData) {
    if (!wsRef.current || !isConnected) {
      console.warn('Cannot send operation: not connected to collaboration session');
      return;
    }
    var operation = {
      type: 'component_operation',
      operation_type: operationType,
      target_id: targetId,
      operation_data: operationData,
      timestamp: Date.now()
    };
    wsRef.current.send(JSON.stringify(operation));

    // Add to pending operations
    setPendingOperations(function (prev) {
      return [].concat(_toConsumableArray(prev), [operation]);
    });
  }, [isConnected]);

  // Update user presence
  var updatePresence = useCallback(function (presenceData) {
    if (!wsRef.current || !isConnected) {
      return;
    }

    // Debounce presence updates
    if (presenceUpdateTimeoutRef.current) {
      clearTimeout(presenceUpdateTimeoutRef.current);
    }
    presenceUpdateTimeoutRef.current = setTimeout(function () {
      if (presenceData.cursor_position) {
        wsRef.current.send(JSON.stringify({
          type: 'cursor_position',
          position: presenceData.cursor_position
        }));
      }
      if (presenceData.selection) {
        wsRef.current.send(JSON.stringify({
          type: 'selection_change',
          selection: presenceData.selection
        }));
      }
    }, 100); // Debounce by 100ms
  }, [isConnected]);

  // Cleanup on unmount
  useEffect(function () {
    return function () {
      disconnectWebSocket();
      if (presenceUpdateTimeoutRef.current) {
        clearTimeout(presenceUpdateTimeoutRef.current);
      }
    };
  }, [disconnectWebSocket]);
  var value = {
    // Session state
    currentSession: currentSession,
    isConnected: isConnected,
    isJoining: isJoining,
    // Participants
    participants: participants,
    activeParticipants: activeParticipants,
    userPresence: userPresence,
    getParticipantColor: getParticipantColor,
    // Comments
    comments: comments,
    // Operations
    pendingOperations: pendingOperations,
    // Actions
    joinSession: joinSession,
    leaveSession: leaveSession,
    createComment: createComment,
    updateComment: updateComment,
    resolveComment: resolveComment,
    sendOperation: sendOperation,
    updatePresence: updatePresence
  };
  return /*#__PURE__*/React.createElement(CollaborationContext.Provider, {
    value: value
  }, children);
};

// Hook to use collaboration context
var useCollaboration = function useCollaboration() {
  var context = useContext(CollaborationContext);
  if (!context) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  return context;
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (CollaborationContext)));

/***/ })

}]);