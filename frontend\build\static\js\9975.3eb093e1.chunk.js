"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9975],{

/***/ 2356:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ performance_PerformanceMonitor)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./src/styles/components.js
var components = __webpack_require__(57749);
;// ./src/styles/optimizedComponents.js

var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12, _templateObject13, _templateObject14, _templateObject15, _templateObject16, _templateObject17, _templateObject18, _templateObject19, _templateObject20, _templateObject21, _templateObject22, _templateObject23;



// Direct Ant Design imports to avoid circular dependency issues



/**
 * Optimized styled components for the application
 * These components are memoized and use shouldForwardProp to prevent
 * unnecessary re-renders and prop forwarding
 */

var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;

// Helper to filter out custom props
var customProps = ['flex', 'flexMd', 'flexLg', 'status', 'messageType', 'isActive', 'isSelected', 'isHovered', 'isDragOver', 'isDragging', 'previewMode', 'visible', 'isValid', 'isInvalid', 'disabled', 'color', 'isDark', 'size', 'minHeight', 'background', 'hoverBackground'];
var shouldForwardCustomProp = function shouldForwardCustomProp(prop) {
  return !customProps.includes(prop);
};

// Optimized Typography components
var PageTitle = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* PageTitle */.sT).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var SectionTitle = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* SectionTitle */._x).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var SubTitle = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* SubTitle */.tK).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Layout components
var Container = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Container */.mc).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var Section = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Section */.wn).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var Row = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Row */.fI).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var Column = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Column */.VP).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var Grid = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Grid */.xA).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Card components
var StyledCard = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledCard */.ee).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var FeatureCard = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* FeatureCard */.dd).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var DashboardCard = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* DashboardCard */.pK).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject1 || (_templateObject1 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Button components
var PrimaryButton = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* PrimaryButton */.jn).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject10 || (_templateObject10 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var SecondaryButton = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* SecondaryButton */.tA).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject11 || (_templateObject11 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var IconButton = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* IconButton */.K0).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject12 || (_templateObject12 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Form components
var FormGroup = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* FormGroup */.gE).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject13 || (_templateObject13 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var StyledInput = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledInput */.sQ).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject14 || (_templateObject14 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var StyledTextArea = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledTextArea */.aQ).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject15 || (_templateObject15 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Alert components
var StyledAlert = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledAlert */.cN).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject16 || (_templateObject16 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Loading components
var LoadingContainer = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* LoadingContainer */.YM).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject17 || (_templateObject17 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var StyledSpin = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledSpin */.IO).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject18 || (_templateObject18 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Tag components
var StyledTag = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StyledTag */.ih).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject19 || (_templateObject19 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));
var StatusTag = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* StatusTag */.hC).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject20 || (_templateObject20 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Divider
var Divider = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Divider */.cG).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject21 || (_templateObject21 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Badge
var Badge = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Badge */.Ex).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject22 || (_templateObject22 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Optimized Avatar
var Avatar = /*#__PURE__*/(0,react.memo)((0,styled_components_browser_esm/* default */.Ay)(components/* Avatar */.eu).withConfig({
  shouldForwardProp: shouldForwardCustomProp
})(_templateObject23 || (_templateObject23 = (0,taggedTemplateLiteral/* default */.A)(["\n  /* Additional styles can be added here */\n"]))));

// Export all components
/* harmony default export */ const optimizedComponents = ({
  PageTitle: PageTitle,
  SectionTitle: SectionTitle,
  SubTitle: SubTitle,
  Container: Container,
  Section: Section,
  Row: Row,
  Column: Column,
  Grid: Grid,
  StyledCard: StyledCard,
  FeatureCard: FeatureCard,
  DashboardCard: DashboardCard,
  PrimaryButton: PrimaryButton,
  SecondaryButton: SecondaryButton,
  IconButton: IconButton,
  FormGroup: FormGroup,
  StyledInput: StyledInput,
  StyledTextArea: StyledTextArea,
  StyledAlert: StyledAlert,
  LoadingContainer: LoadingContainer,
  StyledSpin: StyledSpin,
  StyledTag: StyledTag,
  StatusTag: StatusTag,
  Divider: Divider,
  Badge: Badge,
  Avatar: Avatar
});
;// ./src/components/performance/PerformanceMonitor.js




var PerformanceMonitor_templateObject, PerformanceMonitor_templateObject2, PerformanceMonitor_templateObject3, PerformanceMonitor_templateObject4, PerformanceMonitor_templateObject5, PerformanceMonitor_templateObject6, PerformanceMonitor_templateObject7;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





var PerformanceMonitor_Title = es/* Typography */.o5.Title,
  PerformanceMonitor_Text = es/* Typography */.o5.Text,
  PerformanceMonitor_Paragraph = es/* Typography */.o5.Paragraph;

// Styled components
var MonitorCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(PerformanceMonitor_templateObject || (PerformanceMonitor_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: fixed;\n  bottom: ", ";\n  top: ", ";\n  right: 20px;\n  width: ", ";\n  z-index: 1000;\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);\n  transition: all 0.3s ease;\n"])), function (props) {
  return props.expanded ? '20px' : 'auto';
}, function (props) {
  return props.expanded ? 'auto' : '20px';
}, function (props) {
  return props.expanded ? '600px' : '300px';
});
var MonitorHeader = styled_components_browser_esm/* default */.Ay.div(PerformanceMonitor_templateObject2 || (PerformanceMonitor_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"])));
var ButtonGroup = styled_components_browser_esm/* default */.Ay.div(PerformanceMonitor_templateObject3 || (PerformanceMonitor_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n"])));
var LongTasksList = styled_components_browser_esm/* default */.Ay.div(PerformanceMonitor_templateObject4 || (PerformanceMonitor_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  max-height: 150px;\n  overflow-y: auto;\n"])));
var LongTaskItem = styled_components_browser_esm/* default */.Ay.div(PerformanceMonitor_templateObject5 || (PerformanceMonitor_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 8px;\n"])));
var TaskTime = (0,styled_components_browser_esm/* default */.Ay)(PerformanceMonitor_Text)(PerformanceMonitor_templateObject6 || (PerformanceMonitor_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n"])));
var FooterText = (0,styled_components_browser_esm/* default */.Ay)(PerformanceMonitor_Paragraph)(PerformanceMonitor_templateObject7 || (PerformanceMonitor_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 12px;\n  text-align: center;\n"])));

/**
 * PerformanceMonitor component
 * Displays performance metrics for the application
 */
var PerformanceMonitor = function PerformanceMonitor(_ref) {
  var _ref$visible = _ref.visible,
    visible = _ref$visible === void 0 ? false : _ref$visible;
  var _useState = (0,react.useState)({
      fps: 0,
      memory: {
        used: 0,
        total: 0,
        limit: 0
      },
      timing: {
        domComplete: 0,
        domInteractive: 0,
        loadEvent: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0
      },
      resources: {
        count: 0,
        totalSize: 0
      },
      longTasks: []
    }),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    metrics = _useState2[0],
    setMetrics = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    expanded = _useState4[0],
    setExpanded = _useState4[1];
  var _useState5 = (0,react.useState)(visible),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    isVisible = _useState6[0],
    setIsVisible = _useState6[1];

  // Collect performance metrics
  (0,react.useEffect)(function () {
    if (!isVisible) return;

    // Function to collect metrics
    var collectMetrics = function collectMetrics() {
      // FPS calculation
      var frameCount = 0;
      var lastTime = performance.now();
      var _calculateFPS = function calculateFPS() {
        var now = performance.now();
        var delta = now - lastTime;
        if (delta >= 1000) {
          var fps = Math.round(frameCount * 1000 / delta);
          setMetrics(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              fps: fps
            });
          });
          frameCount = 0;
          lastTime = now;
        }
        frameCount++;
        requestAnimationFrame(_calculateFPS);
      };

      // Start FPS calculation
      var fpsId = requestAnimationFrame(_calculateFPS);

      // Collect memory usage if available
      if (window.performance && window.performance.memory) {
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            memory: {
              used: Math.round(window.performance.memory.usedJSHeapSize / (1024 * 1024)),
              total: Math.round(window.performance.memory.totalJSHeapSize / (1024 * 1024)),
              limit: Math.round(window.performance.memory.jsHeapSizeLimit / (1024 * 1024))
            }
          });
        });
      }

      // Collect navigation timing metrics
      if (performance.timing) {
        var timing = performance.timing;
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            timing: {
              domComplete: timing.domComplete - timing.navigationStart,
              domInteractive: timing.domInteractive - timing.navigationStart,
              loadEvent: timing.loadEventEnd - timing.navigationStart,
              firstContentfulPaint: 0,
              // Will be set later
              largestContentfulPaint: 0 // Will be set later
            }
          });
        });
      }

      // Collect resource metrics
      var resources = performance.getEntriesByType('resource');
      setMetrics(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          resources: {
            count: resources.length,
            totalSize: resources.reduce(function (total, resource) {
              return total + (resource.transferSize || 0);
            }, 0) / (1024 * 1024)
          }
        });
      });

      // Collect paint metrics
      var paintMetrics = performance.getEntriesByType('paint');
      paintMetrics.forEach(function (metric) {
        if (metric.name === 'first-contentful-paint') {
          setMetrics(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              timing: _objectSpread(_objectSpread({}, prev.timing), {}, {
                firstContentfulPaint: metric.startTime
              })
            });
          });
        }
      });

      // Observe Largest Contentful Paint
      var observeLCP = new PerformanceObserver(function (entryList) {
        var entries = entryList.getEntries();
        var lastEntry = entries[entries.length - 1];
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            timing: _objectSpread(_objectSpread({}, prev.timing), {}, {
              largestContentfulPaint: lastEntry.startTime
            })
          });
        });
      });
      observeLCP.observe({
        type: 'largest-contentful-paint',
        buffered: true
      });

      // Observe Long Tasks
      var observeLongTasks = new PerformanceObserver(function (entryList) {
        var entries = entryList.getEntries();
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            longTasks: [].concat((0,toConsumableArray/* default */.A)(prev.longTasks), (0,toConsumableArray/* default */.A)(entries)).slice(-10) // Keep last 10 long tasks
          });
        });
      });
      observeLongTasks.observe({
        type: 'longtask',
        buffered: true
      });

      // Cleanup function
      return function () {
        cancelAnimationFrame(fpsId);
        observeLCP.disconnect();
        observeLongTasks.disconnect();
      };
    };

    // Start collecting metrics
    var cleanup = collectMetrics();

    // Cleanup function
    return cleanup;
  }, [isVisible]);

  // Toggle visibility
  var toggleVisibility = function toggleVisibility() {
    setIsVisible(!isVisible);
  };

  // Toggle expanded view
  var toggleExpanded = function toggleExpanded() {
    setExpanded(!expanded);
  };

  // Reset metrics
  var resetMetrics = function resetMetrics() {
    setMetrics({
      fps: 0,
      memory: {
        used: 0,
        total: 0,
        limit: 0
      },
      timing: {
        domComplete: 0,
        domInteractive: 0,
        loadEvent: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0
      },
      resources: {
        count: 0,
        totalSize: 0
      },
      longTasks: []
    });

    // Clear performance entries
    performance.clearMarks();
    performance.clearMeasures();
    performance.clearResourceTimings();
  };

  // If not visible, render only the toggle button
  if (!isVisible) {
    return /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "Show Performance Monitor"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "primary",
      shape: "circle",
      icon: /*#__PURE__*/react.createElement(icons_es/* DashboardOutlined */.zpd, null),
      onClick: toggleVisibility,
      style: {
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 1000
      }
    }));
  }

  // Get FPS color based on value
  var getFPSColor = function getFPSColor(fps) {
    if (fps >= 55) return '#52c41a'; // Green
    if (fps >= 30) return '#faad14'; // Yellow
    return '#f5222d'; // Red
  };

  // Get memory usage percentage
  var getMemoryPercentage = function getMemoryPercentage() {
    if (!metrics.memory.total) return 0;
    return Math.round(metrics.memory.used / metrics.memory.total * 100);
  };

  // Get memory usage color
  var getMemoryColor = function getMemoryColor() {
    var percentage = getMemoryPercentage();
    if (percentage < 70) return '#52c41a'; // Green
    if (percentage < 90) return '#faad14'; // Yellow
    return '#f5222d'; // Red
  };
  return /*#__PURE__*/react.createElement("div", {
    className: "performance-monitor"
  }, /*#__PURE__*/react.createElement(MonitorCard, {
    expanded: expanded,
    title: /*#__PURE__*/react.createElement(MonitorHeader, null, /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* DashboardOutlined */.zpd, null), " Performance Monitor"), /*#__PURE__*/react.createElement(ButtonGroup, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
      onClick: resetMetrics,
      style: {
        marginRight: '8px'
      },
      "aria-label": "Reset metrics"
    }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      icon: expanded ? /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, null) : /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, null),
      onClick: toggleExpanded,
      style: {
        marginRight: '8px'
      },
      "aria-label": expanded ? "Collapse view" : "Expand view"
    }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "text",
      icon: /*#__PURE__*/react.createElement(icons_es/* CloseOutlined */.r$3, null),
      onClick: toggleVisibility,
      "aria-label": "Close performance monitor"
    }))),
    bodyStyle: {
      padding: expanded ? '16px' : '8px'
    }
  }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: expanded ? 12 : 24
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "FPS",
    value: metrics.fps,
    suffix: "fps",
    valueStyle: {
      color: getFPSColor(metrics.fps)
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* ClockCircleOutlined */.L8Y, null)
  }), expanded && /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: metrics.fps / 60 * 100,
    strokeColor: getFPSColor(metrics.fps),
    showInfo: false,
    size: "small",
    style: {
      marginTop: '8px'
    }
  })), metrics.memory.used > 0 && /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: expanded ? 12 : 24
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Memory Usage",
    value: metrics.memory.used,
    suffix: "MB",
    valueStyle: {
      color: getMemoryColor()
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* BarChartOutlined */.cd5, null)
  }), expanded && /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "".concat(metrics.memory.used, "MB / ").concat(metrics.memory.total, "MB")
  }, /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: getMemoryPercentage(),
    strokeColor: getMemoryColor(),
    showInfo: false,
    size: "small",
    style: {
      marginTop: '8px'
    }
  }))), expanded && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 24
  }, /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    orientation: "left"
  }, "Page Load Timing")), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "DOM Interactive",
    value: metrics.timing.domInteractive,
    suffix: "ms",
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "DOM Complete",
    value: metrics.timing.domComplete,
    suffix: "ms",
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "First Contentful Paint",
    value: Math.round(metrics.timing.firstContentfulPaint),
    suffix: "ms",
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Largest Contentful Paint",
    value: Math.round(metrics.timing.largestContentfulPaint),
    suffix: "ms",
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 24
  }, /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    orientation: "left"
  }, "Resources")), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Resource Count",
    value: metrics.resources.count,
    valueStyle: {
      fontSize: '16px'
    }
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 12
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Total Size",
    value: metrics.resources.totalSize.toFixed(2),
    suffix: "MB",
    valueStyle: {
      fontSize: '16px'
    }
  })), metrics.longTasks.length > 0 && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 24
  }, /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    orientation: "left"
  }, "Long Tasks"), /*#__PURE__*/react.createElement(LongTasksList, null, metrics.longTasks.map(function (task, index) {
    return /*#__PURE__*/react.createElement(LongTaskItem, {
      key: index
    }, /*#__PURE__*/react.createElement(PerformanceMonitor_Text, {
      type: "danger"
    }, /*#__PURE__*/react.createElement(icons_es/* WarningOutlined */.v7y, null), " Task blocked for ", task.duration.toFixed(2), "ms"), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(TaskTime, {
      type: "secondary"
    }, new Date(task.startTime).toLocaleTimeString())));
  })))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 24
  }, /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(FooterText, {
    type: "secondary"
  }, /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null), " Performance monitoring active"))))));
};
/* harmony default export */ const performance_PerformanceMonitor = (PerformanceMonitor);

/***/ })

}]);