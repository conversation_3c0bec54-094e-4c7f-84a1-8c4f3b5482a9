"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9578],{

/***/ 49697:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71468);
/* harmony import */ var _redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(41533);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(70572);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;






var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Paragraph;
var Panel = antd__WEBPACK_IMPORTED_MODULE_4__/* .Collapse */ .SD.Panel;
var TextArea = antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd.TextArea;

// Styled components
var StatusBadge = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Badge */ .Ex)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  .ant-badge-status-dot {\n    width: 10px;\n    height: 10px;\n  }\n"])));
var MessageContainer = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  max-height: 300px;\n  overflow-y: auto;\n  margin-bottom: 16px;\n  padding: 8px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  background-color: ", ";\n"])), function (props) {
  return props.theme === 'dark' ? '#1f1f1f' : '#f5f5f5';
});
var MessageItem = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 8px;\n  margin-bottom: 8px;\n  border-radius: 4px;\n  background-color: ", ";\n  border-left: 4px solid ", ";\n  color: ", ";\n  word-break: break-word;\n"])), function (props) {
  return props.type === 'sent' ? props.theme === 'dark' ? '#177ddc' : '#e6f7ff' : props.theme === 'dark' ? '#2b2b2b' : '#ffffff';
}, function (props) {
  return props.type === 'sent' ? '#1890ff' : props.status === 'error' ? '#ff4d4f' : props.status === 'warning' ? '#faad14' : '#52c41a';
}, function (props) {
  return props.theme === 'dark' ? '#ffffff' : '#000000';
});
var TimeStamp = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4)(Text)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  font-size: 12px;\n  color: ", ";\n  margin-left: 8px;\n"])), function (props) {
  return props.theme === 'dark' ? '#8c8c8c' : '#8c8c8c';
});
var ConnectionStatus = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n"])));

/**
 * WebSocketManager component
 * 
 * This component provides a user interface for managing WebSocket connections
 * and viewing WebSocket messages.
 */
var WebSocketManager = function WebSocketManager(_ref) {
  var _ref$visible = _ref.visible,
    visible = _ref$visible === void 0 ? false : _ref$visible,
    _ref$onClose = _ref.onClose,
    onClose = _ref$onClose === void 0 ? function () {} : _ref$onClose,
    _ref$placement = _ref.placement,
    placement = _ref$placement === void 0 ? 'right' : _ref$placement,
    _ref$width = _ref.width,
    width = _ref$width === void 0 ? 600 : _ref$width;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useDispatch */ .wA)();
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _Form$useForm3 = antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.useForm(),
    _Form$useForm4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm3, 1),
    messageForm = _Form$useForm4[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    autoScroll = _useState2[0],
    setAutoScroll = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    filterText = _useState4[0],
    setFilterText = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    showSettings = _useState6[0],
    setShowSettings = _useState6[1];

  // Get WebSocket state from Redux store with error handling
  var connected = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket;
    return (state === null || state === void 0 || (_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
  });
  var connecting = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket2;
    return (state === null || state === void 0 || (_state$websocket2 = state.websocket) === null || _state$websocket2 === void 0 ? void 0 : _state$websocket2.connecting) || false;
  });
  var error = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket3;
    return (state === null || state === void 0 || (_state$websocket3 = state.websocket) === null || _state$websocket3 === void 0 ? void 0 : _state$websocket3.error) || null;
  });
  var messages = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket4;
    return (state === null || state === void 0 || (_state$websocket4 = state.websocket) === null || _state$websocket4 === void 0 ? void 0 : _state$websocket4.messages) || [];
  });
  var url = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket5;
    return (state === null || state === void 0 || (_state$websocket5 = state.websocket) === null || _state$websocket5 === void 0 ? void 0 : _state$websocket5.url) || null;
  });
  var reconnectAttempts = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket6;
    return (state === null || state === void 0 || (_state$websocket6 = state.websocket) === null || _state$websocket6 === void 0 ? void 0 : _state$websocket6.reconnectAttempts) || 0;
  });
  var reconnectInterval = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket7;
    return (state === null || state === void 0 || (_state$websocket7 = state.websocket) === null || _state$websocket7 === void 0 ? void 0 : _state$websocket7.reconnectInterval) || 5000;
  });

  // Get theme from Redux store
  var isDarkMode = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$themes, _state$themes2;
    var activeThemeId = (_state$themes = state.themes) === null || _state$themes === void 0 ? void 0 : _state$themes.activeTheme;
    var themes = ((_state$themes2 = state.themes) === null || _state$themes2 === void 0 ? void 0 : _state$themes2.themes) || [];
    var activeTheme = themes.find(function (theme) {
      return theme.id === activeThemeId;
    });
    return (activeTheme === null || activeTheme === void 0 ? void 0 : activeTheme.isDark) || false;
  });

  // Auto-scroll to bottom of message container
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (autoScroll) {
      var container = document.getElementById('ws-message-container');
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [messages, autoScroll]);

  // Connect to WebSocket
  var handleConnect = function handleConnect() {
    form.validateFields().then(function (values) {
      var url = values.url,
        reconnectAttempts = values.reconnectAttempts,
        reconnectInterval = values.reconnectInterval,
        protocols = values.protocols;
      dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsConnect */ .Lw)(url, {
        reconnectAttempts: reconnectAttempts,
        reconnectInterval: reconnectInterval,
        protocols: protocols ? protocols.split(',').map(function (p) {
          return p.trim();
        }) : undefined
      }));
    });
  };

  // Disconnect from WebSocket
  var handleDisconnect = function handleDisconnect() {
    dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsDisconnect */ .rD)());
  };

  // Send message
  var handleSendMessage = function handleSendMessage() {
    messageForm.validateFields().then(function (values) {
      var message = values.message;
      try {
        // Try to parse as JSON
        var jsonMessage = JSON.parse(message);
        dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsSendMessage */ .EZ)(jsonMessage));
      } catch (error) {
        // Send as plain text
        dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsSendMessage */ .EZ)(message));
      }

      // Clear message input
      messageForm.resetFields();
    });
  };

  // Clear messages
  var handleClearMessages = function handleClearMessages() {
    dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsClearMessages */ .G2)());
  };

  // Toggle settings drawer
  var toggleSettings = function toggleSettings() {
    setShowSettings(!showSettings);
  };

  // Filter messages
  var filteredMessages = messages.filter(function (message) {
    if (!filterText) return true;
    var messageStr = (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(message.data) === 'object' ? JSON.stringify(message.data) : String(message.data);
    return messageStr.toLowerCase().includes(filterText.toLowerCase());
  });

  // Format message for display
  var formatMessage = function formatMessage(message) {
    if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(message) === 'object') {
      try {
        return JSON.stringify(message, null, 2);
      } catch (error) {
        return String(message);
      }
    }
    return String(message);
  };

  // Get connection status
  var getConnectionStatus = function getConnectionStatus() {
    if (connected) {
      return {
        status: 'success',
        text: 'Connected',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CheckCircleOutlined */ .hWy, null)
      };
    } else if (connecting) {
      return {
        status: 'processing',
        text: 'Connecting',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Spin */ .tK, {
          size: "small"
        })
      };
    } else if (error) {
      return {
        status: 'error',
        text: 'Error',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseCircleOutlined */ .bBN, null)
      };
    } else {
      return {
        status: 'default',
        text: 'Disconnected',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DisconnectOutlined */ .Bu6, null)
      };
    }
  };
  var connectionStatus = getConnectionStatus();

  // Initialize form with current values
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    form.setFieldsValue({
      url: url || 'ws://localhost:8000/ws',
      reconnectAttempts: reconnectAttempts || 5,
      reconnectInterval: reconnectInterval || 3000,
      protocols: ''
    });
  }, [form, url, reconnectAttempts, reconnectInterval]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ApiOutlined */ .bfv, {
      style: {
        marginRight: 8
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "WebSocket Manager")),
    placement: placement,
    width: width,
    onClose: onClose,
    open: visible,
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null),
      onClick: toggleSettings
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ConnectionStatus, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(StatusBadge, {
    status: connectionStatus.status
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, connectionStatus.text), error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
    title: error.message
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .WarningOutlined */ .v7y, {
    style: {
      marginLeft: 8,
      color: '#ff4d4f'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      marginLeft: 'auto'
    }
  }, connected ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DisconnectOutlined */ .Bu6, null),
    onClick: handleDisconnect,
    danger: true
  }, "Disconnect") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ApiOutlined */ .bfv, null),
    onClick: handleConnect,
    type: "primary",
    loading: connecting
  }, "Connect"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Collapse */ .SD, {
    defaultActiveKey: ['1', '2']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Panel, {
    header: "Connection Settings",
    key: "1",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ReloadOutlined */ .KF4, null),
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        form.resetFields();
      }
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    form: form,
    layout: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "url",
    label: "WebSocket URL",
    rules: [{
      required: true,
      message: 'Please enter WebSocket URL'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "ws://localhost:8000/ws",
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "protocols",
    label: "Protocols (comma-separated)"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "protocol1, protocol2",
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "reconnectAttempts",
    label: "Reconnect Attempts",
    rules: [{
      required: true,
      message: 'Please enter reconnect attempts'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 0,
    max: 10,
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "reconnectInterval",
    label: "Reconnect Interval (ms)",
    rules: [{
      required: true,
      message: 'Please enter reconnect interval'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 1000,
    max: 10000,
    disabled: connected || connecting
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Panel, {
    header: "Messages",
    key: "2",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: "Auto-scroll to bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
      checked: autoScroll,
      onChange: setAutoScroll,
      size: "small"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ClearOutlined */ .ohj, null),
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleClearMessages();
      }
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "Filter messages",
    value: filterText,
    onChange: function onChange(e) {
      return setFilterText(e.target.value);
    },
    allowClear: true,
    style: {
      marginBottom: 16
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .InfoCircleOutlined */ .rUN, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(MessageContainer, {
    id: "ws-message-container",
    theme: isDarkMode ? 'dark' : 'light'
  }, filteredMessages.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "No messages") : filteredMessages.map(function (message, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(MessageItem, {
      key: index,
      type: message.type,
      status: message.status,
      theme: isDarkMode ? 'dark' : 'light'
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        marginBottom: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
      color: message.type === 'sent' ? 'blue' : 'green'
    }, message.type === 'sent' ? 'Sent' : 'Received'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TimeStamp, {
      theme: isDarkMode ? 'dark' : 'light'
    }, new Date(message.timestamp).toLocaleTimeString())), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("pre", {
      style: {
        margin: 0,
        whiteSpace: 'pre-wrap'
      }
    }, formatMessage(message.data)));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    form: messageForm,
    layout: "inline",
    style: {
      display: 'flex',
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "message",
    style: {
      flex: 1,
      marginRight: 8
    },
    rules: [{
      required: true,
      message: 'Please enter a message'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, {
    placeholder: "Enter message (plain text or JSON)",
    autoSize: {
      minRows: 1,
      maxRows: 6
    },
    disabled: !connected
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SendOutlined */ .jnF, null),
    onClick: handleSendMessage,
    disabled: !connected
  }, "Send"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: "Advanced Settings",
    placement: "right",
    closable: true,
    onClose: toggleSettings,
    open: showSettings,
    width: 400
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, null, "These settings allow you to configure the WebSocket connection behavior."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    layout: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Ping Interval (ms)",
    name: "pingInterval",
    initialValue: 30000
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 1000,
    max: 60000
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Ping Message",
    name: "pingMessage",
    initialValue: "{\"type\":\"ping\"}"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Auto Reconnect",
    name: "autoReconnect",
    valuePropName: "checked",
    initialValue: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Debug Mode",
    name: "debugMode",
    valuePropName: "checked",
    initialValue: false
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, null)))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WebSocketManager);

/***/ }),

/***/ 99578:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(70572);





var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





// Import the core feature components with fallbacks
var ComponentBuilder, LayoutDesigner, ThemeManager, WebSocketManager;
try {
  ComponentBuilder = (__webpack_require__(16030)["default"]);
} catch (error) {
  console.warn('ComponentBuilder not available, using fallback');
  ComponentBuilder = function ComponentBuilder() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Component Builder"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component Builder is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Add Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Add Text"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Add Input"));
  };
}
try {
  LayoutDesigner = (__webpack_require__(95505)["default"]);
} catch (error) {
  console.warn('LayoutDesigner not available, using fallback');
  LayoutDesigner = function LayoutDesigner() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Layout Designer"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Layout Designer is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Grid Layout"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Flex Layout"));
  };
}
try {
  ThemeManager = (__webpack_require__(71667)["default"]);
} catch (error) {
  console.warn('ThemeManager not available, using fallback');
  ThemeManager = function ThemeManager() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Theme Manager"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Theme Manager is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Primary Color"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Typography"));
  };
}
try {
  WebSocketManager = (__webpack_require__(49697)/* ["default"] */ .A);
} catch (error) {
  console.warn('WebSocketManager not available, using fallback');
  WebSocketManager = function WebSocketManager(_ref) {
    var onConnectionChange = _ref.onConnectionChange;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "WebSocket Manager"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Real-time collaboration features"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary",
      onClick: function onClick() {
        console.log('WebSocket connection simulated');
        if (onConnectionChange) onConnectionChange(true);
      }
    }, "Connect"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      onClick: function onClick() {
        console.log('WebSocket disconnection simulated');
        if (onConnectionChange) onConnectionChange(false);
      }
    }, "Disconnect")));
  };
}
var Title = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Text;
var IntegratedContainer = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: 100vh;\n  background: #f5f5f5;\n  overflow: hidden;\n"])));
var HeaderBar = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  background: #fff;\n  padding: 16px 24px;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.06);\n"])));
var ContentArea = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: calc(100vh - 80px);\n  overflow: auto;\n  padding: 24px;\n"])));
var FeatureGrid = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  grid-template-rows: 1fr 1fr 1fr;\n  gap: 16px;\n  height: 100%;\n  min-height: 800px;\n"])));
var FeatureCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: 100%;\n  \n  .ant-card-body {\n    height: calc(100% - 57px);\n    overflow: auto;\n  }\n"])));
var DemoArea = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  grid-column: 1 / -1;\n  margin-top: 24px;\n  \n  .demo-canvas {\n    min-height: 300px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    padding: 20px;\n    background: #fafafa;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n  }\n"])));

/**
 * AppBuilderIntegrated - Comprehensive integrated app builder with all core features
 */
var AppBuilderIntegrated = function AppBuilderIntegrated() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)('overview'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    activeFeature = _useState2[0],
    setActiveFeature = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      components: [],
      layout: {
        type: 'grid',
        columns: 3
      },
      theme: {
        primaryColor: '#1890ff',
        fontFamily: 'Inter'
      },
      websocket: {
        connected: false,
        collaborators: []
      }
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    projectData = _useState4[0],
    setProjectData = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      components: [],
      layout: null,
      theme: null,
      isBuilding: false
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    demoApp = _useState6[0],
    setDemoApp = _useState6[1];

  // Sample app creation workflow
  var createSampleApp = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee() {
    var sampleComponents, sampleLayout, sampleTheme;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setDemoApp(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              isBuilding: true
            });
          });

          // Step 1: Add components
          sampleComponents = [{
            id: 'btn1',
            type: 'button',
            props: {
              text: 'Get Started',
              type: 'primary'
            },
            position: {
              x: 50,
              y: 50
            }
          }, {
            id: 'txt1',
            type: 'text',
            props: {
              text: 'Welcome to App Builder',
              size: 'large'
            },
            position: {
              x: 50,
              y: 100
            }
          }, {
            id: 'card1',
            type: 'card',
            props: {
              title: 'Feature Card',
              content: 'This is a sample card'
            },
            position: {
              x: 200,
              y: 50
            }
          }, {
            id: 'input1',
            type: 'input',
            props: {
              placeholder: 'Enter your name'
            },
            position: {
              x: 50,
              y: 200
            }
          }]; // Step 2: Apply layout
          sampleLayout = {
            type: 'grid',
            columns: 2,
            gap: '16px',
            responsive: true
          }; // Step 3: Apply theme
          sampleTheme = {
            primaryColor: '#52c41a',
            fontFamily: 'Inter, sans-serif',
            borderRadius: '8px',
            spacing: '16px'
          }; // Simulate building process
          _context.next = 1;
          return new Promise(function (resolve) {
            return setTimeout(resolve, 1000);
          });
        case 1:
          setDemoApp({
            components: sampleComponents,
            layout: sampleLayout,
            theme: sampleTheme,
            isBuilding: false
          });
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              components: sampleComponents,
              layout: sampleLayout,
              theme: sampleTheme
            });
          });
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), []);
  var tabItems = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(function () {
    var _demoApp$layout, _demoApp$layout2;
    return [{
      key: 'overview',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .RocketOutlined */ .PKb, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, "Integrated Builder")),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Alert */ .Fc, {
        message: "App Builder Enhanced - All Features Integrated",
        description: "This integrated view demonstrates all four core features working together: Component Builder, Layout Designer, Theme Manager, and WebSocket Manager.",
        type: "success",
        showIcon: true,
        style: {
          marginBottom: '24px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .AppstoreOutlined */ .rS9, null), "Component Builder"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('components');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ComponentBuilder, {
        onComponentAdd: function onComponentAdd(component) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              components: [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.components), [component])
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .LayoutOutlined */ .hy2, null), "Layout Designer"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('layouts');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(LayoutDesigner, {
        components: projectData.components,
        onLayoutChange: function onLayoutChange(layout) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: layout
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BgColorsOutlined */ .Ebl, null), "Theme Manager"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('themes');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ThemeManager, {
        currentTheme: projectData.theme,
        onThemeChange: function onThemeChange(theme) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              theme: theme
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ApiOutlined */ .bfv, null), "WebSocket Manager"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('websocket');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(WebSocketManager, {
        onConnectionChange: function onConnectionChange(status) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
                connected: status
              })
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .QuestionCircleOutlined */ .faO, null), "Tutorial Assistant"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('tutorial');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Interactive tutorials and context-aware help system"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Start Tutorial"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null), "Testing Tools"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('testing');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component testing, validation, and accessibility checks"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Run Tests"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DatabaseOutlined */ .ose, null), "Data Management"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('data');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Data binding, state management, and flow visualization"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Manage Data"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DashboardOutlined */ .zpd, null), "Performance Monitor"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('performance');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Bundle size tracking and optimization suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Analyze"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExportOutlined */ .PZg, null), "Enhanced Export"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('export');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Multi-framework export with TypeScript generation"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Export Code")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(DemoArea, {
        title: "Sample App Demonstration"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
        gutter: [24, 24]
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
        xs: 24,
        md: 12
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
        direction: "vertical",
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Create Sample App"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Demonstrate the complete workflow from component creation to styled, collaborative application."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "large",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null),
        onClick: createSampleApp,
        loading: demoApp.isBuilding
      }, "Build Sample App"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
        xs: 24,
        md: 12
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        className: "demo-canvas"
      }, demoApp.isBuilding ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Building Sample App..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Adding components, applying layout, styling theme...")) : demoApp.components.length > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Sample App Preview"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: "repeat(".concat(((_demoApp$layout = demoApp.layout) === null || _demoApp$layout === void 0 ? void 0 : _demoApp$layout.columns) || 2, ", 1fr)"),
          gap: ((_demoApp$layout2 = demoApp.layout) === null || _demoApp$layout2 === void 0 ? void 0 : _demoApp$layout2.gap) || '16px',
          marginTop: '16px'
        }
      }, demoApp.components.map(function (component) {
        var _demoApp$theme;
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
          key: component.id,
          style: {
            padding: '12px',
            border: '1px solid #d9d9d9',
            borderRadius: ((_demoApp$theme = demoApp.theme) === null || _demoApp$theme === void 0 ? void 0 : _demoApp$theme.borderRadius) || '4px',
            background: '#fff'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
          strong: true
        }, component.type, ": "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, null, component.props.text || component.props.title || component.props.placeholder));
      }))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4,
        type: "secondary"
      }, "Ready to Build"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Click \"Build Sample App\" to see all features in action")))))))
    }, {
      key: 'components',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .AppstoreOutlined */ .rS9, null), "Component Builder"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ComponentBuilder, null)
    }, {
      key: 'layouts',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .LayoutOutlined */ .hy2, null), "Layout Designer"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(LayoutDesigner, null)
    }, {
      key: 'themes',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BgColorsOutlined */ .Ebl, null), "Theme Manager"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ThemeManager, null)
    }, {
      key: 'websocket',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ApiOutlined */ .bfv, null), "WebSocket Manager"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(WebSocketManager, null)
    }, {
      key: 'tutorial',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .QuestionCircleOutlined */ .faO, null), "Tutorial Assistant"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
        title: "Tutorial Assistant",
        style: {
          height: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Interactive tutorials and context-aware help system"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary"
      }, "Start Tutorial"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        style: {
          marginLeft: '8px'
        }
      }, "View Help"))
    }, {
      key: 'testing',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null), "Testing Tools"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
        title: "Testing Tools",
        style: {
          height: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component testing, validation, and accessibility checks"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary"
      }, "Run Tests"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        style: {
          marginLeft: '8px'
        }
      }, "Accessibility Check"))
    }, {
      key: 'data',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DatabaseOutlined */ .ose, null), "Data Management"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
        title: "Data Management",
        style: {
          height: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Data binding, state management, and flow visualization"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary"
      }, "Manage Data"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        style: {
          marginLeft: '8px'
        }
      }, "View Flow"))
    }, {
      key: 'performance',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DashboardOutlined */ .zpd, null), "Performance Monitor"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
        title: "Performance Monitor",
        style: {
          height: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Bundle size tracking and optimization suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary"
      }, "Analyze Performance"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        style: {
          marginLeft: '8px'
        }
      }, "View Metrics"))
    }, {
      key: 'export',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExportOutlined */ .PZg, null), "Enhanced Export"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
        title: "Enhanced Export",
        style: {
          height: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Multi-framework export with TypeScript generation"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary"
      }, "Export React"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        style: {
          marginLeft: '8px'
        }
      }, "Export Vue"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        style: {
          marginLeft: '8px'
        }
      }, "Export Angular"))
    }];
  }, [projectData, demoApp, createSampleApp]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(IntegratedContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(HeaderBar, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
    level: 3,
    style: {
      margin: 0,
      color: '#1890ff'
    }
  }, "App Builder Enhanced"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    type: "secondary"
  }, "Integrated Development Environment")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .SaveOutlined */ .ylI, null)
  }, "Save Project"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ShareAltOutlined */ .f5H, null)
  }, "Share"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null)
  }, "Preview"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ContentArea, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU, {
    activeKey: activeFeature,
    onChange: setActiveFeature,
    type: "card",
    size: "large",
    items: tabItems
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppBuilderIntegrated);

/***/ })

}]);