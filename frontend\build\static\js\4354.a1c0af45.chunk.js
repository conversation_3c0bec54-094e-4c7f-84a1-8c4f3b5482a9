"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4354],{

/***/ 34354:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;




var Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Title;
var ResponsiveContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  width: 100%;\n  height: 100%;\n  background: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n"])));
var ControlPanel = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  background: white;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  gap: 12px;\n"])));
var PreviewArea = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  overflow: hidden;\n"])));
var ViewportFrame = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  background: white;\n  border: 2px solid #d9d9d9;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  \n  ", "\n  \n  ", "\n  \n  ", "\n"])), function (props) {
  return props.breakpoint === 'mobile' && "\n    border-color: #1890ff;\n    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n  ";
}, function (props) {
  return props.breakpoint === 'tablet' && "\n    border-color: #52c41a;\n    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);\n  ";
}, function (props) {
  return props.breakpoint === 'desktop' && "\n    border-color: #722ed1;\n    box-shadow: 0 0 0 2px rgba(114, 46, 209, 0.2);\n  ";
});
var ViewportContent = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  width: ", "px;\n  height: ", "px;\n  overflow: auto;\n  position: relative;\n"])), function (props) {
  return props.width;
}, function (props) {
  return props.height;
});
var BreakpointIndicator = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  position: absolute;\n  top: -30px;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 12px;\n  font-weight: 500;\n  color: ", ";\n"])), function (props) {
  switch (props.breakpoint) {
    case 'mobile':
      return '#1890ff';
    case 'tablet':
      return '#52c41a';
    case 'desktop':
      return '#722ed1';
    default:
      return '#666';
  }
});
var RulerOverlay = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 10;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 50%;\n    width: 1px;\n    height: 100%;\n    background: rgba(255, 0, 0, 0.5);\n    transform: translateX(-50%);\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 50%;\n    width: 100%;\n    height: 1px;\n    background: rgba(255, 0, 0, 0.5);\n    transform: translateY(-50%);\n  }\n"])));

// Responsive breakpoints
var BREAKPOINTS = {
  mobile: {
    min: 320,
    max: 767,
    "default": 375
  },
  tablet: {
    min: 768,
    max: 1023,
    "default": 768
  },
  desktop: {
    min: 1024,
    max: 1920,
    "default": 1200
  }
};
var ResponsivePreview = function ResponsivePreview(_ref) {
  var children = _ref.children,
    _ref$onBreakpointChan = _ref.onBreakpointChange,
    onBreakpointChange = _ref$onBreakpointChan === void 0 ? function () {} : _ref$onBreakpointChan,
    _ref$onSizeChange = _ref.onSizeChange,
    onSizeChange = _ref$onSizeChange === void 0 ? function () {} : _ref$onSizeChange,
    _ref$showControls = _ref.showControls,
    showControls = _ref$showControls === void 0 ? true : _ref$showControls,
    _ref$defaultBreakpoin = _ref.defaultBreakpoint,
    defaultBreakpoint = _ref$defaultBreakpoin === void 0 ? 'desktop' : _ref$defaultBreakpoin,
    _ref$enableRuler = _ref.enableRuler,
    enableRuler = _ref$enableRuler === void 0 ? false : _ref$enableRuler;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(defaultBreakpoint),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    currentBreakpoint = _useState2[0],
    setCurrentBreakpoint = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(BREAKPOINTS[defaultBreakpoint]["default"]),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    customWidth = _useState4[0],
    setCustomWidth = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(600),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    customHeight = _useState6[0],
    setCustomHeight = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(enableRuler),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState7, 2),
    showRuler = _useState8[0],
    setShowRuler = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState9, 2),
    autoHeight = _useState0[0],
    setAutoHeight = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState1, 2),
    showBreakpointInfo = _useState10[0],
    setShowBreakpointInfo = _useState10[1];
  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);

  // Get current breakpoint based on width
  var getBreakpointFromWidth = function getBreakpointFromWidth(width) {
    if (width <= BREAKPOINTS.mobile.max) return 'mobile';
    if (width <= BREAKPOINTS.tablet.max) return 'tablet';
    return 'desktop';
  };

  // Handle breakpoint change
  var handleBreakpointChange = function handleBreakpointChange(breakpoint) {
    setCurrentBreakpoint(breakpoint);
    var newWidth = BREAKPOINTS[breakpoint]["default"];
    setCustomWidth(newWidth);
    onBreakpointChange(breakpoint, newWidth);
  };

  // Handle width change
  var handleWidthChange = function handleWidthChange(width) {
    setCustomWidth(width);
    var detectedBreakpoint = getBreakpointFromWidth(width);
    if (detectedBreakpoint !== currentBreakpoint) {
      setCurrentBreakpoint(detectedBreakpoint);
    }
    onSizeChange(width, customHeight);
  };

  // Handle height change
  var handleHeightChange = function handleHeightChange(height) {
    setCustomHeight(height);
    onSizeChange(customWidth, height);
  };

  // Auto-adjust container size
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (autoHeight && containerRef.current) {
      var containerHeight = containerRef.current.clientHeight - 100; // Account for padding
      setCustomHeight(Math.max(400, containerHeight));
    }
  }, [autoHeight]);
  var renderBreakpointButtons = function renderBreakpointButtons() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      strong: true
    }, "Breakpoint:"), Object.keys(BREAKPOINTS).map(function (breakpoint) {
      var isActive = currentBreakpoint === breakpoint;
      var Icon = breakpoint === 'mobile' ? _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MobileOutlined */ .jHj : breakpoint === 'tablet' ? _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .TabletOutlined */ .pLH : _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw;
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
        key: breakpoint,
        title: "".concat(breakpoint, " (").concat(BREAKPOINTS[breakpoint].min, "-").concat(BREAKPOINTS[breakpoint].max, "px)")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
        type: isActive ? 'primary' : 'default',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Icon, null),
        onClick: function onClick() {
          return handleBreakpointChange(breakpoint);
        },
        size: "small"
      }, breakpoint.charAt(0).toUpperCase() + breakpoint.slice(1)));
    }));
  };
  var renderSizeControls = function renderSizeControls() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
      wrap: true
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, "Width:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Slider */ .Ap, {
      min: BREAKPOINTS[currentBreakpoint].min,
      max: BREAKPOINTS[currentBreakpoint].max,
      value: customWidth,
      onChange: handleWidthChange,
      style: {
        width: 120
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      style: {
        minWidth: 50
      }
    }, customWidth, "px")), !autoHeight && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, "Height:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Slider */ .Ap, {
      min: 300,
      max: 1200,
      value: customHeight,
      onChange: handleHeightChange,
      style: {
        width: 120
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      style: {
        minWidth: 50
      }
    }, customHeight, "px")));
  };
  var renderUtilityControls = function renderUtilityControls() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
      wrap: true
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      title: "Auto Height"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, "Auto Height:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Switch */ .dO, {
      checked: autoHeight,
      onChange: setAutoHeight,
      size: "small"
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      title: "Show Ruler"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      type: showRuler ? 'primary' : 'default',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .EyeOutlined */ .Om2, null),
      onClick: function onClick() {
        return setShowRuler(!showRuler);
      },
      size: "small"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      title: "Breakpoint Info"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      type: showBreakpointInfo ? 'primary' : 'default',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BugOutlined */ .NhG, null),
      onClick: function onClick() {
        return setShowBreakpointInfo(!showBreakpointInfo);
      },
      size: "small"
    })));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ResponsiveContainer, null, showControls && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ControlPanel, null, renderBreakpointButtons(), renderSizeControls(), renderUtilityControls()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(PreviewArea, {
    ref: containerRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      position: 'relative'
    }
  }, showBreakpointInfo && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(BreakpointIndicator, {
    breakpoint: currentBreakpoint
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
    color: currentBreakpoint === 'mobile' ? 'blue' : currentBreakpoint === 'tablet' ? 'green' : 'purple',
    text: "".concat(currentBreakpoint.toUpperCase(), " - ").concat(customWidth, " \xD7 ").concat(customHeight, "px")
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ViewportFrame, {
    breakpoint: currentBreakpoint
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ViewportContent, {
    width: customWidth,
    height: autoHeight ? 'auto' : customHeight
  }, showRuler && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(RulerOverlay, null), children)))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResponsivePreview);

/***/ })

}]);