"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4816,7136],{

/***/ 23774:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   timeSync: () => (/* binding */ timeSync)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);




var TimeSyncService = /*#__PURE__*/function () {
  function TimeSyncService() {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(this, TimeSyncService);
    this.offset = 0;
    this.syncAttempts = 5; // Number of sync attempts to average
    this.syncing = false;
  }
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(TimeSyncService, [{
    key: "synchronize",
    value: function () {
      var _synchronize = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
        var offsets, i, t0, response, t3, _yield$response$json, serverTime, t1, t2, offset, sortedOffsets, validOffsets, _t;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              if (!this.syncing) {
                _context.next = 1;
                break;
              }
              return _context.abrupt("return");
            case 1:
              this.syncing = true;
              offsets = [];
              i = 0;
            case 2:
              if (!(i < this.syncAttempts)) {
                _context.next = 9;
                break;
              }
              t0 = Date.now();
              _context.prev = 3;
              _context.next = 4;
              return fetch('/api/time');
            case 4:
              response = _context.sent;
              t3 = Date.now();
              _context.next = 5;
              return response.json();
            case 5:
              _yield$response$json = _context.sent;
              serverTime = _yield$response$json.serverTime;
              t1 = new Date(serverTime).getTime();
              t2 = t1; // Server processing time is negligible
              // Calculate offset using Network Time Protocol formula
              offset = (t1 - t0 + (t2 - t3)) / 2;
              offsets.push(offset);
              _context.next = 6;
              return new Promise(function (resolve) {
                return setTimeout(resolve, 100);
              });
            case 6:
              _context.next = 8;
              break;
            case 7:
              _context.prev = 7;
              _t = _context["catch"](3);
              console.error('Time sync failed:', _t);
            case 8:
              i++;
              _context.next = 2;
              break;
            case 9:
              // Calculate average offset, excluding outliers
              sortedOffsets = [].concat(offsets).sort(function (a, b) {
                return a - b;
              });
              validOffsets = sortedOffsets.slice(1, -1); // Remove highest and lowest
              this.offset = validOffsets.reduce(function (sum, val) {
                return sum + val;
              }, 0) / validOffsets.length;
              this.syncing = false;
              console.log('Time synchronized, offset:', this.offset, 'ms');
            case 10:
            case "end":
              return _context.stop();
          }
        }, _callee, this, [[3, 7]]);
      }));
      function synchronize() {
        return _synchronize.apply(this, arguments);
      }
      return synchronize;
    }()
  }, {
    key: "now",
    value: function now() {
      return new Date(Date.now() + this.offset);
    }
  }, {
    key: "getServerTime",
    value: function getServerTime(clientTime) {
      return new Date(clientTime.getTime() + this.offset);
    }
  }, {
    key: "getClientTime",
    value: function getClientTime(serverTime) {
      return new Date(serverTime.getTime() - this.offset);
    }
  }]);
}();
var timeSync = new TimeSyncService();

/***/ }),

/***/ 34816:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   addComponent: () => (/* binding */ addComponent),
/* harmony export */   addLayout: () => (/* binding */ addLayout),
/* harmony export */   addTheme: () => (/* binding */ addTheme),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   removeComponent: () => (/* binding */ removeComponent),
/* harmony export */   removeLayout: () => (/* binding */ removeLayout),
/* harmony export */   removeTheme: () => (/* binding */ removeTheme),
/* harmony export */   setActiveTheme: () => (/* binding */ setActiveTheme),
/* harmony export */   setCurrentView: () => (/* binding */ setCurrentView),
/* harmony export */   togglePreviewMode: () => (/* binding */ togglePreviewMode),
/* harmony export */   toggleSidebar: () => (/* binding */ toggleSidebar),
/* harmony export */   updateComponent: () => (/* binding */ updateComponent),
/* harmony export */   updateLayout: () => (/* binding */ updateLayout),
/* harmony export */   updateTheme: () => (/* binding */ updateTheme),
/* harmony export */   websocketConnected: () => (/* binding */ websocketConnected),
/* harmony export */   websocketDisconnected: () => (/* binding */ websocketDisconnected),
/* harmony export */   websocketMessageReceived: () => (/* binding */ websocketMessageReceived)
/* harmony export */ });
/* harmony import */ var _actions_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4318);
/**
 * Compatibility layer for the minimal-store.js
 * This file re-exports actions from the main Redux store to maintain backward compatibility
 *
 * IMPORTANT: This file is designed to avoid circular dependencies by directly defining
 * action creators rather than importing them from other files.
 */

// Import action types directly to avoid circular dependencies


// Define action creators directly to avoid circular dependencies
// Component actions
var addComponent = function addComponent(component) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_COMPONENT */ .oz || 'ADD_COMPONENT',
    payload: component
  };
};
var updateComponent = function updateComponent(componentId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_COMPONENT */ .ei || 'UPDATE_COMPONENT',
    payload: {
      id: componentId,
      props: props
    }
  };
};
var removeComponent = function removeComponent(componentId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_COMPONENT */ .xS || 'REMOVE_COMPONENT',
    payload: {
      id: componentId
    }
  };
};

// Layout actions
var addLayout = function addLayout(layout) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_LAYOUT */ .vs || 'ADD_LAYOUT',
    payload: layout
  };
};
var updateLayout = function updateLayout(layoutId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_LAYOUT */ .Pe || 'UPDATE_LAYOUT',
    payload: {
      id: layoutId,
      props: props
    }
  };
};
var removeLayout = function removeLayout(layoutId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_LAYOUT */ .gV || 'REMOVE_LAYOUT',
    payload: {
      id: layoutId
    }
  };
};

// Theme actions
var addTheme = function addTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_THEME */ .U_ || 'ADD_THEME',
    payload: theme
  };
};
var updateTheme = function updateTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_THEME */ .gk || 'UPDATE_THEME',
    payload: theme
  };
};
var removeTheme = function removeTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_THEME */ .D || 'REMOVE_THEME',
    payload: {
      id: themeId
    }
  };
};
var setActiveTheme = function setActiveTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .SET_ACTIVE_THEME */ .wH || 'SET_ACTIVE_THEME',
    payload: themeId
  };
};

// WebSocket actions
var websocketConnected = function websocketConnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_CONNECTED */ .Kg || 'WEBSOCKET_CONNECTED'
  };
};
var websocketDisconnected = function websocketDisconnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_DISCONNECTED */ .co || 'WEBSOCKET_DISCONNECTED'
  };
};
var websocketMessageReceived = function websocketMessageReceived(message) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WS_MESSAGE_RECEIVED */ .ZH || 'WEBSOCKET_MESSAGE_RECEIVED',
    payload: message
  };
};

// UI actions
var toggleSidebar = function toggleSidebar() {
  return {
    type: 'TOGGLE_SIDEBAR'
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: 'SET_CURRENT_VIEW',
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: 'TOGGLE_PREVIEW_MODE'
  };
};

// Re-export all actions for backward compatibility


// Export a dummy store for backward compatibility
var dummyStore = {
  getState: function getState() {
    return {};
  },
  dispatch: function dispatch() {},
  subscribe: function subscribe() {
    return function () {};
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dummyStore);

/***/ }),

/***/ 97136:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ enhanced_FixedWebSocketManager)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./src/design-system/index.js + 7 modules
var design_system = __webpack_require__(79146);
// EXTERNAL MODULE: ./src/design-system/theme.js
var theme = __webpack_require__(86020);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./src/hooks/useWebSocket.js
var useWebSocket = __webpack_require__(97787);
// EXTERNAL MODULE: ./src/config/env.js
var env = __webpack_require__(26390);
;// ./src/components/enhanced/WebSocketDemo.js




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;






var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;
var Option = es/* Select */.l6.Option;
var TextArea = es/* Input */.pd.TextArea;
var DemoContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 20px;\n"])));
var DemoCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 20px;\n"])));
var FormGroup = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n\n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"])));
var ButtonGroup = styled_components_browser_esm/* default */.Ay.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"])));
var InfoBox = (0,styled_components_browser_esm/* default */.Ay)(es/* Alert */.Fc)(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n"])));
var MessageList = (0,styled_components_browser_esm/* default */.Ay)(es/* List */.B8)(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid var(--border-color);\n  border-radius: 4px;\n  margin-bottom: 16px;\n\n  .ant-list-item {\n    padding: 8px 16px;\n    border-bottom: 1px solid var(--border-color);\n  }\n\n  .message-direction {\n    font-weight: bold;\n    margin-right: 8px;\n  }\n\n  .message-time {\n    color: var(--text-secondary);\n    font-size: 12px;\n  }\n\n  .message-content {\n    margin-top: 4px;\n    word-break: break-word;\n  }\n"])));
var ConnectionStatus = styled_components_browser_esm/* default */.Ay.div(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 8px 16px;\n  border-radius: 4px;\n  background-color: ", ";\n\n  .status-icon {\n    margin-right: 8px;\n    font-size: 16px;\n    color: ", ";\n  }\n"])), function (props) {
  switch (props.status) {
    case 'connected':
      return 'rgba(82, 196, 26, 0.1)';
    case 'connecting':
      return 'rgba(250, 173, 20, 0.1)';
    case 'disconnected':
      return 'rgba(245, 34, 45, 0.1)';
    default:
      return 'rgba(0, 0, 0, 0.05)';
  }
}, function (props) {
  switch (props.status) {
    case 'connected':
      return '#52c41a';
    case 'connecting':
      return '#faad14';
    case 'disconnected':
      return '#f5222d';
    default:
      return '#8c8c8c';
  }
});

/**
 * WebSocketDemo component
 * Demonstrates the use of WebSockets with the useWebSocket hook
 */
var WebSocketDemo = function WebSocketDemo() {
  // WebSocket URL
  var _useState = (0,react.useState)((0,env/* getWebSocketUrl */.$0)('app_builder')),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    url = _useState2[0],
    setUrl = _useState2[1];

  // WebSocket options
  var _useState3 = (0,react.useState)(true),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    autoReconnect = _useState4[0],
    setAutoReconnect = _useState4[1];
  var _useState5 = (0,react.useState)(true),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    debug = _useState6[0],
    setDebug = _useState6[1];

  // Message state
  var _useState7 = (0,react.useState)(''),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    message = _useState8[0],
    setMessage = _useState8[1];
  var _useState9 = (0,react.useState)([]),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    messages = _useState0[0],
    setMessages = _useState0[1];
  var messagesEndRef = (0,react.useRef)(null);

  // Use our WebSocket hook
  var _useWebSocket = (0,useWebSocket/* default */.A)({
      url: url,
      autoConnect: false,
      reconnect: autoReconnect,
      debug: debug,
      onMessage: function onMessage(data) {
        // Add received message to the list
        var newMessage = {
          id: Date.now(),
          direction: 'received',
          content: (0,esm_typeof/* default */.A)(data) === 'object' ? JSON.stringify(data) : data,
          timestamp: new Date().toISOString()
        };
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [newMessage]);
        });
      }
    }),
    connectionState = _useWebSocket.connectionState,
    lastMessage = _useWebSocket.lastMessage,
    lastError = _useWebSocket.lastError,
    isConnecting = _useWebSocket.isConnecting,
    isOpen = _useWebSocket.isOpen,
    isClosed = _useWebSocket.isClosed,
    hasError = _useWebSocket.hasError,
    connect = _useWebSocket.connect,
    disconnect = _useWebSocket.disconnect,
    send = _useWebSocket.send,
    clearError = _useWebSocket.clearError;

  // Scroll to bottom when messages change
  (0,react.useEffect)(function () {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth'
      });
    }
  }, [messages]);

  // Handle connecting to WebSocket
  var handleConnect = function handleConnect() {
    connect();
  };

  // Handle disconnecting from WebSocket
  var handleDisconnect = function handleDisconnect() {
    disconnect(1000, 'User initiated disconnect');
  };

  // Handle sending a message
  var handleSend = function handleSend() {
    if (!message.trim()) return;

    // Send the message
    var success = send(message);
    if (success) {
      // Add sent message to the list
      var newMessage = {
        id: Date.now(),
        direction: 'sent',
        content: message,
        timestamp: new Date().toISOString()
      };
      setMessages(function (prev) {
        return [].concat((0,toConsumableArray/* default */.A)(prev), [newMessage]);
      });
      setMessage('');
    }
  };

  // Handle clearing messages
  var handleClearMessages = function handleClearMessages() {
    setMessages([]);
  };

  // Get connection status
  var getConnectionStatus = function getConnectionStatus() {
    if (isOpen) return 'connected';
    if (isConnecting) return 'connecting';
    return 'disconnected';
  };

  // Get connection status icon
  var getStatusIcon = function getStatusIcon() {
    if (isOpen) return /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, {
      className: "status-icon"
    });
    if (isConnecting) return /*#__PURE__*/react.createElement(icons_es/* SyncOutlined */.OmY, {
      spin: true,
      className: "status-icon"
    });
    return /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, {
      className: "status-icon"
    });
  };

  // Get connection status text
  var getStatusText = function getStatusText() {
    if (isOpen) return 'Connected';
    if (isConnecting) return 'Connecting...';
    if (hasError) return "Disconnected (".concat((lastError === null || lastError === void 0 ? void 0 : lastError.message) || 'Error', ")");
    return 'Disconnected';
  };
  return /*#__PURE__*/react.createElement(DemoContainer, null, /*#__PURE__*/react.createElement(Title, {
    level: 3
  }, "WebSocket Demo"), /*#__PURE__*/react.createElement(Paragraph, null, "This demo shows how to use WebSockets with the useWebSocket hook."), /*#__PURE__*/react.createElement(DemoCard, {
    title: "WebSocket Connection"
  }, /*#__PURE__*/react.createElement(InfoBox, {
    type: "info",
    message: "WebSocket Configuration",
    description: "Configure and manage your WebSocket connection.",
    icon: /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)
  }), /*#__PURE__*/react.createElement(ConnectionStatus, {
    status: getConnectionStatus()
  }, getStatusIcon(), /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, getStatusText())), hasError && /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    type: "error",
    message: "Connection Error",
    description: (lastError === null || lastError === void 0 ? void 0 : lastError.message) || 'An error occurred with the WebSocket connection',
    style: {
      marginBottom: '16px'
    },
    closable: true,
    onClose: clearError
  }), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "WebSocket URL:"), /*#__PURE__*/react.createElement(es/* Input */.pd, {
    value: url,
    onChange: function onChange(e) {
      return setUrl(e.target.value);
    },
    placeholder: "Enter WebSocket URL",
    disabled: isOpen || isConnecting
  })), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Auto Reconnect:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: autoReconnect,
    onChange: setAutoReconnect,
    disabled: isOpen || isConnecting
  })), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Debug Mode:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: debug,
    onChange: setDebug
  })), /*#__PURE__*/react.createElement(ButtonGroup, null, !isOpen ? /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
    onClick: handleConnect,
    loading: isConnecting,
    disabled: isConnecting
  }, "Connect") : /*#__PURE__*/react.createElement(es/* Button */.$n, {
    danger: true,
    icon: /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, null),
    onClick: handleDisconnect
  }, "Disconnect"))), /*#__PURE__*/react.createElement(DemoCard, {
    title: "Messages"
  }, /*#__PURE__*/react.createElement(InfoBox, {
    type: "info",
    message: "Send and Receive Messages",
    description: "Send messages to the WebSocket server and view received messages.",
    icon: /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)
  }), /*#__PURE__*/react.createElement(MessageList, {
    dataSource: messages,
    locale: {
      emptyText: 'No messages yet'
    },
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, null, /*#__PURE__*/react.createElement("div", {
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
        color: item.direction === 'sent' ? 'blue' : 'green',
        text: /*#__PURE__*/react.createElement("span", {
          className: "message-direction"
        }, item.direction === 'sent' ? 'Sent' : 'Received')
      }), /*#__PURE__*/react.createElement("span", {
        className: "message-time"
      }, new Date(item.timestamp).toLocaleTimeString())), /*#__PURE__*/react.createElement("div", {
        className: "message-content"
      }, /*#__PURE__*/react.createElement(Text, {
        code: true
      }, item.content))));
    }
  }), /*#__PURE__*/react.createElement("div", {
    ref: messagesEndRef
  }), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Message:"), /*#__PURE__*/react.createElement(TextArea, {
    value: message,
    onChange: function onChange(e) {
      return setMessage(e.target.value);
    },
    placeholder: "Enter message to send",
    rows: 4,
    disabled: !isOpen
  })), /*#__PURE__*/react.createElement(ButtonGroup, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* SendOutlined */.jnF, null),
    onClick: handleSend,
    disabled: !isOpen || !message.trim()
  }, "Send"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* ClearOutlined */.ohj, null),
    onClick: handleClearMessages,
    disabled: messages.length === 0
  }, "Clear Messages"))));
};
/* harmony default export */ const enhanced_WebSocketDemo = (WebSocketDemo);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./src/redux/minimal-store.js
var minimal_store = __webpack_require__(34816);
;// ./src/hooks/useWebSocketConnection.js





var _excluded = ["url", "autoConnect", "autoReconnect", "updateRedux", "debug"];

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




// Safely import optional dependencies
var EnhancedWebSocketClient, ConnectionState, timeSync;
try {
  var wsModule = __webpack_require__(17177);
  EnhancedWebSocketClient = wsModule["default"] || wsModule.EnhancedWebSocketClient;
  ConnectionState = wsModule.ConnectionState;
} catch (error) {
  console.warn('EnhancedWebSocketClient not available, using fallback');
}
try {
  var timeSyncModule = __webpack_require__(23774);
  timeSync = timeSyncModule.timeSync || timeSyncModule["default"];
} catch (error) {
  console.warn('TimeSyncService not available, using fallback');
}

/**
 * Custom hook for WebSocket integration with better error handling
 * 
 * @param {Object} options - WebSocket options
 * @param {string} options.url - WebSocket URL
 * @param {boolean} [options.autoConnect=true] - Whether to connect automatically
 * @param {boolean} [options.autoReconnect=true] - Whether to reconnect automatically
 * @param {boolean} [options.updateRedux=true] - Whether to update Redux state
 * @param {boolean} [options.debug=false] - Enable debug logging
 * @returns {Object} WebSocket state and methods
 */
function useWebSocketConnection(options) {
  var url = options.url,
    _options$autoConnect = options.autoConnect,
    autoConnect = _options$autoConnect === void 0 ? true : _options$autoConnect,
    _options$autoReconnec = options.autoReconnect,
    autoReconnect = _options$autoReconnec === void 0 ? true : _options$autoReconnec,
    _options$updateRedux = options.updateRedux,
    updateRedux = _options$updateRedux === void 0 ? true : _options$updateRedux,
    _options$debug = options.debug,
    debug = _options$debug === void 0 ? false : _options$debug,
    wsOptions = (0,objectWithoutProperties/* default */.A)(options, _excluded);
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var wsRef = (0,react.useRef)(null);
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    connected = _useState2[0],
    setConnected = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    connecting = _useState4[0],
    setConnecting = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    messages = _useState6[0],
    setMessages = _useState6[1];
  var _useState7 = (0,react.useState)(null),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    error = _useState8[0],
    setError = _useState8[1];
  var _useState9 = (0,react.useState)(0),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    reconnectAttempt = _useState0[0],
    setReconnectAttempt = _useState0[1];
  var _useState1 = (0,react.useState)(null),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    closeInfo = _useState10[0],
    setCloseInfo = _useState10[1];

  // Initialize WebSocket client
  (0,react.useEffect)(function () {
    if (!url) return;

    // Check if EnhancedWebSocketClient is available
    if (!EnhancedWebSocketClient) {
      console.warn('EnhancedWebSocketClient not available, using native WebSocket');

      // Fallback to native WebSocket
      try {
        var _ws = new WebSocket(url);
        wsRef.current = _ws;

        // Add basic methods for compatibility
        _ws.destroy = function () {
          return _ws.close();
        };
        _ws.open = function () {
          // WebSocket opens automatically on creation
        };
        return function () {
          if (wsRef.current) {
            wsRef.current.close();
            wsRef.current = null;
          }
        };
      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        setError(error);
        return;
      }
    }

    // Create WebSocket client
    var ws = new EnhancedWebSocketClient(_objectSpread({
      url: url,
      autoConnect: autoConnect,
      autoReconnect: autoReconnect,
      debug: debug
    }, wsOptions));

    // Store reference
    wsRef.current = ws;

    // Clean up on unmount
    return function () {
      if (wsRef.current) {
        wsRef.current.destroy();
        wsRef.current = null;
      }
    };
  }, [url, autoConnect, autoReconnect, debug]);

  // Set up event listeners
  (0,react.useEffect)(function () {
    var ws = wsRef.current;
    if (!ws) return;

    // Connection opened
    var handleOpen = /*#__PURE__*/function () {
      var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(event) {
        var _t;
        return regenerator_default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              if (!(timeSync && typeof timeSync.synchronize === 'function')) {
                _context.next = 4;
                break;
              }
              _context.prev = 1;
              _context.next = 2;
              return timeSync.synchronize();
            case 2:
              _context.next = 4;
              break;
            case 3:
              _context.prev = 3;
              _t = _context["catch"](1);
              console.warn('Time synchronization failed:', _t);
            case 4:
              console.log('WebSocket connection opened:', event);
              setConnected(true);
              setConnecting(false);
              setError(null);
              setCloseInfo(null);
              if (updateRedux) {
                try {
                  dispatch((0,minimal_store.websocketConnected)());
                } catch (error) {
                  console.warn('Failed to dispatch websocketConnected:', error);
                }
              }
            case 5:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[1, 3]]);
      }));
      return function handleOpen(_x) {
        return _ref.apply(this, arguments);
      };
    }();

    // Connection closed
    var handleClose = function handleClose(event) {
      console.log('WebSocket connection closed:', event);
      setConnected(false);
      setConnecting(false);
      setCloseInfo(event);
      if (updateRedux) {
        dispatch((0,minimal_store.websocketDisconnected)());
      }
    };

    // Connection error
    var handleError = function handleError(event) {
      console.error('WebSocket error:', event);
      setError(event);
      setConnecting(false);
    };

    // Message received
    var handleMessage = (0,react.useCallback)(function (message) {
      try {
        var processedMessage = message;

        // Handle native WebSocket message events
        if (message.data) {
          try {
            processedMessage = JSON.parse(message.data);
          } catch (error) {
            processedMessage = {
              content: message.data
            };
          }
        }
        var serverTimestamp = new Date(processedMessage.timestamp || Date.now());
        var localTimestamp = serverTimestamp;

        // Only use timeSync if available
        if (timeSync && typeof timeSync.getClientTime === 'function') {
          try {
            localTimestamp = timeSync.getClientTime(serverTimestamp);
          } catch (error) {
            console.warn('Time sync failed, using server timestamp:', error);
          }
        }
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [_objectSpread(_objectSpread({}, processedMessage), {}, {
            timestamp: localTimestamp,
            serverTimestamp: serverTimestamp
          })]);
        });

        // Update Redux if enabled
        if (updateRedux) {
          try {
            dispatch((0,minimal_store.websocketMessageReceived)({
              type: 'received',
              content: processedMessage.content || processedMessage,
              timestamp: localTimestamp,
              serverTimestamp: serverTimestamp
            }));
          } catch (error) {
            console.warn('Failed to dispatch websocketMessageReceived:', error);
          }
        }
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
      }
    }, [dispatch, updateRedux]);

    // Reconnect attempt
    var handleReconnectAttempt = function handleReconnectAttempt(data) {
      console.log('WebSocket reconnect attempt:', data);
      setConnecting(true);
      setReconnectAttempt(data.attempt);
    };

    // Add event listeners with null checks
    ws.addEventListener('open', handleOpen);
    ws.addEventListener('close', handleClose);
    ws.addEventListener('error', handleError);
    ws.addEventListener('message', handleMessage);
    ws.addEventListener('reconnect_attempt', handleReconnectAttempt);

    // Clean up event listeners
    return function () {
      if (ws) {
        ws.removeEventListener('open', handleOpen);
        ws.removeEventListener('close', handleClose);
        ws.removeEventListener('error', handleError);
        ws.removeEventListener('message', handleMessage);
        ws.removeEventListener('reconnect_attempt', handleReconnectAttempt);
      }
    };
  }, [dispatch, updateRedux]);

  // Connect to WebSocket
  var connect = (0,react.useCallback)(function () {
    if (!wsRef.current) return;
    setConnecting(true);
    wsRef.current.open();
  }, []);

  // Disconnect from WebSocket
  var disconnect = (0,react.useCallback)(function () {
    if (!wsRef.current) return;
    wsRef.current.close();
  }, []);

  // Send message
  var sendMessage = (0,react.useCallback)(function (data) {
    var batch = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
    if (!wsRef.current) {
      return false;
    }

    // Check connection state
    var isConnected = ConnectionState ? wsRef.current.connectionState === ConnectionState.OPEN : wsRef.current.readyState === WebSocket.OPEN;
    if (!isConnected) {
      return false;
    }
    try {
      var success = false;

      // Send message using appropriate method
      if (wsRef.current.send && typeof wsRef.current.send === 'function') {
        // Enhanced WebSocket client
        success = wsRef.current.send(data, batch);
      } else {
        // Native WebSocket
        var messageStr = typeof data === 'string' ? data : JSON.stringify(data);
        wsRef.current.send(messageStr);
        success = true;
      }

      // Add to messages if successful
      if (success) {
        var timestamp = new Date().toISOString();

        // Add message to state
        setMessages(function (prev) {
          return [].concat((0,toConsumableArray/* default */.A)(prev), [{
            type: 'sent',
            content: data,
            timestamp: timestamp
          }]);
        });

        // Update Redux if enabled
        if (updateRedux) {
          try {
            dispatch((0,minimal_store.websocketMessageReceived)({
              type: 'sent',
              content: data,
              timestamp: timestamp
            }));
          } catch (error) {
            console.warn('Failed to dispatch sent message:', error);
          }
        }
      }
      return success;
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      return false;
    }
  }, [dispatch, updateRedux]);

  // Clear messages
  var clearMessages = (0,react.useCallback)(function () {
    setMessages([]);
  }, []);

  // Reset connection (close and reopen)
  var resetConnection = (0,react.useCallback)(function () {
    if (!wsRef.current) return;
    wsRef.current.close();
    setTimeout(function () {
      setError(null);
      setCloseInfo(null);
      setReconnectAttempt(0);
      wsRef.current.open();
    }, 1000);
  }, []);
  return {
    connected: connected,
    connecting: connecting,
    reconnectAttempt: reconnectAttempt,
    messages: messages,
    error: error,
    closeInfo: closeInfo,
    connect: connect,
    disconnect: disconnect,
    sendMessage: sendMessage,
    clearMessages: clearMessages,
    resetConnection: resetConnection,
    client: wsRef.current
  };
}
/* harmony default export */ const hooks_useWebSocketConnection = (useWebSocketConnection);
;// ./src/components/enhanced/LegacyWebSocketManager.js



var LegacyWebSocketManager_templateObject, LegacyWebSocketManager_templateObject2, LegacyWebSocketManager_templateObject3, LegacyWebSocketManager_templateObject4, LegacyWebSocketManager_templateObject5, LegacyWebSocketManager_templateObject6, LegacyWebSocketManager_templateObject7, _templateObject8, _templateObject9, _templateObject0;







// Import custom hook

var TabPane = es/* Tabs */.tU.TabPane;
var LegacyWebSocketManager_TextArea = es/* Input */.pd.TextArea;
var LegacyWebSocketManager_Title = es/* Typography */.o5.Title,
  LegacyWebSocketManager_Text = es/* Typography */.o5.Text,
  LegacyWebSocketManager_Paragraph = es/* Typography */.o5.Paragraph;
var WebSocketManagerContainer = design_system.styled.div(LegacyWebSocketManager_templateObject || (LegacyWebSocketManager_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4]);
var LegacyWebSocketManager_ConnectionStatus = design_system.styled.div(LegacyWebSocketManager_templateObject2 || (LegacyWebSocketManager_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: ", ";\n  border-radius: ", ";\n  background-color: ", ";\n  color: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3], theme/* default.borderRadius */.Ay.borderRadius.md, function (props) {
  return props.connected ? theme/* default.colors */.Ay.colors.success.light : theme/* default.colors */.Ay.colors.error.light;
}, function (props) {
  return props.connected ? theme/* default.colors */.Ay.colors.success.dark : theme/* default.colors */.Ay.colors.error.dark;
});
var LegacyWebSocketManager_MessageList = design_system.styled.div(LegacyWebSocketManager_templateObject3 || (LegacyWebSocketManager_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n  max-height: 400px;\n  overflow-y: auto;\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[2], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.colors */.Ay.colors.neutral[50]);
var Message = design_system.styled.div(LegacyWebSocketManager_templateObject4 || (LegacyWebSocketManager_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: ", " ", ";\n  border-radius: ", ";\n  background-color: ", ";\n  border-left: 4px solid ", ";\n  box-shadow: ", ";\n\n  .message-header {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: ", ";\n    font-size: ", ";\n    color: ", ";\n  }\n\n  .timestamp {\n    font-family: monospace;\n    font-size: ", ";\n    color: ", ";\n    background-color: ", ";\n    padding: 2px 4px;\n    border-radius: 3px;\n  }\n\n  .message-content {\n    word-break: break-word;\n  }\n\n  pre {\n    background-color: ", ";\n    padding: ", ";\n    border-radius: ", ";\n    overflow-x: auto;\n    font-family: ", ";\n    font-size: ", ";\n    margin: ", " 0 0 0;\n  }\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3], theme/* default.borderRadius */.Ay.borderRadius.md, function (props) {
  return props.type === 'sent' ? theme/* default.colors */.Ay.colors.primary.light : 'white';
}, function (props) {
  return props.type === 'sent' ? theme/* default.colors */.Ay.colors.primary.main : theme/* default.colors */.Ay.colors.secondary.main;
}, theme/* default.shadows */.Ay.shadows.sm, theme/* default.spacing */.Ay.spacing[1], theme/* default.typography */.Ay.typography.fontSize.sm, theme/* default.colors */.Ay.colors.neutral[500], theme/* default.typography */.Ay.typography.fontSize.xs, theme/* default.colors */.Ay.colors.neutral[600], theme/* default.colors */.Ay.colors.neutral[100], theme/* default.colors */.Ay.colors.neutral[100], theme/* default.spacing */.Ay.spacing[2], theme/* default.borderRadius */.Ay.borderRadius.sm, theme/* default.typography */.Ay.typography.fontFamily.code, theme/* default.typography */.Ay.typography.fontSize.sm, theme/* default.spacing */.Ay.spacing[2]);
var MessageInput = design_system.styled.div(LegacyWebSocketManager_templateObject5 || (LegacyWebSocketManager_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2]);
var SettingsPanel = design_system.styled.div(LegacyWebSocketManager_templateObject6 || (LegacyWebSocketManager_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: white;\n"])), theme/* default.spacing */.Ay.spacing[3], theme/* default.spacing */.Ay.spacing[3], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md);
var SettingsGroup = design_system.styled.div(LegacyWebSocketManager_templateObject7 || (LegacyWebSocketManager_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2]);
var MessageTemplates = design_system.styled.div(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: ", ";\n  margin-bottom: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3]);
var DiagnosticsPanel = design_system.styled.div(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: white;\n"])), theme/* default.spacing */.Ay.spacing[3], theme/* default.spacing */.Ay.spacing[3], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md);
var DiagnosticsItem = design_system.styled.div(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  background-color: ", ";\n"])), theme/* default.spacing */.Ay.spacing[1], theme/* default.spacing */.Ay.spacing[2], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.colors */.Ay.colors.neutral[50]);

/**
 * Legacy WebSocket Manager with error handling for code 1006
 */
var LegacyWebSocketManager = function LegacyWebSocketManager() {
  // Safely get websocket state with fallback
  var websocketState = (0,react_redux/* useSelector */.d4)(function (state) {
    try {
      return (state === null || state === void 0 ? void 0 : state.websocket) || {};
    } catch (error) {
      console.warn('Error accessing websocket state:', error);
      return {};
    }
  });
  var storeUrl = websocketState === null || websocketState === void 0 ? void 0 : websocketState.url;
  var _useState = (0,react.useState)('messages'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react.useState)(storeUrl || 'ws://localhost:8000/ws/test/'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    wsUrl = _useState4[0],
    setWsUrl = _useState4[1];
  var _useState5 = (0,react.useState)(''),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    messageText = _useState6[0],
    setMessageText = _useState6[1];
  var _useState7 = (0,react.useState)(true),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    autoReconnect = _useState8[0],
    setAutoReconnect = _useState8[1];
  var _useState9 = (0,react.useState)(true),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    showTimestamp = _useState0[0],
    setShowTimestamp = _useState0[1];
  var _useState1 = (0,react.useState)(true),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    heartbeatEnabled = _useState10[0],
    setHeartbeatEnabled = _useState10[1];
  var messagesEndRef = (0,react.useRef)(null);

  // Use the WebSocket hook - MUST be called before any conditional returns
  var _useWebSocketConnecti = hooks_useWebSocketConnection({
      url: wsUrl,
      autoConnect: false,
      autoReconnect: autoReconnect,
      debug: true,
      updateRedux: true,
      heartbeatInterval: heartbeatEnabled ? 30000 : 0,
      reconnectInterval: 2000,
      maxReconnectAttempts: 5
    }),
    connected = _useWebSocketConnecti.connected,
    connecting = _useWebSocketConnecti.connecting,
    reconnectAttempt = _useWebSocketConnecti.reconnectAttempt,
    messages = _useWebSocketConnecti.messages,
    error = _useWebSocketConnecti.error,
    closeInfo = _useWebSocketConnecti.closeInfo,
    connect = _useWebSocketConnecti.connect,
    disconnect = _useWebSocketConnecti.disconnect,
    sendMessage = _useWebSocketConnecti.sendMessage,
    clearMessages = _useWebSocketConnecti.clearMessages,
    resetConnection = _useWebSocketConnecti.resetConnection;

  // Early return with simple UI if there are any issues
  if (!websocketState || (0,esm_typeof/* default */.A)(websocketState) !== 'object') {
    return /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      title: "Legacy WebSocket Manager",
      style: {
        margin: '20px'
      }
    }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
      message: "Legacy Component",
      description: "This is the legacy WebSocket manager. We recommend using the new WebSocket Demo component for better performance and features.",
      type: "warning",
      showIcon: true,
      style: {
        marginBottom: '16px'
      }
    }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "primary",
      onClick: function onClick() {
        return window.location.reload();
      }
    }, "Switch to New WebSocket Demo"));
  }

  // Auto-scroll to bottom of messages
  (0,react.useEffect)(function () {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth'
      });
    }
  }, [messages]);

  // Handle connect button click
  var handleConnect = function handleConnect() {
    try {
      // Validate WebSocket URL
      if (!wsUrl.trim().startsWith('ws://') && !wsUrl.trim().startsWith('wss://')) {
        es/* message */.iU.error('Invalid WebSocket URL. It should start with ws:// or wss://');
        return;
      }
      connect();
      es/* message */.iU.info('Connecting to WebSocket...');
    } catch (error) {
      es/* message */.iU.error("Connection error: ".concat(error.message));
    }
  };

  // Handle disconnect button click
  var handleDisconnect = function handleDisconnect() {
    try {
      disconnect();
      es/* message */.iU.info('Disconnected from WebSocket');
    } catch (error) {
      es/* message */.iU.error("Disconnect error: ".concat(error.message));
    }
  };

  // Handle reconnect with new URL
  var handleReconnect = function handleReconnect() {
    try {
      resetConnection();
      setTimeout(function () {
        connect();
        es/* message */.iU.info('Reconnecting to WebSocket...');
      }, 500);
    } catch (error) {
      es/* message */.iU.error("Reconnection error: ".concat(error.message));
    }
  };

  // Handle send message button click
  var handleSendMessage = function handleSendMessage() {
    if (!messageText.trim() || !connected) return;
    try {
      // Try to parse as JSON if it looks like JSON
      var messageData;
      if (messageText.trim().startsWith('{') || messageText.trim().startsWith('[')) {
        messageData = JSON.parse(messageText);
      } else {
        messageData = {
          message: messageText
        };
      }

      // Send the message
      sendMessage(messageData);

      // Clear the input
      setMessageText('');
    } catch (error) {
      es/* message */.iU.error("Error sending message: ".concat(error.message));
    }
  };

  // Handle key down in message input
  var handleKeyDown = function handleKeyDown(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Format timestamp
  var formatTimestamp = function formatTimestamp(timestamp) {
    if (!showTimestamp) return null;
    try {
      // Parse the timestamp string to a Date object
      var date = new Date(timestamp);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid timestamp:', timestamp);
        return null;
      }

      // Format the date as YYYY-MM-DD HH:MM:SS.mmm
      var year = date.getFullYear();
      var month = String(date.getMonth() + 1).padStart(2, '0');
      var day = String(date.getDate()).padStart(2, '0');
      var hours = String(date.getHours()).padStart(2, '0');
      var minutes = String(date.getMinutes()).padStart(2, '0');
      var seconds = String(date.getSeconds()).padStart(2, '0');
      var milliseconds = String(date.getMilliseconds()).padStart(3, '0');
      return "".concat(year, "-").concat(month, "-").concat(day, " ").concat(hours, ":").concat(minutes, ":").concat(seconds, ".").concat(milliseconds);
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return null;
    }
  };

  // Check if a string is JSON
  var isJsonString = function isJsonString(str) {
    try {
      var json = JSON.parse(str);
      return (0,esm_typeof/* default */.A)(json) === 'object';
    } catch (e) {
      return false;
    }
  };

  // Render message content
  var renderMessageContent = function renderMessageContent(content) {
    if (typeof content === 'string' && isJsonString(content)) {
      return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", {
        className: "message-content"
      }, "JSON Message"), /*#__PURE__*/react.createElement("pre", null, JSON.stringify(JSON.parse(content), null, 2)));
    } else if ((0,esm_typeof/* default */.A)(content) === 'object') {
      return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", {
        className: "message-content"
      }, "Object Message"), /*#__PURE__*/react.createElement("pre", null, JSON.stringify(content, null, 2)));
    } else {
      return /*#__PURE__*/react.createElement("div", {
        className: "message-content"
      }, content);
    }
  };

  // Message templates
  var messageTemplates = [{
    name: 'Ping',
    content: '{"type": "ping"}'
  }, {
    name: 'Request Data',
    content: '{"type": "request_data"}'
  }, {
    name: 'Hello',
    content: '{"type": "message", "content": "Hello, WebSocket!"}'
  }, {
    name: 'Status',
    content: '{"type": "status_request"}'
  }, {
    name: 'Get Components',
    content: '{"type": "get_components"}'
  }, {
    name: 'Get Layouts',
    content: '{"type": "get_layouts"}'
  }, {
    name: 'Get Themes',
    content: '{"type": "get_themes"}'
  }, {
    name: 'Create Component',
    content: '{"type": "create_component", "data": {"name": "Test Button", "type": "button", "props": {"text": "Click Me", "variant": "primary"}}}'
  }, {
    name: 'Update Theme',
    content: '{"type": "update_theme", "theme_id": "default", "data": {"primaryColor": "#FF5733"}}'
  }];

  // Render error alert for code 1006
  var renderErrorAlert = function renderErrorAlert() {
    if (!closeInfo || closeInfo.code !== 1006) return null;
    return /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
      message: "WebSocket Connection Error (Code 1006)",
      description: /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("p", null, "The WebSocket connection was closed abnormally (code 1006). This typically indicates one of the following issues:"), /*#__PURE__*/react.createElement("ul", null, /*#__PURE__*/react.createElement("li", null, "Network connectivity problems"), /*#__PURE__*/react.createElement("li", null, "Server process termination"), /*#__PURE__*/react.createElement("li", null, "Proxy or firewall blocking the connection"), /*#__PURE__*/react.createElement("li", null, "Connection timeout")), /*#__PURE__*/react.createElement("p", null, /*#__PURE__*/react.createElement("strong", null, "Troubleshooting steps:")), /*#__PURE__*/react.createElement("ol", null, /*#__PURE__*/react.createElement("li", null, "Check if the WebSocket server is running"), /*#__PURE__*/react.createElement("li", null, "Verify the WebSocket URL is correct"), /*#__PURE__*/react.createElement("li", null, "Check for network restrictions"), /*#__PURE__*/react.createElement("li", null, "Try using a different WebSocket endpoint")), /*#__PURE__*/react.createElement("div", {
        style: {
          marginTop: '10px'
        }
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        type: "primary",
        icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
        onClick: resetConnection
      }, "Reset Connection"))),
      type: "error",
      showIcon: true,
      icon: /*#__PURE__*/react.createElement(icons_es/* BugOutlined */.NhG, null),
      style: {
        marginBottom: '16px'
      }
    });
  };
  return /*#__PURE__*/react.createElement(WebSocketManagerContainer, null, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "Legacy WebSocket Manager"
  }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "Legacy Component",
    description: "This is the legacy WebSocket manager. We recommend using the new WebSocket Demo component for better performance and features.",
    type: "warning",
    showIcon: true,
    style: {
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    onClick: function onClick() {
      return window.location.reload();
    },
    style: {
      marginBottom: '16px'
    }
  }, "Switch to New WebSocket Demo")));
};
/* harmony default export */ const enhanced_LegacyWebSocketManager = (LegacyWebSocketManager);
;// ./src/components/enhanced/FixedWebSocketManager.js


var FixedWebSocketManager_templateObject;





// Import WebSocket components


var FixedWebSocketManager_WebSocketManagerContainer = design_system.styled.div(FixedWebSocketManager_templateObject || (FixedWebSocketManager_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4]);

/**
 * Enhanced WebSocket Manager with improved features and error handling
 */
var FixedWebSocketManager = function FixedWebSocketManager() {
  var _useState = (0,react.useState)('new'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  return /*#__PURE__*/react.createElement(FixedWebSocketManager_WebSocketManagerContainer, null, /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react.createElement(es/* Tabs */.tU.TabPane, {
    tab: "Enhanced WebSocket",
    key: "new"
  }, /*#__PURE__*/react.createElement(enhanced_WebSocketDemo, null)), /*#__PURE__*/react.createElement(es/* Tabs */.tU.TabPane, {
    tab: "Legacy WebSocket",
    key: "legacy"
  }, /*#__PURE__*/react.createElement(enhanced_LegacyWebSocketManager, null))));
};
/* harmony default export */ const enhanced_FixedWebSocketManager = (FixedWebSocketManager);

/***/ })

}]);