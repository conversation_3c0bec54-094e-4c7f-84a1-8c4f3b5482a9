"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3726],{

/***/ 23726:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5556);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(70572);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(82569);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;








// Styled components for better contrast and theming
var TutorialContainer = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  .ant-card-body {\n    padding: var(--spacing-lg);\n  }\n"])));
var ResponseCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background-color: var(--color-background-secondary);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-md);\n  margin-bottom: var(--spacing-md);\n\n  .ant-card-body {\n    padding: var(--spacing-md);\n  }\n\n  .ant-typography {\n    color: var(--color-text);\n    margin-bottom: 0;\n  }\n"])));
var QuickActionTag = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  cursor: pointer;\n  padding: var(--spacing-sm) var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  background-color: var(--color-primary);\n  border-color: var(--color-primary);\n  color: white;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background-color: var(--color-primary-hover);\n    border-color: var(--color-primary-hover);\n    color: white;\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-sm);\n  }\n\n  .anticon {\n    color: white;\n  }\n"])));
var SectionDivider = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Divider */ .cG)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  border-color: var(--color-border);\n  margin: var(--spacing-md) 0;\n\n  .ant-divider-inner-text {\n    color: var(--color-text-secondary);\n    font-size: 12px;\n    font-weight: 500;\n  }\n"])));
var HelpText = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  color: var(--color-text-tertiary);\n  font-size: 12px;\n  text-align: center;\n  display: block;\n"])));
var InputContainer = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x.Compact)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: 100%;\n\n  .ant-input {\n    background-color: var(--color-surface);\n    border-color: var(--color-border);\n    color: var(--color-text);\n\n    &::placeholder {\n      color: var(--color-text-tertiary);\n    }\n\n    &:focus {\n      border-color: var(--color-primary);\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n  }\n\n  .ant-btn {\n    background-color: var(--color-primary);\n    border-color: var(--color-primary);\n    color: white;\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      border-color: var(--color-primary-hover);\n    }\n\n    &:disabled {\n      background-color: var(--color-text-tertiary);\n      border-color: var(--color-text-tertiary);\n      color: var(--color-background);\n    }\n  }\n"])));
var TutorialAIPlugin = function TutorialAIPlugin(_ref) {
  var onComplete = _ref.onComplete;
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_9__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    colors = _useEnhancedTheme.colors;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('Welcome to App Builder 201! I\'m your tutorial assistant. Ask me anything about how to use this application.'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    tutorialResponse = _useState4[0],
    setTutorialResponse = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(''),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    userQuery = _useState6[0],
    setUserQuery = _useState6[1];

  // Tutorial responses based on common questions
  var tutorialResponses = {
    'how to add component': 'To add a component, go to the Component Builder tab, enter a name, select a type, add any properties in JSON format, and click "Add Component".',
    'how to create layout': 'To create a layout, navigate to the Layouts tab, select a layout type, and arrange your components within it.',
    'what is websocket': 'WebSockets provide real-time communication between your browser and the server. You can test this functionality in the WebSocket Manager.',
    'how to use': 'App Builder 201 lets you create applications by adding components, designing layouts, and applying themes. Start by adding some components in the Component Builder.',
    'help': 'I can help you learn how to use App Builder 201. Try asking specific questions about components, layouts, themes, or WebSockets.',
    'getting started': 'Welcome to App Builder! Here\'s how to get started:\n1. 📦 Create components in the Component Builder\n2. 🎨 Design layouts in the Layout Designer\n3. 🎭 Customize themes in the Theme Manager\n4. 🔌 Test real-time features with WebSocket Manager\n5. 📊 Monitor performance and export your code when ready!',
    'component builder': 'The Component Builder lets you create reusable UI components. You can add buttons, text, inputs, cards, and more. Each component can have custom properties like colors, sizes, and behaviors.',
    'layout designer': 'The Layout Designer helps you arrange your components into responsive layouts. You can create grids, flexbox layouts, and organize your components visually.',
    'theme manager': 'The Theme Manager allows you to customize the look and feel of your application. You can change colors, fonts, spacing, and create consistent design systems.',
    'websocket manager': 'The WebSocket Manager enables real-time communication features. You can test live updates, chat functionality, and real-time data synchronization.'
  };

  // Quick action buttons for common questions
  var quickActions = [{
    label: 'Getting Started',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .QuestionCircleOutlined */ .faO, null),
    query: 'getting started'
  }, {
    label: 'Component Builder',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .AppstoreOutlined */ .rS9, null),
    query: 'component builder'
  }, {
    label: 'Layout Designer',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .LayoutOutlined */ .hy2, null),
    query: 'layout designer'
  }, {
    label: 'Theme Manager',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .BgColorsOutlined */ .Ebl, null),
    query: 'theme manager'
  }, {
    label: 'WebSocket Manager',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .ApiOutlined */ .bfv, null),
    query: 'websocket manager'
  }];
  var generateTutorialResponse = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(prompt) {
      var response, data, lowerPrompt, _i, _Object$entries, _Object$entries$_i, key, value, _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setLoading(true);
            _context.prev = 1;
            _context.next = 2;
            return fetch('/api/tutorial/response/', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                prompt: prompt
              })
            });
          case 2:
            response = _context.sent;
            if (response.ok) {
              _context.next = 3;
              break;
            }
            throw new Error('Failed to get tutorial response');
          case 3:
            _context.next = 4;
            return response.json();
          case 4:
            data = _context.sent;
            return _context.abrupt("return", data.response);
          case 5:
            _context.prev = 5;
            _t = _context["catch"](1);
            console.error('Tutorial generation error:', _t);
            // Fall back to local responses
            lowerPrompt = prompt.toLowerCase();
            _i = 0, _Object$entries = Object.entries(tutorialResponses);
          case 6:
            if (!(_i < _Object$entries.length)) {
              _context.next = 8;
              break;
            }
            _Object$entries$_i = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Object$entries[_i], 2), key = _Object$entries$_i[0], value = _Object$entries$_i[1];
            if (!lowerPrompt.includes(key)) {
              _context.next = 7;
              break;
            }
            return _context.abrupt("return", value);
          case 7:
            _i++;
            _context.next = 6;
            break;
          case 8:
            return _context.abrupt("return", 'I encountered an error. Please try again.');
          case 9:
            _context.prev = 9;
            setLoading(false);
            return _context.finish(9);
          case 10:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 5, 9, 10]]);
    }));
    return function generateTutorialResponse(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleQuickAction = /*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(query) {
      var response;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 1;
            return generateTutorialResponse(query);
          case 1:
            response = _context2.sent;
            setTutorialResponse(response);
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function handleQuickAction(_x2) {
      return _ref3.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TutorialContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginBottom: '16px'
    }
  }, tutorialResponse && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ResponseCard, {
    type: "inner"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Paragraph, {
    style: {
      color: 'var(--color-text)',
      margin: 0
    }
  }, tutorialResponse))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(InputContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Input */ .pd, {
    value: userQuery,
    onChange: function onChange(e) {
      return setUserQuery(e.target.value);
    },
    placeholder: "Ask anything about the app...",
    disabled: loading,
    onPressEnter: /*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee3() {
      var userQueryText, response;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            if (userQuery.trim()) {
              _context3.next = 1;
              break;
            }
            return _context3.abrupt("return");
          case 1:
            userQueryText = userQuery;
            setUserQuery('');
            _context3.next = 2;
            return generateTutorialResponse(userQueryText);
          case 2:
            response = _context3.sent;
            setTutorialResponse(response);
          case 3:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }))
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    type: "primary",
    loading: loading,
    onClick: /*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee4() {
      var userQueryText, response;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            if (userQuery.trim()) {
              _context4.next = 1;
              break;
            }
            return _context4.abrupt("return");
          case 1:
            userQueryText = userQuery;
            setUserQuery('');
            _context4.next = 2;
            return generateTutorialResponse(userQueryText);
          case 2:
            response = _context4.sent;
            setTutorialResponse(response);
          case 3:
          case "end":
            return _context4.stop();
        }
      }, _callee4);
    })),
    disabled: !userQuery.trim()
  }, loading ? 'Thinking...' : 'Ask')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SectionDivider, {
    orientation: "left"
  }, "Quick Help Topics"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginBottom: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
    wrap: true
  }, quickActions.map(function (action, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(QuickActionTag, {
      key: index,
      icon: action.icon,
      onClick: function onClick() {
        return handleQuickAction(action.query);
      }
    }, action.label);
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(HelpText, null, "Click on a topic above or ask your own question"));
};
TutorialAIPlugin.propTypes = {
  onComplete: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().func)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TutorialAIPlugin);

/***/ })

}]);