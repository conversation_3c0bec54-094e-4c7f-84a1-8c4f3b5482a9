"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5831],{

/***/ 65831:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(11080);
/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(83590);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(63710);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(70572);
/* harmony import */ var _styles_components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(57749);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;









var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;

// Styled components
var RegisterContainer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: calc(100vh - 200px);\n  padding: 24px;\n"])));
var RegisterCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: 100%;\n  max-width: 500px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n"])));
var RegisterForm = (0,styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"])));
var RegisterButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: 100%;\n"])));
var LoginLink = (0,styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay)(Text)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: block;\n  text-align: center;\n  margin-top: 16px;\n"])));

/**
 * RegisterPage component
 * Allows users to register for the application
 */
var RegisterPage = function RegisterPage() {
  var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__/* .useAuth */ .As)(),
    register = _useAuth.register;
  var navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_7__/* .useNavigate */ .Zp)();
  var _useTranslation = (0,react_i18next__WEBPACK_IMPORTED_MODULE_9__/* .useTranslation */ .Bd)(),
    t = _useTranslation.t;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];

  // Handle form submission
  var handleSubmit = /*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(values) {
      var _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setLoading(true);
            setError(null);
            _context.prev = 1;
            _context.next = 2;
            return register({
              username: values.username,
              email: values.email,
              password: values.password,
              firstName: values.firstName,
              lastName: values.lastName,
              phone: values.phone
            });
          case 2:
            // Redirect to home page
            navigate('/', {
              replace: true
            });
            _context.next = 4;
            break;
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            setError(_t.message || 'Registration failed. Please try again.');
          case 4:
            _context.prev = 4;
            setLoading(false);
            return _context.finish(4);
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3, 4, 5]]);
    }));
    return function handleSubmit(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(RegisterContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(RegisterCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 2,
    style: {
      textAlign: 'center',
      marginBottom: '24px'
    }
  }, t('auth.register.title')), error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: t('auth.register.error'),
    description: error,
    type: "error",
    showIcon: true,
    style: {
      marginBottom: '24px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(RegisterForm, {
    name: "register",
    onFinish: handleSubmit,
    layout: "vertical",
    scrollToFirstError: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('auth.register.username'),
    name: "username",
    rules: [{
      required: true,
      message: t('auth.register.usernameRequired')
    }, {
      min: 3,
      message: t('auth.register.usernameTooShort')
    }, {
      max: 20,
      message: t('auth.register.usernameTooLong')
    }, {
      pattern: /^[a-zA-Z0-9_]+$/,
      message: t('auth.register.usernameInvalid')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UserOutlined */ .qmv, null),
    placeholder: t('auth.register.usernamePlaceholder'),
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('auth.register.email'),
    name: "email",
    rules: [{
      required: true,
      message: t('auth.register.emailRequired')
    }, {
      type: 'email',
      message: t('auth.register.emailInvalid')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .MailOutlined */ ._Wu, null),
    placeholder: t('auth.register.emailPlaceholder'),
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('auth.register.password'),
    name: "password",
    rules: [{
      required: true,
      message: t('auth.register.passwordRequired')
    }, {
      min: 8,
      message: t('auth.register.passwordTooShort')
    }, {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,
      message: t('auth.register.passwordInvalid')
    }],
    hasFeedback: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LockOutlined */ .sXv, null),
    placeholder: t('auth.register.passwordPlaceholder'),
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('auth.register.confirmPassword'),
    name: "confirmPassword",
    dependencies: ['password'],
    hasFeedback: true,
    rules: [{
      required: true,
      message: t('auth.register.confirmPasswordRequired')
    }, function (_ref2) {
      var getFieldValue = _ref2.getFieldValue;
      return {
        validator: function validator(_, value) {
          if (!value || getFieldValue('password') === value) {
            return Promise.resolve();
          }
          return Promise.reject(new Error(t('auth.register.passwordsMismatch')));
        }
      };
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LockOutlined */ .sXv, null),
    placeholder: t('auth.register.confirmPasswordPlaceholder'),
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('auth.register.firstName'),
    name: "firstName",
    rules: [{
      required: true,
      message: t('auth.register.firstNameRequired')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    placeholder: t('auth.register.firstNamePlaceholder'),
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('auth.register.lastName'),
    name: "lastName",
    rules: [{
      required: true,
      message: t('auth.register.lastNameRequired')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    placeholder: t('auth.register.lastNamePlaceholder'),
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('auth.register.phone'),
    name: "phone",
    rules: [{
      required: true,
      message: t('auth.register.phoneRequired')
    }, {
      pattern: /^\+?[0-9]{10,15}$/,
      message: t('auth.register.phoneInvalid')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PhoneOutlined */ .NW5, null),
    placeholder: t('auth.register.phonePlaceholder'),
    size: "large"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "agreement",
    valuePropName: "checked",
    rules: [{
      validator: function validator(_, value) {
        return value ? Promise.resolve() : Promise.reject(new Error(t('auth.register.agreementRequired')));
      }
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Checkbox */ .Sc, null, t('auth.register.agreement'), ' ', /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_7__/* .Link */ .N_, {
    to: "/terms"
  }, t('auth.register.terms')))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(RegisterButton, {
    type: "primary",
    htmlType: "submit",
    size: "large",
    loading: loading
  }, t('auth.register.submit')))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(LoginLink, null, t('auth.register.haveAccount'), ' ', /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_7__/* .Link */ .N_, {
    to: "/login"
  }, t('auth.register.login')))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RegisterPage);

/***/ })

}]);