"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3585],{

/***/ 2643:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export useTemplates */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/**
 * Custom hook for managing templates
 */
var useTemplates = function useTemplates() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$enabled = options.enabled,
    enabled = _options$enabled === void 0 ? true : _options$enabled,
    projectId = options.projectId;

  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    templates = _useState2[0],
    setTemplates = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    error = _useState6[0],
    setError = _useState6[1];

  // Mock templates data
  var mockTemplates = [{
    id: 'template_1',
    name: 'Basic Layout',
    description: 'A simple layout with header, content, and footer',
    type: 'layout',
    category: 'basic',
    components: [{
      id: 'header_1',
      type: 'Header',
      props: {
        title: 'My App'
      }
    }, {
      id: 'content_1',
      type: 'Content',
      props: {}
    }, {
      id: 'footer_1',
      type: 'Footer',
      props: {
        text: 'Copyright 2024'
      }
    }],
    thumbnail: '/templates/basic-layout.png',
    isPublic: true,
    createdAt: '2024-01-01T00:00:00Z'
  }, {
    id: 'template_2',
    name: 'Dashboard Layout',
    description: 'A dashboard layout with sidebar and main content area',
    type: 'layout',
    category: 'dashboard',
    components: [{
      id: 'sidebar_1',
      type: 'Sidebar',
      props: {
        width: 250
      }
    }, {
      id: 'main_1',
      type: 'MainContent',
      props: {}
    }, {
      id: 'header_2',
      type: 'Header',
      props: {
        title: 'Dashboard'
      }
    }],
    thumbnail: '/templates/dashboard-layout.png',
    isPublic: true,
    createdAt: '2024-01-02T00:00:00Z'
  }, {
    id: 'template_3',
    name: 'Contact Form',
    description: 'A complete contact form with validation',
    type: 'component',
    category: 'forms',
    components: [{
      id: 'form_1',
      type: 'Form',
      props: {
        title: 'Contact Us'
      }
    }, {
      id: 'input_1',
      type: 'Input',
      props: {
        label: 'Name',
        required: true
      }
    }, {
      id: 'input_2',
      type: 'Input',
      props: {
        label: 'Email',
        type: 'email',
        required: true
      }
    }, {
      id: 'textarea_1',
      type: 'TextArea',
      props: {
        label: 'Message',
        required: true
      }
    }, {
      id: 'button_1',
      type: 'Button',
      props: {
        text: 'Send Message',
        type: 'primary'
      }
    }],
    thumbnail: '/templates/contact-form.png',
    isPublic: true,
    createdAt: '2024-01-03T00:00:00Z'
  }];

  // Load templates
  var loadTemplates = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
    var _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (enabled) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return");
        case 1:
          _context.prev = 1;
          setLoading(true);
          setError(null);

          // Simulate API call
          _context.next = 2;
          return new Promise(function (resolve) {
            return setTimeout(resolve, 1000);
          });
        case 2:
          // In a real app, this would be an API call
          // const response = await fetch('/api/templates');
          // const data = await response.json();

          setTemplates(mockTemplates);
          _context.next = 4;
          break;
        case 3:
          _context.prev = 3;
          _t = _context["catch"](1);
          console.error('Error loading templates:', _t);
          setError(_t);
          antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.error('Failed to load templates');
        case 4:
          _context.prev = 4;
          setLoading(false);
          return _context.finish(4);
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 3, 4, 5]]);
  })), [enabled]);

  // Save as template
  var saveAsTemplate = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2(templateData) {
      var newTemplate, _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            if (enabled) {
              _context2.next = 1;
              break;
            }
            return _context2.abrupt("return", null);
          case 1:
            _context2.prev = 1;
            setLoading(true);
            setError(null);
            newTemplate = _objectSpread(_objectSpread({
              id: "template_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9))
            }, templateData), {}, {
              createdAt: new Date().toISOString(),
              isPublic: false,
              projectId: projectId
            }); // Simulate API call
            _context2.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 500);
            });
          case 2:
            // In a real app, this would be an API call
            // const response = await fetch('/api/templates', {
            //   method: 'POST',
            //   headers: { 'Content-Type': 'application/json' },
            //   body: JSON.stringify(newTemplate)
            // });
            // const savedTemplate = await response.json();

            setTemplates(function (prev) {
              return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [newTemplate]);
            });
            antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.success('Template saved successfully');
            return _context2.abrupt("return", newTemplate);
          case 3:
            _context2.prev = 3;
            _t2 = _context2["catch"](1);
            console.error('Error saving template:', _t2);
            setError(_t2);
            antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.error('Failed to save template');
            return _context2.abrupt("return", null);
          case 4:
            _context2.prev = 4;
            setLoading(false);
            return _context2.finish(4);
          case 5:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 3, 4, 5]]);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), [enabled, projectId]);

  // Load template
  var loadTemplate = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3(templateId) {
      var template, _t3;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            if (enabled) {
              _context3.next = 1;
              break;
            }
            return _context3.abrupt("return", null);
          case 1:
            _context3.prev = 1;
            setLoading(true);
            setError(null);
            template = templates.find(function (t) {
              return t.id === templateId;
            });
            if (template) {
              _context3.next = 2;
              break;
            }
            throw new Error('Template not found');
          case 2:
            _context3.next = 3;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 300);
            });
          case 3:
            antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.success("Template \"".concat(template.name, "\" loaded"));
            return _context3.abrupt("return", {
              components: template.components,
              metadata: {
                name: template.name,
                description: template.description,
                type: template.type,
                category: template.category
              }
            });
          case 4:
            _context3.prev = 4;
            _t3 = _context3["catch"](1);
            console.error('Error loading template:', _t3);
            setError(_t3);
            antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.error('Failed to load template');
            return _context3.abrupt("return", null);
          case 5:
            _context3.prev = 5;
            setLoading(false);
            return _context3.finish(5);
          case 6:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 4, 5, 6]]);
    }));
    return function (_x2) {
      return _ref3.apply(this, arguments);
    };
  }(), [enabled, templates]);

  // Delete template
  var deleteTemplate = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/function () {
    var _ref4 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee4(templateId) {
      var _t4;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            if (enabled) {
              _context4.next = 1;
              break;
            }
            return _context4.abrupt("return", false);
          case 1:
            _context4.prev = 1;
            setLoading(true);
            setError(null);

            // Simulate API call
            _context4.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 300);
            });
          case 2:
            // In a real app, this would be an API call
            // await fetch(`/api/templates/${templateId}`, { method: 'DELETE' });

            setTemplates(function (prev) {
              return prev.filter(function (t) {
                return t.id !== templateId;
              });
            });
            antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.success('Template deleted successfully');
            return _context4.abrupt("return", true);
          case 3:
            _context4.prev = 3;
            _t4 = _context4["catch"](1);
            console.error('Error deleting template:', _t4);
            setError(_t4);
            antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.error('Failed to delete template');
            return _context4.abrupt("return", false);
          case 4:
            _context4.prev = 4;
            setLoading(false);
            return _context4.finish(4);
          case 5:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 3, 4, 5]]);
    }));
    return function (_x3) {
      return _ref4.apply(this, arguments);
    };
  }(), [enabled]);

  // Get templates by category
  var getTemplatesByCategory = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (category) {
    return templates.filter(function (template) {
      return template.category === category;
    });
  }, [templates]);

  // Get templates by type
  var getTemplatesByType = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (type) {
    return templates.filter(function (template) {
      return template.type === type;
    });
  }, [templates]);

  // Search templates
  var searchTemplates = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (query) {
    if (!query) return templates;
    var lowercaseQuery = query.toLowerCase();
    return templates.filter(function (template) {
      return template.name.toLowerCase().includes(lowercaseQuery) || template.description.toLowerCase().includes(lowercaseQuery) || template.category.toLowerCase().includes(lowercaseQuery);
    });
  }, [templates]);

  // Load templates on mount
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (enabled) {
      loadTemplates();
    }
  }, [enabled, loadTemplates]);
  return {
    // State
    templates: templates,
    loading: loading,
    error: error,
    // Actions
    loadTemplates: loadTemplates,
    saveAsTemplate: saveAsTemplate,
    loadTemplate: loadTemplate,
    deleteTemplate: deleteTemplate,
    // Utility functions
    getTemplatesByCategory: getTemplatesByCategory,
    getTemplatesByType: getTemplatesByType,
    searchTemplates: searchTemplates,
    // Computed values
    templateCount: templates.length,
    categories: (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(new Set(templates.map(function (t) {
      return t.category;
    }))),
    types: (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(new Set(templates.map(function (t) {
      return t.type;
    }))),
    hasTemplates: templates.length > 0
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useTemplates);

/***/ }),

/***/ 7805:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export useCollaboration */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(83590);
/* harmony import */ var _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(77641);




/**
 * React hook for real-time collaboration
 */





/**
 * Hook for managing real-time collaboration
 * @param {string} documentId - Document/app ID to collaborate on
 * @param {Object} options - Configuration options
 * @returns {Object} Collaboration state and methods
 */
var useCollaboration = function useCollaboration(documentId) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__/* .useAuth */ .As)(),
    user = _useAuth.user;
  var _options$autoConnect = options.autoConnect,
    autoConnect = _options$autoConnect === void 0 ? true : _options$autoConnect,
    _options$onOperation = options.onOperation,
    onOperation = _options$onOperation === void 0 ? null : _options$onOperation,
    _options$onCollaborat = options.onCollaboratorJoined,
    onCollaboratorJoined = _options$onCollaborat === void 0 ? null : _options$onCollaborat,
    _options$onCollaborat2 = options.onCollaboratorLeft,
    onCollaboratorLeft = _options$onCollaborat2 === void 0 ? null : _options$onCollaborat2,
    _options$onCursorUpda = options.onCursorUpdate,
    onCursorUpdate = _options$onCursorUpda === void 0 ? null : _options$onCursorUpda,
    _options$onPresenceUp = options.onPresenceUpdate,
    onPresenceUpdate = _options$onPresenceUp === void 0 ? null : _options$onPresenceUp;

  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    isConnected = _useState2[0],
    setIsConnected = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    collaborators = _useState4[0],
    setCollaborators = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    isInitializing = _useState6[0],
    setIsInitializing = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    error = _useState8[0],
    setError = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    operations = _useState0[0],
    setOperations = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    cursors = _useState10[0],
    setCursors = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    presence = _useState12[0],
    setPresence = _useState12[1];

  // Refs
  var isInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(false);
  var eventListenersRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(new Map());

  /**
   * Initialize collaboration
   */
  var initializeCollaboration = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
    var _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (!(!documentId || !user || isInitializedRef.current)) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return");
        case 1:
          setIsInitializing(true);
          setError(null);
          _context.prev = 2;
          _context.next = 3;
          return _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.initializeCollaboration(documentId, user);
        case 3:
          isInitializedRef.current = true;
          setIsConnected(true);
          _context.next = 5;
          break;
        case 4:
          _context.prev = 4;
          _t = _context["catch"](2);
          setError(_t.message);
          console.error('Failed to initialize collaboration:', _t);
        case 5:
          _context.prev = 5;
          setIsInitializing(false);
          return _context.finish(5);
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[2, 4, 5, 6]]);
  })), [documentId, user]);

  /**
   * Disconnect from collaboration
   */
  var disconnect = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (isInitializedRef.current) {
      _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.leaveDocument();
      _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.disconnect();
      isInitializedRef.current = false;
      setIsConnected(false);
      setCollaborators([]);
      setOperations([]);
      setCursors(new Map());
      setPresence(new Map());
    }
  }, []);

  /**
   * Send an operation
   */
  var sendOperation = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (operation) {
    if (isConnected) {
      _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.sendOperation(operation);
    }
  }, [isConnected]);

  /**
   * Send cursor update
   */
  var sendCursorUpdate = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (cursor) {
    if (isConnected) {
      _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.sendCursorUpdate(cursor);
    }
  }, [isConnected]);

  /**
   * Send presence update
   */
  var sendPresenceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (presenceData) {
    if (isConnected) {
      _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.sendPresenceUpdate(presenceData);
    }
  }, [isConnected]);

  /**
   * Get current collaborators
   */
  var getCollaborators = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    return _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getCollaborators();
  }, []);

  /**
   * Set up event listeners
   */
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    var listeners = eventListenersRef.current;

    // Connected event
    var handleConnected = function handleConnected() {
      setIsConnected(true);
      setError(null);
    };

    // Disconnected event
    var handleDisconnected = function handleDisconnected() {
      setIsConnected(false);
    };

    // Error event
    var handleError = function handleError(error) {
      setError(error.message || 'Collaboration error');
    };

    // Operation event
    var handleOperation = function handleOperation(operation) {
      setOperations(function (prev) {
        return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [operation]);
      });
      if (onOperation) {
        onOperation(operation);
      }
    };

    // Collaborator joined event
    var handleCollaboratorJoined = function handleCollaboratorJoined(collaborator) {
      setCollaborators(function (prev) {
        var existing = prev.find(function (c) {
          return c.id === collaborator.id;
        });
        if (existing) {
          return prev.map(function (c) {
            return c.id === collaborator.id ? collaborator : c;
          });
        }
        return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [collaborator]);
      });
      if (onCollaboratorJoined) {
        onCollaboratorJoined(collaborator);
      }
    };

    // Collaborator left event
    var handleCollaboratorLeft = function handleCollaboratorLeft(_ref2) {
      var userId = _ref2.userId,
        collaborator = _ref2.collaborator;
      setCollaborators(function (prev) {
        return prev.filter(function (c) {
          return c.id !== userId;
        });
      });
      setCursors(function (prev) {
        var newCursors = new Map(prev);
        newCursors["delete"](userId);
        return newCursors;
      });
      setPresence(function (prev) {
        var newPresence = new Map(prev);
        newPresence["delete"](userId);
        return newPresence;
      });
      if (onCollaboratorLeft) {
        onCollaboratorLeft({
          userId: userId,
          collaborator: collaborator
        });
      }
    };

    // Cursor update event
    var handleCursorUpdate = function handleCursorUpdate(_ref3) {
      var cursor = _ref3.cursor,
        userId = _ref3.userId;
      setCursors(function (prev) {
        var newCursors = new Map(prev);
        newCursors.set(userId, cursor);
        return newCursors;
      });
      if (onCursorUpdate) {
        onCursorUpdate({
          cursor: cursor,
          userId: userId
        });
      }
    };

    // Presence update event
    var handlePresenceUpdate = function handlePresenceUpdate(_ref4) {
      var presenceData = _ref4.presence,
        userId = _ref4.userId;
      setPresence(function (prev) {
        var newPresence = new Map(prev);
        newPresence.set(userId, presenceData);
        return newPresence;
      });
      if (onPresenceUpdate) {
        onPresenceUpdate({
          presence: presenceData,
          userId: userId
        });
      }
    };

    // Document joined event
    var handleDocumentJoined = function handleDocumentJoined(data) {
      setCollaborators(data.collaborators || []);
    };

    // Add event listeners
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('connected', handleConnected);
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('disconnected', handleDisconnected);
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('error', handleError);
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('operation', handleOperation);
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('collaborator_joined', handleCollaboratorJoined);
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('collaborator_left', handleCollaboratorLeft);
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('cursor_update', handleCursorUpdate);
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('presence_update', handlePresenceUpdate);
    _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.addEventListener('document_joined', handleDocumentJoined);

    // Store listeners for cleanup
    listeners.set('connected', handleConnected);
    listeners.set('disconnected', handleDisconnected);
    listeners.set('error', handleError);
    listeners.set('operation', handleOperation);
    listeners.set('collaborator_joined', handleCollaboratorJoined);
    listeners.set('collaborator_left', handleCollaboratorLeft);
    listeners.set('cursor_update', handleCursorUpdate);
    listeners.set('presence_update', handlePresenceUpdate);
    listeners.set('document_joined', handleDocumentJoined);
    return function () {
      // Remove event listeners
      listeners.forEach(function (listener, event) {
        _services_CollaborationService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.removeEventListener(event, listener);
      });
      listeners.clear();
    };
  }, [onOperation, onCollaboratorJoined, onCollaboratorLeft, onCursorUpdate, onPresenceUpdate]);

  /**
   * Auto-connect when documentId and user are available
   */
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (autoConnect && documentId && user && !isInitializedRef.current) {
      initializeCollaboration();
    }
    return function () {
      if (isInitializedRef.current) {
        disconnect();
      }
    };
  }, [autoConnect, documentId, user, initializeCollaboration, disconnect]);

  /**
   * Update collaborators list periodically
   */
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (isConnected) {
      var interval = setInterval(function () {
        var currentCollaborators = getCollaborators();
        setCollaborators(currentCollaborators);
      }, 5000); // Update every 5 seconds

      return function () {
        return clearInterval(interval);
      };
    }
  }, [isConnected, getCollaborators]);
  return {
    // State
    isConnected: isConnected,
    collaborators: collaborators,
    isInitializing: isInitializing,
    error: error,
    operations: operations,
    cursors: cursors,
    presence: presence,
    // Methods
    initializeCollaboration: initializeCollaboration,
    disconnect: disconnect,
    sendOperation: sendOperation,
    sendCursorUpdate: sendCursorUpdate,
    sendPresenceUpdate: sendPresenceUpdate,
    getCollaborators: getCollaborators,
    // Computed
    collaboratorCount: collaborators.length,
    hasCollaborators: collaborators.length > 0,
    isReady: isConnected && !isInitializing && !error
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCollaboration);

/***/ }),

/***/ 20364:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export useAppBuilder */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71468);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1807);




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





/**
 * Custom hook for managing App Builder state and operations
 */
var useAppBuilder = function useAppBuilder() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var projectId = options.projectId,
    _options$initialCompo = options.initialComponents,
    initialComponents = _options$initialCompo === void 0 ? [] : _options$initialCompo,
    _options$autoSave = options.autoSave,
    autoSave = _options$autoSave === void 0 ? false : _options$autoSave,
    onSave = options.onSave,
    onLoad = options.onLoad,
    onError = options.onError;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useDispatch */ .wA)();

  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(initialComponents),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    components = _useState2[0],
    setComponents = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    isModified = _useState4[0],
    setIsModified = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    history = _useState6[0],
    setHistory = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(-1),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    historyIndex = _useState8[0],
    setHistoryIndex = _useState8[1];

  // Redux state
  var project = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$projects;
    return (_state$projects = state.projects) === null || _state$projects === void 0 ? void 0 : _state$projects.current;
  });

  // Add component
  var addComponent = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee(componentType) {
      var position,
        newComponent,
        _args = arguments,
        _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            position = _args.length > 1 && _args[1] !== undefined ? _args[1] : {};
            _context.prev = 1;
            newComponent = {
              id: "component_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
              type: componentType,
              props: {},
              position: position,
              children: [],
              createdAt: new Date().toISOString()
            };
            setComponents(function (prev) {
              var updated = [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [newComponent]);
              setIsModified(true);

              // Add to history
              setHistory(function (prevHistory) {
                return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prevHistory.slice(0, historyIndex + 1)), [updated]);
              });
              setHistoryIndex(function (prev) {
                return prev + 1;
              });
              return updated;
            });
            antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.success("Added ".concat(componentType, " component"));
            return _context.abrupt("return", newComponent);
          case 2:
            _context.prev = 2;
            _t = _context["catch"](1);
            console.error('Error adding component:', _t);
            if (onError) onError(_t);
            antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.error('Failed to add component');
            return _context.abrupt("return", null);
          case 3:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 2]]);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), [historyIndex, onError]);

  // Update component
  var updateComponent = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (componentId, updates) {
    try {
      setComponents(function (prev) {
        var updated = prev.map(function (comp) {
          return comp.id === componentId ? _objectSpread(_objectSpread(_objectSpread({}, comp), updates), {}, {
            updatedAt: new Date().toISOString()
          }) : comp;
        });
        setIsModified(true);

        // Add to history
        setHistory(function (prevHistory) {
          return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prevHistory.slice(0, historyIndex + 1)), [updated]);
        });
        setHistoryIndex(function (prev) {
          return prev + 1;
        });
        return updated;
      });
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.success('Component updated');
    } catch (error) {
      console.error('Error updating component:', error);
      if (onError) onError(error);
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.error('Failed to update component');
    }
  }, [historyIndex, onError]);

  // Delete component
  var deleteComponent = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (componentId) {
    try {
      setComponents(function (prev) {
        var updated = prev.filter(function (comp) {
          return comp.id !== componentId;
        });
        setIsModified(true);

        // Add to history
        setHistory(function (prevHistory) {
          return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prevHistory.slice(0, historyIndex + 1)), [updated]);
        });
        setHistoryIndex(function (prev) {
          return prev + 1;
        });
        return updated;
      });
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.success('Component deleted');
    } catch (error) {
      console.error('Error deleting component:', error);
      if (onError) onError(error);
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.error('Failed to delete component');
    }
  }, [historyIndex, onError]);

  // Move component
  var moveComponent = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (componentId, newPosition) {
    try {
      setComponents(function (prev) {
        var updated = prev.map(function (comp) {
          return comp.id === componentId ? _objectSpread(_objectSpread({}, comp), {}, {
            position: newPosition,
            updatedAt: new Date().toISOString()
          }) : comp;
        });
        setIsModified(true);

        // Add to history
        setHistory(function (prevHistory) {
          return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prevHistory.slice(0, historyIndex + 1)), [updated]);
        });
        setHistoryIndex(function (prev) {
          return prev + 1;
        });
        return updated;
      });
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.success('Component moved');
    } catch (error) {
      console.error('Error moving component:', error);
      if (onError) onError(error);
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.error('Failed to move component');
    }
  }, [historyIndex, onError]);

  // Duplicate component
  var duplicateComponent = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (componentId) {
    try {
      var _component$position, _component$position2;
      var component = components.find(function (comp) {
        return comp.id === componentId;
      });
      if (!component) return null;
      var duplicated = _objectSpread(_objectSpread({}, component), {}, {
        id: "component_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9)),
        position: _objectSpread(_objectSpread({}, component.position), {}, {
          x: (((_component$position = component.position) === null || _component$position === void 0 ? void 0 : _component$position.x) || 0) + 20,
          y: (((_component$position2 = component.position) === null || _component$position2 === void 0 ? void 0 : _component$position2.y) || 0) + 20
        }),
        createdAt: new Date().toISOString()
      });
      setComponents(function (prev) {
        var updated = [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [duplicated]);
        setIsModified(true);

        // Add to history
        setHistory(function (prevHistory) {
          return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prevHistory.slice(0, historyIndex + 1)), [updated]);
        });
        setHistoryIndex(function (prev) {
          return prev + 1;
        });
        return updated;
      });
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.success('Component duplicated');
      return duplicated;
    } catch (error) {
      console.error('Error duplicating component:', error);
      if (onError) onError(error);
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.error('Failed to duplicate component');
      return null;
    }
  }, [components, historyIndex, onError]);

  // Undo action
  var undoAction = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function () {
    if (historyIndex > 0) {
      setHistoryIndex(function (prev) {
        return prev - 1;
      });
      setComponents(history[historyIndex - 1]);
      setIsModified(true);
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.info('Action undone');
    }
  }, [history, historyIndex]);

  // Redo action
  var redoAction = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function () {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(function (prev) {
        return prev + 1;
      });
      setComponents(history[historyIndex + 1]);
      setIsModified(true);
      antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.info('Action redone');
    }
  }, [history, historyIndex]);

  // Can undo/redo
  var canUndo = historyIndex > 0;
  var canRedo = historyIndex < history.length - 1;

  // Save project
  var saveProject = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2() {
    var _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          if (!onSave) {
            _context2.next = 1;
            break;
          }
          _context2.next = 1;
          return onSave({
            components: components,
            projectId: projectId
          });
        case 1:
          setIsModified(false);
          antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.success('Project saved');
          _context2.next = 3;
          break;
        case 2:
          _context2.prev = 2;
          _t2 = _context2["catch"](0);
          console.error('Error saving project:', _t2);
          if (onError) onError(_t2);
          antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.error('Failed to save project');
        case 3:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 2]]);
  })), [components, projectId, onSave, onError]);

  // Load project
  var loadProject = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3(id) {
      var projectData, _t3;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.prev = 0;
            if (!onLoad) {
              _context3.next = 2;
              break;
            }
            _context3.next = 1;
            return onLoad(id);
          case 1:
            projectData = _context3.sent;
            if (projectData !== null && projectData !== void 0 && projectData.components) {
              setComponents(projectData.components);
              setIsModified(false);

              // Reset history
              setHistory([projectData.components]);
              setHistoryIndex(0);
            }
          case 2:
            antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.success('Project loaded');
            _context3.next = 4;
            break;
          case 3:
            _context3.prev = 3;
            _t3 = _context3["catch"](0);
            console.error('Error loading project:', _t3);
            if (onError) onError(_t3);
            antd__WEBPACK_IMPORTED_MODULE_7__/* .message */ .iU.error('Failed to load project');
          case 4:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[0, 3]]);
    }));
    return function (_x2) {
      return _ref3.apply(this, arguments);
    };
  }(), [onLoad, onError]);

  // Auto-save effect
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (autoSave && isModified && components.length > 0) {
      var timer = setTimeout(function () {
        // Call saveProject directly to avoid dependency issues
        saveProject();
      }, 5000); // Auto-save after 5 seconds of inactivity

      return function () {
        return clearTimeout(timer);
      };
    }
  }, [autoSave, isModified, components.length]); // Removed saveProject to prevent circular dependency

  // Initialize history
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (history.length === 0 && components.length > 0) {
      setHistory([components]);
      setHistoryIndex(0);
    }
  }, [components, history.length]);
  return {
    // State
    components: components,
    isModified: isModified,
    project: project,
    // Actions
    addComponent: addComponent,
    updateComponent: updateComponent,
    deleteComponent: deleteComponent,
    moveComponent: moveComponent,
    duplicateComponent: duplicateComponent,
    undoAction: undoAction,
    redoAction: redoAction,
    saveProject: saveProject,
    loadProject: loadProject,
    // Computed values
    canUndo: canUndo,
    canRedo: canRedo,
    componentCount: components.length
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAppBuilder);

/***/ }),

/***/ 31960:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Custom hook for keyboard shortcuts
 */


/**
 * Custom hook for keyboard shortcuts
 * @param {Object} shortcuts - Object with key combinations as keys and callback functions as values
 * @param {Object} options - Options for the hook
 * @param {boolean} options.enabled - Whether shortcuts are enabled
 * @param {HTMLElement} options.target - Target element for event listeners (defaults to document)
 * @param {boolean} options.preventDefault - Whether to prevent default behavior
 * @param {boolean} options.stopPropagation - Whether to stop event propagation
 * @returns {Object} - Keyboard shortcuts state and handlers
 */
var useKeyboardShortcuts = function useKeyboardShortcuts() {
  var shortcuts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$enabled = options.enabled,
    enabled = _options$enabled === void 0 ? true : _options$enabled,
    _options$target = options.target,
    target = _options$target === void 0 ? typeof document !== 'undefined' ? document : null : _options$target,
    _options$preventDefau = options.preventDefault,
    preventDefault = _options$preventDefau === void 0 ? true : _options$preventDefau,
    _options$stopPropagat = options.stopPropagation,
    stopPropagation = _options$stopPropagat === void 0 ? false : _options$stopPropagat;

  // Keep track of pressed keys
  var pressedKeys = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(new Set());

  // Keep track of registered shortcuts
  var registeredShortcuts = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(shortcuts);

  // Update registered shortcuts when they change
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    registeredShortcuts.current = shortcuts;
  }, [shortcuts]);

  // Parse key combination string
  var parseKeyCombination = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (combination) {
    return combination.toLowerCase().split('+').map(function (key) {
      return key.trim();
    });
  }, []);

  // Check if a key combination matches the pressed keys
  var matchesCombination = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (combination) {
    var keys = parseKeyCombination(combination);

    // Check if all required keys are pressed
    var allKeysPressed = keys.every(function (key) {
      return pressedKeys.current.has(key);
    });

    // Check if only the required keys are pressed
    var onlyRequiredKeys = pressedKeys.current.size === keys.length;
    return allKeysPressed && onlyRequiredKeys;
  }, []);

  // Handle key down
  var handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    if (!enabled) return;

    // Get the key in lowercase
    var key = e.key.toLowerCase();

    // Add the key to the set of pressed keys
    pressedKeys.current.add(key);

    // Add modifier keys if pressed
    if (e.ctrlKey) pressedKeys.current.add('ctrl');
    if (e.shiftKey) pressedKeys.current.add('shift');
    if (e.altKey) pressedKeys.current.add('alt');
    if (e.metaKey) pressedKeys.current.add('meta');

    // Check if any registered shortcut matches the pressed keys
    Object.entries(registeredShortcuts.current).forEach(function (_ref) {
      var _ref2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref, 2),
        combination = _ref2[0],
        callback = _ref2[1];
      if (matchesCombination(combination)) {
        // Prevent default behavior if specified
        if (preventDefault) e.preventDefault();

        // Stop propagation if specified
        if (stopPropagation) e.stopPropagation();

        // Call the callback
        callback(e);
      }
    });
  }, [enabled, preventDefault, stopPropagation, matchesCombination]);

  // Handle key up
  var handleKeyUp = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    // Get the key in lowercase
    var key = e.key.toLowerCase();

    // Remove the key from the set of pressed keys
    pressedKeys.current["delete"](key);

    // Remove modifier keys if released
    if (!e.ctrlKey) pressedKeys.current["delete"]('ctrl');
    if (!e.shiftKey) pressedKeys.current["delete"]('shift');
    if (!e.altKey) pressedKeys.current["delete"]('alt');
    if (!e.metaKey) pressedKeys.current["delete"]('meta');
  }, []);

  // Add event listeners
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!target || !enabled) return;
    target.addEventListener('keydown', handleKeyDown);
    target.addEventListener('keyup', handleKeyUp);

    // Clean up event listeners
    return function () {
      target.removeEventListener('keydown', handleKeyDown);
      target.removeEventListener('keyup', handleKeyUp);
    };
  }, [target, enabled, handleKeyDown, handleKeyUp]);

  // Register a new shortcut
  var registerShortcut = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (combination, callback) {
    registeredShortcuts.current = _objectSpread(_objectSpread({}, registeredShortcuts.current), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, combination, callback));
  }, []);

  // Unregister a shortcut
  var unregisterShortcut = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (combination) {
    var newShortcuts = _objectSpread({}, registeredShortcuts.current);
    delete newShortcuts[combination];
    registeredShortcuts.current = newShortcuts;
  }, []);

  // Clear all shortcuts
  var clearShortcuts = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    registeredShortcuts.current = {};
  }, []);
  return {
    registerShortcut: registerShortcut,
    unregisterShortcut: unregisterShortcut,
    clearShortcuts: clearShortcuts
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useKeyboardShortcuts);

/***/ }),

/***/ 52648:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ useTutorial)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var _useLocalStorage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(92382);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Tutorial Hook
 * 
 * Manages tutorial state and progression for the App Builder
 */



var TUTORIAL_STEPS = {
  WELCOME: 'welcome',
  COMPONENT_PALETTE: 'component-palette',
  DRAG_DROP: 'drag-drop',
  PROPERTIES: 'properties',
  PREVIEW: 'preview',
  SAVE: 'save',
  COMPLETE: 'complete'
};
var TUTORIAL_STORAGE_KEY = 'app-builder-tutorial-progress';
function useTutorial() {
  var _useLocalStorage = (0,_useLocalStorage__WEBPACK_IMPORTED_MODULE_4__/* .useLocalStorage */ .M)(TUTORIAL_STORAGE_KEY, {
      currentStep: TUTORIAL_STEPS.WELCOME,
      completedSteps: [],
      isActive: false,
      hasCompletedTutorial: false
    }),
    _useLocalStorage2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useLocalStorage, 2),
    tutorialProgress = _useLocalStorage2[0],
    setTutorialProgress = _useLocalStorage2[1];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    isVisible = _useState2[0],
    setIsVisible = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    currentStepData = _useState4[0],
    setCurrentStepData = _useState4[1];

  // Tutorial step definitions
  var tutorialSteps = (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, TUTORIAL_STEPS.WELCOME, {
    title: 'Welcome to App Builder',
    description: 'Let\'s take a quick tour of the App Builder interface.',
    target: null,
    position: 'center'
  }), TUTORIAL_STEPS.COMPONENT_PALETTE, {
    title: 'Component Palette',
    description: 'Drag components from here to build your app.',
    target: '.component-palette',
    position: 'right'
  }), TUTORIAL_STEPS.DRAG_DROP, {
    title: 'Drag and Drop',
    description: 'Drag components to the canvas to add them to your app.',
    target: '.canvas-area',
    position: 'left'
  }), TUTORIAL_STEPS.PROPERTIES, {
    title: 'Properties Panel',
    description: 'Customize component properties here.',
    target: '.properties-panel',
    position: 'left'
  }), TUTORIAL_STEPS.PREVIEW, {
    title: 'Preview',
    description: 'See how your app looks in real-time.',
    target: '.preview-area',
    position: 'bottom'
  }), TUTORIAL_STEPS.SAVE, {
    title: 'Save Your Work',
    description: 'Don\'t forget to save your progress!',
    target: '.save-button',
    position: 'bottom'
  }), TUTORIAL_STEPS.COMPLETE, {
    title: 'Tutorial Complete!',
    description: 'You\'re ready to build amazing apps!',
    target: null,
    position: 'center'
  });

  // Start tutorial
  var startTutorial = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setTutorialProgress(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        isActive: true,
        currentStep: TUTORIAL_STEPS.WELCOME
      });
    });
    setIsVisible(true);
    setCurrentStepData(tutorialSteps[TUTORIAL_STEPS.WELCOME]);
  }, [setTutorialProgress]);

  // Next step
  var nextStep = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    var stepOrder = Object.values(TUTORIAL_STEPS);
    var currentIndex = stepOrder.indexOf(tutorialProgress.currentStep);
    if (currentIndex < stepOrder.length - 1) {
      var _nextStep = stepOrder[currentIndex + 1];
      setTutorialProgress(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          currentStep: _nextStep,
          completedSteps: [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.completedSteps), [prev.currentStep])
        });
      });
      setCurrentStepData(tutorialSteps[_nextStep]);
    } else {
      completeTutorial();
    }
  }, [tutorialProgress.currentStep, setTutorialProgress]);

  // Previous step
  var previousStep = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    var stepOrder = Object.values(TUTORIAL_STEPS);
    var currentIndex = stepOrder.indexOf(tutorialProgress.currentStep);
    if (currentIndex > 0) {
      var prevStep = stepOrder[currentIndex - 1];
      setTutorialProgress(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          currentStep: prevStep,
          completedSteps: prev.completedSteps.filter(function (step) {
            return step !== prevStep;
          })
        });
      });
      setCurrentStepData(tutorialSteps[prevStep]);
    }
  }, [tutorialProgress.currentStep, setTutorialProgress]);

  // Skip tutorial
  var skipTutorial = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setTutorialProgress(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        isActive: false,
        hasCompletedTutorial: true
      });
    });
    setIsVisible(false);
    setCurrentStepData(null);
  }, [setTutorialProgress]);

  // Complete tutorial
  var completeTutorial = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setTutorialProgress(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        isActive: false,
        hasCompletedTutorial: true,
        completedSteps: Object.values(TUTORIAL_STEPS)
      });
    });
    setIsVisible(false);
    setCurrentStepData(null);
  }, [setTutorialProgress]);

  // Reset tutorial
  var resetTutorial = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setTutorialProgress({
      currentStep: TUTORIAL_STEPS.WELCOME,
      completedSteps: [],
      isActive: false,
      hasCompletedTutorial: false
    });
    setIsVisible(false);
    setCurrentStepData(null);
  }, [setTutorialProgress]);

  // Go to specific step
  var goToStep = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (step) {
    if (tutorialSteps[step]) {
      setTutorialProgress(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          currentStep: step
        });
      });
      setCurrentStepData(tutorialSteps[step]);
    }
  }, [setTutorialProgress]);

  // Update current step data when step changes
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (tutorialProgress.isActive && tutorialProgress.currentStep) {
      setCurrentStepData(tutorialSteps[tutorialProgress.currentStep]);
    }
  }, [tutorialProgress.currentStep, tutorialProgress.isActive]);

  // Calculate progress
  var progress = {
    current: Object.values(TUTORIAL_STEPS).indexOf(tutorialProgress.currentStep) + 1,
    total: Object.values(TUTORIAL_STEPS).length,
    percentage: Math.round((Object.values(TUTORIAL_STEPS).indexOf(tutorialProgress.currentStep) + 1) / Object.values(TUTORIAL_STEPS).length * 100)
  };
  return {
    // State
    isActive: tutorialProgress.isActive,
    isVisible: isVisible,
    currentStep: tutorialProgress.currentStep,
    currentStepData: currentStepData,
    completedSteps: tutorialProgress.completedSteps,
    hasCompletedTutorial: tutorialProgress.hasCompletedTutorial,
    progress: progress,
    // Actions
    startTutorial: startTutorial,
    nextStep: nextStep,
    previousStep: previousStep,
    skipTutorial: skipTutorial,
    completeTutorial: completeTutorial,
    resetTutorial: resetTutorial,
    goToStep: goToStep,
    // Utilities
    setIsVisible: setIsVisible,
    steps: TUTORIAL_STEPS,
    allSteps: tutorialSteps
  };
}

/***/ }),

/***/ 66337:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export useCodeExport */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);







/**
 * Custom hook for managing code export functionality
 */
var useCodeExport = function useCodeExport() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$enabled = options.enabled,
    enabled = _options$enabled === void 0 ? true : _options$enabled,
    _options$components = options.components,
    components = _options$components === void 0 ? [] : _options$components,
    _options$projectSetti = options.projectSettings,
    projectSettings = _options$projectSetti === void 0 ? {} : _options$projectSetti;

  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    exportHistory = _useState6[0],
    setExportHistory = _useState6[1];

  // Available export formats
  var exportFormats = [{
    id: 'react',
    name: 'React',
    description: 'Export as React components with JSX',
    extension: 'jsx',
    icon: '⚛️'
  }, {
    id: 'vue',
    name: 'Vue.js',
    description: 'Export as Vue.js single file components',
    extension: 'vue',
    icon: '🟢'
  }, {
    id: 'angular',
    name: 'Angular',
    description: 'Export as Angular components with TypeScript',
    extension: 'ts',
    icon: '🔺'
  }, {
    id: 'html',
    name: 'HTML/CSS',
    description: 'Export as static HTML with CSS',
    extension: 'html',
    icon: '🌐'
  }, {
    id: 'svelte',
    name: 'Svelte',
    description: 'Export as Svelte components',
    extension: 'svelte',
    icon: '🧡'
  }, {
    id: 'nextjs',
    name: 'Next.js',
    description: 'Export as Next.js pages and components',
    extension: 'jsx',
    icon: '▲'
  }];

  // Generate code for components
  var generateComponentCode = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (component, format) {
    switch (format) {
      case 'react':
        return "import React from 'react';\n\nconst ".concat(component.type, "Component = (props) => {\n  return (\n    <div className=\"").concat(component.type.toLowerCase(), "-component\">\n      {/* ").concat(component.type, " component content */}\n      <h2>{props.title || '").concat(component.type, "'}</h2>\n      {props.children}\n    </div>\n  );\n};\n\nexport default ").concat(component.type, "Component;");
      case 'vue':
        return "<template>\n  <div class=\"".concat(component.type.toLowerCase(), "-component\">\n    <!-- ").concat(component.type, " component content -->\n    <h2>{{ title || '").concat(component.type, "' }}</h2>\n    <slot></slot>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: '").concat(component.type, "Component',\n  props: {\n    title: {\n      type: String,\n      default: '").concat(component.type, "'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.").concat(component.type.toLowerCase(), "-component {\n  /* Component styles */\n}\n</style>");
      case 'angular':
        return "import { Component, Input } from '@angular/core';\n\n@Component({\n  selector: 'app-".concat(component.type.toLowerCase(), "',\n  template: `\n    <div class=\"").concat(component.type.toLowerCase(), "-component\">\n      <!-- ").concat(component.type, " component content -->\n      <h2>{{ title || '").concat(component.type, "' }}</h2>\n      <ng-content></ng-content>\n    </div>\n  `,\n  styleUrls: ['./").concat(component.type.toLowerCase(), ".component.css']\n})\nexport class ").concat(component.type, "Component {\n  @Input() title: string = '").concat(component.type, "';\n}");
      case 'html':
        return "<div class=\"".concat(component.type.toLowerCase(), "-component\">\n  <!-- ").concat(component.type, " component content -->\n  <h2>").concat(component.type, "</h2>\n</div>");
      case 'svelte':
        return "<script>\n  export let title = '".concat(component.type, "';\n</script>\n\n<div class=\"").concat(component.type.toLowerCase(), "-component\">\n  <!-- ").concat(component.type, " component content -->\n  <h2>{title}</h2>\n  <slot></slot>\n</div>\n\n<style>\n  .").concat(component.type.toLowerCase(), "-component {\n    /* Component styles */\n  }\n</style>");
      case 'nextjs':
        return "import React from 'react';\n\nconst ".concat(component.type, "Component = ({ title = '").concat(component.type, "', children }) => {\n  return (\n    <div className=\"").concat(component.type.toLowerCase(), "-component\">\n      {/* ").concat(component.type, " component content */}\n      <h2>{title}</h2>\n      {children}\n    </div>\n  );\n};\n\nexport default ").concat(component.type, "Component;");
      default:
        return "// ".concat(component.type, " component code");
    }
  }, []);

  // Export code
  var exportCode = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(format) {
      var options,
        exportData,
        exportRecord,
        _args = arguments,
        _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            options = _args.length > 1 && _args[1] !== undefined ? _args[1] : {};
            if (enabled) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return", null);
          case 1:
            _context.prev = 1;
            setLoading(true);
            setError(null);

            // Simulate processing time
            _context.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 1000);
            });
          case 2:
            exportData = {
              format: format,
              components: {},
              styles: {},
              metadata: {
                generatedAt: new Date().toISOString(),
                format: format,
                componentCount: components.length,
                projectSettings: projectSettings
              }
            }; // Generate code for each component
            components.forEach(function (component) {
              exportData.components[component.id] = {
                name: component.type,
                code: generateComponentCode(component, format),
                props: component.props || {},
                type: component.type
              };
            });

            // Generate main app file
            if (format === 'react' || format === 'nextjs') {
              exportData.mainApp = "import React from 'react';\n".concat(components.map(function (comp) {
                return "import ".concat(comp.type, "Component from './").concat(comp.type, "Component';");
              }).join('\n'), "\n\nconst App = () => {\n  return (\n    <div className=\"app\">\n      ").concat(components.map(function (comp) {
                return "<".concat(comp.type, "Component />");
              }).join('\n      '), "\n    </div>\n  );\n};\n\nexport default App;");
            }

            // Add to export history
            exportRecord = {
              id: Date.now(),
              format: format,
              timestamp: new Date().toISOString(),
              componentCount: components.length,
              options: options
            };
            setExportHistory(function (prev) {
              return [exportRecord].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.slice(0, 9)));
            }); // Keep last 10 exports

            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success("Code exported successfully as ".concat(format.toUpperCase()));
            return _context.abrupt("return", exportData);
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            console.error('Error exporting code:', _t);
            setError(_t);
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Failed to export code');
            return _context.abrupt("return", null);
          case 4:
            _context.prev = 4;
            setLoading(false);
            return _context.finish(4);
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3, 4, 5]]);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), [enabled, components, generateComponentCode, projectSettings]);

  // Download code
  var downloadCode = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(format) {
      var options,
        exportData,
        formatInfo,
        extension,
        blob,
        url,
        a,
        _args2 = arguments,
        _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            options = _args2.length > 1 && _args2[1] !== undefined ? _args2[1] : {};
            _context2.prev = 1;
            _context2.next = 2;
            return exportCode(format, options);
          case 2:
            exportData = _context2.sent;
            if (exportData) {
              _context2.next = 3;
              break;
            }
            return _context2.abrupt("return");
          case 3:
            formatInfo = exportFormats.find(function (f) {
              return f.id === format;
            });
            extension = (formatInfo === null || formatInfo === void 0 ? void 0 : formatInfo.extension) || 'txt';
            if (exportData.mainApp) {
              // Download main app file
              blob = new Blob([exportData.mainApp], {
                type: 'text/plain'
              });
              url = URL.createObjectURL(blob);
              a = document.createElement('a');
              a.href = url;
              a.download = "App.".concat(extension);
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            }

            // Download component files
            Object.entries(exportData.components).forEach(function (_ref3) {
              var _ref4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref3, 2),
                id = _ref4[0],
                component = _ref4[1];
              var blob = new Blob([component.code], {
                type: 'text/plain'
              });
              var url = URL.createObjectURL(blob);
              var a = document.createElement('a');
              a.href = url;
              a.download = "".concat(component.name, "Component.").concat(extension);
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            });
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Files downloaded successfully');
            _context2.next = 5;
            break;
          case 4:
            _context2.prev = 4;
            _t2 = _context2["catch"](1);
            console.error('Error downloading code:', _t2);
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Failed to download code');
          case 5:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 4]]);
    }));
    return function (_x2) {
      return _ref2.apply(this, arguments);
    };
  }(), [exportCode, exportFormats]);

  // Get export format by id
  var getExportFormat = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (formatId) {
    return exportFormats.find(function (format) {
      return format.id === formatId;
    });
  }, [exportFormats]);

  // Clear export history
  var clearExportHistory = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    setExportHistory([]);
    antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Export history cleared');
  }, []);
  return {
    // State
    loading: loading,
    error: error,
    exportHistory: exportHistory,
    exportFormats: exportFormats,
    // Actions
    exportCode: exportCode,
    downloadCode: downloadCode,
    clearExportHistory: clearExportHistory,
    // Utility functions
    getExportFormat: getExportFormat,
    // Computed values
    hasComponents: components.length > 0,
    canExport: enabled && components.length > 0,
    formatCount: exportFormats.length,
    historyCount: exportHistory.length
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCodeExport);

/***/ }),

/***/ 80582:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);



/**
 * Custom hook for responsive design
 * @param {string} query - Media query string
 * @returns {boolean} - Whether the media query matches
 */
var useMediaQuery = function useMediaQuery(query) {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    matches = _useState2[0],
    setMatches = _useState2[1];
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);
    var handler = function handler(event) {
      return setMatches(event.matches);
    };
    mediaQuery.addEventListener('change', handler);
    return function () {
      return mediaQuery.removeEventListener('change', handler);
    };
  }, [query]);
  return matches;
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMediaQuery);

/***/ }),

/***/ 92382:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   M: () => (/* binding */ useLocalStorage)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);

/**
 * useLocalStorage Hook
 * 
 * A React hook for managing localStorage with automatic JSON serialization,
 * SSR compatibility, and error handling.
 */


function useLocalStorage(key, initialValue) {
  // State to store our value
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
      try {
        // Check if window is available (SSR compatibility)
        if (typeof window === 'undefined') {
          return initialValue;
        }

        // Get from local storage by key
        var item = window.localStorage.getItem(key);

        // Parse stored json or if none return initialValue
        return item ? JSON.parse(item) : initialValue;
      } catch (error) {
        // If error also return initialValue
        console.warn("Error reading localStorage key \"".concat(key, "\":"), error);
        return initialValue;
      }
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    storedValue = _useState2[0],
    setStoredValue = _useState2[1];

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  var setValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (value) {
    try {
      // Allow value to be a function so we have the same API as useState
      var valueToStore = value instanceof Function ? value(storedValue) : value;

      // Save state
      setStoredValue(valueToStore);

      // Save to local storage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      // A more advanced implementation would handle the error case
      console.warn("Error setting localStorage key \"".concat(key, "\":"), error);
    }
  }, [key, storedValue]);

  // Listen for changes to this key from other tabs/windows
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (typeof window === 'undefined') {
      return;
    }
    var handleStorageChange = function handleStorageChange(e) {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue));
        } catch (error) {
          console.warn("Error parsing localStorage value for key \"".concat(key, "\":"), error);
        }
      }
    };
    window.addEventListener('storage', handleStorageChange);
    return function () {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key]);
  return [storedValue, setValue];
}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useLocalStorage)));

/***/ })

}]);