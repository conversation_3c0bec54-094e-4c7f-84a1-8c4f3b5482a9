"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6285],{

/***/ 86285:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(35346);
/* harmony import */ var _SharedEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96903);





var Title = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Text;

/**
 * QuillTestPage component
 * A test page to verify Quill.js integration and functionality
 */
var QuillTestPage = function QuillTestPage() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('<p>Welcome to the Quill.js test page!</p><p>This editor demonstrates rich text editing capabilities.</p>'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    content1 = _useState2[0],
    setContent1 = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    content2 = _useState4[0],
    setContent2 = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    savedContent = _useState6[0],
    setSavedContent = _useState6[1];
  var handleSaveContent = function handleSaveContent() {
    setSavedContent(content1);
    antd__WEBPACK_IMPORTED_MODULE_2__/* .message */ .iU.success('Content saved successfully!');
  };
  var handleClearContent = function handleClearContent() {
    setContent1('');
    setContent2('');
    antd__WEBPACK_IMPORTED_MODULE_2__/* .message */ .iU.info('Content cleared');
  };
  var handleLoadSavedContent = function handleLoadSavedContent() {
    if (savedContent) {
      setContent2(savedContent);
      antd__WEBPACK_IMPORTED_MODULE_2__/* .message */ .iU.success('Saved content loaded!');
    } else {
      antd__WEBPACK_IMPORTED_MODULE_2__/* .message */ .iU.warning('No saved content available');
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 2
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .FileTextOutlined */ .y9H, {
    style: {
      marginRight: '8px'
    }
  }), "Quill.js Integration Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "secondary"
  }, "This page demonstrates the integration and functionality of Quill.js rich text editor in the App Builder application.")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical",
    size: "large",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "Primary Rich Text Editor",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "secondary",
    style: {
      display: 'block',
      marginBottom: '16px'
    }
  }, "Test basic rich text editing features: bold, italic, lists, headers, etc."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_SharedEditor__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    documentId: "test-doc-1",
    userId: "test-user-1",
    username: "Test User",
    title: "Rich Text Editor",
    height: 300,
    onContentChange: setContent1
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      marginTop: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .SaveOutlined */ .ylI, null),
    onClick: handleSaveContent
  }, "Save Content"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .ClearOutlined */ .ohj, null),
    onClick: handleClearContent
  }, "Clear All")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "Secondary Editor (Read-Only Mode)",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "secondary",
    style: {
      display: 'block',
      marginBottom: '16px'
    }
  }, "This editor demonstrates read-only mode and content loading."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_SharedEditor__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    documentId: "test-doc-2",
    userId: "test-user-2",
    username: "Test User 2",
    title: "Read-Only Editor",
    height: 200,
    readOnly: true,
    onContentChange: setContent2
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      marginTop: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    onClick: handleLoadSavedContent
  }, "Load Saved Content"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "Content Preview",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "secondary",
    style: {
      display: 'block',
      marginBottom: '16px'
    }
  }, "Raw HTML content from the editor:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      background: '#f5f5f5',
      padding: '12px',
      borderRadius: '4px',
      fontFamily: 'monospace',
      fontSize: '12px',
      maxHeight: '200px',
      overflow: 'auto'
    }
  }, content1 || '<em>No content</em>')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "Feature Verification Checklist",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", {
    style: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 5
  }, "Basic Formatting"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("ul", {
    style: {
      fontSize: '14px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Bold text (Ctrl+B)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Italic text (Ctrl+I)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Underline text (Ctrl+U)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Strikethrough text"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 5
  }, "Structure"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("ul", {
    style: {
      fontSize: '14px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Headers (H1, H2)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Bullet lists"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Numbered lists"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Blockquotes"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 5
  }, "Advanced"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("ul", {
    style: {
      fontSize: '14px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Text colors"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Background colors"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Code blocks"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Subscript/Superscript"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 5
  }, "Integration"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("ul", {
    style: {
      fontSize: '14px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Ant Design styling"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Event handling"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Content persistence"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("li", null, "Read-only mode"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "Performance & Bundle Information",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "secondary"
  }, "Quill.js adds approximately 400KB to the bundle size but provides comprehensive rich text editing capabilities. The integration uses react-quill wrapper for better React compatibility and includes CSS styling that integrates well with the Ant Design theme."))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuillTestPage);

/***/ })

}]);