"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4509],{

/***/ 44509:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_TemplatesPage)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/axios/index.js + 49 modules
var axios = __webpack_require__(84447);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react-router-dom/dist/index.js + 1 modules
var dist = __webpack_require__(11080);
// EXTERNAL MODULE: ./src/services/AuthService.js
var AuthService = __webpack_require__(11606);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
;// ./src/components/auth/UserProfile.js


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;





var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text;

// Styled components
var UserProfileDropdown = (0,styled_components_browser_esm/* default */.Ay)(es/* Space */.$x)(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.05);\n  }\n"])));
var ProfileCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  max-width: 500px;\n  margin: 0 auto;\n"])));
var ProfileHeader = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n"])));
var ProfileInfo = styled_components_browser_esm/* default */.Ay.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  flex: 1;\n"])));
var ProfileStats = styled_components_browser_esm/* default */.Ay.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-around;\n  text-align: center;\n"])));
var StatItem = styled_components_browser_esm/* default */.Ay.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 0 16px;\n"])));
var ProfileActions = styled_components_browser_esm/* default */.Ay.div(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-between;\n  gap: 8px;\n"])));

/**
 * UserProfile component
 * Displays user profile information and actions
 */
var UserProfile = function UserProfile(_ref) {
  var onLogout = _ref.onLogout,
    _ref$showAvatar = _ref.showAvatar,
    showAvatar = _ref$showAvatar === void 0 ? true : _ref$showAvatar,
    _ref$showName = _ref.showName,
    showName = _ref$showName === void 0 ? true : _ref$showName,
    _ref$showMenu = _ref.showMenu,
    showMenu = _ref$showMenu === void 0 ? true : _ref$showMenu;
  var _useState = (0,react.useState)(AuthService/* default */.A.getUser()),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    user = _useState2[0],
    setUser = _useState2[1];

  // Update user when auth state changes
  (0,react.useEffect)(function () {
    var removeListener = AuthService/* default */.A.addListener(function (event) {
      if (event === 'login' || event === 'register') {
        setUser(AuthService/* default */.A.getUser());
      } else if (event === 'logout') {
        setUser(null);
      }
    });
    return removeListener;
  }, []);

  // Handle logout
  var handleLogout = function handleLogout() {
    AuthService/* default */.A.logout();
    if (onLogout) {
      onLogout();
    }
  };

  // If not authenticated, return null
  if (!user) {
    return null;
  }

  // Menu items
  var menuItems = [{
    key: 'profile',
    label: 'Profile',
    icon: /*#__PURE__*/react.createElement(icons_es/* UserOutlined */.qmv, null),
    onClick: function onClick() {
      return window.location.href = '/profile';
    }
  }, {
    key: 'settings',
    label: 'Settings',
    icon: /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null),
    onClick: function onClick() {
      return window.location.href = '/settings';
    }
  }, {
    key: 'divider',
    type: 'divider'
  }, {
    key: 'logout',
    label: 'Logout',
    icon: /*#__PURE__*/react.createElement(icons_es/* LogoutOutlined */.$DG, null),
    onClick: handleLogout
  }];

  // Render avatar only
  if (showAvatar && !showName && !showMenu) {
    return /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
      size: "large",
      src: user.avatar,
      icon: !user.avatar && /*#__PURE__*/react.createElement(icons_es/* UserOutlined */.qmv, null),
      alt: user.name
    });
  }

  // Render avatar and name
  if (showAvatar && showName && !showMenu) {
    return /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
      size: "default",
      src: user.avatar,
      icon: !user.avatar && /*#__PURE__*/react.createElement(icons_es/* UserOutlined */.qmv, null),
      alt: user.name
    }), /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, user.name));
  }

  // Render full dropdown menu
  if (showMenu) {
    return /*#__PURE__*/react.createElement(es/* Dropdown */.ms, {
      overlay: /*#__PURE__*/react.createElement(es/* Menu */.W1, {
        items: menuItems
      }),
      trigger: ['click'],
      placement: "bottomRight"
    }, /*#__PURE__*/react.createElement(UserProfileDropdown, null, /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
      size: "default",
      src: user.avatar,
      icon: !user.avatar && /*#__PURE__*/react.createElement(icons_es/* UserOutlined */.qmv, null),
      alt: user.name
    }), showName && /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, user.name), /*#__PURE__*/react.createElement(icons_es/* DownOutlined */.lHd, null)));
  }

  // Fallback to simple name
  return /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, user.name);
};

/**
 * UserProfileCard component
 * Displays detailed user profile information in a card
 */
var UserProfileCard = function UserProfileCard() {
  var user = authService.getUser();
  if (!user) {
    return null;
  }
  return /*#__PURE__*/React.createElement(ProfileCard, null, /*#__PURE__*/React.createElement(ProfileHeader, null, /*#__PURE__*/React.createElement(Avatar, {
    size: 80,
    src: user.avatar,
    icon: !user.avatar && /*#__PURE__*/React.createElement(UserOutlined, null),
    alt: user.name
  }), /*#__PURE__*/React.createElement(ProfileInfo, null, /*#__PURE__*/React.createElement(Title, {
    level: 4
  }, user.name), /*#__PURE__*/React.createElement(Text, {
    type: "secondary"
  }, user.email)), /*#__PURE__*/React.createElement(Button, {
    type: "primary",
    shape: "circle",
    icon: /*#__PURE__*/React.createElement(EditOutlined, null),
    onClick: function onClick() {
      return window.location.href = '/profile/edit';
    }
  })), /*#__PURE__*/React.createElement(Divider, null), /*#__PURE__*/React.createElement(ProfileStats, null, /*#__PURE__*/React.createElement(StatItem, null, /*#__PURE__*/React.createElement(Title, {
    level: 5
  }, "5"), /*#__PURE__*/React.createElement(Text, {
    type: "secondary"
  }, "Apps")), /*#__PURE__*/React.createElement(StatItem, null, /*#__PURE__*/React.createElement(Title, {
    level: 5
  }, "12"), /*#__PURE__*/React.createElement(Text, {
    type: "secondary"
  }, "Components")), /*#__PURE__*/React.createElement(StatItem, null, /*#__PURE__*/React.createElement(Title, {
    level: 5
  }, "3"), /*#__PURE__*/React.createElement(Text, {
    type: "secondary"
  }, "Templates"))), /*#__PURE__*/React.createElement(Divider, null), /*#__PURE__*/React.createElement(ProfileActions, null, /*#__PURE__*/React.createElement(Button, {
    type: "default",
    icon: /*#__PURE__*/React.createElement(SettingOutlined, null),
    onClick: function onClick() {
      return window.location.href = '/settings';
    }
  }, "Settings"), /*#__PURE__*/React.createElement(Button, {
    type: "default",
    icon: /*#__PURE__*/React.createElement(BellOutlined, null),
    onClick: function onClick() {
      return window.location.href = '/notifications';
    }
  }, "Notifications", /*#__PURE__*/React.createElement(Badge, {
    count: 3,
    size: "small",
    offset: [5, -3]
  })), /*#__PURE__*/React.createElement(Button, {
    type: "primary",
    danger: true,
    icon: /*#__PURE__*/React.createElement(LogoutOutlined, null),
    onClick: function onClick() {
      return authService.logout();
    }
  }, "Logout")));
};
/* harmony default export */ const auth_UserProfile = (UserProfile);
;// ./src/components/ThemeSwitcher.js

var ThemeSwitcher_templateObject;




var ThemeButton = (0,styled_components_browser_esm/* default */.Ay)(es/* Button */.$n)(ThemeSwitcher_templateObject || (ThemeSwitcher_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s;\n\n  &:hover {\n    transform: rotate(30deg);\n  }\n"])));

/**
 * ThemeSwitcher component
 * Provides a simple toggle between light and dark themes
 */
var ThemeSwitcher = function ThemeSwitcher(_ref) {
  var isDarkMode = _ref.isDarkMode,
    toggleTheme = _ref.toggleTheme;
  return /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'
  }, /*#__PURE__*/react.createElement(ThemeButton, {
    type: "text",
    icon: isDarkMode ? /*#__PURE__*/react.createElement(icons_es/* BulbOutlined */.o3f, null) : /*#__PURE__*/react.createElement(icons_es/* BulbFilled */._r6, null),
    onClick: toggleTheme,
    "aria-label": isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'
  }));
};
/* harmony default export */ const components_ThemeSwitcher = (ThemeSwitcher);
// EXTERNAL MODULE: ./src/components/WebSocketStatus.js
var WebSocketStatus = __webpack_require__(51162);
;// ./src/components/layout/Header.js

var Header_templateObject, Header_templateObject2, Header_templateObject3, Header_templateObject4;









var AntHeader = es/* Layout */.PE.Header;

// Styled components
var StyledHeader = (0,styled_components_browser_esm/* default */.Ay)(AntHeader)(Header_templateObject || (Header_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  background-color: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  padding: 0 24px;\n  height: 64px;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n"])));
var Logo = styled_components_browser_esm/* default */.Ay.div(Header_templateObject2 || (Header_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 20px;\n  font-weight: bold;\n  margin-right: 48px;\n\n  a {\n    color: #1890ff;\n    text-decoration: none;\n  }\n"])));
var StyledMenu = (0,styled_components_browser_esm/* default */.Ay)(es/* Menu */.W1)(Header_templateObject3 || (Header_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  flex: 1;\n  border-bottom: none;\n  background: transparent;\n"])));
var AuthControls = styled_components_browser_esm/* default */.Ay.div(Header_templateObject4 || (Header_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n"])));

/**
 * Header component
 * Main navigation header with authentication controls
 */
var Header = function Header() {
  var navigate = (0,dist/* useNavigate */.Zp)();
  var location = (0,dist/* useLocation */.zy)();
  var isAuthenticated = AuthService/* default */.A.isAuthenticated();

  // Get current path for active menu item
  var currentPath = location.pathname;

  // Handle login button click
  var handleLoginClick = function handleLoginClick() {
    navigate('/auth?tab=login');
  };

  // Handle register button click
  var handleRegisterClick = function handleRegisterClick() {
    navigate('/auth?tab=register');
  };

  // Handle logout
  var handleLogout = function handleLogout() {
    AuthService/* default */.A.logout();
    navigate('/');
  };
  return /*#__PURE__*/react.createElement(StyledHeader, null, /*#__PURE__*/react.createElement(Logo, null, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/"
  }, "App Builder")), /*#__PURE__*/react.createElement(StyledMenu, {
    theme: "light",
    mode: "horizontal",
    selectedKeys: [currentPath]
  }, /*#__PURE__*/react.createElement(es/* Menu */.W1.Item, {
    key: "/",
    icon: /*#__PURE__*/react.createElement(icons_es/* HomeOutlined */.aod, null)
  }, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/"
  }, "Home")), /*#__PURE__*/react.createElement(es/* Menu */.W1.Item, {
    key: "/apps",
    icon: /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null)
  }, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/apps"
  }, "My Apps")), /*#__PURE__*/react.createElement(es/* Menu */.W1.Item, {
    key: "/templates",
    icon: /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null)
  }, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/templates"
  }, "Templates"))), /*#__PURE__*/react.createElement(AuthControls, null, /*#__PURE__*/react.createElement(WebSocketStatus/* default */.A, null), /*#__PURE__*/react.createElement(es/* Divider */.cG, {
    type: "vertical",
    style: {
      margin: '0 8px'
    }
  }), /*#__PURE__*/react.createElement(components_ThemeSwitcher, null), isAuthenticated ? /*#__PURE__*/react.createElement(auth_UserProfile, {
    showAvatar: true,
    showName: true,
    showMenu: true,
    onLogout: handleLogout
  }) : /*#__PURE__*/react.createElement(es/* Space */.$x, {
    split: /*#__PURE__*/react.createElement(es/* Divider */.cG, {
      type: "vertical"
    })
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "link",
    icon: /*#__PURE__*/react.createElement(icons_es/* LoginOutlined */.W8X, null),
    onClick: handleLoginClick
  }, "Login"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* UserAddOutlined */.Nnt, null),
    onClick: handleRegisterClick
  }, "Register"))));
};
/* harmony default export */ const layout_Header = (Header);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
;// ./src/components/AccessibilityHelpers.js


var _excluded = ["children", "onClick", "role", "tabIndex"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/**
 * SkipLink component
 * Provides a skip link for keyboard users to bypass navigation
 */
var SkipLink = function SkipLink(_ref) {
  var _ref$targetId = _ref.targetId,
    targetId = _ref$targetId === void 0 ? 'main-content' : _ref$targetId,
    _ref$children = _ref.children,
    children = _ref$children === void 0 ? 'Skip to main content' : _ref$children;
  return /*#__PURE__*/react.createElement("a", {
    href: "#".concat(targetId),
    className: "skip-link",
    "aria-label": children
  }, children, /*#__PURE__*/react.createElement("style", null, "\n        .skip-link {\n          position: absolute;\n          top: -40px;\n          left: 0;\n          background: #2563EB;\n          color: white;\n          padding: 8px;\n          z-index: 100;\n          transition: top 0.3s;\n        }\n\n        .skip-link:focus {\n          top: 0;\n        }\n      "));
};

/**
 * FocusTrap component
 * Traps focus within a component (useful for modals)
 */
var FocusTrap = function FocusTrap(_ref2) {
  var children = _ref2.children,
    _ref2$active = _ref2.active,
    active = _ref2$active === void 0 ? true : _ref2$active;
  (0,react.useEffect)(function () {
    if (!active) return;

    // Save the current active element
    var previouslyFocused = document.activeElement;

    // Function to handle tab key
    var handleTabKey = function handleTabKey(e) {
      // Only handle tab key
      if (e.key !== 'Tab') return;

      // Get all focusable elements
      var container = document.getElementById('focus-trap-container');
      var focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
      var firstElement = focusableElements[0];
      var lastElement = focusableElements[focusableElements.length - 1];

      // If shift+tab and on first element, move to last element
      if (e.shiftKey && document.activeElement === firstElement) {
        lastElement.focus();
        e.preventDefault();
      }
      // If tab and on last element, move to first element
      else if (!e.shiftKey && document.activeElement === lastElement) {
        firstElement.focus();
        e.preventDefault();
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleTabKey);

    // Focus the first focusable element
    var container = document.getElementById('focus-trap-container');
    var focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }

    // Cleanup
    return function () {
      document.removeEventListener('keydown', handleTabKey);

      // Restore focus
      if (previouslyFocused) {
        previouslyFocused.focus();
      }
    };
  }, [active]);
  return /*#__PURE__*/react.createElement("div", {
    id: "focus-trap-container"
  }, children);
};

/**
 * ScreenReaderOnly component
 * Content that is only visible to screen readers
 */
var ScreenReaderOnly = function ScreenReaderOnly(_ref3) {
  var children = _ref3.children;
  return /*#__PURE__*/react.createElement("span", {
    className: "sr-only"
  }, children, /*#__PURE__*/react.createElement("style", null, "\n        .sr-only {\n          position: absolute;\n          width: 1px;\n          height: 1px;\n          padding: 0;\n          margin: -1px;\n          overflow: hidden;\n          clip: rect(0, 0, 0, 0);\n          white-space: nowrap;\n          border-width: 0;\n        }\n      "));
};

/**
 * KeyboardAccessible component
 * Makes a component accessible via keyboard
 */
var KeyboardAccessible = function KeyboardAccessible(_ref4) {
  var children = _ref4.children,
    onClick = _ref4.onClick,
    _ref4$role = _ref4.role,
    role = _ref4$role === void 0 ? 'button' : _ref4$role,
    _ref4$tabIndex = _ref4.tabIndex,
    tabIndex = _ref4$tabIndex === void 0 ? 0 : _ref4$tabIndex,
    props = (0,objectWithoutProperties/* default */.A)(_ref4, _excluded);
  // Handle key down events
  var handleKeyDown = function handleKeyDown(e) {
    // Handle Enter and Space keys
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick(e);
    }
  };
  return /*#__PURE__*/react.cloneElement(children, _objectSpread({
    onClick: onClick,
    onKeyDown: handleKeyDown,
    role: role,
    tabIndex: tabIndex
  }, props));
};

/**
 * LiveRegion component
 * Creates an ARIA live region for announcing dynamic content changes
 */
var LiveRegion = function LiveRegion(_ref5) {
  var children = _ref5.children,
    _ref5$ariaLive = _ref5.ariaLive,
    ariaLive = _ref5$ariaLive === void 0 ? 'polite' : _ref5$ariaLive,
    _ref5$ariaAtomic = _ref5.ariaAtomic,
    ariaAtomic = _ref5$ariaAtomic === void 0 ? true : _ref5$ariaAtomic,
    _ref5$ariaRelevant = _ref5.ariaRelevant,
    ariaRelevant = _ref5$ariaRelevant === void 0 ? 'additions text' : _ref5$ariaRelevant,
    _ref5$className = _ref5.className,
    className = _ref5$className === void 0 ? '' : _ref5$className;
  return /*#__PURE__*/react.createElement("div", {
    className: "live-region ".concat(className),
    "aria-live": ariaLive,
    "aria-atomic": ariaAtomic,
    "aria-relevant": ariaRelevant
  }, children, /*#__PURE__*/react.createElement("style", null, "\n        .live-region {\n          position: absolute;\n          width: 1px;\n          height: 1px;\n          padding: 0;\n          margin: -1px;\n          overflow: hidden;\n          clip: rect(0, 0, 0, 0);\n          white-space: nowrap;\n          border-width: 0;\n        }\n      "));
};

/**
 * FocusHighlight component
 * Adds a visible focus indicator to elements when using keyboard navigation
 */
var FocusHighlight = function FocusHighlight(_ref6) {
  var children = _ref6.children;
  // Add event listeners to track keyboard vs mouse navigation
  react.useEffect(function () {
    var handleKeyDown = function handleKeyDown(e) {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    };
    var handleMouseDown = function handleMouseDown() {
      document.body.classList.remove('keyboard-navigation');
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    // Add global styles
    var style = document.createElement('style');
    style.innerHTML = "\n      .keyboard-navigation *:focus {\n        outline: 2px solid #2563EB !important;\n        outline-offset: 2px !important;\n      }\n    ";
    document.head.appendChild(style);

    // Cleanup
    return function () {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
      document.head.removeChild(style);
    };
  }, []);
  return children;
};
/* harmony default export */ const AccessibilityHelpers = ({
  SkipLink: SkipLink,
  FocusTrap: FocusTrap,
  ScreenReaderOnly: ScreenReaderOnly,
  KeyboardAccessible: KeyboardAccessible,
  LiveRegion: LiveRegion,
  FocusHighlight: FocusHighlight
});
;// ./src/components/layout/MainLayout.js

var MainLayout_templateObject, MainLayout_templateObject2, MainLayout_templateObject3;





var Content = es/* Layout */.PE.Content,
  Footer = es/* Layout */.PE.Footer;

// Styled components
var StyledLayout = (0,styled_components_browser_esm/* default */.Ay)(es/* Layout */.PE)(MainLayout_templateObject || (MainLayout_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  min-height: 100vh;\n"])));
var StyledContent = (0,styled_components_browser_esm/* default */.Ay)(Content)(MainLayout_templateObject2 || (MainLayout_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 24px;\n  background-color: #f5f5f5;\n  min-height: calc(100vh - 64px - 70px); /* Viewport height - header - footer */\n"])));
var StyledFooter = (0,styled_components_browser_esm/* default */.Ay)(Footer)(MainLayout_templateObject3 || (MainLayout_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  text-align: center;\n  padding: 24px;\n  background-color: #fff;\n"])));

/**
 * MainLayout component
 * Main layout for the application with header, content, and footer
 */
var MainLayout = function MainLayout(_ref) {
  var children = _ref.children;
  return /*#__PURE__*/react.createElement(StyledLayout, null, /*#__PURE__*/react.createElement(SkipLink, {
    targetId: "main-content"
  }), /*#__PURE__*/react.createElement(layout_Header, null), /*#__PURE__*/react.createElement(StyledContent, {
    id: "main-content"
  }, children), /*#__PURE__*/react.createElement(StyledFooter, null, "App Builder \xA9", new Date().getFullYear(), " - Build with ease"), /*#__PURE__*/react.createElement(es/* BackTop */.XT, null));
};
/* harmony default export */ const layout_MainLayout = (MainLayout);
;// ./src/pages/TemplatesPage.js





function TemplatesPage_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function TemplatesPage_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? TemplatesPage_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : TemplatesPage_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






var TemplatesPage_Title = es/* Typography */.o5.Title,
  TemplatesPage_Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;
var Option = es/* Select */.l6.Option;
var TabPane = es/* Tabs */.tU.TabPane;
var TextArea = es/* Input */.pd.TextArea;

/**
 * TemplatesPage component
 * Displays and manages component, layout, and app templates
 */
var TemplatesPage = function TemplatesPage() {
  // Template data states
  var _useState = (0,react.useState)([]),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    componentTemplates = _useState2[0],
    setComponentTemplates = _useState2[1];
  var _useState3 = (0,react.useState)([]),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    layoutTemplates = _useState4[0],
    setLayoutTemplates = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    appTemplates = _useState6[0],
    setAppTemplates = _useState6[1];

  // UI states
  var _useState7 = (0,react.useState)(true),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    loading = _useState8[0],
    setLoading = _useState8[1];
  var _useState9 = (0,react.useState)(''),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    searchText = _useState0[0],
    setSearchText = _useState0[1];
  var _useState1 = (0,react.useState)(''),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    filterType = _useState10[0],
    setFilterType = _useState10[1];
  var _useState11 = (0,react.useState)(''),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    filterCategory = _useState12[0],
    setFilterCategory = _useState12[1];
  var _useState13 = (0,react.useState)('all'),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    visibility = _useState14[0],
    setVisibility = _useState14[1];
  var _useState15 = (0,react.useState)(false),
    _useState16 = (0,slicedToArray/* default */.A)(_useState15, 2),
    createModalVisible = _useState16[0],
    setCreateModalVisible = _useState16[1];
  var _useState17 = (0,react.useState)(false),
    _useState18 = (0,slicedToArray/* default */.A)(_useState17, 2),
    editModalVisible = _useState18[0],
    setEditModalVisible = _useState18[1];
  var _useState19 = (0,react.useState)(false),
    _useState20 = (0,slicedToArray/* default */.A)(_useState19, 2),
    importModalVisible = _useState20[0],
    setImportModalVisible = _useState20[1];
  var _useState21 = (0,react.useState)(null),
    _useState22 = (0,slicedToArray/* default */.A)(_useState21, 2),
    currentTemplate = _useState22[0],
    setCurrentTemplate = _useState22[1];
  var _Form$useForm = es/* Form */.lV.useForm(),
    _Form$useForm2 = (0,slicedToArray/* default */.A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState23 = (0,react.useState)(false),
    _useState24 = (0,slicedToArray/* default */.A)(_useState23, 2),
    saving = _useState24[0],
    setSaving = _useState24[1];
  var _useState25 = (0,react.useState)('my'),
    _useState26 = (0,slicedToArray/* default */.A)(_useState25, 2),
    activeTab = _useState26[0],
    setActiveTab = _useState26[1];
  var _useState27 = (0,react.useState)('components'),
    _useState28 = (0,slicedToArray/* default */.A)(_useState27, 2),
    templateType = _useState28[0],
    setTemplateType = _useState28[1]; // components, layouts, apps

  // Categories for different template types
  var componentTypes = ['button', 'input', 'select', 'checkbox', 'radio', 'switch', 'slider', 'date-picker', 'time-picker', 'upload', 'form', 'table', 'list', 'card', 'tabs', 'modal', 'drawer', 'menu', 'layout', 'custom'];
  var layoutTypes = ['grid', 'flex', 'sidebar', 'header-footer', 'dashboard', 'landing', 'blog', 'portfolio', 'ecommerce', 'admin', 'custom'];
  var appCategories = [{
    value: 'business',
    label: 'Business Apps'
  }, {
    value: 'ecommerce',
    label: 'E-commerce'
  }, {
    value: 'portfolio',
    label: 'Portfolio'
  }, {
    value: 'dashboard',
    label: 'Dashboard'
  }, {
    value: 'landing',
    label: 'Landing Page'
  }, {
    value: 'blog',
    label: 'Blog'
  }, {
    value: 'social',
    label: 'Social Media'
  }, {
    value: 'education',
    label: 'Education'
  }, {
    value: 'healthcare',
    label: 'Healthcare'
  }, {
    value: 'finance',
    label: 'Finance'
  }, {
    value: 'other',
    label: 'Other'
  }];

  // Fetch templates when component mounts or tab changes
  (0,react.useEffect)(function () {
    fetchAllTemplates();
  }, [activeTab, templateType]);

  // Fetch all template types from API
  var fetchAllTemplates = /*#__PURE__*/function () {
    var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
      var _t;
      return regenerator_default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setLoading(true);
            _context.prev = 1;
            _context.next = 2;
            return Promise.all([fetchComponentTemplates(), fetchLayoutTemplates(), fetchAppTemplates()]);
          case 2:
            _context.next = 4;
            break;
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            console.error('Error fetching templates:', _t);
            es/* message */.iU.error('Failed to load templates');
          case 4:
            _context.prev = 4;
            setLoading(false);
            return _context.finish(4);
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3, 4, 5]]);
    }));
    return function fetchAllTemplates() {
      return _ref.apply(this, arguments);
    };
  }();

  // Fetch component templates
  var fetchComponentTemplates = /*#__PURE__*/function () {
    var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2() {
      var url, params, response, _t2;
      return regenerator_default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            url = '/api/component-templates/';
            params = new URLSearchParams();
            if (activeTab === 'my') {
              params.append('user', 'current');
            } else if (activeTab === 'public') {
              params.append('is_public', 'true');
            }
            if (params.toString()) {
              url += "?".concat(params.toString());
            }
            _context2.next = 1;
            return axios/* default */.Ay.get(url);
          case 1:
            response = _context2.sent;
            setComponentTemplates(response.data.results || response.data);
            _context2.next = 3;
            break;
          case 2:
            _context2.prev = 2;
            _t2 = _context2["catch"](0);
            console.error('Error fetching component templates:', _t2);
          case 3:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 2]]);
    }));
    return function fetchComponentTemplates() {
      return _ref2.apply(this, arguments);
    };
  }();

  // Fetch layout templates
  var fetchLayoutTemplates = /*#__PURE__*/function () {
    var _ref3 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3() {
      var url, params, response, _t3;
      return regenerator_default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.prev = 0;
            url = '/api/layout-templates/';
            params = new URLSearchParams();
            if (activeTab === 'my') {
              params.append('user', 'current');
            } else if (activeTab === 'public') {
              params.append('is_public', 'true');
            }
            if (params.toString()) {
              url += "?".concat(params.toString());
            }
            _context3.next = 1;
            return axios/* default */.Ay.get(url);
          case 1:
            response = _context3.sent;
            setLayoutTemplates(response.data.results || response.data);
            _context3.next = 3;
            break;
          case 2:
            _context3.prev = 2;
            _t3 = _context3["catch"](0);
            console.error('Error fetching layout templates:', _t3);
          case 3:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[0, 2]]);
    }));
    return function fetchLayoutTemplates() {
      return _ref3.apply(this, arguments);
    };
  }();

  // Fetch app templates
  var fetchAppTemplates = /*#__PURE__*/function () {
    var _ref4 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4() {
      var url, params, response, _t4;
      return regenerator_default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            _context4.prev = 0;
            url = '/api/app-templates/';
            params = new URLSearchParams();
            if (activeTab === 'my') {
              params.append('user', 'current');
            } else if (activeTab === 'public') {
              params.append('is_public', 'true');
            }
            if (params.toString()) {
              url += "?".concat(params.toString());
            }
            _context4.next = 1;
            return axios/* default */.Ay.get(url);
          case 1:
            response = _context4.sent;
            setAppTemplates(response.data.results || response.data);
            _context4.next = 3;
            break;
          case 2:
            _context4.prev = 2;
            _t4 = _context4["catch"](0);
            console.error('Error fetching app templates:', _t4);
          case 3:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[0, 2]]);
    }));
    return function fetchAppTemplates() {
      return _ref4.apply(this, arguments);
    };
  }();

  // Create a new template
  var createTemplate = /*#__PURE__*/function () {
    var _ref5 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee5(values) {
      var url, data, _t5;
      return regenerator_default().wrap(function (_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            setSaving(true);
            _context5.prev = 1;
            if (templateType === 'components') {
              url = '/api/component-templates/';
              data = TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, values), {}, {
                default_props: JSON.stringify(values.default_props || {})
              });
            } else if (templateType === 'layouts') {
              url = '/api/layout-templates/';
              data = TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, values), {}, {
                components: JSON.stringify(values.components || {}),
                default_props: JSON.stringify(values.default_props || {})
              });
            } else if (templateType === 'apps') {
              url = '/api/app-templates/';
              data = TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, values), {}, {
                components: JSON.stringify(values.components || {}),
                default_props: JSON.stringify(values.default_props || {}),
                required_components: JSON.stringify(values.required_components || [])
              });
            }
            _context5.next = 2;
            return axios/* default */.Ay.post(url, data);
          case 2:
            es/* message */.iU.success('Template created successfully');
            setCreateModalVisible(false);
            form.resetFields();

            // Refresh templates
            fetchAllTemplates();
            _context5.next = 4;
            break;
          case 3:
            _context5.prev = 3;
            _t5 = _context5["catch"](1);
            console.error('Error creating template:', _t5);
            es/* message */.iU.error('Failed to create template');
          case 4:
            _context5.prev = 4;
            setSaving(false);
            return _context5.finish(4);
          case 5:
          case "end":
            return _context5.stop();
        }
      }, _callee5, null, [[1, 3, 4, 5]]);
    }));
    return function createTemplate(_x) {
      return _ref5.apply(this, arguments);
    };
  }();

  // Update a template
  var updateTemplate = /*#__PURE__*/function () {
    var _ref6 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee6(values) {
      var url, data, _t6;
      return regenerator_default().wrap(function (_context6) {
        while (1) switch (_context6.prev = _context6.next) {
          case 0:
            setSaving(true);
            _context6.prev = 1;
            if (currentTemplate.template_type === 'component') {
              url = "/api/component-templates/".concat(currentTemplate.id, "/");
              data = TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, values), {}, {
                default_props: (0,esm_typeof/* default */.A)(values.default_props) === 'object' ? JSON.stringify(values.default_props) : values.default_props
              });
            } else if (currentTemplate.template_type === 'layout') {
              url = "/api/layout-templates/".concat(currentTemplate.id, "/");
              data = TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, values), {}, {
                components: (0,esm_typeof/* default */.A)(values.components) === 'object' ? JSON.stringify(values.components) : values.components,
                default_props: (0,esm_typeof/* default */.A)(values.default_props) === 'object' ? JSON.stringify(values.default_props) : values.default_props
              });
            } else if (currentTemplate.template_type === 'app') {
              url = "/api/app-templates/".concat(currentTemplate.id, "/");
              data = TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, values), {}, {
                components: (0,esm_typeof/* default */.A)(values.components) === 'object' ? JSON.stringify(values.components) : values.components,
                default_props: (0,esm_typeof/* default */.A)(values.default_props) === 'object' ? JSON.stringify(values.default_props) : values.default_props,
                required_components: (0,esm_typeof/* default */.A)(values.required_components) === 'object' ? JSON.stringify(values.required_components) : values.required_components
              });
            }
            _context6.next = 2;
            return axios/* default */.Ay.patch(url, data);
          case 2:
            es/* message */.iU.success('Template updated successfully');
            setEditModalVisible(false);

            // Refresh templates
            fetchAllTemplates();
            _context6.next = 4;
            break;
          case 3:
            _context6.prev = 3;
            _t6 = _context6["catch"](1);
            console.error('Error updating template:', _t6);
            es/* message */.iU.error('Failed to update template');
          case 4:
            _context6.prev = 4;
            setSaving(false);
            return _context6.finish(4);
          case 5:
          case "end":
            return _context6.stop();
        }
      }, _callee6, null, [[1, 3, 4, 5]]);
    }));
    return function updateTemplate(_x2) {
      return _ref6.apply(this, arguments);
    };
  }();

  // Delete a template
  var deleteTemplate = /*#__PURE__*/function () {
    var _ref7 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee7(template) {
      var url, _t7;
      return regenerator_default().wrap(function (_context7) {
        while (1) switch (_context7.prev = _context7.next) {
          case 0:
            _context7.prev = 0;
            if (template.template_type === 'component') {
              url = "/api/component-templates/".concat(template.id, "/");
            } else if (template.template_type === 'layout') {
              url = "/api/layout-templates/".concat(template.id, "/");
            } else if (template.template_type === 'app') {
              url = "/api/app-templates/".concat(template.id, "/");
            }
            _context7.next = 1;
            return axios/* default */.Ay["delete"](url);
          case 1:
            es/* message */.iU.success('Template deleted successfully');

            // Refresh templates
            fetchAllTemplates();
            _context7.next = 3;
            break;
          case 2:
            _context7.prev = 2;
            _t7 = _context7["catch"](0);
            console.error('Error deleting template:', _t7);
            es/* message */.iU.error('Failed to delete template');
          case 3:
          case "end":
            return _context7.stop();
        }
      }, _callee7, null, [[0, 2]]);
    }));
    return function deleteTemplate(_x3) {
      return _ref7.apply(this, arguments);
    };
  }();

  // Edit a template
  var editTemplate = function editTemplate(template) {
    setCurrentTemplate(template);

    // Parse JSON fields if they're strings
    var defaultProps = typeof template.default_props === 'string' ? JSON.parse(template.default_props) : template.default_props;
    var components = template.components && typeof template.components === 'string' ? JSON.parse(template.components) : template.components;
    var requiredComponents = template.required_components && typeof template.required_components === 'string' ? JSON.parse(template.required_components) : template.required_components;
    var formValues = {
      name: template.name,
      description: template.description,
      default_props: defaultProps,
      is_public: template.is_public
    };

    // Add type-specific fields
    if (template.template_type === 'component') {
      formValues.component_type = template.component_type;
    } else if (template.template_type === 'layout') {
      formValues.layout_type = template.layout_type;
      formValues.components = components;
    } else if (template.template_type === 'app') {
      formValues.app_category = template.app_category;
      formValues.components = components;
      formValues.required_components = requiredComponents;
      formValues.preview_image = template.preview_image;
    }
    form.setFieldsValue(formValues);
    setEditModalVisible(true);
  };

  // Get current templates based on active template type
  var getCurrentTemplates = function getCurrentTemplates() {
    switch (templateType) {
      case 'layouts':
        return layoutTemplates.map(function (t) {
          return TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, t), {}, {
            template_type: 'layout'
          });
        });
      case 'apps':
        return appTemplates.map(function (t) {
          return TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, t), {}, {
            template_type: 'app'
          });
        });
      default:
        return componentTemplates.map(function (t) {
          return TemplatesPage_objectSpread(TemplatesPage_objectSpread({}, t), {}, {
            template_type: 'component'
          });
        });
    }
  };

  // Get template type display name
  var getTemplateTypeDisplayName = function getTemplateTypeDisplayName() {
    switch (templateType) {
      case 'layouts':
        return 'Layout Template';
      case 'apps':
        return 'App Template';
      default:
        return 'Component Template';
    }
  };

  // Filter templates
  var filteredTemplates = getCurrentTemplates().filter(function (template) {
    // Filter by search text
    var matchesSearch = searchText === '' || template.name.toLowerCase().includes(searchText.toLowerCase()) || template.description.toLowerCase().includes(searchText.toLowerCase());

    // Filter by type/category
    var matchesType = true;
    if (templateType === 'components' && filterType) {
      matchesType = template.component_type === filterType;
    } else if (templateType === 'layouts' && filterType) {
      matchesType = template.layout_type === filterType;
    } else if (templateType === 'apps' && filterCategory) {
      matchesType = template.app_category === filterCategory;
    }

    // Filter by visibility
    var matchesVisibility = visibility === 'all' || visibility === 'public' && template.is_public || visibility === 'private' && !template.is_public;
    return matchesSearch && matchesType && matchesVisibility;
  });

  // Get unique types for current template type
  var getUniqueTypes = function getUniqueTypes() {
    var templates = getCurrentTemplates();
    switch (templateType) {
      case 'layouts':
        return (0,toConsumableArray/* default */.A)(new Set(templates.map(function (t) {
          return t.layout_type;
        })));
      case 'apps':
        return (0,toConsumableArray/* default */.A)(new Set(templates.map(function (t) {
          return t.app_category;
        })));
      default:
        return (0,toConsumableArray/* default */.A)(new Set(templates.map(function (t) {
          return t.component_type;
        })));
    }
  };

  // Render template item
  var renderTemplateItem = function renderTemplateItem(template) {
    var getTemplateIcon = function getTemplateIcon() {
      switch (template.template_type) {
        case 'layout':
          return /*#__PURE__*/react.createElement(icons_es/* LayoutOutlined */.hy2, null);
        case 'app':
          return /*#__PURE__*/react.createElement(icons_es/* MobileOutlined */.jHj, null);
        default:
          return /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null);
      }
    };
    var getTemplateTypeTag = function getTemplateTypeTag() {
      switch (template.template_type) {
        case 'layout':
          return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
            color: "purple"
          }, template.layout_type);
        case 'app':
          return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
            color: "orange"
          }, template.app_category);
        default:
          return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
            color: "blue"
          }, template.component_type);
      }
    };
    return /*#__PURE__*/react.createElement(es/* List */.B8.Item, null, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
      title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, getTemplateIcon(), /*#__PURE__*/react.createElement(TemplatesPage_Text, {
        strong: true
      }, template.name), template.is_public ? /*#__PURE__*/react.createElement(es/* Tag */.vw, {
        icon: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
        color: "green"
      }, "Public") : /*#__PURE__*/react.createElement(es/* Tag */.vw, {
        icon: /*#__PURE__*/react.createElement(icons_es/* LockOutlined */.sXv, null),
        color: "default"
      }, "Private"), template.template_type === 'app' && template.preview_image && /*#__PURE__*/react.createElement(es/* Tag */.vw, {
        icon: /*#__PURE__*/react.createElement(icons_es/* StarOutlined */.L0Y, null),
        color: "gold"
      }, "Featured")),
      extra: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Use Template"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        icon: /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, null)
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Export Template"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        icon: /*#__PURE__*/react.createElement(icons_es/* DownloadOutlined */.jsW, null),
        onClick: function onClick() {
          return exportTemplate(template);
        }
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Edit Template"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        icon: /*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, null),
        onClick: function onClick() {
          return editTemplate(template);
        }
      })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Delete Template"
      }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
        danger: true,
        icon: /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null),
        onClick: function onClick() {
          return es/* Modal */.aF.confirm({
            title: 'Delete Template',
            content: "Are you sure you want to delete \"".concat(template.name, "\"?"),
            okText: 'Delete',
            okType: 'danger',
            onOk: function onOk() {
              return deleteTemplate(template);
            }
          });
        }
      }))),
      style: {
        width: '100%'
      },
      cover: template.template_type === 'app' && template.preview_image ? /*#__PURE__*/react.createElement(es/* Image */._V, {
        alt: template.name,
        src: template.preview_image,
        height: 120,
        style: {
          objectFit: 'cover'
        },
        fallback: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
      }) : null
    }, /*#__PURE__*/react.createElement(Paragraph, {
      ellipsis: {
        rows: 2
      }
    }, template.description || 'No description provided'), /*#__PURE__*/react.createElement(es/* Space */.$x, {
      wrap: true
    }, getTemplateTypeTag(), template.template_type === 'app' && template.required_components && template.required_components.length > 0 && /*#__PURE__*/react.createElement(es/* Tag */.vw, {
      color: "cyan"
    }, /*#__PURE__*/react.createElement(icons_es/* BulbOutlined */.o3f, null), " ", template.required_components.length, " dependencies"), /*#__PURE__*/react.createElement(TemplatesPage_Text, {
      type: "secondary"
    }, "Created by: ", template.user ? template.user.username : 'Anonymous'), /*#__PURE__*/react.createElement(TemplatesPage_Text, {
      type: "secondary"
    }, "Created: ", new Date(template.created_at).toLocaleDateString()))));
  };

  // Export template
  var exportTemplate = /*#__PURE__*/function () {
    var _ref8 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee8(template) {
      var url, response, dataStr, dataBlob, link, _t8;
      return regenerator_default().wrap(function (_context8) {
        while (1) switch (_context8.prev = _context8.next) {
          case 0:
            _context8.prev = 0;
            if (template.template_type === 'component') {
              url = "/api/component-templates/".concat(template.id, "/export_template/");
            } else if (template.template_type === 'layout') {
              url = "/api/layout-templates/".concat(template.id, "/export_template/");
            } else if (template.template_type === 'app') {
              url = "/api/app-templates/".concat(template.id, "/export_template/");
            }
            _context8.next = 1;
            return axios/* default */.Ay.get(url);
          case 1:
            response = _context8.sent;
            dataStr = JSON.stringify(response.data, null, 2);
            dataBlob = new Blob([dataStr], {
              type: 'application/json'
            });
            link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = "".concat(template.name.replace(/\s+/g, '_'), "_template.json");
            link.click();
            es/* message */.iU.success('Template exported successfully');
            _context8.next = 3;
            break;
          case 2:
            _context8.prev = 2;
            _t8 = _context8["catch"](0);
            console.error('Error exporting template:', _t8);
            es/* message */.iU.error('Failed to export template');
          case 3:
          case "end":
            return _context8.stop();
        }
      }, _callee8, null, [[0, 2]]);
    }));
    return function exportTemplate(_x4) {
      return _ref8.apply(this, arguments);
    };
  }();

  // Render template form
  var renderTemplateForm = function renderTemplateForm() {
    return /*#__PURE__*/react.createElement(es/* Form */.lV, {
      form: form,
      layout: "vertical",
      onFinish: currentTemplate ? updateTemplate : createTemplate
    }, /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "name",
      label: "Template Name",
      rules: [{
        required: true,
        message: 'Please enter template name'
      }]
    }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
      placeholder: "Enter template name"
    })), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "description",
      label: "Description"
    }, /*#__PURE__*/react.createElement(TextArea, {
      placeholder: "Enter template description",
      rows: 4,
      maxLength: 500,
      showCount: true
    })), templateType === 'components' && /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "component_type",
      label: "Component Type",
      rules: [{
        required: true,
        message: 'Please select component type'
      }]
    }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
      placeholder: "Select component type"
    }, componentTypes.map(function (type) {
      return /*#__PURE__*/react.createElement(Option, {
        key: type,
        value: type
      }, type);
    }))), templateType === 'layouts' && /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "layout_type",
      label: "Layout Type",
      rules: [{
        required: true,
        message: 'Please select layout type'
      }]
    }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
      placeholder: "Select layout type"
    }, layoutTypes.map(function (type) {
      return /*#__PURE__*/react.createElement(Option, {
        key: type,
        value: type
      }, type);
    }))), templateType === 'apps' && /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "app_category",
      label: "App Category",
      rules: [{
        required: true,
        message: 'Please select app category'
      }]
    }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
      placeholder: "Select app category"
    }, appCategories.map(function (category) {
      return /*#__PURE__*/react.createElement(Option, {
        key: category.value,
        value: category.value
      }, category.label);
    }))), (templateType === 'layouts' || templateType === 'apps') && /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "components",
      label: "Components Configuration",
      rules: [{
        required: true,
        message: 'Please enter components configuration'
      }, {
        validator: function validator(_, value) {
          try {
            if (typeof value === 'string') {
              JSON.parse(value);
            }
            return Promise.resolve();
          } catch (error) {
            return Promise.reject('Please enter valid JSON');
          }
        }
      }]
    }, /*#__PURE__*/react.createElement(TextArea, {
      placeholder: "Enter components configuration in JSON format",
      rows: 8,
      style: {
        fontFamily: 'monospace'
      }
    })), templateType === 'apps' && /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "required_components",
      label: "Required Components",
      rules: [{
        validator: function validator(_, value) {
          try {
            if (value && typeof value === 'string') {
              var parsed = JSON.parse(value);
              if (!Array.isArray(parsed)) {
                return Promise.reject('Required components must be an array');
              }
            }
            return Promise.resolve();
          } catch (error) {
            return Promise.reject('Please enter valid JSON array');
          }
        }
      }]
    }, /*#__PURE__*/react.createElement(TextArea, {
      placeholder: "Enter required components as JSON array, e.g., [\"button\", \"input\"]",
      rows: 3,
      style: {
        fontFamily: 'monospace'
      }
    })), templateType === 'apps' && /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "preview_image",
      label: "Preview Image URL"
    }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
      placeholder: "Enter preview image URL (optional)"
    })), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "default_props",
      label: "Default Properties",
      rules: [{
        validator: function validator(_, value) {
          try {
            if (value && typeof value === 'string') {
              JSON.parse(value);
            }
            return Promise.resolve();
          } catch (error) {
            return Promise.reject('Please enter valid JSON');
          }
        }
      }]
    }, /*#__PURE__*/react.createElement(TextArea, {
      placeholder: "Enter default properties in JSON format (optional)",
      rows: 4,
      style: {
        fontFamily: 'monospace'
      }
    })), /*#__PURE__*/react.createElement(es/* Form */.lV.Item, {
      name: "is_public",
      label: "Visibility",
      valuePropName: "checked"
    }, /*#__PURE__*/react.createElement(es/* Switch */.dO, {
      checkedChildren: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
      unCheckedChildren: /*#__PURE__*/react.createElement(icons_es/* LockOutlined */.sXv, null)
    }), /*#__PURE__*/react.createElement(TemplatesPage_Text, {
      type: "secondary",
      style: {
        marginLeft: 8
      }
    }, "Make this template public")));
  };

  // Import template
  var importTemplate = /*#__PURE__*/function () {
    var _ref9 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee9(templateData) {
      var url, _t9;
      return regenerator_default().wrap(function (_context9) {
        while (1) switch (_context9.prev = _context9.next) {
          case 0:
            _context9.prev = 0;
            if (templateType === 'components') {
              url = '/api/component-templates/import_template/';
            } else if (templateType === 'layouts') {
              url = '/api/layout-templates/import_template/';
            } else if (templateType === 'apps') {
              url = '/api/app-templates/import_template/';
            }
            _context9.next = 1;
            return axios/* default */.Ay.post(url, {
              template_data: templateData
            });
          case 1:
            es/* message */.iU.success('Template imported successfully');
            setImportModalVisible(false);

            // Refresh templates
            fetchAllTemplates();
            _context9.next = 3;
            break;
          case 2:
            _context9.prev = 2;
            _t9 = _context9["catch"](0);
            console.error('Error importing template:', _t9);
            es/* message */.iU.error('Failed to import template');
          case 3:
          case "end":
            return _context9.stop();
        }
      }, _callee9, null, [[0, 2]]);
    }));
    return function importTemplate(_x5) {
      return _ref9.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/react.createElement(layout_MainLayout, null, /*#__PURE__*/react.createElement("div", {
    className: "templates-page"
  }, /*#__PURE__*/react.createElement("div", {
    className: "templates-header"
  }, /*#__PURE__*/react.createElement(TemplatesPage_Title, {
    level: 2
  }, /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null), " Template Library"), /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* UploadOutlined */.qvO, null),
    onClick: function onClick() {
      return setImportModalVisible(true);
    }
  }, "Import Template"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null),
    onClick: function onClick() {
      form.resetFields();
      setCreateModalVisible(true);
    }
  }, "Create ", getTemplateTypeDisplayName()))), /*#__PURE__*/react.createElement("div", {
    className: "template-type-tabs"
  }, /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    activeKey: templateType,
    onChange: setTemplateType,
    type: "card",
    size: "large"
  }, /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* AppstoreOutlined */.rS9, null), "Components", /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      count: componentTemplates.length,
      style: {
        marginLeft: 8
      }
    })),
    key: "components"
  }), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* LayoutOutlined */.hy2, null), "Layouts", /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      count: layoutTemplates.length,
      style: {
        marginLeft: 8
      }
    })),
    key: "layouts"
  }), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* MobileOutlined */.jHj, null), "App Starters", /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      count: appTemplates.length,
      style: {
        marginLeft: 8
      }
    })),
    key: "apps"
  }))), /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    className: "templates-tabs"
  }, /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* UserOutlined */.qmv, null), " My Templates"),
    key: "my"
  }, /*#__PURE__*/react.createElement("div", {
    className: "templates-filters"
  }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
    placeholder: "Search templates",
    prefix: /*#__PURE__*/react.createElement(icons_es/* SearchOutlined */.VrN, null),
    value: searchText,
    onChange: function onChange(e) {
      return setSearchText(e.target.value);
    },
    style: {
      width: 250
    }
  }), /*#__PURE__*/react.createElement(es/* Select */.l6, {
    placeholder: "Filter by ".concat(templateType === 'components' ? 'component type' : templateType === 'layouts' ? 'layout type' : 'category'),
    value: templateType === 'apps' ? filterCategory : filterType,
    onChange: templateType === 'apps' ? setFilterCategory : setFilterType,
    style: {
      width: 200
    },
    allowClear: true,
    suffixIcon: /*#__PURE__*/react.createElement(icons_es/* FilterOutlined */.Lxx, null)
  }, templateType === 'apps' ? appCategories.map(function (category) {
    return /*#__PURE__*/react.createElement(Option, {
      key: category.value,
      value: category.value
    }, category.label);
  }) : getUniqueTypes().map(function (type) {
    return /*#__PURE__*/react.createElement(Option, {
      key: type,
      value: type
    }, type);
  })), /*#__PURE__*/react.createElement(es/* Select */.l6, {
    placeholder: "Filter by visibility",
    value: visibility,
    onChange: setVisibility,
    style: {
      width: 150
    },
    suffixIcon: /*#__PURE__*/react.createElement(icons_es/* FilterOutlined */.Lxx, null)
  }, /*#__PURE__*/react.createElement(Option, {
    value: "all"
  }, "All"), /*#__PURE__*/react.createElement(Option, {
    value: "public"
  }, "Public"), /*#__PURE__*/react.createElement(Option, {
    value: "private"
  }, "Private"))), /*#__PURE__*/react.createElement("div", {
    className: "templates-list"
  }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
    spinning: loading
  }, filteredTemplates.length > 0 ? /*#__PURE__*/react.createElement(es/* List */.B8, {
    grid: {
      gutter: 16,
      xs: 1,
      sm: 1,
      md: 2,
      lg: 2,
      xl: 3,
      xxl: 3
    },
    dataSource: filteredTemplates,
    renderItem: renderTemplateItem,
    pagination: {
      pageSize: 9,
      hideOnSinglePage: true
    }
  }) : /*#__PURE__*/react.createElement(es/* Empty */.Sv, {
    description: /*#__PURE__*/react.createElement("span", null, loading ? 'Loading templates...' : "No ".concat(templateType, " templates found"))
  })))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* GlobalOutlined */.clv, null), " Public Templates"),
    key: "public"
  }, /*#__PURE__*/react.createElement("div", {
    className: "templates-filters"
  }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
    placeholder: "Search templates",
    prefix: /*#__PURE__*/react.createElement(icons_es/* SearchOutlined */.VrN, null),
    value: searchText,
    onChange: function onChange(e) {
      return setSearchText(e.target.value);
    },
    style: {
      width: 250
    }
  }), /*#__PURE__*/react.createElement(es/* Select */.l6, {
    placeholder: "Filter by ".concat(templateType === 'components' ? 'component type' : templateType === 'layouts' ? 'layout type' : 'category'),
    value: templateType === 'apps' ? filterCategory : filterType,
    onChange: templateType === 'apps' ? setFilterCategory : setFilterType,
    style: {
      width: 200
    },
    allowClear: true,
    suffixIcon: /*#__PURE__*/react.createElement(icons_es/* FilterOutlined */.Lxx, null)
  }, templateType === 'apps' ? appCategories.map(function (category) {
    return /*#__PURE__*/react.createElement(Option, {
      key: category.value,
      value: category.value
    }, category.label);
  }) : getUniqueTypes().map(function (type) {
    return /*#__PURE__*/react.createElement(Option, {
      key: type,
      value: type
    }, type);
  }))), /*#__PURE__*/react.createElement("div", {
    className: "templates-list"
  }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
    spinning: loading
  }, filteredTemplates.length > 0 ? /*#__PURE__*/react.createElement(es/* List */.B8, {
    grid: {
      gutter: 16,
      xs: 1,
      sm: 1,
      md: 2,
      lg: 2,
      xl: 3,
      xxl: 3
    },
    dataSource: filteredTemplates,
    renderItem: renderTemplateItem,
    pagination: {
      pageSize: 9,
      hideOnSinglePage: true
    }
  }) : /*#__PURE__*/react.createElement(es/* Empty */.Sv, {
    description: /*#__PURE__*/react.createElement("span", null, loading ? 'Loading templates...' : "No public ".concat(templateType, " templates found"))
  }))))), /*#__PURE__*/react.createElement(es/* Modal */.aF, {
    title: "Create ".concat(getTemplateTypeDisplayName()),
    open: createModalVisible,
    onCancel: function onCancel() {
      return setCreateModalVisible(false);
    },
    onOk: function onOk() {
      return form.submit();
    },
    okText: "Create",
    confirmLoading: saving,
    width: 800
  }, renderTemplateForm()), /*#__PURE__*/react.createElement(es/* Modal */.aF, {
    title: "Edit ".concat((currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.template_type) === 'component' ? 'Component' : (currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.template_type) === 'layout' ? 'Layout' : 'App', " Template"),
    open: editModalVisible,
    onCancel: function onCancel() {
      return setEditModalVisible(false);
    },
    onOk: function onOk() {
      return form.submit();
    },
    okText: "Save Changes",
    confirmLoading: saving,
    width: 800
  }, renderTemplateForm()), /*#__PURE__*/react.createElement(es/* Modal */.aF, {
    title: "Import Template",
    open: importModalVisible,
    onCancel: function onCancel() {
      return setImportModalVisible(false);
    },
    footer: null,
    width: 600
  }, /*#__PURE__*/react.createElement(es/* Upload */._O.Dragger, {
    name: "file",
    multiple: false,
    accept: ".json",
    beforeUpload: function beforeUpload(file) {
      var reader = new FileReader();
      reader.onload = /*#__PURE__*/function () {
        var _ref0 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee0(e) {
          var templateData, _t0;
          return regenerator_default().wrap(function (_context0) {
            while (1) switch (_context0.prev = _context0.next) {
              case 0:
                _context0.prev = 0;
                templateData = JSON.parse(e.target.result);
                _context0.next = 1;
                return importTemplate(templateData);
              case 1:
                _context0.next = 3;
                break;
              case 2:
                _context0.prev = 2;
                _t0 = _context0["catch"](0);
                es/* message */.iU.error('Invalid JSON file');
              case 3:
              case "end":
                return _context0.stop();
            }
          }, _callee0, null, [[0, 2]]);
        }));
        return function (_x6) {
          return _ref0.apply(this, arguments);
        };
      }();
      reader.readAsText(file);
      return false; // Prevent automatic upload
    }
  }, /*#__PURE__*/react.createElement("p", {
    className: "ant-upload-drag-icon"
  }, /*#__PURE__*/react.createElement(icons_es/* UploadOutlined */.qvO, null)), /*#__PURE__*/react.createElement("p", {
    className: "ant-upload-text"
  }, "Click or drag file to this area to upload"), /*#__PURE__*/react.createElement("p", {
    className: "ant-upload-hint"
  }, "Support for JSON template files only. The template will be imported to the current template type (", templateType, ").")))), /*#__PURE__*/react.createElement("style", {
    jsx: "true"
  }, "\n        .templates-page {\n          max-width: 1400px;\n          margin: 0 auto;\n          padding: 0 16px;\n        }\n\n        .templates-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 24px;\n        }\n\n        .template-type-tabs {\n          margin-bottom: 24px;\n        }\n\n        .template-type-tabs .ant-tabs-card > .ant-tabs-content {\n          margin-top: 0;\n        }\n\n        .template-type-tabs .ant-tabs-card > .ant-tabs-content > .ant-tabs-tabpane {\n          background: transparent;\n          border: none;\n        }\n\n        .templates-tabs {\n          background-color: #fff;\n          padding: 16px;\n          border-radius: 8px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n        }\n\n        .templates-filters {\n          display: flex;\n          gap: 16px;\n          margin-bottom: 24px;\n          flex-wrap: wrap;\n        }\n\n        .templates-list {\n          min-height: 400px;\n        }\n\n        .ant-card-cover img {\n          border-radius: 8px 8px 0 0;\n        }\n\n        .ant-upload-drag {\n          border: 2px dashed #d9d9d9;\n          border-radius: 8px;\n          background: #fafafa;\n          text-align: center;\n          padding: 40px 20px;\n        }\n\n        .ant-upload-drag:hover {\n          border-color: #1890ff;\n        }\n\n        .ant-upload-drag-icon {\n          font-size: 48px;\n          color: #d9d9d9;\n          margin-bottom: 16px;\n        }\n\n        .ant-upload-text {\n          font-size: 16px;\n          color: #666;\n          margin-bottom: 8px;\n        }\n\n        .ant-upload-hint {\n          font-size: 14px;\n          color: #999;\n        }\n      "));
};
/* harmony default export */ const pages_TemplatesPage = (TemplatesPage);

/***/ }),

/***/ 51162:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(17053);


var _templateObject, _templateObject2;





var StatusContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: inline-flex;\n  align-items: center;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.05);\n  }\n"])));
var StatusText = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.span(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  margin-left: 8px;\n  font-size: 12px;\n  @media (max-width: 768px) {\n    display: none;\n  }\n"])));

/**
 * WebSocketStatus component displays the current WebSocket connection status
 * with appropriate visual indicators.
 */
var WebSocketStatus = function WebSocketStatus() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('disconnected'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    status = _useState2[0],
    setStatus = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    lastError = _useState4[0],
    setLastError = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    reconnectCount = _useState6[0],
    setReconnectCount = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState7, 2),
    tooltipVisible = _useState8[0],
    setTooltipVisible = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(navigator.onLine),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState9, 2),
    isOnline = _useState0[0],
    setIsOnline = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState1, 2),
    offlineQueueStatus = _useState10[0],
    setOfflineQueueStatus = _useState10[1];
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    // Set initial state
    setStatus(_services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.isConnected ? 'connected' : 'disconnected');
    setIsOnline(navigator.onLine);
    setReconnectCount(0);

    // Get initial offline queue status if available
    try {
      if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus === 'function') {
        var queueStatus = _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus();
        setOfflineQueueStatus(queueStatus);
      }
    } catch (error) {
      console.error('Error getting offline queue status:', error);
    }

    // Subscribe to WebSocket events
    var handleConnect = function handleConnect() {
      setStatus('connected');
      setReconnectCount(0);
    };
    var handleDisconnect = function handleDisconnect(event) {
      setStatus('disconnected');
      if (event && event.code) {
        setLastError({
          message: "Disconnected (code: ".concat(event.code, ")"),
          timestamp: new Date()
        });
      }
    };
    var handleConnecting = function handleConnecting() {
      setStatus('connecting');
    };
    var handleError = function handleError(error) {
      setStatus('error');
      setLastError(error);
    };
    var handleReconnectScheduled = function handleReconnectScheduled(data) {
      setStatus('connecting');
      setReconnectCount(data.attempt);
    };
    var handleNetworkStatus = function handleNetworkStatus(data) {
      setIsOnline(data.online);
    };
    var handleOfflineQueueUpdate = function handleOfflineQueueUpdate() {
      try {
        if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus === 'function') {
          var _queueStatus = _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus();
          setOfflineQueueStatus(_queueStatus);
        }
      } catch (error) {
        console.error('Error getting updated offline queue status:', error);
      }
    };

    // Register event listeners using the new API
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('connect', handleConnect);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('disconnect', handleDisconnect);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('connecting', handleConnecting);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('error', handleError);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('reconnect_scheduled', handleReconnectScheduled);
    _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('network_status', handleNetworkStatus);

    // Register offline queue events if the methods exist
    if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on === 'function') {
      if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus === 'function') {
        _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('message_queued_offline', handleOfflineQueueUpdate);
        _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('offline_queue_loaded', handleOfflineQueueUpdate);
        _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.on('offline_queue_cleared', handleOfflineQueueUpdate);
      }
    }

    // Also listen for browser online/offline events
    var handleBrowserOnline = function handleBrowserOnline() {
      return setIsOnline(true);
    };
    var handleBrowserOffline = function handleBrowserOffline() {
      return setIsOnline(false);
    };
    window.addEventListener('online', handleBrowserOnline);
    window.addEventListener('offline', handleBrowserOffline);

    // Cleanup event listeners on unmount
    return function () {
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('connect', handleConnect);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('disconnect', handleDisconnect);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('connecting', handleConnecting);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('error', handleError);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('reconnect_scheduled', handleReconnectScheduled);
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('network_status', handleNetworkStatus);

      // Unregister offline queue events if the methods exist
      if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off === 'function') {
        if (typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.getOfflineQueueStatus === 'function') {
          _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('message_queued_offline', handleOfflineQueueUpdate);
          _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('offline_queue_loaded', handleOfflineQueueUpdate);
          _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.off('offline_queue_cleared', handleOfflineQueueUpdate);
        }
      }
      window.removeEventListener('online', handleBrowserOnline);
      window.removeEventListener('offline', handleBrowserOffline);
    };
  }, []);

  // Determine status display properties
  var getStatusProps = function getStatusProps() {
    // First check if browser is offline
    if (!isOnline) {
      var queueCount = (offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) || 0;
      return {
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CloudOutlined */ .gDm, {
          style: {
            color: '#faad14'
          }
        }),
        text: queueCount > 0 ? "Offline (".concat(queueCount, ")") : 'Offline',
        status: 'warning',
        tooltip: 'Browser is offline. ' + (queueCount > 0 ? "".concat(queueCount, " messages queued for delivery when online.") : 'No queued messages.')
      };
    }

    // If online but has offline queue
    if (isOnline && (offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && status === 'connected') {
      return {
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CloudDownloadOutlined */ .Gkj, {
          style: {
            color: '#1890ff'
          }
        }),
        text: "Syncing (".concat(offlineQueueStatus.count, ")"),
        status: 'processing',
        tooltip: "Connected. Syncing ".concat(offlineQueueStatus.count, " queued messages.")
      };
    }

    // Normal connection states
    switch (status) {
      case 'connected':
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .LinkOutlined */ .t7c, {
            style: {
              color: '#52c41a'
            }
          }),
          text: 'Connected',
          status: 'success',
          tooltip: 'WebSocket connection established'
        };
      case 'disconnected':
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DisconnectOutlined */ .Bu6, {
            style: {
              color: '#bfbfbf'
            }
          }),
          text: 'Disconnected',
          status: 'default',
          tooltip: lastError ? "Disconnected: ".concat(lastError.message) : 'WebSocket disconnected'
        };
      case 'connecting':
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .LoadingOutlined */ .NKq, {
            style: {
              color: '#1890ff'
            }
          }),
          text: reconnectCount > 0 ? "Reconnecting (".concat(reconnectCount, ")") : 'Connecting',
          status: 'processing',
          tooltip: 'Attempting to establish WebSocket connection'
        };
      case 'error':
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .WarningOutlined */ .v7y, {
            style: {
              color: '#ff4d4f'
            }
          }),
          text: 'Connection Error',
          status: 'error',
          tooltip: lastError ? "Error: ".concat(lastError.message) : 'WebSocket connection error'
        };
      default:
        return {
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DisconnectOutlined */ .Bu6, {
            style: {
              color: '#bfbfbf'
            }
          }),
          text: 'Unknown',
          status: 'default',
          tooltip: 'WebSocket status unknown'
        };
    }
  };
  var statusProps = getStatusProps();

  // Handle manual reconnect attempt
  var handleReconnect = function handleReconnect() {
    if (!isOnline) {
      // Can't reconnect when offline, but we can show a message
      alert('You are currently offline. Please check your internet connection.');
      return;
    }
    if (status !== 'connected') {
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.reconnect();
    } else if ((offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.processOfflineQueue === 'function') {
      // If connected but has offline queue, process it
      _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.processOfflineQueue();
    }
  };

  // Handle clearing the offline queue
  var handleClearOfflineQueue = function handleClearOfflineQueue(e) {
    e.stopPropagation(); // Prevent triggering the parent onClick

    if ((offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && typeof _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.clearOfflineQueue === 'function') {
      var count = _services_WebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.clearOfflineQueue();
      alert("Cleared ".concat(count, " messages from the offline queue."));
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, statusProps.tooltip), lastError && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 4
      }
    }, "Last error: ", lastError.message, lastError.timestamp && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '0.8em',
        opacity: 0.8
      }
    }, new Date(lastError.timestamp).toLocaleTimeString())), (offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 4,
        borderTop: '1px solid rgba(255,255,255,0.2)',
        paddingTop: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, "Offline queue: ", offlineQueueStatus.count, " message", offlineQueueStatus.count !== 1 ? 's' : ''), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("button", {
      onClick: handleClearOfflineQueue,
      style: {
        background: 'rgba(255,255,255,0.2)',
        border: 'none',
        padding: '2px 8px',
        borderRadius: '4px',
        cursor: 'pointer',
        fontSize: '0.8em'
      }
    }, "Clear Queue"))), !isOnline && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 8,
        fontSize: '0.9em'
      }
    }, "You are offline. Check your internet connection."), isOnline && status !== 'connected' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 8,
        fontSize: '0.9em'
      }
    }, "Click to reconnect"), isOnline && status === 'connected' && (offlineQueueStatus === null || offlineQueueStatus === void 0 ? void 0 : offlineQueueStatus.count) > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        marginTop: 8,
        fontSize: '0.9em'
      }
    }, "Click to sync offline messages")),
    open: tooltipVisible,
    onOpenChange: setTooltipVisible
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StatusContainer, {
    onClick: handleReconnect,
    "data-testid": "websocket-status"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
    status: statusProps.status
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    size: 4
  }, statusProps.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StatusText, null, statusProps.text))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WebSocketStatus);

/***/ })

}]);