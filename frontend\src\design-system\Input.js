import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { styled } from './styled-components';
import theme from './theme';

const InputWrapper = styled.div.withConfig({
  shouldForwardProp: (prop) => !['fullWidth'].includes(prop),
})`
  display: flex;
  flex-direction: column;
  width: ${props => props.fullWidth ? '100%' : 'auto'};
`;

const InputLabel = styled.label`
  font-size: ${theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.neutral[700]};
  margin-bottom: ${theme.spacing[1]};
`;

const StyledInput = styled.input.withConfig({
  shouldForwardProp: (prop) => !['error', 'focused', 'fullWidth'].includes(prop),
})`
  font-family: ${theme?.typography?.fontFamily?.primary || 'Inter, sans-serif'};
  font-size: ${theme?.typography?.fontSize?.md || '16px'};
  color: ${theme?.colors?.neutral?.[900] || '#111827'};
  background-color: ${props => props.disabled ? theme?.colors?.neutral?.[100] || '#F3F4F6' : 'white'};
  border: 1px solid ${props => props.error ? theme?.colors?.error?.main || '#DC2626' : props.focused ? theme?.colors?.primary?.main || '#2563EB' : theme?.colors?.neutral?.[300] || '#D1D5DB'};
  border-radius: ${theme?.borderRadius?.md || '4px'};
  padding: ${theme?.spacing?.[2] || '8px'} ${theme?.spacing?.[3] || '12px'};
  transition: ${theme?.transitions?.default || 'all 0.2s ease-in-out'};
  width: 100%;

  &:focus {
    outline: none;
    border-color: ${theme?.colors?.primary?.main || '#2563EB'};
    box-shadow: 0 0 0 3px ${theme?.colors?.primary?.light || 'rgba(37, 99, 235, 0.2)'};
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }

  &::placeholder {
    color: ${theme?.colors?.neutral?.[400] || '#9CA3AF'};
  }
`;

const InputPrefix = styled.div`
  display: flex;
  align-items: center;
  margin-right: ${theme?.spacing?.[2] || '8px'};
`;

const InputSuffix = styled.div`
  display: flex;
  align-items: center;
  margin-left: ${theme?.spacing?.[2] || '8px'};
`;

const InputContainer = styled.div`
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
`;

const HelperText = styled.div.withConfig({
  shouldForwardProp: (prop) => !['error'].includes(prop),
})`
  font-size: ${theme?.typography?.fontSize?.xs || '12px'};
  margin-top: ${theme?.spacing?.[1] || '4px'};
  color: ${props => props.error ? theme?.colors?.error?.main || '#DC2626' : theme?.colors?.neutral?.[500] || '#6B7280'};
`;

const Input = forwardRef(({
  label,
  helperText,
  error,
  fullWidth = false,
  prefix,
  suffix,
  disabled = false,
  ...props
}, ref) => {
  const [focused, setFocused] = React.useState(false);

  const handleFocus = (e) => {
    setFocused(true);
    if (props.onFocus) props.onFocus(e);
  };

  const handleBlur = (e) => {
    setFocused(false);
    if (props.onBlur) props.onBlur(e);
  };

  return (
    <InputWrapper fullWidth={fullWidth}>
      {label && <InputLabel>{label}</InputLabel>}
      <InputContainer>
        {prefix && <InputPrefix>{prefix}</InputPrefix>}
        <StyledInput
          ref={ref}
          disabled={disabled}
          error={error}
          focused={focused}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...props}
        />
        {suffix && <InputSuffix>{suffix}</InputSuffix>}
      </InputContainer>
      {helperText && <HelperText error={error}>{helperText}</HelperText>}
    </InputWrapper>
  );
});

Input.displayName = 'Input';

Input.propTypes = {
  label: PropTypes.string,
  helperText: PropTypes.string,
  error: PropTypes.bool,
  fullWidth: PropTypes.bool,
  prefix: PropTypes.node,
  suffix: PropTypes.node,
  disabled: PropTypes.bool,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func
};

export default Input;
