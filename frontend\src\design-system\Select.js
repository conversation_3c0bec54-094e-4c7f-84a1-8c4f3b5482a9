import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { styled } from './styled-components';
import theme from './theme';

const SelectWrapper = styled.div.withConfig({
  shouldForwardProp: (prop) => !['fullWidth'].includes(prop),
})`
  display: flex;
  flex-direction: column;
  width: ${props => props.fullWidth ? '100%' : 'auto'};
`;

const SelectLabel = styled.label`
  font-size: ${theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.neutral[700]};
  margin-bottom: ${theme.spacing[1]};
`;

const StyledSelect = styled.select`
  font-family: ${theme.typography.fontFamily.primary};
  font-size: ${theme.typography.fontSize.md};
  color: ${theme.colors.neutral[900]};
  background-color: ${props => props.disabled ? theme.colors.neutral[100] : 'white'};
  border: 1px solid ${props => props.error ? theme.colors.error.main : props.focused ? theme.colors.primary.main : theme.colors.neutral[300]};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing[2]} ${theme.spacing[3]};
  transition: ${theme.transitions.default};
  width: 100%;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
  background-position: right ${theme.spacing[2]} center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5em;

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary.main};
    box-shadow: 0 0 0 3px ${theme.colors.primary.light};
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
`;

const HelperText = styled.div`
  font-size: ${theme.typography.fontSize.xs};
  margin-top: ${theme.spacing[1]};
  color: ${props => props.error ? theme.colors.error.main : theme.colors.neutral[500]};
`;

const Select = forwardRef(({
  label,
  helperText,
  error,
  fullWidth = false,
  options = [],
  disabled = false,
  ...props
}, ref) => {
  const [focused, setFocused] = React.useState(false);

  const handleFocus = (e) => {
    setFocused(true);
    if (props.onFocus) props.onFocus(e);
  };

  const handleBlur = (e) => {
    setFocused(false);
    if (props.onBlur) props.onBlur(e);
  };

  return (
    <SelectWrapper fullWidth={fullWidth}>
      {label && <SelectLabel>{label}</SelectLabel>}
      <StyledSelect
        ref={ref}
        disabled={disabled}
        error={error}
        focused={focused}
        onFocus={handleFocus}
        onBlur={handleBlur}
        {...props}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </StyledSelect>
      {helperText && <HelperText error={error}>{helperText}</HelperText>}
    </SelectWrapper>
  );
});

Select.displayName = 'Select';

Select.propTypes = {
  label: PropTypes.string,
  helperText: PropTypes.string,
  error: PropTypes.bool,
  fullWidth: PropTypes.bool,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      label: PropTypes.string.isRequired
    })
  ),
  disabled: PropTypes.bool,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func
};

export default Select;
