"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8274],{

/***/ 37812:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ UXEnhancedPropertyEditor)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79146);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8;
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UX Enhanced Property Editor
 * 
 * A comprehensive property editor with enhanced UI/UX features:
 * - Intuitive form controls with better validation
 * - Real-time preview integration
 * - Property grouping and organization
 * - Contextual help and tooltips
 * - Improved visual hierarchy
 * - Accessibility enhancements
 */






var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var Panel = antd__WEBPACK_IMPORTED_MODULE_5__/* .Collapse */ .SD.Panel;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU.TabPane;
var TextArea = antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.TextArea;

// Enhanced styled components
var PropertyEditorContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  background: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.md, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light);
var PropertyHeader = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", ";\n  background: linear-gradient(135deg, ", " 0%, ", " 100%);\n  color: ", ";\n  border-bottom: 1px solid ", ";\n  \n  .ant-typography {\n    color: ", " !important;\n    margin-bottom: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.secondary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1]);
var PropertyContent = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  flex: 1;\n  overflow-y: auto;\n  \n  /* Custom scrollbar */\n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: ", ";\n    border-radius: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.full);
var PropertyGroup = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin: ", ";\n  border-radius: ", ";\n  \n  .ant-card-head {\n    background: ", ";\n    border-bottom: 1px solid ", ";\n    padding: ", " ", ";\n    min-height: auto;\n  }\n  \n  .ant-card-body {\n    padding: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3]);
var PropertyField = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin-bottom: ", ";\n  \n  .ant-form-item {\n    margin-bottom: ", ";\n  }\n  \n  .ant-form-item-label {\n    padding-bottom: ", ";\n    \n    label {\n      font-weight: ", ";\n      color: ", ";\n      font-size: ", ";\n    }\n  }\n  \n  .property-help {\n    margin-top: ", ";\n    font-size: ", ";\n    color: ", ";\n    line-height: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.primary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.sm, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.lineHeight.normal);
var ValidationMessage = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin-top: ", ";\n  padding: ", " ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  \n  &.error {\n    background: ", ";\n    color: ", ";\n    border: 1px solid ", ";\n  }\n  \n  &.warning {\n    background: ", ";\n    color: ", ";\n    border: 1px solid ", ";\n  }\n  \n  &.success {\n    background: ", ";\n    color: ", ";\n    border: 1px solid ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.dark, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.dark, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.dark, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main);
var PreviewToggle = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: ", ";\n  right: ", ";\n  z-index: 10;\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]);
var QuickActions = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", " ", ";\n  background: ", ";\n  border-top: 1px solid ", ";\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light);

// Property validation rules
var VALIDATION_RULES = {
  required: function required(value) {
    return value !== undefined && value !== null && value !== '';
  },
  minLength: function minLength(min) {
    return function (value) {
      return !value || value.length >= min;
    };
  },
  maxLength: function maxLength(max) {
    return function (value) {
      return !value || value.length <= max;
    };
  },
  pattern: function pattern(regex) {
    return function (value) {
      return !value || regex.test(value);
    };
  },
  range: function range(min, max) {
    return function (value) {
      return !value || value >= min && value <= max;
    };
  },
  url: function url(value) {
    return !value || /^https?:\/\/.+/.test(value);
  },
  color: function color(value) {
    return !value || /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value);
  }
};

// Enhanced property schemas with usage frequency and better organization
var PROPERTY_SCHEMAS = {
  text: {
    content: {
      type: 'textarea',
      label: 'Text Content',
      placeholder: 'Enter your text here...',
      help: 'The main text content to display. Supports basic formatting.',
      validation: [VALIDATION_RULES.required],
      group: 'essential',
      priority: 1,
      usage: 'high'
    },
    type: {
      type: 'select',
      label: 'Text Style',
      options: [{
        value: 'paragraph',
        label: 'Paragraph',
        description: 'Regular body text'
      }, {
        value: 'title',
        label: 'Title',
        description: 'Large heading text'
      }, {
        value: 'secondary',
        label: 'Secondary',
        description: 'Muted text'
      }, {
        value: 'warning',
        label: 'Warning',
        description: 'Warning text'
      }, {
        value: 'danger',
        label: 'Danger',
        description: 'Error or danger text'
      }],
      defaultValue: 'paragraph',
      help: 'Choose the visual style and semantic meaning of the text',
      group: 'essential',
      priority: 2,
      usage: 'high'
    },
    strong: {
      type: 'switch',
      label: 'Bold Text',
      help: 'Make text bold and emphasize importance',
      group: 'typography',
      priority: 3,
      usage: 'medium'
    },
    italic: {
      type: 'switch',
      label: 'Italic',
      help: 'Make text italic/emphasized',
      group: 'typography'
    },
    underline: {
      type: 'switch',
      label: 'Underline',
      help: 'Add underline decoration',
      group: 'typography'
    }
  },
  button: {
    text: {
      type: 'input',
      label: 'Button Text',
      placeholder: 'Enter button text...',
      help: 'The text displayed on the button',
      validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(50)],
      group: 'content'
    },
    type: {
      type: 'select',
      label: 'Button Type',
      options: [{
        value: 'default',
        label: 'Default'
      }, {
        value: 'primary',
        label: 'Primary'
      }, {
        value: 'dashed',
        label: 'Dashed'
      }, {
        value: 'text',
        label: 'Text'
      }, {
        value: 'link',
        label: 'Link'
      }],
      defaultValue: 'default',
      help: 'Visual style of the button',
      group: 'appearance'
    },
    size: {
      type: 'select',
      label: 'Size',
      options: [{
        value: 'small',
        label: 'Small'
      }, {
        value: 'middle',
        label: 'Medium'
      }, {
        value: 'large',
        label: 'Large'
      }],
      defaultValue: 'middle',
      help: 'Size of the button',
      group: 'layout'
    },
    disabled: {
      type: 'switch',
      label: 'Disabled',
      help: 'Disable button interaction',
      group: 'behavior'
    },
    action: {
      type: 'input',
      label: 'Action',
      placeholder: 'Enter action name...',
      help: 'Action to perform when clicked',
      group: 'behavior'
    }
  },
  header: {
    title: {
      type: 'input',
      label: 'Title',
      placeholder: 'Enter header title...',
      help: 'Main title text',
      validation: [VALIDATION_RULES.required],
      group: 'content'
    },
    subtitle: {
      type: 'input',
      label: 'Subtitle',
      placeholder: 'Enter subtitle...',
      help: 'Optional subtitle text',
      group: 'content'
    },
    alignment: {
      type: 'select',
      label: 'Alignment',
      options: [{
        value: 'left',
        label: 'Left'
      }, {
        value: 'center',
        label: 'Center'
      }, {
        value: 'right',
        label: 'Right'
      }],
      defaultValue: 'center',
      help: 'Text alignment',
      group: 'layout'
    },
    background: {
      type: 'color',
      label: 'Background Color',
      help: 'Background color of the header',
      group: 'appearance'
    },
    textColor: {
      type: 'color',
      label: 'Text Color',
      help: 'Color of the text',
      group: 'appearance'
    }
  },
  input: {
    placeholder: {
      type: 'input',
      label: 'Placeholder Text',
      placeholder: 'Enter placeholder text...',
      help: 'Text shown when input is empty',
      group: 'content',
      priority: 1,
      usage: 'high'
    },
    value: {
      type: 'input',
      label: 'Default Value',
      placeholder: 'Enter default value...',
      help: 'Initial value of the input',
      group: 'content',
      priority: 2,
      usage: 'medium'
    },
    type: {
      type: 'select',
      label: 'Input Type',
      options: [{
        value: 'text',
        label: 'Text'
      }, {
        value: 'email',
        label: 'Email'
      }, {
        value: 'password',
        label: 'Password'
      }, {
        value: 'number',
        label: 'Number'
      }, {
        value: 'tel',
        label: 'Telephone'
      }, {
        value: 'url',
        label: 'URL'
      }, {
        value: 'search',
        label: 'Search'
      }],
      defaultValue: 'text',
      help: 'Type of input field',
      group: 'behavior',
      priority: 3,
      usage: 'high'
    },
    required: {
      type: 'switch',
      label: 'Required',
      help: 'Whether the input is required',
      group: 'behavior',
      priority: 4,
      usage: 'medium'
    },
    disabled: {
      type: 'switch',
      label: 'Disabled',
      help: 'Whether the input is disabled',
      group: 'behavior',
      priority: 5,
      usage: 'low'
    },
    maxLength: {
      type: 'number',
      label: 'Max Length',
      placeholder: 'Maximum characters...',
      help: 'Maximum number of characters allowed',
      validation: [VALIDATION_RULES.positive],
      group: 'behavior',
      priority: 6,
      usage: 'low'
    }
  },
  container: {
    padding: {
      type: 'input',
      label: 'Padding',
      placeholder: 'e.g., 16px, 1rem, 10px 20px',
      help: 'Internal spacing of the container',
      group: 'layout',
      priority: 1,
      usage: 'high'
    },
    margin: {
      type: 'input',
      label: 'Margin',
      placeholder: 'e.g., 0px, 1rem, 10px 20px',
      help: 'External spacing around the container',
      group: 'layout',
      priority: 2,
      usage: 'medium'
    },
    backgroundColor: {
      type: 'color',
      label: 'Background Color',
      help: 'Background color of the container',
      group: 'appearance',
      priority: 3,
      usage: 'high'
    },
    borderRadius: {
      type: 'number',
      label: 'Border Radius (px)',
      placeholder: 'Border radius in pixels...',
      help: 'Roundness of the container corners',
      validation: [VALIDATION_RULES.nonNegative],
      group: 'appearance',
      priority: 4,
      usage: 'medium'
    },
    border: {
      type: 'input',
      label: 'Border',
      placeholder: 'e.g., 1px solid #ccc',
      help: 'Border style of the container',
      group: 'appearance',
      priority: 5,
      usage: 'medium'
    },
    width: {
      type: 'input',
      label: 'Width',
      placeholder: 'e.g., 100%, 300px, auto',
      help: 'Width of the container',
      group: 'layout',
      priority: 6,
      usage: 'medium'
    },
    height: {
      type: 'input',
      label: 'Height',
      placeholder: 'e.g., auto, 200px, 100vh',
      help: 'Height of the container',
      group: 'layout',
      priority: 7,
      usage: 'medium'
    },
    display: {
      type: 'select',
      label: 'Display',
      options: [{
        value: 'block',
        label: 'Block'
      }, {
        value: 'inline',
        label: 'Inline'
      }, {
        value: 'inline-block',
        label: 'Inline Block'
      }, {
        value: 'flex',
        label: 'Flex'
      }, {
        value: 'grid',
        label: 'Grid'
      }, {
        value: 'none',
        label: 'Hidden'
      }],
      defaultValue: 'block',
      help: 'Display type of the container',
      group: 'layout',
      priority: 8,
      usage: 'low'
    }
  },
  image: {
    src: {
      type: 'input',
      label: 'Image URL',
      placeholder: 'Enter image URL...',
      help: 'URL of the image to display',
      validation: [VALIDATION_RULES.required, VALIDATION_RULES.url],
      group: 'content',
      priority: 1,
      usage: 'high'
    },
    alt: {
      type: 'input',
      label: 'Alt Text',
      placeholder: 'Describe the image...',
      help: 'Alternative text for screen readers',
      validation: [VALIDATION_RULES.required],
      group: 'content',
      priority: 2,
      usage: 'high'
    },
    width: {
      type: 'input',
      label: 'Width',
      placeholder: 'e.g., 100%, 300px, auto',
      help: 'Width of the image',
      group: 'layout',
      priority: 3,
      usage: 'medium'
    },
    height: {
      type: 'input',
      label: 'Height',
      placeholder: 'e.g., auto, 200px',
      help: 'Height of the image',
      group: 'layout',
      priority: 4,
      usage: 'medium'
    },
    objectFit: {
      type: 'select',
      label: 'Object Fit',
      options: [{
        value: 'fill',
        label: 'Fill'
      }, {
        value: 'contain',
        label: 'Contain'
      }, {
        value: 'cover',
        label: 'Cover'
      }, {
        value: 'none',
        label: 'None'
      }, {
        value: 'scale-down',
        label: 'Scale Down'
      }],
      defaultValue: 'cover',
      help: 'How the image should be resized',
      group: 'appearance',
      priority: 5,
      usage: 'low'
    }
  }
};

// Enhanced property groups configuration with priority and usage frequency
var PROPERTY_GROUPS = {
  essential: {
    title: 'Essential',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, null),
    description: 'Most commonly used properties',
    priority: 1,
    defaultExpanded: true,
    color: '#1890ff'
  },
  content: {
    title: 'Content',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FileTextOutlined */ .y9H, null),
    description: 'Text, images, and other content properties',
    priority: 2,
    defaultExpanded: true,
    color: '#52c41a'
  },
  appearance: {
    title: 'Appearance',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BgColorsOutlined */ .Ebl, null),
    description: 'Visual styling and colors',
    priority: 3,
    defaultExpanded: false,
    color: '#722ed1'
  },
  layout: {
    title: 'Layout',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ExpandOutlined */ .V9b, null),
    description: 'Size, spacing, and positioning',
    priority: 4,
    defaultExpanded: false,
    color: '#fa8c16'
  },
  typography: {
    title: 'Typography',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FontSizeOutlined */ .ld1, null),
    description: 'Font styles and text formatting',
    priority: 5,
    defaultExpanded: false,
    color: '#eb2f96'
  },
  behavior: {
    title: 'Behavior',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, null),
    description: 'Interactions and functionality',
    priority: 6,
    defaultExpanded: false,
    color: '#13c2c2'
  },
  advanced: {
    title: 'Advanced',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, null),
    description: 'Custom CSS and advanced options',
    priority: 7,
    defaultExpanded: false,
    color: '#8c8c8c'
  }
};
function UXEnhancedPropertyEditor(_ref) {
  var component = _ref.component,
    onUpdateComponent = _ref.onUpdateComponent,
    onPreviewChange = _ref.onPreviewChange,
    _ref$dataSources = _ref.dataSources,
    dataSources = _ref$dataSources === void 0 ? [] : _ref$dataSources,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    _ref$enableRealTimePr = _ref.enableRealTimePreview,
    enableRealTimePreview = _ref$enableRealTimePr === void 0 ? true : _ref$enableRealTimePr;
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('properties'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];

  // Initialize expanded groups based on default configuration
  var defaultExpandedGroups = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    return Object.entries(PROPERTY_GROUPS).filter(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref2, 2),
        _ = _ref3[0],
        config = _ref3[1];
      return config.defaultExpanded;
    }).map(function (_ref4) {
      var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref4, 2),
        key = _ref5[0],
        _ = _ref5[1];
      return key;
    });
  }, []);
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(defaultExpandedGroups),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    expandedGroups = _useState4[0],
    setExpandedGroups = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({}),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    validationErrors = _useState6[0],
    setValidationErrors = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    hasUnsavedChanges = _useState8[0],
    setHasUnsavedChanges = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(enableRealTimePreview),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    previewEnabled = _useState0[0],
    setPreviewEnabled = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(''),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    searchTerm = _useState10[0],
    setSearchTerm = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    showOnlyPopular = _useState12[0],
    setShowOnlyPopular = _useState12[1];

  // Get property schema for current component type
  var propertySchema = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    return PROPERTY_SCHEMAS[component === null || component === void 0 ? void 0 : component.type] || {};
  }, [component === null || component === void 0 ? void 0 : component.type]);

  // Enhanced property grouping with search and filtering
  var groupedProperties = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    var groups = {};
    Object.entries(propertySchema).forEach(function (_ref6) {
      var _ref7 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref6, 2),
        key = _ref7[0],
        schema = _ref7[1];
      // Filter by search term
      if (searchTerm) {
        var _schema$label, _schema$help;
        var searchLower = searchTerm.toLowerCase();
        var matchesSearch = ((_schema$label = schema.label) === null || _schema$label === void 0 ? void 0 : _schema$label.toLowerCase().includes(searchLower)) || ((_schema$help = schema.help) === null || _schema$help === void 0 ? void 0 : _schema$help.toLowerCase().includes(searchLower)) || key.toLowerCase().includes(searchLower);
        if (!matchesSearch) return;
      }

      // Filter by popularity if enabled
      if (showOnlyPopular && schema.usage !== 'high') {
        return;
      }
      var groupKey = schema.group || 'advanced';
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(_objectSpread({
        key: key
      }, schema));
    });

    // Sort properties within each group by priority
    Object.keys(groups).forEach(function (groupKey) {
      groups[groupKey].sort(function (a, b) {
        return (a.priority || 999) - (b.priority || 999);
      });
    });

    // Sort groups by priority
    var sortedGroups = {};
    Object.entries(PROPERTY_GROUPS).sort(function (_ref8, _ref9) {
      var _ref0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref8, 2),
        a = _ref0[1];
      var _ref1 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref9, 2),
        b = _ref1[1];
      return (a.priority || 999) - (b.priority || 999);
    }).forEach(function (_ref10) {
      var _ref11 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref10, 1),
        groupKey = _ref11[0];
      if (groups[groupKey]) {
        sortedGroups[groupKey] = groups[groupKey];
      }
    });
    return sortedGroups;
  }, [propertySchema, searchTerm, showOnlyPopular]);

  // Validate a single property
  var validateProperty = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (key, value, schema) {
    if (!schema.validation) return null;
    var _iterator = _createForOfIteratorHelper(schema.validation),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var rule = _step.value;
        if (!rule(value)) {
          return "Invalid ".concat(schema.label.toLowerCase());
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    return null;
  }, []);

  // Validate all properties
  var validateAllProperties = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    var errors = {};
    var values = form.getFieldsValue();
    Object.entries(propertySchema).forEach(function (_ref12) {
      var _ref13 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref12, 2),
        key = _ref13[0],
        schema = _ref13[1];
      var error = validateProperty(key, values[key], schema);
      if (error) {
        errors[key] = error;
      }
    });
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [form, propertySchema, validateProperty]);

  // Handle form value changes
  var handleValuesChange = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (changedValues, allValues) {
    setHasUnsavedChanges(true);

    // Validate changed values
    var errors = _objectSpread({}, validationErrors);
    Object.entries(changedValues).forEach(function (_ref14) {
      var _ref15 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref14, 2),
        key = _ref15[0],
        value = _ref15[1];
      var schema = propertySchema[key];
      if (schema) {
        var error = validateProperty(key, value, schema);
        if (error) {
          errors[key] = error;
        } else {
          delete errors[key];
        }
      }
    });
    setValidationErrors(errors);

    // Real-time preview update
    if (previewEnabled && onUpdateComponent) {
      onUpdateComponent(component.id, changedValues);
    }

    // Notify preview change
    if (onPreviewChange) {
      onPreviewChange(allValues);
    }
  }, [component === null || component === void 0 ? void 0 : component.id, onUpdateComponent, onPreviewChange, previewEnabled, validationErrors, propertySchema, validateProperty]);

  // Save changes
  var handleSave = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (validateAllProperties()) {
      var values = form.getFieldsValue();
      onUpdateComponent(component.id, values);
      setHasUnsavedChanges(false);
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Properties saved successfully');
    } else {
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Please fix validation errors before saving');
    }
  }, [component === null || component === void 0 ? void 0 : component.id, form, onUpdateComponent, validateAllProperties]);

  // Reset form
  var handleReset = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (!component) {
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.warning('No component selected to reset');
      return;
    }
    try {
      // Get the original component properties
      var originalProps = component.props || {};

      // Reset form to original values
      form.setFieldsValue(originalProps);

      // Clear validation errors
      setValidationErrors({});

      // Mark as no unsaved changes
      setHasUnsavedChanges(false);

      // Notify parent component of reset if needed
      if (onUpdateComponent && previewEnabled) {
        onUpdateComponent(component.id, originalProps);
      }
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Properties reset to original values');
    } catch (error) {
      console.error('Error resetting properties:', error);
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Failed to reset properties');
    }
  }, [component, form, onUpdateComponent, previewEnabled]);

  // Set initial form values
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (component !== null && component !== void 0 && component.props) {
      form.setFieldsValue(component.props);
      setHasUnsavedChanges(false);
      setValidationErrors({});
    }
  }, [component, form]);

  // Render property field based on type
  var renderPropertyField = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (property) {
    var key = property.key,
      type = property.type,
      label = property.label,
      placeholder = property.placeholder,
      help = property.help,
      options = property.options,
      defaultValue = property.defaultValue,
      validation = property.validation,
      usage = property.usage,
      priority = property.priority;
    var hasError = validationErrors[key];
    var isRequired = validation === null || validation === void 0 ? void 0 : validation.some(function (rule) {
      return rule === VALIDATION_RULES.required;
    });
    var fieldProps = {
      name: key,
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, usage === 'high' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, {
        style: {
          color: '#faad14',
          fontSize: '10px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
        style: {
          fontWeight: usage === 'high' ? _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.semibold : _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.medium
        }
      }, label), isRequired && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
        type: "danger"
      }, "*"), help && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
        title: help,
        placement: "topLeft"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .QuestionCircleOutlined */ .faO, {
        style: {
          color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.tertiary,
          cursor: 'help'
        }
      }))), priority <= 3 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
        title: "Essential property"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          width: 6,
          height: 6,
          borderRadius: '50%',
          backgroundColor: '#1890ff',
          opacity: 0.6
        }
      }))),
      validateStatus: hasError ? 'error' : '',
      help: hasError ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ValidationMessage, {
        className: "error"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ExclamationCircleOutlined */ .G2i, null), " ", hasError) : help ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        className: "property-help"
      }, help) : null
    };
    switch (type) {
      case 'input':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
          placeholder: placeholder,
          style: {
            borderRadius: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md
          }
        }));
      case 'textarea':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TextArea, {
          rows: 4,
          placeholder: placeholder,
          style: {
            borderRadius: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md
          }
        }));
      case 'select':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
          placeholder: placeholder,
          style: {
            borderRadius: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md
          }
        }, options === null || options === void 0 ? void 0 : options.map(function (option) {
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
            key: option.value,
            value: option.value
          }, option.label);
        })));
      case 'switch':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, fieldProps, {
          valuePropName: "checked"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, null));
      case 'number':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .InputNumber */ .YI, {
          placeholder: placeholder,
          style: {
            width: '100%',
            borderRadius: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md
          }
        }));
      case 'slider':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Slider */ .Ap, null));
      case 'color':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .ColorPicker */ .sk, null));
      default:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
          placeholder: placeholder
        }));
    }
  }, [validationErrors]);
  if (!component) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyEditorContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[8],
        textAlign: 'center',
        color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, {
      style: {
        fontSize: 48,
        marginBottom: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4]
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
      level: 4,
      style: {
        color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary
      }
    }, "No Component Selected"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, null, "Select a component from the canvas to edit its properties")));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyEditorContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 5,
    style: {
      margin: 0,
      color: 'white'
    }
  }, "Properties"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      color: 'rgba(255, 255, 255, 0.8)'
    }
  }, component.type, " component")), showPreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PreviewToggle, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
    title: previewEnabled ? 'Disable real-time preview' : 'Enable real-time preview'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: previewEnabled,
    onChange: setPreviewEnabled,
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EyeOutlined */ .Om2, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EyeOutlined */ .Om2, null),
    size: "small"
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      gap: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2],
      alignItems: 'center',
      marginTop: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3],
      padding: "".concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], " 0"),
      borderTop: '1px solid rgba(255, 255, 255, 0.1)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    placeholder: "Search properties...",
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: 'rgba(255, 255, 255, 0.6)'
      }
    }),
    allowClear: true,
    size: "small",
    style: {
      flex: 1,
      background: 'rgba(255, 255, 255, 0.1)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      color: 'white'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
    title: "Show only frequently used properties"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: showOnlyPopular,
    onChange: setShowOnlyPopular,
    size: "small",
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, null)
  }))), hasUnsavedChanges && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: "You have unsaved changes",
    type: "warning",
    showIcon: true,
    style: {
      marginTop: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2],
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      color: 'white'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    style: {
      padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Properties",
    key: "properties"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV, {
    form: form,
    layout: "vertical",
    onValuesChange: handleValuesChange,
    initialValues: component.props
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Collapse */ .SD, {
    activeKey: expandedGroups,
    onChange: setExpandedGroups,
    ghost: true
  }, Object.entries(groupedProperties).map(function (_ref16) {
    var _ref17 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref16, 2),
      groupKey = _ref17[0],
      properties = _ref17[1];
    var groupConfig = PROPERTY_GROUPS[groupKey];
    if (!groupConfig || properties.length === 0) return null;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Panel, {
      header: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          width: 12,
          height: 12,
          borderRadius: '50%',
          backgroundColor: groupConfig.color,
          marginRight: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1]
        }
      }), groupConfig.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
        style: {
          fontWeight: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.semibold
        }
      }, groupConfig.title), groupKey === 'essential' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, {
        style: {
          color: '#faad14',
          fontSize: '12px'
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
        count: properties.length,
        size: "small",
        style: {
          backgroundColor: groupConfig.color
        }
      }), properties.some(function (p) {
        return p.usage === 'high';
      }) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
        title: "Contains frequently used properties"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, {
        style: {
          color: '#faad14',
          fontSize: '10px'
        }
      })))),
      key: groupKey
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs
      }
    }, groupConfig.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Divider */ .cG, {
      style: {
        margin: "".concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], " 0")
      }
    }), properties.map(function (property) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyField, {
        key: property.key
      }, renderPropertyField(property));
    })));
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Style",
    key: "style"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: "Style Editor",
    description: "Advanced styling options will be available in the next update.",
    type: "info",
    showIcon: true
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Advanced",
    key: "advanced"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: "Advanced Options",
    description: "Custom CSS and JavaScript options will be available in the next update.",
    type: "info",
    showIcon: true
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(QuickActions, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UndoOutlined */ .Xrf, null),
    onClick: handleReset,
    disabled: !hasUnsavedChanges,
    size: "small"
  }, "Reset"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ReloadOutlined */ .KF4, null),
    onClick: function onClick() {
      return form.resetFields();
    },
    size: "small"
  }, "Refresh")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      fontSize: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs,
      color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary
    }
  }, Object.keys(validationErrors).length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
    style: {
      color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.main
    }
  }, Object.keys(validationErrors).length, " error(s)"), Object.keys(validationErrors).length === 0 && hasUnsavedChanges && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
    style: {
      color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckCircleOutlined */ .hWy, null), " Ready to save")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SaveOutlined */ .ylI, null),
    onClick: handleSave,
    disabled: Object.keys(validationErrors).length > 0 || !hasUnsavedChanges,
    size: "small"
  }, "Save"))));
}

/***/ }),

/***/ 79647:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ UXEnhancedPreviewArea)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _hooks_useRealTimePreview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79459);
/* harmony import */ var _hooks_usePreviewPerformance__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(48860);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12, _templateObject13, _templateObject14, _templateObject15, _templateObject16, _templateObject17;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UX Enhanced Preview Area
 * 
 * A comprehensive preview area with enhanced UI/UX features:
 * - Responsive breakpoint indicators and device frames
 * - Advanced zoom controls and canvas interactions
 * - Enhanced drag-and-drop visual feedback
 * - Real-time collaboration indicators
 * - Performance monitoring and optimization
 * - Accessibility improvements
 */








var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;

// Device configurations with enhanced metadata
var DEVICE_CONFIGS = {
  mobile: {
    name: 'Mobile',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MobileOutlined */ .jHj, null),
    width: 375,
    height: 812,
    scale: 0.8,
    frame: true,
    breakpoint: 'sm',
    description: 'iPhone 12 Pro (375×812)',
    userAgent: 'mobile'
  },
  tablet: {
    name: 'Tablet',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .TabletOutlined */ .pLH, null),
    width: 768,
    height: 1024,
    scale: 0.7,
    frame: true,
    breakpoint: 'md',
    description: 'iPad (768×1024)',
    userAgent: 'tablet'
  },
  desktop: {
    name: 'Desktop',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DesktopOutlined */ .zlw, null),
    width: 1440,
    height: 900,
    scale: 1,
    frame: false,
    breakpoint: 'lg',
    description: 'Desktop (1440×900)',
    userAgent: 'desktop'
  },
  wide: {
    name: 'Wide Screen',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ExpandOutlined */ .V9b, null),
    width: 1920,
    height: 1080,
    scale: 0.8,
    frame: false,
    breakpoint: 'xl',
    description: 'Wide Screen (1920×1080)',
    userAgent: 'desktop'
  }
};

// Enhanced styled components
var PreviewContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  height: 100%;\n  background: ", ";\n  border-radius: ", ";\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid ", ";\n  \n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 2px solid ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.dark);
var PreviewToolbar = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ", " ", ";\n  background: ", ";\n  border-bottom: 1px solid ", ";\n  box-shadow: ", ";\n  z-index: ", ";\n  flex-wrap: wrap;\n  gap: ", ";\n  min-height: 60px;\n\n  ", " {\n    padding: ", " ", ";\n    gap: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.sticky, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var ToolbarSection = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  \n  ", " {\n    gap: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var DeviceSelector = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: ", ";\n  background: ", ";\n  border-radius: ", ";\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light);
var DeviceButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  border: none;\n  background: ", ";\n  color: ", ";\n  box-shadow: none;\n  border-radius: ", ";\n  transition: ", ";\n  min-width: auto;\n  padding: ", " ", ";\n\n  &:hover {\n    background: ", ";\n    color: ", ";\n  }\n  \n  &:focus {\n    ", ";\n  }\n  \n  ", " {\n    padding: ", ";\n    \n    .device-label {\n      display: none;\n    }\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], function (props) {
  return props.active ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main : 'transparent';
}, function (props) {
  return props.active ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.secondary;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], function (props) {
  return props.active ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.dark : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.interactive.hover;
}, function (props) {
  return props.active ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.a11yUtils.focusRing(), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var ZoomControls = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: ", " ", ";\n  background: ", ";\n  border-radius: ", ";\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light);
var ZoomSlider = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Slider */ .Ap)(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: 100px;\n  margin: 0 ", ";\n  \n  .ant-slider-rail {\n    background: ", ";\n  }\n  \n  .ant-slider-track {\n    background: ", ";\n  }\n  \n  .ant-slider-handle {\n    border-color: ", ";\n    \n    &:focus {\n      box-shadow: ", ";\n    }\n  }\n  \n  ", " {\n    width: 60px;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.focus, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd);
var StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: ", " ", ";\n  background: ", ";\n  color: ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], function (props) {
  return props.connected ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.light : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.light;
}, function (props) {
  return props.connected ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.dark : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.dark;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, function (props) {
  return props.connected ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.main : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.main;
});
var CanvasContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  flex: 1;\n  position: relative;\n  overflow: auto;\n  background: ", ";\n  background-size: ", "px ", "px;\n  background-position: ", "px ", "px;\n  \n  /* Custom scrollbar */\n  &::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: ", ";\n    border-radius: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb:hover {\n    background: ", ";\n  }\n"])), function (props) {
  return props.showGrid ? "radial-gradient(circle, ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light, " 1px, transparent 1px)") : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.secondary;
}, function (props) {
  return props.gridSize || 20;
}, function (props) {
  return props.gridSize || 20;
}, function (props) {
  var _props$gridOffset;
  return ((_props$gridOffset = props.gridOffset) === null || _props$gridOffset === void 0 ? void 0 : _props$gridOffset.x) || 0;
}, function (props) {
  var _props$gridOffset2;
  return ((_props$gridOffset2 = props.gridOffset) === null || _props$gridOffset2 === void 0 ? void 0 : _props$gridOffset2.y) || 0;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.full, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.dark);
var DeviceFrame = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  margin: ", " auto;\n  background: ", ";\n  border-radius: ", ";\n  padding: ", ";\n  box-shadow: ", ";\n  transition: ", ";\n  \n  /* Mobile device frame details */\n  ", "\n\n  /* Tablet device frame details */\n  ", "\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], function (props) {
  switch (props.deviceType) {
    case 'mobile':
      return _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[800];
    case 'tablet':
      return _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[700];
    default:
      return 'transparent';
  }
}, function (props) {
  switch (props.deviceType) {
    case 'mobile':
      return '30px';
    case 'tablet':
      return '20px';
    default:
      return '0';
  }
}, function (props) {
  switch (props.deviceType) {
    case 'mobile':
      return "".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[6], " ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[3]);
    case 'tablet':
      return "".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], " ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2]);
    default:
      return '0';
  }
}, function (props) {
  return props.frame ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.xl : 'none';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], function (props) {
  return props.deviceType === 'mobile' && "\n    &::before {\n      content: '';\n      position: absolute;\n      top: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], ";\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[600], ";\n      border-radius: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.full, ";\n    }\n\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], ";\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[600], ";\n      border-radius: 50%;\n    }\n  ");
}, function (props) {
  return props.deviceType === 'tablet' && "\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], ";\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[600], ";\n      border-radius: 50%;\n    }\n  ");
});
var ResponsiveCanvas = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: ", "px;\n  height: ", "px;\n  max-width: 100%;\n  max-height: 100%;\n  background: ", ";\n  border-radius: ", ";\n  overflow: auto;\n  position: relative;\n  transform: scale(", ");\n  transform-origin: top center;\n  transition: ", ";\n  box-shadow: ", ";\n  \n  /* Responsive scaling */\n  ", " {\n    transform: scale(", ");\n  }\n  \n  ", " {\n    transform: scale(", ");\n  }\n  \n  ", " {\n    transform: scale(", ");\n  }\n  \n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n  }\n"])), function (props) {
  return props.deviceWidth;
}, function (props) {
  return props.deviceHeight;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, function (props) {
  switch (props.deviceType) {
    case 'mobile':
      return _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg;
    case 'tablet':
      return _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md;
    default:
      return '0';
  }
}, function (props) {
  return props.scale;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], function (props) {
  return props.deviceType !== 'desktop' ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.inner : 'none';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxXl, function (props) {
  return Math.min(props.scale, 0.9);
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxLg, function (props) {
  return Math.min(props.scale, 0.8);
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, function (props) {
  return Math.min(props.scale, 0.7);
});
var BreakpointIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject10 || (_templateObject10 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: ", ";\n  left: ", ";\n  background: ", ";\n  color: ", ";\n  padding: ", " ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  z-index: ", ";\n  box-shadow: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.popover, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.sm);
var PerformanceIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject11 || (_templateObject11 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: ", ";\n  right: ", ";\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  z-index: ", ";\n  min-width: 120px;\n  \n  .metric {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: ", ";\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.popover, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var DropZone = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['visible', 'isActive'].includes(prop);
  }
})(_templateObject12 || (_templateObject12 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed ", ";\n  border-radius: ", ";\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.9);\n  opacity: ", ";\n  pointer-events: ", ";\n  transition: ", ";\n  z-index: ", ";\n\n  ", "\n  \n  .drop-message {\n    color: ", ";\n    font-weight: ", ";\n    font-size: ", ";\n    margin-top: ", ";\n  }\n  \n  .drop-hint {\n    color: ", ";\n    font-size: ", ";\n    margin-top: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, function (props) {
  return props.visible ? 1 : 0;
}, function (props) {
  return props.visible ? 'auto' : 'none';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.overlay, function (props) {
  return props.isActive && "\n    border-color: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.main, ";\n    background: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.light, ";\n\n    .drop-message {\n      color: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.dark, ";\n    }\n  ");
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var LoadingOverlay = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject13 || (_templateObject13 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: ", ";\n  \n  .loading-text {\n    margin-top: ", ";\n    color: ", ";\n    font-weight: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.modal, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium);

// Component renderer function - moved to inside the main component

// Enhanced component wrapper with clear visual indicators
var ComponentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['previewMode', 'isSelected', 'isHovered', 'isDragOver'].includes(prop);
  }
})(_templateObject14 || (_templateObject14 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n    position: relative;\n    margin: 4px 0;\n    border: ", ";\n    border-radius: ", ";\n    background: ", ";\n    transition: all 0.2s ease;\n    cursor: ", ";\n    min-height: ", ";\n    padding: ", ";\n\n    &:hover {\n      ", "\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: -1px;\n      left: -1px;\n      right: -1px;\n      bottom: -1px;\n      border: 2px solid transparent;\n      border-radius: ", ";\n      pointer-events: none;\n      transition: border-color 0.2s ease;\n      ", "\n    }\n  "])), function (props) {
  if (props.previewMode) return '1px solid transparent';
  if (props.isSelected) return "2px solid ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main);
  return '1px dashed rgba(0, 0, 0, 0.1)';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, function (props) {
  if (props.previewMode) return 'transparent';
  if (props.isSelected) return "".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, "08");
  return 'rgba(255, 255, 255, 0.8)';
}, function (props) {
  return props.previewMode ? 'default' : 'pointer';
}, function (props) {
  return props.previewMode ? 'auto' : '32px';
}, function (props) {
  return props.previewMode ? '0' : '8px';
}, function (props) {
  return !props.previewMode && "\n        border-color: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, ";\n        background: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, "12;\n        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n        transform: translateY(-1px);\n      ");
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, function (props) {
  return props.isSelected && "border-color: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, ";");
});

// Component type badge for identification
var ComponentBadge = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['isSelected', 'previewMode'].includes(prop);
  }
})(_templateObject15 || (_templateObject15 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n    position: absolute;\n    top: -8px;\n    left: 8px;\n    background: ", ";\n    color: white;\n    padding: 2px 8px;\n    border-radius: 12px;\n    font-size: 10px;\n    font-weight: 500;\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n    z-index: 10;\n    opacity: ", ";\n    transform: ", ";\n    transition: all 0.2s ease;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  "])), function (props) {
  return props.isSelected ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main : '#666';
}, function (props) {
  return props.previewMode ? 0 : 1;
}, function (props) {
  return props.previewMode ? 'scale(0.8)' : 'scale(1)';
});

// Selection handles for resize/move operations
var SelectionHandles = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject16 || (_templateObject16 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    pointer-events: none;\n    opacity: ", ";\n    transition: opacity 0.2s ease;\n\n    &::before, &::after {\n      content: '';\n      position: absolute;\n      width: 8px;\n      height: 8px;\n      background: ", ";\n      border: 2px solid white;\n      border-radius: 50%;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n    }\n\n    &::before {\n      top: -4px;\n      left: -4px;\n    }\n\n    &::after {\n      bottom: -4px;\n      right: -4px;\n    }\n  "])), function (props) {
  return props.visible ? 1 : 0;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main);

// Edit indicator for text components
var EditIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject17 || (_templateObject17 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n    position: absolute;\n    top: 4px;\n    right: 4px;\n    width: 16px;\n    height: 16px;\n    background: ", ";\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    opacity: ", ";\n    transition: all 0.2s ease;\n    cursor: pointer;\n    z-index: 10;\n\n    &:hover {\n      transform: scale(1.1);\n      background: ", ";\n    }\n\n    svg {\n      width: 10px;\n      height: 10px;\n      color: white;\n    }\n  "])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, function (props) {
  return props.visible ? 1 : 0;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.dark);

// Component rendering logic moved inside main component

function UXEnhancedPreviewArea(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onSelectComponent = _ref.onSelectComponent,
    onDeleteComponent = _ref.onDeleteComponent,
    onUpdateComponent = _ref.onUpdateComponent,
    onMoveComponent = _ref.onMoveComponent,
    _ref$previewMode = _ref.previewMode,
    previewMode = _ref$previewMode === void 0 ? false : _ref$previewMode,
    selectedComponentId = _ref.selectedComponentId,
    onDrop = _ref.onDrop,
    onDragOver = _ref.onDragOver,
    onDragLeave = _ref.onDragLeave,
    _ref$realTimeUpdates = _ref.realTimeUpdates,
    realTimeUpdates = _ref$realTimeUpdates === void 0 ? true : _ref$realTimeUpdates,
    _ref$websocketConnect = _ref.websocketConnected,
    websocketConnected = _ref$websocketConnect === void 0 ? false : _ref$websocketConnect,
    _ref$showPerformanceM = _ref.showPerformanceMetrics,
    showPerformanceMetrics = _ref$showPerformanceM === void 0 ? false : _ref$showPerformanceM,
    _ref$enableDeviceFram = _ref.enableDeviceFrames,
    enableDeviceFrames = _ref$enableDeviceFram === void 0 ? true : _ref$enableDeviceFram,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading;
  // State management
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('desktop'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    deviceType = _useState2[0],
    setDeviceType = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(1),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    zoom = _useState4[0],
    setZoom = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    showGrid = _useState6[0],
    setShowGrid = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(20),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    gridSize = _useState8[0],
    setGridSize = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    showBreakpoints = _useState0[0],
    setShowBreakpoints = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    isDragOver = _useState10[0],
    setIsDragOver = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState11, 2),
    isFullscreen = _useState12[0],
    setIsFullscreen = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState13, 2),
    lastUpdateTime = _useState14[0],
    setLastUpdateTime = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState15, 2),
    hoveredComponent = _useState16[0],
    setHoveredComponent = _useState16[1];

  // Refs
  var canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);
  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);

  // Enhanced component rendering function with visual indicators
  var renderComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (component, currentDeviceType, currentPreviewMode) {
    if (!component) return null;
    var isSelected = selectedComponentId === component.id;
    var isHovered = hoveredComponent === component.id;
    var showIndicators = !currentPreviewMode && (isSelected || isHovered);

    // Responsive styles based on device type
    var getResponsiveStyles = function getResponsiveStyles() {
      var baseStyles = {
        fontSize: currentDeviceType === 'mobile' ? '14px' : currentDeviceType === 'tablet' ? '16px' : '16px',
        padding: currentDeviceType === 'mobile' ? '8px' : '12px',
        margin: currentDeviceType === 'mobile' ? '4px 0' : '8px 0'
      };
      return _objectSpread(_objectSpread({}, baseStyles), component.style);
    };
    var responsiveStyles = getResponsiveStyles();

    // Component content renderer
    var renderComponentContent = function renderComponentContent() {
      var _component$props, _component$props2, _component$props3, _component$props4, _component$props5, _component$props6, _component$props7, _component$props8, _component$props9, _component$props0, _component$props1;
      switch (component.type) {
        case 'text':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
            style: _objectSpread(_objectSpread({}, responsiveStyles), {}, {
              minHeight: currentPreviewMode ? 'auto' : '20px',
              display: 'block',
              padding: currentPreviewMode ? '0' : '4px',
              background: currentPreviewMode ? 'transparent' : 'rgba(255, 255, 255, 0.9)',
              borderRadius: '2px'
            })
          }, ((_component$props = component.props) === null || _component$props === void 0 ? void 0 : _component$props.content) || 'Click to edit text...');
        case 'button':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
            type: ((_component$props2 = component.props) === null || _component$props2 === void 0 ? void 0 : _component$props2.type) || 'default',
            size: currentDeviceType === 'mobile' ? 'small' : 'middle',
            style: {
              fontSize: responsiveStyles.fontSize,
              minWidth: currentPreviewMode ? 'auto' : '80px'
            }
          }, ((_component$props3 = component.props) === null || _component$props3 === void 0 ? void 0 : _component$props3.text) || 'Button');
        case 'header':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
            level: ((_component$props4 = component.props) === null || _component$props4 === void 0 ? void 0 : _component$props4.level) || (currentDeviceType === 'mobile' ? 4 : 2),
            style: responsiveStyles
          }, ((_component$props5 = component.props) === null || _component$props5 === void 0 ? void 0 : _component$props5.text) || 'Header');
        case 'card':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
            title: ((_component$props6 = component.props) === null || _component$props6 === void 0 ? void 0 : _component$props6.title) || 'Card Title',
            size: currentDeviceType === 'mobile' ? 'small' : 'default',
            style: {
              fontSize: responsiveStyles.fontSize
            }
          }, ((_component$props7 = component.props) === null || _component$props7 === void 0 ? void 0 : _component$props7.content) || 'Card content');
        case 'image':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("img", {
            src: ((_component$props8 = component.props) === null || _component$props8 === void 0 ? void 0 : _component$props8.src) || 'https://via.placeholder.com/150',
            alt: ((_component$props9 = component.props) === null || _component$props9 === void 0 ? void 0 : _component$props9.alt) || 'Image',
            style: {
              maxWidth: '100%',
              height: 'auto',
              borderRadius: currentDeviceType === 'mobile' ? '4px' : '6px'
            }
          });
        case 'divider':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, {
            style: responsiveStyles
          }, (_component$props0 = component.props) === null || _component$props0 === void 0 ? void 0 : _component$props0.text);
        case 'input':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
            placeholder: ((_component$props1 = component.props) === null || _component$props1 === void 0 ? void 0 : _component$props1.placeholder) || 'Enter text',
            disabled: currentPreviewMode ? false : true,
            size: currentDeviceType === 'mobile' ? 'small' : 'middle',
            style: responsiveStyles
          });
        case 'form':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
            layout: "vertical",
            size: currentDeviceType === 'mobile' ? 'small' : 'middle'
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
            label: "Sample Field"
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
            placeholder: "Sample input",
            disabled: !currentPreviewMode,
            style: responsiveStyles
          })));
        case 'table':
          var columns = [{
            title: 'Name',
            dataIndex: 'name',
            key: 'name'
          }, {
            title: 'Age',
            dataIndex: 'age',
            key: 'age'
          }];
          var data = [{
            key: '1',
            name: 'John',
            age: 32
          }, {
            key: '2',
            name: 'Jane',
            age: 28
          }];
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Table */ .XI, {
            columns: columns,
            dataSource: data,
            size: currentDeviceType === 'mobile' ? 'small' : 'middle',
            scroll: currentDeviceType === 'mobile' ? {
              x: true
            } : undefined
          });
        default:
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
            style: {
              padding: '8px',
              textAlign: 'center',
              color: '#666',
              border: '1px dashed #ccc',
              borderRadius: currentDeviceType === 'mobile' ? '4px' : '6px'
            }
          }, component.type, " Component");
      }
    };
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      key: component.id,
      onMouseEnter: function onMouseEnter() {
        return setHoveredComponent(component.id);
      },
      onMouseLeave: function onMouseLeave() {
        return setHoveredComponent(null);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ComponentWrapper, {
      isSelected: isSelected,
      previewMode: currentPreviewMode,
      style: responsiveStyles,
      onClick: function onClick(e) {
        e.stopPropagation();
        if (!currentPreviewMode && onSelectComponent) {
          onSelectComponent(component);
        }
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ComponentBadge, {
      isSelected: isSelected,
      previewMode: currentPreviewMode
    }, component.type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(SelectionHandles, {
      visible: showIndicators
    }), component.type === 'text' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(EditIndicator, {
      visible: showIndicators
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .EditOutlined */ .xjh, null)), renderComponentContent()));
  }, [selectedComponentId, hoveredComponent, onSelectComponent]);

  // Debug logging
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    console.log('UXEnhancedPreviewArea - Components:', components);
    console.log('UXEnhancedPreviewArea - Components length:', components.length);
    console.log('UXEnhancedPreviewArea - Device type:', deviceType);
    console.log('UXEnhancedPreviewArea - Preview mode:', previewMode);
    console.log('UXEnhancedPreviewArea - Component rendering fixed!');
  }, [components, deviceType, previewMode]);

  // Test component addition
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (components.length === 0) {
      console.log('No components found. You can test by clicking on components in the palette.');

      // Add a test component for debugging (only in development)
      if (false) {}
    }
  }, [components.length, onDrop]);

  // Get current device configuration
  var currentDevice = DEVICE_CONFIGS[deviceType];

  // Memoized device-specific styles
  var deviceStyles = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return {
      width: currentDevice.width,
      height: currentDevice.height,
      scale: zoom
    };
  }, [currentDevice, zoom]);

  // Real-time preview hook (with fallback)
  var realTimePreviewHook = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    try {
      return (0,_hooks_useRealTimePreview__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)({
        components: components,
        onUpdateComponent: onUpdateComponent,
        onDeleteComponent: onDeleteComponent,
        enableWebSocket: realTimeUpdates && websocketConnected
      });
    } catch (error) {
      console.warn('Real-time preview hook failed, using fallback:', error);
      return {
        isUpdating: false,
        websocketConnected: false,
        updateComponent: onUpdateComponent || function () {},
        getAllComponents: function getAllComponents() {
          return components;
        }
      };
    }
  }, [components, onUpdateComponent, onDeleteComponent, realTimeUpdates, websocketConnected]);
  var isUpdating = realTimePreviewHook.isUpdating,
    realtimeConnected = realTimePreviewHook.websocketConnected,
    updateComponent = realTimePreviewHook.updateComponent,
    getAllComponents = realTimePreviewHook.getAllComponents;

  // Performance monitoring hook (with fallback)
  var performanceHook = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    try {
      return (0,_hooks_usePreviewPerformance__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A)({
        components: getAllComponents(),
        enablePerformanceMonitoring: showPerformanceMetrics
      });
    } catch (error) {
      console.warn('Performance monitoring hook failed, using fallback:', error);
      return {
        renderTime: 0,
        frameRate: 60,
        memoryUsage: 0,
        componentCount: components.length
      };
    }
  }, [getAllComponents, showPerformanceMetrics, components.length]);
  var renderTime = performanceHook.renderTime,
    frameRate = performanceHook.frameRate,
    memoryUsage = performanceHook.memoryUsage,
    componentCount = performanceHook.componentCount;

  // Device change handler
  var handleDeviceChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (newDeviceType) {
    setDeviceType(newDeviceType);
    var device = DEVICE_CONFIGS[newDeviceType];
    setZoom(device.scale);
  }, []);

  // Zoom controls
  var handleZoomIn = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setZoom(function (prev) {
      return Math.min(prev + 0.1, 2);
    });
  }, []);
  var handleZoomOut = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setZoom(function (prev) {
      return Math.max(prev - 0.1, 0.3);
    });
  }, []);
  var handleZoomReset = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setZoom(currentDevice.scale);
  }, [currentDevice.scale]);

  // Fullscreen toggle
  var handleFullscreenToggle = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (!document.fullscreenElement) {
      var _containerRef$current;
      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 || _containerRef$current.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Drag and drop handlers
  var handleDragEnter = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    setIsDragOver(true);
  }, []);
  var handleDragOverInternal = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    if (onDragOver) onDragOver(e);
  }, [onDragOver]);
  var handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
      if (onDragLeave) onDragLeave(e);
    }
  }, [onDragLeave]);
  var handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    setIsDragOver(false);
    if (onDrop) {
      var _canvasRef$current;
      var rect = (_canvasRef$current = canvasRef.current) === null || _canvasRef$current === void 0 ? void 0 : _canvasRef$current.getBoundingClientRect();
      if (rect) {
        var x = (e.clientX - rect.left) / zoom;
        var y = (e.clientY - rect.top) / zoom;
        onDrop(e, {
          x: x,
          y: y
        });
      }
    }
  }, [onDrop, zoom]);

  // Settings menu
  var settingsMenu = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Item, {
    key: "grid"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
    size: "small",
    checked: showGrid,
    onChange: setShowGrid
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "Show Grid"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Item, {
    key: "breakpoints"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
    size: "small",
    checked: showBreakpoints,
    onChange: setShowBreakpoints
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "Show Breakpoints"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Item, {
    key: "performance"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
    size: "small",
    checked: showPerformanceMetrics,
    onChange: function onChange() {}
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "Performance Metrics"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Divider, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Item, {
    key: "gridSize"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    style: {
      fontSize: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs
    }
  }, "Grid Size: ", gridSize, "px"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Slider */ .Ap, {
    min: 10,
    max: 50,
    value: gridSize,
    onChange: setGridSize,
    style: {
      width: 100,
      margin: "".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], " 0")
    }
  }))));
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PreviewContainer, {
    ref: containerRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PreviewToolbar, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ToolbarSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceSelector, null, Object.entries(DEVICE_CONFIGS).map(function (_ref2) {
    var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref2, 2),
      key = _ref3[0],
      device = _ref3[1];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceButton, {
      key: key,
      size: "small",
      active: deviceType === key,
      onClick: function onClick() {
        return handleDeviceChange(key);
      },
      icon: device.icon,
      title: device.description
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
      className: "device-label"
    }, device.name));
  })), showBreakpoints && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(BreakpointIndicator, null, currentDevice.breakpoint.toUpperCase(), " \u2022 ", currentDevice.width, "\xD7", currentDevice.height)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ToolbarSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ZoomControls, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ZoomOutOutlined */ .uC4, null),
    onClick: handleZoomOut,
    disabled: zoom <= 0.3,
    title: "Zoom Out"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ZoomSlider, {
    min: 30,
    max: 200,
    value: Math.round(zoom * 100),
    onChange: function onChange(value) {
      return setZoom(value / 100);
    },
    tooltip: {
      formatter: function formatter(value) {
        return "".concat(value, "%");
      }
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ZoomInOutlined */ .$gz, null),
    onClick: handleZoomIn,
    disabled: zoom >= 2,
    title: "Zoom In"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    onClick: handleZoomReset,
    title: "Reset Zoom"
  }, Math.round(zoom * 100), "%")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(StatusIndicator, {
    connected: realtimeConnected
  }, realtimeConnected ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .WifiOutlined */ ._bA, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DisconnectOutlined */ .Bu6, null), realtimeConnected ? 'Connected' : 'Offline', isUpdating && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SyncOutlined */ .OmY, {
    spin: true
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Dropdown */ .ms, {
    overlay: settingsMenu,
    trigger: ['click']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null),
    title: "Settings"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    icon: isFullscreen ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CompressOutlined */ .J5k, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenOutlined */ .KrH, null),
    onClick: handleFullscreenToggle,
    title: isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(CanvasContainer, {
    showGrid: showGrid,
    gridSize: gridSize,
    onDragEnter: handleDragEnter,
    onDragOver: handleDragOverInternal,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop
  }, enableDeviceFrames && currentDevice.frame ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceFrame, {
    deviceType: deviceType,
    frame: currentDevice.frame
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ResponsiveCanvas, {
    ref: canvasRef,
    deviceWidth: deviceStyles.width,
    deviceHeight: deviceStyles.height,
    deviceType: deviceType,
    scale: zoom,
    onClick: function onClick() {
      return onSelectComponent && onSelectComponent(null);
    }
  }, components.length > 0 ? components.map(function (component) {
    return renderComponent(component, deviceType, previewMode);
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Empty */ .Sv, {
    description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "No components added yet.", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'),
    style: {
      margin: deviceType === 'mobile' ? '50px 20px' : '100px 0',
      fontSize: deviceType === 'mobile' ? '14px' : '16px'
    }
  }))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ResponsiveCanvas, {
    ref: canvasRef,
    deviceWidth: deviceStyles.width,
    deviceHeight: deviceStyles.height,
    deviceType: deviceType,
    scale: zoom,
    onClick: function onClick() {
      return onSelectComponent && onSelectComponent(null);
    }
  }, components.length > 0 ? components.map(function (component) {
    return renderComponent(component, deviceType, previewMode);
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Empty */ .Sv, {
    description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "No components added yet.", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'),
    style: {
      margin: deviceType === 'mobile' ? '50px 20px' : '100px 0',
      fontSize: deviceType === 'mobile' ? '14px' : '16px'
    }
  })), showPerformanceMetrics && "production" === 'development' && /*#__PURE__*/0, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DropZone, {
    visible: isDragOver,
    isActive: isDragOver
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DragOutlined */ .duJ, {
    style: {
      fontSize: 48,
      color: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.tertiary
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "drop-message"
  }, "Drop component here"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "drop-hint"
  }, "Release to add to canvas")), loading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(LoadingOverlay, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "loading-text"
  }, "Loading preview..."))));
}

/***/ })

}]);