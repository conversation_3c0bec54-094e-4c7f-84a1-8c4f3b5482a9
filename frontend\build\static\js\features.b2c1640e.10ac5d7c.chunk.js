"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8267],{

/***/ 26248:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   S: () => (/* binding */ ComponentCombinationsList)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5556);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);








var Text = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Paragraph;

/**
 * Component Combination Card
 * Displays individual component combination suggestions
 */
var ComponentCombinationCard = function ComponentCombinationCard(_ref) {
  var suggestion = _ref.suggestion,
    onApply = _ref.onApply,
    onPreview = _ref.onPreview,
    _ref$applied = _ref.applied,
    applied = _ref$applied === void 0 ? false : _ref$applied,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    _ref$showScore = _ref.showScore,
    showScore = _ref$showScore === void 0 ? true : _ref$showScore,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    previewVisible = _useState2[0],
    setPreviewVisible = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    applying = _useState4[0],
    setApplying = _useState4[1];

  // Handle apply button click
  var handleApply = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
      var _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(applied || applying)) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return");
          case 1:
            setApplying(true);
            _context.prev = 2;
            if (!onApply) {
              _context.next = 3;
              break;
            }
            _context.next = 3;
            return onApply(suggestion);
          case 3:
            _context.next = 5;
            break;
          case 4:
            _context.prev = 4;
            _t = _context["catch"](2);
            console.error('Error applying component combination:', _t);
          case 5:
            _context.prev = 5;
            setApplying(false);
            return _context.finish(5);
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[2, 4, 5, 6]]);
    }));
    return function handleApply() {
      return _ref2.apply(this, arguments);
    };
  }();

  // Handle preview button click
  var handlePreview = function handlePreview() {
    if (onPreview) {
      onPreview(suggestion);
    } else {
      setPreviewVisible(true);
    }
  };

  // Get score color based on value
  var getScoreColor = function getScoreColor(score) {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#1890ff';
    if (score >= 40) return '#faad14';
    return '#ff4d4f';
  };

  // Get component icon
  var getComponentIcon = function getComponentIcon(componentType) {
    var iconMap = {
      button: '🔘',
      form: '📝',
      input: '📝',
      text: '📄',
      image: '🖼️',
      card: '🃏',
      header: '📋',
      nav: '🧭',
      list: '📋',
      divider: '➖',
      section: '📦',
      modal: '🪟',
      table: '📊',
      chart: '📈'
    };
    return iconMap[componentType] || '🔧';
  };

  // Render component combination preview
  var renderCombinationPreview = function renderCombinationPreview() {
    var _suggestion$component = suggestion.components,
      components = _suggestion$component === void 0 ? [] : _suggestion$component,
      _suggestion$missing_c = suggestion.missing_components,
      missing_components = _suggestion$missing_c === void 0 ? [] : _suggestion$missing_c;
    var allComponents = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(components);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px',
        background: '#fafafa',
        borderRadius: '6px',
        border: '1px solid #d9d9d9',
        minHeight: '80px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
      size: "small",
      wrap: true
    }, allComponents.map(function (component, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        key: index,
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Avatar */ .eu, {
        size: "small",
        style: {
          backgroundColor: missing_components.includes(component) ? '#ff4d4f' : '#1890ff',
          color: 'white',
          fontSize: '12px'
        }
      }, getComponentIcon(component)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          fontSize: '10px',
          marginTop: '2px',
          color: missing_components.includes(component) ? '#ff4d4f' : '#666'
        }
      }, component));
    }), missing_components.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .PlusOutlined */ .bW0, {
      style: {
        color: '#999',
        margin: '0 4px'
      }
    }), missing_components.map(function (component, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        key: "missing-".concat(index),
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Avatar */ .eu, {
        size: "small",
        style: {
          backgroundColor: '#52c41a',
          color: 'white',
          fontSize: '12px',
          border: '2px dashed #52c41a'
        }
      }, getComponentIcon(component)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          fontSize: '10px',
          marginTop: '2px',
          color: '#52c41a'
        }
      }, "+", component));
    }))));
  };

  // Render detailed preview modal
  var renderPreviewModal = function renderPreviewModal() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Modal */ .aF, {
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .AppstoreOutlined */ .rS9, null), suggestion.name, showScore && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Badge */ .Ex, {
        count: suggestion.score,
        style: {
          backgroundColor: getScoreColor(suggestion.score)
        }
      })),
      open: previewVisible,
      onCancel: function onCancel() {
        return setPreviewVisible(false);
      },
      footer: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
        key: "cancel",
        onClick: function onClick() {
          return setPreviewVisible(false);
        }
      }, "Close"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
        key: "apply",
        type: "primary",
        onClick: function onClick() {
          setPreviewVisible(false);
          handleApply();
        },
        disabled: applied,
        loading: applying
      }, applied ? 'Applied' : 'Add Components')],
      width: 600
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        marginBottom: '16px'
      }
    }, renderCombinationPreview()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, null, suggestion.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      strong: true
    }, "Why this combination?"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, {
      type: "secondary"
    }, suggestion.explanation)), suggestion.missing_components && suggestion.missing_components.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      strong: true
    }, "Components to add:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        marginTop: '8px'
      }
    }, suggestion.missing_components.map(function (component, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
        key: index,
        color: "green",
        style: {
          marginBottom: '4px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .PlusOutlined */ .bW0, {
        style: {
          marginRight: '4px'
        }
      }), component);
    }))), suggestion.use_cases && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      strong: true
    }, "Best for:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        marginTop: '8px'
      }
    }, suggestion.use_cases.map(function (useCase, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
        key: index,
        color: "blue",
        style: {
          marginBottom: '4px'
        }
      }, useCase.replace('_', ' '));
    })))));
  };
  if (compact) {
    var _suggestion$component2, _suggestion$missing_c2, _suggestion$missing_c3;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        padding: '8px',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        marginBottom: '8px',
        background: applied ? '#f6ffed' : 'white'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        width: '60px',
        marginRight: '12px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
      size: "small"
    }, (_suggestion$component2 = suggestion.components) === null || _suggestion$component2 === void 0 ? void 0 : _suggestion$component2.slice(0, 2).map(function (component, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Avatar */ .eu, {
        key: index,
        size: "small",
        style: {
          backgroundColor: '#1890ff',
          fontSize: '10px'
        }
      }, getComponentIcon(component));
    }), ((_suggestion$missing_c2 = suggestion.missing_components) === null || _suggestion$missing_c2 === void 0 ? void 0 : _suggestion$missing_c2.length) > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Avatar */ .eu, {
      size: "small",
      style: {
        backgroundColor: '#52c41a',
        fontSize: '10px'
      }
    }, "+"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        flex: 1
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '2px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      strong: true,
      style: {
        fontSize: '12px'
      }
    }, suggestion.name), showScore && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Badge */ .Ex, {
      count: suggestion.score,
      style: {
        backgroundColor: getScoreColor(suggestion.score),
        marginLeft: '8px'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '11px'
      }
    }, ((_suggestion$missing_c3 = suggestion.missing_components) === null || _suggestion$missing_c3 === void 0 ? void 0 : _suggestion$missing_c3.length) > 0 ? "Add ".concat(suggestion.missing_components.join(', ')) : suggestion.explanation)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, showPreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
      title: "Preview"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .EyeOutlined */ .Om2, null),
      onClick: handlePreview
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
      type: applied ? 'default' : 'primary',
      size: "small",
      icon: applied ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckOutlined */ .JIb, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .PlusOutlined */ .bW0, null),
      onClick: handleApply,
      disabled: applied,
      loading: applying
    }, applied ? 'Added' : 'Add'))), renderPreviewModal());
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    size: "small",
    style: {
      marginBottom: '12px',
      border: applied ? '2px solid #52c41a' : '1px solid #d9d9d9'
    },
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      strong: true
    }, suggestion.name), showScore && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Badge */ .Ex, {
      count: suggestion.score,
      style: {
        backgroundColor: getScoreColor(suggestion.score)
      }
    })), applied && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckOutlined */ .JIb, {
      style: {
        color: '#52c41a'
      }
    })),
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, null, showPreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
      title: "Preview combination"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .EyeOutlined */ .Om2, null),
      onClick: handlePreview
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
      type: applied ? 'default' : 'primary',
      size: "small",
      icon: applied ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .CheckOutlined */ .JIb, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .PlusOutlined */ .bW0, null),
      onClick: handleApply,
      disabled: applied,
      loading: applying
    }, applied ? 'Added' : 'Add Components'))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginBottom: '12px'
    }
  }, renderCombinationPreview()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, {
    style: {
      margin: '0 0 8px 0',
      fontSize: '12px',
      color: '#666'
    }
  }, suggestion.description), suggestion.missing_components && suggestion.missing_components.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginBottom: '8px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true,
    style: {
      fontSize: '11px'
    }
  }, "Missing components: "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Space */ .$x, {
    size: "small",
    wrap: true
  }, suggestion.missing_components.map(function (component, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tag */ .vw, {
      key: index,
      color: "green",
      size: "small"
    }, component);
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .InfoCircleOutlined */ .rUN, {
    style: {
      marginRight: '4px',
      color: '#1890ff'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      fontSize: '11px',
      fontStyle: 'italic'
    }
  }, suggestion.explanation))), renderPreviewModal());
};
ComponentCombinationCard.propTypes = {
  suggestion: prop_types__WEBPACK_IMPORTED_MODULE_5___default().shape({
    id: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string).isRequired,
    name: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string).isRequired,
    description: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string).isRequired,
    score: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().number).isRequired,
    explanation: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string).isRequired,
    components: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().array),
    missing_components: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().array),
    use_cases: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().array)
  }).isRequired,
  onApply: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().func),
  onPreview: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().func),
  applied: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),
  showPreview: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),
  showScore: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),
  compact: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool)
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (ComponentCombinationCard)));

/**
 * Component Combinations List Component
 * Container for multiple component combination cards
 */
var ComponentCombinationsList = function ComponentCombinationsList(_ref3) {
  var _ref3$suggestions = _ref3.suggestions,
    suggestions = _ref3$suggestions === void 0 ? [] : _ref3$suggestions,
    onApply = _ref3.onApply,
    onPreview = _ref3.onPreview,
    _ref3$appliedSuggesti = _ref3.appliedSuggestions,
    appliedSuggestions = _ref3$appliedSuggesti === void 0 ? new Set() : _ref3$appliedSuggesti,
    _ref3$loading = _ref3.loading,
    loading = _ref3$loading === void 0 ? false : _ref3$loading,
    _ref3$compact = _ref3.compact,
    compact = _ref3$compact === void 0 ? false : _ref3$compact,
    _ref3$showScore = _ref3.showScore,
    showScore = _ref3$showScore === void 0 ? true : _ref3$showScore,
    _ref3$showPreview = _ref3.showPreview,
    showPreview = _ref3$showPreview === void 0 ? true : _ref3$showPreview,
    _ref3$emptyMessage = _ref3.emptyMessage,
    emptyMessage = _ref3$emptyMessage === void 0 ? "No component combinations available" : _ref3$emptyMessage;
  if (loading) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
      tip: "Finding component combinations..."
    }));
  }
  if (suggestions.length === 0) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px',
        color: '#999'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .AppstoreOutlined */ .rS9, {
      style: {
        fontSize: '48px',
        marginBottom: '16px'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, emptyMessage));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      maxHeight: '400px',
      overflowY: 'auto'
    }
  }, suggestions.map(function (suggestion) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentCombinationCard, {
      key: suggestion.id,
      suggestion: suggestion,
      onApply: onApply,
      onPreview: onPreview,
      applied: appliedSuggestions.has(suggestion.id),
      compact: compact,
      showScore: showScore,
      showPreview: showPreview
    });
  }));
};
ComponentCombinationsList.propTypes = {
  suggestions: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().array),
  onApply: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().func),
  onPreview: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().func),
  appliedSuggestions: prop_types__WEBPACK_IMPORTED_MODULE_5___default().instanceOf(Set),
  loading: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),
  compact: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),
  showScore: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),
  showPreview: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),
  emptyMessage: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string)
};

/***/ }),

/***/ 26379:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title;
var SuggestionContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: 16px;\n"])));
var SuggestionCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin-bottom: 12px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    transform: translateY(-2px);\n  }\n"])));
var LayoutPreview = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 100%;\n  height: 80px;\n  background: #f5f5f5;\n  border: 1px dashed #d9d9d9;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 8px 0;\n  position: relative;\n  overflow: hidden;\n"])));
var LayoutElement = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  background: ", ";\n  border-radius: 2px;\n  position: absolute;\n  opacity: 0.7;\n"])), function (props) {
  return props.color || '#1890ff';
});
var ScoreIndicator = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  color: #52c41a;\n  font-weight: 500;\n"])));
var AILayoutSuggestions = function AILayoutSuggestions(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$currentLayout = _ref.currentLayout,
    currentLayout = _ref$currentLayout === void 0 ? null : _ref$currentLayout,
    _ref$onApplySuggestio = _ref.onApplySuggestion,
    onApplySuggestion = _ref$onApplySuggestio === void 0 ? function () {} : _ref$onApplySuggestio,
    _ref$onRefresh = _ref.onRefresh,
    onRefresh = _ref$onRefresh === void 0 ? function () {} : _ref$onRefresh,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading,
    _ref$enabled = _ref.enabled,
    enabled = _ref$enabled === void 0 ? true : _ref$enabled;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    suggestions = _useState2[0],
    setSuggestions = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set()),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    appliedSuggestions = _useState4[0],
    setAppliedSuggestions = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    isLoading = _useState6[0],
    setIsLoading = _useState6[1];

  // Mock AI suggestions based on current components
  var generateSuggestions = function generateSuggestions() {
    var mockSuggestions = [{
      id: 'grid-layout',
      name: 'Grid Layout',
      description: 'Organize components in a responsive grid system',
      type: 'layout',
      score: 95,
      reasoning: 'Your components would benefit from a structured grid layout for better visual hierarchy',
      layout: {
        type: 'grid',
        columns: 3,
        gap: 16,
        responsive: true
      },
      preview: [{
        x: 5,
        y: 10,
        width: 25,
        height: 15,
        color: '#1890ff'
      }, {
        x: 35,
        y: 10,
        width: 25,
        height: 15,
        color: '#52c41a'
      }, {
        x: 65,
        y: 10,
        width: 25,
        height: 15,
        color: '#fa8c16'
      }, {
        x: 5,
        y: 35,
        width: 25,
        height: 15,
        color: '#eb2f96'
      }, {
        x: 35,
        y: 35,
        width: 25,
        height: 15,
        color: '#722ed1'
      }, {
        x: 65,
        y: 35,
        width: 25,
        height: 15,
        color: '#13c2c2'
      }]
    }, {
      id: 'sidebar-layout',
      name: 'Sidebar Layout',
      description: 'Create a sidebar navigation with main content area',
      type: 'layout',
      score: 88,
      reasoning: 'Perfect for applications with navigation and content sections',
      layout: {
        type: 'sidebar',
        sidebarWidth: 250,
        position: 'left'
      },
      preview: [{
        x: 5,
        y: 10,
        width: 20,
        height: 60,
        color: '#1890ff'
      }, {
        x: 30,
        y: 10,
        width: 65,
        height: 25,
        color: '#52c41a'
      }, {
        x: 30,
        y: 40,
        width: 65,
        height: 30,
        color: '#fa8c16'
      }]
    }, {
      id: 'hero-layout',
      name: 'Hero Section Layout',
      description: 'Feature a prominent hero section with supporting content',
      type: 'layout',
      score: 82,
      reasoning: 'Great for landing pages and marketing content',
      layout: {
        type: 'hero',
        heroHeight: '50vh',
        contentSections: 3
      },
      preview: [{
        x: 5,
        y: 5,
        width: 90,
        height: 30,
        color: '#1890ff'
      }, {
        x: 5,
        y: 40,
        width: 28,
        height: 25,
        color: '#52c41a'
      }, {
        x: 36,
        y: 40,
        width: 28,
        height: 25,
        color: '#fa8c16'
      }, {
        x: 67,
        y: 40,
        width: 28,
        height: 25,
        color: '#eb2f96'
      }]
    }, {
      id: 'dashboard-layout',
      name: 'Dashboard Layout',
      description: 'Organize data and controls in a dashboard format',
      type: 'layout',
      score: 90,
      reasoning: 'Ideal for data-heavy applications with multiple widgets',
      layout: {
        type: 'dashboard',
        widgets: true,
        responsive: true
      },
      preview: [{
        x: 5,
        y: 5,
        width: 42,
        height: 20,
        color: '#1890ff'
      }, {
        x: 52,
        y: 5,
        width: 43,
        height: 20,
        color: '#52c41a'
      }, {
        x: 5,
        y: 30,
        width: 28,
        height: 35,
        color: '#fa8c16'
      }, {
        x: 38,
        y: 30,
        width: 28,
        height: 35,
        color: '#eb2f96'
      }, {
        x: 71,
        y: 30,
        width: 24,
        height: 35,
        color: '#722ed1'
      }]
    }];

    // Filter suggestions based on component count and types
    var componentCount = components.length;
    var filteredSuggestions = mockSuggestions;
    if (componentCount < 3) {
      filteredSuggestions = mockSuggestions.filter(function (s) {
        return s.id !== 'dashboard-layout';
      });
    }
    if (componentCount > 6) {
      filteredSuggestions = filteredSuggestions.map(function (s) {
        return _objectSpread(_objectSpread({}, s), {}, {
          score: s.id === 'dashboard-layout' ? s.score + 5 : s.score
        });
      });
    }
    return filteredSuggestions.sort(function (a, b) {
      return b.score - a.score;
    });
  };
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (enabled && components.length > 0) {
      setIsLoading(true);
      // Simulate AI processing time
      setTimeout(function () {
        setSuggestions(generateSuggestions());
        setIsLoading(false);
      }, 1000);
    } else {
      setSuggestions([]);
    }
  }, [components, enabled]);
  var handleApplySuggestion = function handleApplySuggestion(suggestion) {
    onApplySuggestion(suggestion);
    setAppliedSuggestions(function (prev) {
      return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [suggestion.id]));
    });
  };
  var handleRefresh = function handleRefresh() {
    setIsLoading(true);
    onRefresh();
    setTimeout(function () {
      setSuggestions(generateSuggestions());
      setIsLoading(false);
    }, 800);
  };
  var renderLayoutPreview = function renderLayoutPreview(preview) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(LayoutPreview, null, preview.map(function (element, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(LayoutElement, {
        key: index,
        color: element.color,
        style: {
          left: "".concat(element.x, "%"),
          top: "".concat(element.y, "%"),
          width: "".concat(element.width, "%"),
          height: "".concat(element.height, "%")
        }
      });
    }));
  };
  if (!enabled) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
      message: "AI Layout Suggestions Disabled",
      description: "Enable AI features to get intelligent layout suggestions.",
      type: "info",
      showIcon: true
    });
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SuggestionContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 4,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BulbOutlined */ .o3f, {
    style: {
      marginRight: 8,
      color: '#faad14'
    }
  }), "AI Layout Suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
    title: "Refresh suggestions"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ReloadOutlined */ .KF4, null),
    onClick: handleRefresh,
    loading: isLoading || loading
  }))), components.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: "No Components",
    description: "Add some components to your app to get AI-powered layout suggestions.",
    type: "info",
    showIcon: true,
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .AppstoreOutlined */ .rS9, null)
  }) : isLoading || loading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '40px 0'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary"
  }, "Analyzing your components..."))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, suggestions.map(function (suggestion) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SuggestionCard, {
      key: suggestion.id,
      size: "small",
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LayoutOutlined */ .hy2, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
        strong: true
      }, suggestion.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ScoreIndicator, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, null), suggestion.score, "%")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
        type: appliedSuggestions.has(suggestion.id) ? 'default' : 'primary',
        size: "small",
        icon: appliedSuggestions.has(suggestion.id) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckOutlined */ .JIb, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ThunderboltOutlined */ .CwG, null),
        onClick: function onClick() {
          return handleApplySuggestion(suggestion);
        },
        disabled: appliedSuggestions.has(suggestion.id)
      }, appliedSuggestions.has(suggestion.id) ? 'Applied' : 'Apply'))
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px',
        display: 'block',
        marginBottom: 8
      }
    }, suggestion.description), renderLayoutPreview(suggestion.preview), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        marginTop: 8
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      style: {
        fontSize: '11px',
        fontStyle: 'italic',
        color: '#666'
      }
    }, "\uD83D\uDCA1 ", suggestion.reasoning)));
  }), suggestions.length === 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '20px',
      color: '#999'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BulbOutlined */ .o3f, {
    style: {
      fontSize: '24px',
      marginBottom: 8
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, "No layout suggestions available at the moment."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      fontSize: '12px'
    }
  }, "Try adding more components or changing your current layout."))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AILayoutSuggestions);

/***/ }),

/***/ 27379:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ collaboration_CollaborationIndicator)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./src/components/collaboration/CollaborationPanel.js
var CollaborationPanel = __webpack_require__(80344);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./src/design-system/index.js + 7 modules
var design_system = __webpack_require__(79146);
// EXTERNAL MODULE: ./src/contexts/CollaborationContext.js
var CollaborationContext = __webpack_require__(17492);
// EXTERNAL MODULE: ./src/design-system/theme.js
var design_system_theme = __webpack_require__(86020);
;// ./src/components/collaboration/UserPresence.js



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12;








var PresenceContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n"])));
var UsersList = styled_components_browser_esm/* default */.Ay.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ", ";\n"])), design_system_theme/* default.spacing */.Ay.spacing[2]);
var UserItem = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  padding: ", ";\n  border-radius: ", ";\n  background-color: ", ";\n  \n  &:hover {\n    background-color: ", ";\n  }\n"])), design_system_theme/* default.spacing */.Ay.spacing[2], design_system_theme/* default.borderRadius */.Ay.borderRadius.md, design_system_theme/* default.colors */.Ay.colors.neutral[100], design_system_theme/* default.colors */.Ay.colors.neutral[200]);
var UserAvatar = styled_components_browser_esm/* default */.Ay.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: ", ";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  margin-right: ", ";\n"])), function (props) {
  return props.color || design_system_theme/* default.colors */.Ay.colors.primary.main;
}, design_system_theme/* default.spacing */.Ay.spacing[2]);
var UserInfo = styled_components_browser_esm/* default */.Ay.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  flex: 1;\n"])));
var UserName = styled_components_browser_esm/* default */.Ay.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-weight: ", ";\n"])), design_system_theme/* default.typography */.Ay.typography.fontWeight.medium);
var UserStatus = styled_components_browser_esm/* default */.Ay.div(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  font-size: ", ";\n  color: ", ";\n"])), design_system_theme/* default.typography */.Ay.typography.fontSize.xs, function (props) {
  switch (props.status) {
    case 'active':
      return design_system_theme/* default.colors */.Ay.colors.success.main;
    case 'idle':
      return design_system_theme/* default.colors */.Ay.colors.warning.main;
    case 'offline':
      return design_system_theme/* default.colors */.Ay.colors.neutral[500];
    default:
      return design_system_theme/* default.colors */.Ay.colors.neutral[500];
  }
});
var StatusDot = styled_components_browser_esm/* default */.Ay.div(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: ", ";\n  margin-right: ", ";\n"])), function (props) {
  switch (props.status) {
    case 'active':
      return design_system_theme/* default.colors */.Ay.colors.success.main;
    case 'idle':
      return design_system_theme/* default.colors */.Ay.colors.warning.main;
    case 'offline':
      return design_system_theme/* default.colors */.Ay.colors.neutral[500];
    default:
      return design_system_theme/* default.colors */.Ay.colors.neutral[500];
  }
}, design_system_theme/* default.spacing */.Ay.spacing[1]);
var LastSeen = styled_components_browser_esm/* default */.Ay.div(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  font-size: ", ";\n  color: ", ";\n  margin-left: ", ";\n"])), design_system_theme/* default.typography */.Ay.typography.fontSize.xs, design_system_theme/* default.colors */.Ay.colors.neutral[500], design_system_theme/* default.spacing */.Ay.spacing[2]);

// New components for real-time collaboration
var CursorIndicator = styled_components_browser_esm/* default */.Ay.div(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  width: 2px;\n  height: 20px;\n  background-color: ", ";\n  pointer-events: none;\n  z-index: 1000;\n  transition: all 0.1s ease;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    width: 0;\n    height: 0;\n    border-left: 8px solid ", ";\n    border-top: 8px solid transparent;\n    border-bottom: 8px solid transparent;\n  }\n"])), function (props) {
  return props.color;
}, function (props) {
  return props.color;
});
var CursorLabel = styled_components_browser_esm/* default */.Ay.div(_templateObject1 || (_templateObject1 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  top: -25px;\n  left: 10px;\n  background-color: ", ";\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  pointer-events: none;\n  z-index: 1001;\n"])), function (props) {
  return props.color;
});
var SelectionHighlight = styled_components_browser_esm/* default */.Ay.div(_templateObject10 || (_templateObject10 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  border: 2px solid ", ";\n  background-color: ", "20;\n  pointer-events: none;\n  z-index: 999;\n  border-radius: 4px;\n  transition: all 0.2s ease;\n"])), function (props) {
  return props.color;
}, function (props) {
  return props.color;
});
var CollaboratorsBarContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject11 || (_templateObject11 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border: 1px solid ", ";\n"])), design_system_theme/* default.colors */.Ay.colors.neutral[200]);
var StatusIndicator = styled_components_browser_esm/* default */.Ay.div(_templateObject12 || (_templateObject12 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: ", ";\n  position: absolute;\n  bottom: 0;\n  right: 0;\n  border: 2px solid white;\n"])), function (props) {
  switch (props.status) {
    case 'active':
      return design_system_theme/* default.colors */.Ay.colors.success.main;
    case 'idle':
      return design_system_theme/* default.colors */.Ay.colors.warning.main;
    case 'away':
      return design_system_theme/* default.colors */.Ay.colors.error.main;
    default:
      return design_system_theme/* default.colors */.Ay.colors.neutral[400];
  }
});

// User cursor component
var UserCursor = function UserCursor(_ref) {
  var userId = _ref.userId,
    position = _ref.position,
    username = _ref.username,
    color = _ref.color;
  var _useState = useState(true),
    _useState2 = _slicedToArray(_useState, 2),
    visible = _useState2[0],
    setVisible = _useState2[1];
  var timeoutRef = useRef();
  useEffect(function () {
    setVisible(true);

    // Hide cursor after 3 seconds of inactivity
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(function () {
      setVisible(false);
    }, 3000);
    return function () {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [position]);
  if (!visible || !position) {
    return null;
  }
  return /*#__PURE__*/React.createElement(CursorIndicator, {
    color: color,
    style: {
      left: position.x,
      top: position.y,
      opacity: visible ? 1 : 0
    }
  }, /*#__PURE__*/React.createElement(CursorLabel, {
    color: color
  }, username));
};

// Component selection highlight
var ComponentSelectionHighlight = function ComponentSelectionHighlight(_ref2) {
  var userId = _ref2.userId,
    selection = _ref2.selection,
    color = _ref2.color;
  if (!selection || !selection.bounds) {
    return null;
  }
  var _selection$bounds = selection.bounds,
    x = _selection$bounds.x,
    y = _selection$bounds.y,
    width = _selection$bounds.width,
    height = _selection$bounds.height;
  return /*#__PURE__*/React.createElement(SelectionHighlight, {
    color: color,
    style: {
      left: x,
      top: y,
      width: width,
      height: height
    }
  });
};

// Collaborator avatar with status
var CollaboratorAvatar = function CollaboratorAvatar(_ref3) {
  var _user$username;
  var user = _ref3.user,
    color = _ref3.color,
    _ref3$status = _ref3.status,
    status = _ref3$status === void 0 ? 'active' : _ref3$status,
    _ref3$showStatus = _ref3.showStatus,
    showStatus = _ref3$showStatus === void 0 ? true : _ref3$showStatus,
    _ref3$size = _ref3.size,
    size = _ref3$size === void 0 ? 'default' : _ref3$size;
  var getStatusText = function getStatusText(status) {
    switch (status) {
      case 'active':
        return 'Active';
      case 'idle':
        return 'Idle';
      case 'away':
        return 'Away';
      default:
        return 'Offline';
    }
  };
  return /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "".concat(user.username, " - ").concat(getStatusText(status))
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      position: 'relative',
      display: 'inline-block'
    }
  }, /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
    size: size,
    style: {
      backgroundColor: color,
      cursor: 'pointer'
    },
    icon: /*#__PURE__*/react.createElement(icons_es/* UserOutlined */.qmv, null)
  }, (_user$username = user.username) === null || _user$username === void 0 || (_user$username = _user$username.charAt(0)) === null || _user$username === void 0 ? void 0 : _user$username.toUpperCase()), showStatus && /*#__PURE__*/react.createElement(StatusIndicator, {
    status: status
  })));
};

// Compact collaborators bar
var CollaboratorsBar = function CollaboratorsBar(_ref4) {
  var _ref4$maxVisible = _ref4.maxVisible,
    maxVisible = _ref4$maxVisible === void 0 ? 5 : _ref4$maxVisible,
    _ref4$showStatus = _ref4.showStatus,
    showStatus = _ref4$showStatus === void 0 ? true : _ref4$showStatus,
    _ref4$size = _ref4.size,
    size = _ref4$size === void 0 ? 'small' : _ref4$size;
  var _useCollaboration = useCollaboration(),
    activeParticipants = _useCollaboration.activeParticipants,
    getParticipantColor = _useCollaboration.getParticipantColor,
    userPresence = _useCollaboration.userPresence;
  var getParticipantStatus = function getParticipantStatus(participant) {
    var presence = userPresence[participant.user_id];
    if (!presence) return 'offline';
    var lastSeen = new Date(presence.timestamp || participant.last_seen);
    var now = new Date();
    var timeDiff = now - lastSeen;
    if (timeDiff < 30000) return 'active'; // 30 seconds
    if (timeDiff < 300000) return 'idle'; // 5 minutes
    return 'away';
  };
  var visibleParticipants = activeParticipants.slice(0, maxVisible);
  var hiddenCount = Math.max(0, activeParticipants.length - maxVisible);
  if (activeParticipants.length === 0) {
    return null;
  }
  return /*#__PURE__*/React.createElement(CollaboratorsBarContainer, null, visibleParticipants.map(function (participant) {
    return /*#__PURE__*/React.createElement(CollaboratorAvatar, {
      key: participant.user_id,
      user: {
        id: participant.user_id,
        username: participant.username
      },
      color: getParticipantColor(participant.user_id),
      status: getParticipantStatus(participant),
      showStatus: showStatus,
      size: size
    });
  }), hiddenCount > 0 && /*#__PURE__*/React.createElement(Tooltip, {
    title: "".concat(hiddenCount, " more collaborator").concat(hiddenCount > 1 ? 's' : '')
  }, /*#__PURE__*/React.createElement(Avatar, {
    size: size,
    style: {
      backgroundColor: '#f0f0f0',
      color: '#666'
    }
  }, "+", hiddenCount)));
};

// Presence overlay component for the canvas
var PresenceOverlay = function PresenceOverlay(_ref5) {
  var containerRef = _ref5.containerRef;
  var _useCollaboration2 = useCollaboration(),
    activeParticipants = _useCollaboration2.activeParticipants,
    userPresence = _useCollaboration2.userPresence,
    getParticipantColor = _useCollaboration2.getParticipantColor;
  return /*#__PURE__*/React.createElement("div", {
    style: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      pointerEvents: 'none'
    }
  }, activeParticipants.map(function (participant) {
    var presence = userPresence[participant.user_id];
    var color = getParticipantColor(participant.user_id);
    return /*#__PURE__*/React.createElement(React.Fragment, {
      key: participant.user_id
    }, (presence === null || presence === void 0 ? void 0 : presence.cursor_position) && /*#__PURE__*/React.createElement(UserCursor, {
      userId: participant.user_id,
      position: presence.cursor_position,
      username: participant.username,
      color: color
    }), (presence === null || presence === void 0 ? void 0 : presence.selection) && /*#__PURE__*/React.createElement(ComponentSelectionHighlight, {
      userId: participant.user_id,
      selection: presence.selection,
      color: color
    }));
  }));
};
var UserPresence = function UserPresence() {
  var _useCollaboration3 = useCollaboration(),
    activeParticipants = _useCollaboration3.activeParticipants,
    getParticipantColor = _useCollaboration3.getParticipantColor,
    userPresence = _useCollaboration3.userPresence,
    isConnected = _useCollaboration3.isConnected;
  var currentUser = useSelector(function (state) {
    var _state$user;
    return ((_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.username) || 'User';
  });
  var getParticipantStatus = function getParticipantStatus(participant) {
    var presence = userPresence[participant.user_id];
    if (!presence) return 'offline';
    var lastSeen = new Date(presence.timestamp || participant.last_seen);
    var now = new Date();
    var timeDiff = now - lastSeen;
    if (timeDiff < 30000) return 'active'; // 30 seconds
    if (timeDiff < 300000) return 'idle'; // 5 minutes
    return 'away';
  };
  var formatLastSeen = function formatLastSeen(timestamp) {
    if (!timestamp) return 'Never';
    var now = Date.now();
    var diff = now - timestamp;
    if (diff < 60 * 1000) {
      return 'Just now';
    } else if (diff < 60 * 60 * 1000) {
      var minutes = Math.floor(diff / (60 * 1000));
      return "".concat(minutes, " minute").concat(minutes !== 1 ? 's' : '', " ago");
    } else if (diff < 24 * 60 * 60 * 1000) {
      var hours = Math.floor(diff / (60 * 60 * 1000));
      return "".concat(hours, " hour").concat(hours !== 1 ? 's' : '', " ago");
    } else {
      var days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return "".concat(days, " day").concat(days !== 1 ? 's' : '', " ago");
    }
  };

  // Sort participants: current user first, then by status
  var sortedParticipants = _toConsumableArray(activeParticipants).sort(function (a, b) {
    if (a.username === currentUser) return -1;
    if (b.username === currentUser) return 1;
    var statusA = getParticipantStatus(a);
    var statusB = getParticipantStatus(b);
    var statusOrder = {
      active: 0,
      idle: 1,
      away: 2,
      offline: 3
    };
    return statusOrder[statusA] - statusOrder[statusB];
  });
  return /*#__PURE__*/React.createElement(Card, {
    title: "Team Members",
    fullWidth: true
  }, /*#__PURE__*/React.createElement(PresenceContainer, null, /*#__PURE__*/React.createElement(UsersList, null, sortedParticipants.length === 0 ? /*#__PURE__*/React.createElement("div", {
    style: {
      textAlign: 'center',
      color: theme.colors.neutral[500],
      padding: theme.spacing[4]
    }
  }, isConnected ? 'No other collaborators online.' : 'Not connected to collaboration session.') : sortedParticipants.map(function (participant) {
    var status = getParticipantStatus(participant);
    var color = getParticipantColor(participant.user_id);
    return /*#__PURE__*/React.createElement(UserItem, {
      key: participant.user_id
    }, /*#__PURE__*/React.createElement(UserAvatar, {
      color: color
    }, /*#__PURE__*/React.createElement(UserOutlined, null)), /*#__PURE__*/React.createElement(UserInfo, null, /*#__PURE__*/React.createElement(UserName, null, participant.username, participant.username === currentUser && ' (You)'), /*#__PURE__*/React.createElement(UserStatus, {
      status: status
    }, /*#__PURE__*/React.createElement(StatusDot, {
      status: status
    }), status === 'active' ? 'Online' : status === 'idle' ? 'Idle' : status === 'away' ? 'Away' : 'Offline')), status !== 'active' && /*#__PURE__*/React.createElement(LastSeen, null, /*#__PURE__*/React.createElement(ClockCircleOutlined, {
      style: {
        marginRight: '4px'
      }
    }), formatLastSeen(new Date(participant.last_seen).getTime())));
  }))));
};

// Hook for tracking mouse position and sending presence updates
var usePresenceTracking = function usePresenceTracking(containerRef) {
  var _useCollaboration4 = useCollaboration(),
    updatePresence = _useCollaboration4.updatePresence,
    isConnected = _useCollaboration4.isConnected;
  var _useState3 = useState(null),
    _useState4 = _slicedToArray(_useState3, 2),
    selectedComponent = _useState4[0],
    setSelectedComponent = _useState4[1];
  useEffect(function () {
    if (!containerRef.current || !isConnected) return;
    var container = containerRef.current;
    var mousePosition = {
      x: 0,
      y: 0
    };
    var updateTimeout;
    var handleMouseMove = function handleMouseMove(event) {
      var rect = container.getBoundingClientRect();
      mousePosition = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      };

      // Debounce presence updates
      if (updateTimeout) {
        clearTimeout(updateTimeout);
      }
      updateTimeout = setTimeout(function () {
        updatePresence({
          cursor_position: mousePosition
        });
      }, 100);
    };
    var handleMouseLeave = function handleMouseLeave() {
      updatePresence({
        cursor_position: null
      });
    };
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseleave', handleMouseLeave);
    return function () {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseleave', handleMouseLeave);
      if (updateTimeout) {
        clearTimeout(updateTimeout);
      }
    };
  }, [containerRef, isConnected, updatePresence]);
  var selectComponent = function selectComponent(componentId) {
    var bounds = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    setSelectedComponent(componentId);
    updatePresence({
      selection: {
        component_id: componentId,
        bounds: bounds
      }
    });
  };
  var clearSelection = function clearSelection() {
    setSelectedComponent(null);
    updatePresence({
      selection: null
    });
  };
  return {
    selectedComponent: selectedComponent,
    selectComponent: selectComponent,
    clearSelection: clearSelection
  };
};
/* harmony default export */ const collaboration_UserPresence = ((/* unused pure expression or super */ null && (UserPresence)));
;// ./src/components/collaboration/CollaborationIndicator.js



var CollaborationIndicator_templateObject, CollaborationIndicator_templateObject2, CollaborationIndicator_templateObject3;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






var Text = es/* Typography */.o5.Text;

/**
 * Collaboration Indicator
 * 
 * Shows real-time collaboration status, connected users, and provides
 * access to collaboration features like chat and shared editing.
 */

var IndicatorContainer = styled_components_browser_esm/* default */.Ay.div(CollaborationIndicator_templateObject || (CollaborationIndicator_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 8px;\n  background: ", ";\n  border: 1px solid ", ";\n  border-radius: 6px;\n  transition: all 0.2s;\n  \n  &:hover {\n    background: ", ";\n  }\n"])), function (props) {
  return props.connected ? '#f6ffed' : '#fff2f0';
}, function (props) {
  return props.connected ? '#b7eb8f' : '#ffb3b3';
}, function (props) {
  return props.connected ? '#f0f9ff' : '#fef2f2';
});
var CollaborationIndicator_StatusDot = styled_components_browser_esm/* default */.Ay.div(CollaborationIndicator_templateObject2 || (CollaborationIndicator_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ", ";\n  animation: ", ";\n  \n  @keyframes pulse {\n    0% { opacity: 1; }\n    50% { opacity: 0.5; }\n    100% { opacity: 1; }\n  }\n"])), function (props) {
  return props.connected ? '#52c41a' : '#ff4d4f';
}, function (props) {
  return props.connected ? 'pulse 2s infinite' : 'none';
});
var CollaboratorsList = styled_components_browser_esm/* default */.Ay.div(CollaborationIndicator_templateObject3 || (CollaborationIndicator_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  gap: -4px; /* Overlap avatars slightly */\n  \n  .ant-avatar {\n    border: 2px solid white;\n    cursor: pointer;\n    transition: transform 0.2s;\n    \n    &:hover {\n      transform: scale(1.1);\n      z-index: 10;\n    }\n  }\n"])));
var CollaborationIndicator = function CollaborationIndicator(_ref) {
  var _ref$connected = _ref.connected,
    connected = _ref$connected === void 0 ? false : _ref$connected,
    _ref$collaborators = _ref.collaborators,
    collaborators = _ref$collaborators === void 0 ? [] : _ref$collaborators,
    _ref$activeUsers = _ref.activeUsers,
    activeUsers = _ref$activeUsers === void 0 ? [] : _ref$activeUsers,
    onOpenChat = _ref.onOpenChat,
    onOpenPanel = _ref.onOpenPanel,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact,
    _ref$showUserCount = _ref.showUserCount,
    showUserCount = _ref$showUserCount === void 0 ? true : _ref$showUserCount,
    _ref$maxVisibleUsers = _ref.maxVisibleUsers,
    maxVisibleUsers = _ref$maxVisibleUsers === void 0 ? 3 : _ref$maxVisibleUsers;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    panelVisible = _useState2[0],
    setPanelVisible = _useState2[1];
  var _useState3 = (0,react.useState)(Date.now()),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    lastActivity = _useState4[0],
    setLastActivity = _useState4[1];

  // Combine collaborators and active users
  var allUsers = react.useMemo(function () {
    var userMap = new Map();

    // Add collaborators
    collaborators.forEach(function (user) {
      userMap.set(user.id || user.username, _objectSpread(_objectSpread({}, user), {}, {
        type: 'collaborator',
        isActive: true
      }));
    });

    // Add active users
    activeUsers.forEach(function (user) {
      var existing = userMap.get(user.id || user.username);
      userMap.set(user.id || user.username, _objectSpread(_objectSpread(_objectSpread({}, existing), user), {}, {
        type: existing ? 'collaborator' : 'viewer',
        isActive: true
      }));
    });
    return Array.from(userMap.values());
  }, [collaborators, activeUsers]);

  // Update last activity when users change
  (0,react.useEffect)(function () {
    if (allUsers.length > 0) {
      setLastActivity(Date.now());
    }
  }, [allUsers]);
  var getStatusText = function getStatusText() {
    if (!connected) return 'Disconnected';
    if (allUsers.length === 0) return 'Connected';
    if (allUsers.length === 1) return '1 user online';
    return "".concat(allUsers.length, " users online");
  };
  var getStatusColor = function getStatusColor() {
    if (!connected) return 'error';
    if (allUsers.length === 0) return 'default';
    return 'success';
  };
  var renderUserPopover = function renderUserPopover(user) {
    return /*#__PURE__*/react.createElement("div", {
      style: {
        maxWidth: 200
      }
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
      direction: "vertical",
      size: "small"
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: 8
      }
    }, /*#__PURE__*/react.createElement(CollaboratorAvatar, {
      user: user,
      color: user.color || '#1890ff',
      status: user.status || 'active',
      size: "small"
    }), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, user.username || user.name), /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 12
      }
    }, user.type === 'collaborator' ? 'Collaborator' : 'Viewer'))), user.lastSeen && /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 11
      }
    }, "Last seen: ", new Date(user.lastSeen).toLocaleTimeString()), user.currentAction && /*#__PURE__*/react.createElement(Text, {
      style: {
        fontSize: 11,
        color: '#1890ff'
      }
    }, user.currentAction)));
  };
  var renderCollaboratorsList = function renderCollaboratorsList() {
    var visibleUsers = allUsers.slice(0, maxVisibleUsers);
    var hiddenCount = Math.max(0, allUsers.length - maxVisibleUsers);
    return /*#__PURE__*/react.createElement(CollaboratorsList, null, visibleUsers.map(function (user, index) {
      return /*#__PURE__*/react.createElement(es/* Popover */.AM, {
        key: user.id || user.username,
        content: renderUserPopover(user),
        placement: "bottom",
        trigger: "hover"
      }, /*#__PURE__*/react.createElement(CollaboratorAvatar, {
        user: user,
        color: user.color || '#1890ff',
        status: user.status || 'active',
        size: "small",
        style: {
          marginLeft: index > 0 ? -4 : 0
        }
      }));
    }), hiddenCount > 0 && /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "".concat(hiddenCount, " more user").concat(hiddenCount > 1 ? 's' : '')
    }, /*#__PURE__*/react.createElement(es/* Avatar */.eu, {
      size: "small",
      style: {
        backgroundColor: '#f0f0f0',
        color: '#666',
        marginLeft: -4,
        fontSize: 11
      }
    }, "+", hiddenCount)));
  };
  var renderStatusPopover = function renderStatusPopover() {
    return /*#__PURE__*/react.createElement("div", {
      style: {
        minWidth: 200
      }
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: 8
      }
    }, /*#__PURE__*/react.createElement(CollaborationIndicator_StatusDot, {
      connected: connected
    }), /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, getStatusText())), /*#__PURE__*/react.createElement(es/* Divider */.cG, {
      style: {
        margin: '8px 0'
      }
    }), allUsers.length > 0 ? /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 12
      }
    }, "Active Users:"), /*#__PURE__*/react.createElement("div", {
      style: {
        marginTop: 8
      }
    }, allUsers.slice(0, 5).map(function (user) {
      return /*#__PURE__*/react.createElement("div", {
        key: user.id || user.username,
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: 8,
          marginBottom: 4
        }
      }, /*#__PURE__*/react.createElement(CollaboratorAvatar, {
        user: user,
        color: user.color || '#1890ff',
        status: user.status || 'active',
        size: "small"
      }), /*#__PURE__*/react.createElement(Text, {
        style: {
          fontSize: 12
        }
      }, user.username || user.name));
    }), allUsers.length > 5 && /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 11
      }
    }, "and ", allUsers.length - 5, " more..."))) : /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 12
      }
    }, "No other users online"), /*#__PURE__*/react.createElement(es/* Divider */.cG, {
      style: {
        margin: '8px 0'
      }
    }), /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      size: "small",
      icon: /*#__PURE__*/react.createElement(icons_es/* TeamOutlined */.QM0, null),
      onClick: function onClick() {
        return setPanelVisible(true);
      }
    }, "Open Panel"), onOpenChat && /*#__PURE__*/react.createElement(es/* Button */.$n, {
      size: "small",
      icon: /*#__PURE__*/react.createElement(icons_es/* MessageOutlined */.g4F, null),
      onClick: onOpenChat
    }, "Chat"))));
  };

  // Compact mode for minimal space
  if (compact) {
    return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Popover */.AM, {
      content: renderStatusPopover(),
      placement: "bottomRight",
      trigger: "hover"
    }, /*#__PURE__*/react.createElement(IndicatorContainer, {
      connected: connected
    }, /*#__PURE__*/react.createElement(CollaborationIndicator_StatusDot, {
      connected: connected
    }), allUsers.length > 0 && /*#__PURE__*/react.createElement(es/* Badge */.Ex, {
      count: allUsers.length,
      size: "small"
    }, /*#__PURE__*/react.createElement(icons_es/* TeamOutlined */.QM0, {
      style: {
        fontSize: 14
      }
    })))), /*#__PURE__*/react.createElement(CollaborationPanel["default"], {
      visible: panelVisible,
      onClose: function onClose() {
        return setPanelVisible(false);
      },
      users: allUsers,
      connected: connected
    }));
  }

  // Full mode with user avatars
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    align: "center"
  }, /*#__PURE__*/react.createElement(es/* Popover */.AM, {
    content: renderStatusPopover(),
    placement: "bottom",
    trigger: "hover"
  }, /*#__PURE__*/react.createElement(IndicatorContainer, {
    connected: connected
  }, /*#__PURE__*/react.createElement(CollaborationIndicator_StatusDot, {
    connected: connected
  }), /*#__PURE__*/react.createElement(icons_es/* WifiOutlined */._bA, {
    style: {
      fontSize: 12
    }
  }), showUserCount && /*#__PURE__*/react.createElement(Text, {
    style: {
      fontSize: 11,
      color: connected ? '#52c41a' : '#ff4d4f'
    }
  }, connected ? 'Online' : 'Offline'))), allUsers.length > 0 && renderCollaboratorsList(), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null),
    onClick: function onClick() {
      return setPanelVisible(true);
    },
    style: {
      opacity: 0.7
    }
  })), /*#__PURE__*/react.createElement(CollaborationPanel["default"], {
    visible: panelVisible,
    onClose: function onClose() {
      return setPanelVisible(false);
    },
    users: allUsers,
    connected: connected
  }));
};
/* harmony default export */ const collaboration_CollaborationIndicator = (CollaborationIndicator);

/***/ }),

/***/ 44459:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);
/* harmony import */ var _hooks_useAIDesignSuggestions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(87169);
/* harmony import */ var _AISuggestionsPanel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(83719);




var _templateObject, _templateObject2, _templateObject3;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;

/**
 * AI Design Suggestions
 * 
 * Main AI suggestions component that provides intelligent design recommendations
 * based on current app state and user interactions.
 */

var SuggestionsContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: relative;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var SuggestionCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  .ant-card-body {\n    padding: 12px;\n  }\n  \n  .suggestion-title {\n    font-weight: 600;\n    margin-bottom: 4px;\n    color: #262626;\n  }\n  \n  .suggestion-description {\n    color: #595959;\n    font-size: 12px;\n    line-height: 1.4;\n  }\n  \n  .suggestion-actions {\n    margin-top: 8px;\n    display: flex;\n    gap: 4px;\n  }\n"])));
var CompactSuggestion = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: #f6ffed;\n  border: 1px solid #b7eb8f;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s;\n  \n  &:hover {\n    background: #f0f9ff;\n    border-color: #91d5ff;\n  }\n  \n  .suggestion-icon {\n    color: #52c41a;\n    font-size: 16px;\n  }\n  \n  .suggestion-text {\n    flex: 1;\n    font-size: 13px;\n    color: #262626;\n  }\n  \n  .suggestion-count {\n    background: #52c41a;\n    color: white;\n    border-radius: 10px;\n    padding: 2px 6px;\n    font-size: 11px;\n    font-weight: 600;\n  }\n"])));
var AIDesignSuggestions = function AIDesignSuggestions(_ref) {
  var _ref$suggestions = _ref.suggestions,
    suggestions = _ref$suggestions === void 0 ? [] : _ref$suggestions,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading,
    onApply = _ref.onApply,
    onDismiss = _ref.onDismiss,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact,
    _ref$showPanel = _ref.showPanel,
    showPanel = _ref$showPanel === void 0 ? false : _ref$showPanel,
    _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$selectedComponen = _ref.selectedComponent,
    selectedComponent = _ref$selectedComponen === void 0 ? null : _ref$selectedComponen;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(showPanel),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    panelVisible = _useState2[0],
    setPanelVisible = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set()),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    dismissedSuggestions = _useState4[0],
    setDismissedSuggestions = _useState4[1];

  // AI suggestions hook
  var _useAIDesignSuggestio = (0,_hooks_useAIDesignSuggestions__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)({
      autoRefresh: true,
      context: {
        selectedComponent: selectedComponent
      }
    }),
    hookSuggestions = _useAIDesignSuggestio.suggestions,
    hookLoading = _useAIDesignSuggestio.loading,
    hasLayoutSuggestions = _useAIDesignSuggestio.hasLayoutSuggestions,
    hasCombinationSuggestions = _useAIDesignSuggestio.hasCombinationSuggestions,
    applyLayoutSuggestion = _useAIDesignSuggestio.applyLayoutSuggestion,
    applyComponentCombination = _useAIDesignSuggestio.applyComponentCombination,
    refresh = _useAIDesignSuggestio.refresh;

  // Use hook data if no props provided
  var activeSuggestions = suggestions.length > 0 ? suggestions : [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(hookSuggestions.layout.map(function (s) {
    return _objectSpread(_objectSpread({}, s), {}, {
      type: 'layout'
    });
  })), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(hookSuggestions.combinations.map(function (s) {
    return _objectSpread(_objectSpread({}, s), {}, {
      type: 'combination'
    });
  })));
  var isLoading = loading || hookLoading;
  var totalSuggestions = activeSuggestions.filter(function (s) {
    return !dismissedSuggestions.has(s.id);
  }).length;

  // Handle applying suggestions
  var handleApply = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (suggestion) {
    if (onApply) {
      onApply(suggestion);
    } else {
      // Use hook methods based on suggestion type
      if (suggestion.type === 'layout') {
        applyLayoutSuggestion(suggestion);
      } else if (suggestion.type === 'combination') {
        applyComponentCombination(suggestion);
      }
    }
  }, [onApply, applyLayoutSuggestion, applyComponentCombination]);

  // Handle dismissing suggestions
  var handleDismiss = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (suggestion) {
    setDismissedSuggestions(function (prev) {
      return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [suggestion.id]));
    });
    if (onDismiss) {
      onDismiss(suggestion);
    }
  }, [onDismiss]);

  // Auto-show panel when suggestions are available
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (totalSuggestions > 0 && !panelVisible && components.length > 2) {
      // Auto-show after a delay to not interrupt user workflow
      var timer = setTimeout(function () {
        setPanelVisible(true);
      }, 3000);
      return function () {
        return clearTimeout(timer);
      };
    }
  }, [totalSuggestions, panelVisible, components.length]);

  // Don't render if no suggestions
  if (totalSuggestions === 0 && !isLoading) {
    return null;
  }

  // Compact mode for header/toolbar
  if (compact) {
    var topSuggestion = activeSuggestions.find(function (s) {
      return !dismissedSuggestions.has(s.id);
    });
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SuggestionsContainer, null, isLoading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
      status: "processing",
      text: "AI analyzing..."
    }) : topSuggestion ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Popover */ .AM, {
      content: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          maxWidth: 300
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        className: "suggestion-title"
      }, topSuggestion.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        className: "suggestion-description"
      }, topSuggestion.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        className: "suggestion-actions"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
        type: "primary",
        size: "small",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckOutlined */ .JIb, null),
        onClick: function onClick() {
          return handleApply(topSuggestion);
        }
      }, "Apply"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
        size: "small",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CloseOutlined */ .r$3, null),
        onClick: function onClick() {
          return handleDismiss(topSuggestion);
        }
      }, "Dismiss"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
        size: "small",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EyeOutlined */ .Om2, null),
        onClick: function onClick() {
          return setPanelVisible(true);
        }
      }, "View All"))),
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RobotOutlined */ .J_h, {
        style: {
          color: '#1890ff'
        }
      }), "AI Suggestion"),
      trigger: "hover"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(CompactSuggestion, {
      onClick: function onClick() {
        return setPanelVisible(true);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BulbOutlined */ .o3f, {
      className: "suggestion-icon"
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
      className: "suggestion-text"
    }, topSuggestion.title), totalSuggestions > 1 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
      className: "suggestion-count"
    }, "+", totalSuggestions - 1))) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
      title: "Open AI Suggestions Panel"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      type: "text",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RobotOutlined */ .J_h, null),
      onClick: function onClick() {
        return setPanelVisible(true);
      },
      style: {
        color: totalSuggestions > 0 ? '#1890ff' : '#8c8c8c'
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_AISuggestionsPanel__WEBPACK_IMPORTED_MODULE_9__["default"], {
      visible: panelVisible,
      onClose: function onClose() {
        return setPanelVisible(false);
      },
      components: components,
      selectedComponent: selectedComponent,
      onApplyLayoutSuggestion: handleApply,
      onApplyComponentCombination: handleApply
    }));
  }

  // Full mode for dedicated suggestions area
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RobotOutlined */ .J_h, {
    style: {
      color: '#1890ff'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "AI Suggestions"), totalSuggestions > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
    count: totalSuggestions,
    style: {
      backgroundColor: '#52c41a'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
    title: "Refresh suggestions"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "text",
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ThunderboltOutlined */ .CwG, null),
    onClick: refresh,
    loading: isLoading
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    size: "small",
    onClick: function onClick() {
      return setPanelVisible(true);
    }
  }, "View All"))), isLoading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, {
    loading: true
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .List */ .B8, {
    dataSource: activeSuggestions.filter(function (s) {
      return !dismissedSuggestions.has(s.id);
    }).slice(0, 3),
    renderItem: function renderItem(suggestion) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .List */ .B8.Item, {
        key: suggestion.id
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SuggestionCard, {
        size: "small",
        style: {
          width: '100%'
        },
        actions: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
          key: "apply",
          type: "primary",
          size: "small",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckOutlined */ .JIb, null),
          onClick: function onClick() {
            return handleApply(suggestion);
          }
        }, "Apply"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
          key: "dismiss",
          size: "small",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CloseOutlined */ .r$3, null),
          onClick: function onClick() {
            return handleDismiss(suggestion);
          }
        }, "Dismiss")]
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        className: "suggestion-title"
      }, suggestion.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        className: "suggestion-description"
      }, suggestion.description), suggestion.confidence && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          marginTop: 4
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
        type: "secondary",
        style: {
          fontSize: 11
        }
      }, "Confidence: ", Math.round(suggestion.confidence * 100), "%"))));
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_AISuggestionsPanel__WEBPACK_IMPORTED_MODULE_9__["default"], {
    visible: panelVisible,
    onClose: function onClose() {
      return setPanelVisible(false);
    },
    components: components,
    selectedComponent: selectedComponent,
    onApplyLayoutSuggestion: handleApply,
    onApplyComponentCombination: handleApply
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIDesignSuggestions);

/***/ }),

/***/ 76508:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useLiveCursors: () => (/* binding */ useLiveCursors)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var fadeIn = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* .keyframes */ .i7)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n"])));
var pulse = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* .keyframes */ .i7)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n  100% {\n    transform: scale(1);\n  }\n"])));
var CursorContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  pointer-events: none;\n  z-index: 9999;\n  transition: all 0.1s ease-out;\n  animation: ", " 0.3s ease-out;\n"])), fadeIn);
var CursorPointer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  width: 0;\n  height: 0;\n  border-left: 8px solid ", ";\n  border-right: 8px solid transparent;\n  border-bottom: 12px solid ", ";\n  transform: rotate(-45deg);\n  position: relative;\n"])), function (props) {
  return props.color || '#1890ff';
}, function (props) {
  return props.color || '#1890ff';
});
var CursorLabel = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  background: ", ";\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 500;\n  white-space: nowrap;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  animation: ", " 2s infinite;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: 8px;\n    width: 0;\n    height: 0;\n    border-left: 4px solid transparent;\n    border-right: 4px solid transparent;\n    border-bottom: 4px solid ", ";\n  }\n"])), function (props) {
  return props.color || '#1890ff';
}, pulse, function (props) {
  return props.color || '#1890ff';
});
var SelectionHighlight = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  border: 2px solid ", ";\n  border-radius: 4px;\n  background: ", "20;\n  pointer-events: none;\n  z-index: 9998;\n  transition: all 0.2s ease-out;\n"])), function (props) {
  return props.color || '#1890ff';
}, function (props) {
  return props.color || '#1890ff';
});
var UserColors = ['#1890ff',
// Blue
'#52c41a',
// Green
'#fa8c16',
// Orange
'#eb2f96',
// Pink
'#722ed1',
// Purple
'#13c2c2',
// Cyan
'#faad14',
// Gold
'#f5222d' // Red
];
var LiveCursors = function LiveCursors(_ref) {
  var _ref$collaborators = _ref.collaborators,
    collaborators = _ref$collaborators === void 0 ? [] : _ref$collaborators,
    _ref$currentUser = _ref.currentUser,
    currentUser = _ref$currentUser === void 0 ? null : _ref$currentUser,
    _ref$containerRef = _ref.containerRef,
    containerRef = _ref$containerRef === void 0 ? null : _ref$containerRef,
    _ref$showSelections = _ref.showSelections,
    showSelections = _ref$showSelections === void 0 ? true : _ref$showSelections,
    _ref$showLabels = _ref.showLabels,
    showLabels = _ref$showLabels === void 0 ? true : _ref$showLabels,
    _ref$fadeTimeout = _ref.fadeTimeout,
    fadeTimeout = _ref$fadeTimeout === void 0 ? 3000 : _ref$fadeTimeout;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    visibleCursors = _useState2[0],
    setVisibleCursors = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    selections = _useState4[0],
    setSelections = _useState4[1];
  var timeoutRefs = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(new Map());

  // Mock data for demonstration
  var mockCollaborators = collaborators.length > 0 ? collaborators : [{
    id: 'user-1',
    name: 'John Doe',
    cursor: {
      x: 300,
      y: 150
    },
    selection: {
      x: 280,
      y: 200,
      width: 120,
      height: 40
    },
    lastUpdate: Date.now(),
    online: true
  }, {
    id: 'user-2',
    name: 'Jane Smith',
    cursor: {
      x: 500,
      y: 300
    },
    selection: null,
    lastUpdate: Date.now() - 1000,
    online: true
  }];

  // Assign colors to users
  var getUserColor = function getUserColor(userId) {
    var index = userId.charCodeAt(userId.length - 1) % UserColors.length;
    return UserColors[index];
  };

  // Update cursor positions
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    var activeCursors = new Map();
    var activeSelections = new Map();
    mockCollaborators.forEach(function (user) {
      if (user.online && user.id !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) && user.cursor) {
        activeCursors.set(user.id, _objectSpread(_objectSpread({}, user), {}, {
          color: getUserColor(user.id)
        }));
        if (user.selection && showSelections) {
          activeSelections.set(user.id, _objectSpread(_objectSpread({}, user.selection), {}, {
            color: getUserColor(user.id),
            userName: user.name
          }));
        }

        // Set fade timeout
        if (timeoutRefs.current.has(user.id)) {
          clearTimeout(timeoutRefs.current.get(user.id));
        }
        var timeoutId = setTimeout(function () {
          setVisibleCursors(function (prev) {
            var newMap = new Map(prev);
            newMap["delete"](user.id);
            return newMap;
          });
          setSelections(function (prev) {
            var newMap = new Map(prev);
            newMap["delete"](user.id);
            return newMap;
          });
        }, fadeTimeout);
        timeoutRefs.current.set(user.id, timeoutId);
      }
    });
    setVisibleCursors(activeCursors);
    setSelections(activeSelections);
    return function () {
      timeoutRefs.current.forEach(function (timeoutId) {
        return clearTimeout(timeoutId);
      });
      timeoutRefs.current.clear();
    };
  }, [collaborators, currentUser, showSelections, fadeTimeout]);

  // Calculate relative position if container is provided
  var getRelativePosition = function getRelativePosition(absolutePos) {
    if (!(containerRef !== null && containerRef !== void 0 && containerRef.current)) return absolutePos;
    var containerRect = containerRef.current.getBoundingClientRect();
    return {
      x: absolutePos.x - containerRect.left,
      y: absolutePos.y - containerRect.top
    };
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, Array.from(selections.entries()).map(function (_ref2) {
    var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref2, 2),
      userId = _ref3[0],
      selection = _ref3[1];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SelectionHighlight, {
      key: "selection-".concat(userId),
      color: selection.color,
      style: {
        left: selection.x,
        top: selection.y,
        width: selection.width,
        height: selection.height
      }
    });
  }), Array.from(visibleCursors.entries()).map(function (_ref4) {
    var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref4, 2),
      userId = _ref5[0],
      user = _ref5[1];
    var position = getRelativePosition(user.cursor);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(CursorContainer, {
      key: "cursor-".concat(userId),
      style: {
        left: position.x,
        top: position.y
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(CursorPointer, {
      color: user.color
    }), showLabels && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
      title: "".concat(user.name, " is here"),
      placement: "topLeft"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(CursorLabel, {
      color: user.color
    }, user.name)));
  }));
};

// Hook for managing cursor tracking
var useLiveCursors = function useLiveCursors(websocketConnection, currentUser) {
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    collaborators = _useState6[0],
    setCollaborators = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      x: 0,
      y: 0
    }),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    myPosition = _useState8[0],
    setMyPosition = _useState8[1];

  // Track mouse movement
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    var handleMouseMove = function handleMouseMove(e) {
      var newPosition = {
        x: e.clientX,
        y: e.clientY
      };
      setMyPosition(newPosition);

      // Send cursor position via WebSocket
      if (websocketConnection && websocketConnection.readyState === WebSocket.OPEN) {
        websocketConnection.send(JSON.stringify({
          type: 'cursor_move',
          userId: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,
          position: newPosition,
          timestamp: Date.now()
        }));
      }
    };
    var throttledMouseMove = throttle(handleMouseMove, 50); // Throttle to 20fps
    document.addEventListener('mousemove', throttledMouseMove);
    return function () {
      document.removeEventListener('mousemove', throttledMouseMove);
    };
  }, [websocketConnection, currentUser]);

  // Listen for WebSocket messages
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (!websocketConnection) return;
    var handleMessage = function handleMessage(event) {
      try {
        var data = JSON.parse(event.data);
        if (data.type === 'cursor_move' && data.userId !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id)) {
          setCollaborators(function (prev) {
            var updated = prev.filter(function (user) {
              return user.id !== data.userId;
            });
            return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(updated), [{
              id: data.userId,
              name: data.userName || "User ".concat(data.userId),
              cursor: data.position,
              lastUpdate: data.timestamp,
              online: true
            }]);
          });
        }
        if (data.type === 'user_disconnect') {
          setCollaborators(function (prev) {
            return prev.filter(function (user) {
              return user.id !== data.userId;
            });
          });
        }
      } catch (error) {
        console.warn('Failed to parse WebSocket message:', error);
      }
    };
    websocketConnection.addEventListener('message', handleMessage);
    return function () {
      websocketConnection.removeEventListener('message', handleMessage);
    };
  }, [websocketConnection, currentUser]);
  return {
    collaborators: collaborators,
    myPosition: myPosition,
    setCollaborators: setCollaborators
  };
};

// Utility function for throttling
var throttle = function throttle(func, limit) {
  var inThrottle;
  return function () {
    var args = arguments;
    var context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(function () {
        return inThrottle = false;
      }, limit);
    }
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LiveCursors);

/***/ }),

/***/ 80344:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;




var Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Title;
var TextArea = antd__WEBPACK_IMPORTED_MODULE_3__/* .Input */ .pd.TextArea;
var CollaborationContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n"])));
var UserList = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  max-height: 200px;\n  overflow-y: auto;\n  margin-bottom: 16px;\n"])));
var CommentSection = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n"])));
var CommentList = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  flex: 1;\n  overflow-y: auto;\n  margin-bottom: 16px;\n  max-height: 300px;\n"])));
var CommentInput = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  border-top: 1px solid #f0f0f0;\n  padding-top: 12px;\n"])));
var OnlineIndicator = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: ", ";\n  display: inline-block;\n  margin-right: 8px;\n"])), function (props) {
  return props.online ? '#52c41a' : '#d9d9d9';
});
var CollaborationPanel = function CollaborationPanel(_ref) {
  var _ref$collaborators = _ref.collaborators,
    collaborators = _ref$collaborators === void 0 ? [] : _ref$collaborators,
    _ref$comments = _ref.comments,
    comments = _ref$comments === void 0 ? [] : _ref$comments,
    _ref$currentUser = _ref.currentUser,
    currentUser = _ref$currentUser === void 0 ? null : _ref$currentUser,
    _ref$onAddComment = _ref.onAddComment,
    onAddComment = _ref$onAddComment === void 0 ? function () {} : _ref$onAddComment,
    _ref$onResolveComment = _ref.onResolveComment,
    onResolveComment = _ref$onResolveComment === void 0 ? function () {} : _ref$onResolveComment,
    _ref$onDeleteComment = _ref.onDeleteComment,
    onDeleteComment = _ref$onDeleteComment === void 0 ? function () {} : _ref$onDeleteComment,
    _ref$onUserClick = _ref.onUserClick,
    onUserClick = _ref$onUserClick === void 0 ? function () {} : _ref$onUserClick,
    _ref$isConnected = _ref.isConnected,
    isConnected = _ref$isConnected === void 0 ? false : _ref$isConnected;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    newComment = _useState2[0],
    setNewComment = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('users'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    activeTab = _useState4[0],
    setActiveTab = _useState4[1];

  // Mock data if no props provided
  var mockCollaborators = collaborators.length > 0 ? collaborators : [{
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: null,
    online: true,
    lastSeen: new Date(),
    cursor: {
      x: 100,
      y: 200
    },
    currentComponent: 'button-1'
  }, {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    avatar: null,
    online: false,
    lastSeen: new Date(Date.now() - 300000),
    // 5 minutes ago
    cursor: null,
    currentComponent: null
  }];
  var mockComments = comments.length > 0 ? comments : [{
    id: 1,
    author: 'John Doe',
    content: 'This button needs to be larger for better accessibility.',
    timestamp: new Date(Date.now() - 3600000),
    // 1 hour ago
    componentId: 'button-1',
    resolved: false,
    replies: []
  }, {
    id: 2,
    author: 'Jane Smith',
    content: 'Great work on the layout! The spacing looks perfect.',
    timestamp: new Date(Date.now() - 1800000),
    // 30 minutes ago
    componentId: null,
    resolved: true,
    replies: [{
      id: 3,
      author: 'John Doe',
      content: 'Thanks! I spent a lot of time on the grid system.',
      timestamp: new Date(Date.now() - 1200000) // 20 minutes ago
    }]
  }];
  var handleAddComment = function handleAddComment() {
    if (newComment.trim()) {
      var comment = {
        id: Date.now(),
        author: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || 'Anonymous',
        content: newComment.trim(),
        timestamp: new Date(),
        componentId: null,
        resolved: false,
        replies: []
      };
      onAddComment(comment);
      setNewComment('');
    }
  };
  var handleKeyPress = function handleKeyPress(e) {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleAddComment();
    }
  };
  var formatTime = function formatTime(timestamp) {
    var now = new Date();
    var diff = now - timestamp;
    var minutes = Math.floor(diff / 60000);
    var hours = Math.floor(diff / 3600000);
    var days = Math.floor(diff / 86400000);
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return "".concat(minutes, "m ago");
    if (hours < 24) return "".concat(hours, "h ago");
    return "".concat(days, "d ago");
  };
  var renderUserList = function renderUserList() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(UserList, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
      level: 5
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .TeamOutlined */ .QM0, null), " Collaborators (", mockCollaborators.length, ")"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8, {
      size: "small",
      dataSource: mockCollaborators,
      renderItem: function renderItem(user) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8.Item, {
          style: {
            padding: '8px 0',
            cursor: 'pointer'
          },
          onClick: function onClick() {
            return onUserClick(user);
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8.Item.Meta, {
          avatar: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
            dot: user.online,
            color: user.online ? 'green' : 'default'
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Avatar */ .eu, {
            size: "small",
            src: user.avatar,
            icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .UserOutlined */ .qmv, null)
          })),
          title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
            style: {
              display: 'flex',
              alignItems: 'center'
            }
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(OnlineIndicator, {
            online: user.online
          }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
            strong: user.online
          }, user.name)),
          description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
            type: "secondary",
            style: {
              fontSize: '12px'
            }
          }, user.online ? user.currentComponent ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .EditOutlined */ .xjh, {
            style: {
              marginRight: 4
            }
          }), "Editing ", user.currentComponent) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .EyeOutlined */ .Om2, {
            style: {
              marginRight: 4
            }
          }), "Viewing") : "Last seen ".concat(formatTime(user.lastSeen)))
        }));
      }
    }));
  };
  var renderComments = function renderComments() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(CommentSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
      level: 5
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MessageOutlined */ .g4F, null), " Comments (", mockComments.length, ")"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(CommentList, null, mockComments.map(function (comment) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
        key: comment.id,
        size: "small",
        style: {
          marginBottom: 12
        },
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
          style: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Avatar */ .eu, {
          size: "small",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .UserOutlined */ .qmv, null)
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
          strong: true
        }, comment.author), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
          type: "secondary",
          style: {
            fontSize: '12px'
          }
        }, formatTime(comment.timestamp))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, !comment.resolved && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
          type: "text",
          size: "small",
          onClick: function onClick() {
            return onResolveComment(comment.id);
          }
        }, "Resolve"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
          type: "text",
          size: "small",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CloseOutlined */ .r$3, null),
          onClick: function onClick() {
            return onDeleteComment(comment.id);
          }
        })))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, comment.content), comment.componentId && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          marginTop: 8
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: "secondary",
        style: {
          fontSize: '11px'
        }
      }, "Component: ", comment.componentId)), comment.resolved && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          marginTop: 8
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: "success",
        style: {
          fontSize: '11px'
        }
      }, "\u2713 Resolved")), comment.replies && comment.replies.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          marginTop: 12,
          paddingLeft: 16,
          borderLeft: '2px solid #f0f0f0'
        }
      }, comment.replies.map(function (reply) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
          key: reply.id,
          style: {
            marginBottom: 8
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Avatar */ .eu, {
          size: "small",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .UserOutlined */ .qmv, null)
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
          strong: true,
          style: {
            fontSize: '12px'
          }
        }, reply.author), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
          type: "secondary",
          style: {
            fontSize: '11px'
          }
        }, formatTime(reply.timestamp))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
          style: {
            marginTop: 4,
            fontSize: '12px'
          }
        }, reply.content));
      })));
    }), mockComments.length === 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '20px',
        color: '#999'
      }
    }, "No comments yet. Start a conversation!")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(CommentInput, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TextArea, {
      value: newComment,
      onChange: function onChange(e) {
        return setNewComment(e.target.value);
      },
      onKeyPress: handleKeyPress,
      placeholder: "Add a comment... (Ctrl+Enter to send)",
      rows: 3,
      style: {
        marginBottom: 8
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '11px'
      }
    }, isConnected ? '🟢 Connected' : '🔴 Disconnected'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      type: "primary",
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SendOutlined */ .jnF, null),
      onClick: handleAddComment,
      disabled: !newComment.trim()
    }, "Send"))));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(CollaborationContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
    title: "Collaboration",
    size: "small",
    style: {
      height: '100%'
    },
    bodyStyle: {
      padding: '12px',
      height: 'calc(100% - 57px)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }
  }, renderUserList(), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Divider */ .cG, {
    style: {
      margin: '12px 0'
    }
  }), renderComments())));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CollaborationPanel);

/***/ }),

/***/ 83719:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5556);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var _hooks_useAIDesignSuggestions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(87169);
/* harmony import */ var _plugins_EnhancedAIPlugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(71538);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






var Panel = antd__WEBPACK_IMPORTED_MODULE_4__/* .Collapse */ .SD.Panel;
var Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title;

/**
 * AI Suggestions Panel Component
 * Contextual sidebar that appears based on user actions and app state
 */
var AISuggestionsPanel = function AISuggestionsPanel(_ref) {
  var visible = _ref.visible,
    onClose = _ref.onClose,
    _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$layouts = _ref.layouts,
    layouts = _ref$layouts === void 0 ? [] : _ref$layouts,
    _ref$selectedComponen = _ref.selectedComponent,
    selectedComponent = _ref$selectedComponen === void 0 ? null : _ref$selectedComponen,
    onApplyLayoutSuggestion = _ref.onApplyLayoutSuggestion,
    onApplyComponentCombination = _ref.onApplyComponentCombination,
    onComponentAdd = _ref.onComponentAdd,
    _ref$placement = _ref.placement,
    placement = _ref$placement === void 0 ? 'right' : _ref$placement,
    _ref$width = _ref.width,
    width = _ref$width === void 0 ? 400 : _ref$width,
    _ref$autoShow = _ref.autoShow,
    autoShow = _ref$autoShow === void 0 ? true : _ref$autoShow,
    _ref$showSettings = _ref.showSettings,
    showSettings = _ref$showSettings === void 0 ? true : _ref$showSettings;
  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    autoRefresh = _useState2[0],
    setAutoRefresh = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    showAdvanced = _useState4[0],
    setShowAdvanced = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      showLayoutSuggestions: true,
      showComponentCombinations: true,
      showAnalysis: true,
      autoHide: false
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    panelSettings = _useState6[0],
    setPanelSettings = _useState6[1];

  // AI suggestions hook
  var _useAIDesignSuggestio = (0,_hooks_useAIDesignSuggestions__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)({
      autoRefresh: autoRefresh,
      refreshInterval: 30000,
      enableCache: true,
      context: {
        selectedComponent: selectedComponent
      }
    }),
    suggestions = _useAIDesignSuggestio.suggestions,
    loading = _useAIDesignSuggestio.loading,
    error = _useAIDesignSuggestio.error,
    lastRefresh = _useAIDesignSuggestio.lastRefresh,
    loadSuggestions = _useAIDesignSuggestio.loadSuggestions,
    applyLayoutSuggestion = _useAIDesignSuggestio.applyLayoutSuggestion,
    applyComponentCombination = _useAIDesignSuggestio.applyComponentCombination,
    refresh = _useAIDesignSuggestio.refresh,
    clearError = _useAIDesignSuggestio.clearError,
    hasLayoutSuggestions = _useAIDesignSuggestio.hasLayoutSuggestions,
    hasCombinationSuggestions = _useAIDesignSuggestio.hasCombinationSuggestions,
    hasAnalysis = _useAIDesignSuggestio.hasAnalysis,
    isLoading = _useAIDesignSuggestio.isLoading,
    componentCount = _useAIDesignSuggestio.componentCount;

  // Auto-show panel when suggestions are available
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (autoShow && !visible && (hasLayoutSuggestions || hasCombinationSuggestions)) {
      // Don't auto-show immediately, give user time to work
      var timer = setTimeout(function () {
        if (componentCount > 2) {// Only auto-show after user has added some components
          // You would call a parent function here to show the panel
          // onAutoShow?.();
        }
      }, 5000);
      return function () {
        return clearTimeout(timer);
      };
    }
  }, [autoShow, visible, hasLayoutSuggestions, hasCombinationSuggestions, componentCount]);

  // Handle applying layout suggestion
  var handleApplyLayoutSuggestion = function handleApplyLayoutSuggestion(suggestion) {
    var success = applyLayoutSuggestion(suggestion);
    if (success && onApplyLayoutSuggestion) {
      onApplyLayoutSuggestion(suggestion);
    }
  };

  // Handle applying component combination
  var handleApplyComponentCombination = function handleApplyComponentCombination(suggestion) {
    var success = applyComponentCombination(suggestion);
    if (success && onApplyComponentCombination) {
      onApplyComponentCombination(suggestion);
    }
  };

  // Render panel header
  var renderHeader = function renderHeader() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '16px 24px',
        borderBottom: '1px solid #f0f0f0'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .RobotOutlined */ .J_h, {
      style: {
        fontSize: '20px',
        color: '#1890ff',
        marginRight: '8px'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
      level: 4,
      style: {
        margin: 0
      }
    }, "AI Assistant"), isLoading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Badge */ .Ex, {
      status: "processing",
      style: {
        marginLeft: '8px'
      },
      title: "Analyzing your app..."
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, showSettings && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: "Settings"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null),
      onClick: function onClick() {
        return setShowAdvanced(!showAdvanced);
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: "Refresh suggestions"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null),
      onClick: refresh,
      loading: isLoading
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      type: "text",
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseOutlined */ .r$3, null),
      onClick: onClose
    })));
  };

  // Render settings panel
  var renderSettings = function renderSettings() {
    return showAdvanced && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
      size: "small",
      style: {
        margin: '16px',
        marginTop: 0
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, "Auto-refresh suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
      size: "small",
      checked: autoRefresh,
      onChange: setAutoRefresh
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, "Show layout suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
      size: "small",
      checked: panelSettings.showLayoutSuggestions,
      onChange: function onChange(checked) {
        return setPanelSettings(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            showLayoutSuggestions: checked
          });
        });
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, "Show component combinations"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
      size: "small",
      checked: panelSettings.showComponentCombinations,
      onChange: function onChange(checked) {
        return setPanelSettings(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            showComponentCombinations: checked
          });
        });
      }
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, "Show analysis"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
      size: "small",
      checked: panelSettings.showAnalysis,
      onChange: function onChange(checked) {
        return setPanelSettings(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            showAnalysis: checked
          });
        });
      }
    }))));
  };

  // Render quick stats
  var renderQuickStats = function renderQuickStats() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
      size: "small",
      style: {
        margin: '16px',
        marginTop: showAdvanced ? '8px' : '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: '1fr 1fr 1fr',
        gap: '8px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1890ff'
      }
    }, suggestions.layout.length), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '12px',
        color: '#666'
      }
    }, "Layouts")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#52c41a'
      }
    }, suggestions.combinations.length), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '12px',
        color: '#666'
      }
    }, "Combos")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#722ed1'
      }
    }, componentCount), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        fontSize: '12px',
        color: '#666'
      }
    }, "Components"))), lastRefresh && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        textAlign: 'center',
        marginTop: '8px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '11px'
      }
    }, "Last updated: ", lastRefresh.toLocaleTimeString())));
  };

  // Render suggestions content
  var renderSuggestionsContent = function renderSuggestionsContent() {
    if (componentCount === 0) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          padding: '32px 16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Empty */ .Sv, {
        image: antd__WEBPACK_IMPORTED_MODULE_4__/* .Empty */ .Sv.PRESENTED_IMAGE_SIMPLE,
        description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", null, "Start building your app!", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), "Add components to get AI suggestions.")
      }));
    }
    if (!hasLayoutSuggestions && !hasCombinationSuggestions && !hasAnalysis) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          padding: '32px 16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Empty */ .Sv, {
        image: antd__WEBPACK_IMPORTED_MODULE_4__/* .Empty */ .Sv.PRESENTED_IMAGE_SIMPLE,
        description: "No suggestions available"
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
        type: "primary",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null),
        onClick: refresh,
        style: {
          marginTop: '16px'
        }
      }, "Generate Suggestions"));
    }
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        padding: '0 16px 16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_plugins_EnhancedAIPlugin__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {
      components: components,
      layouts: layouts,
      selectedComponent: selectedComponent,
      onApplyLayoutSuggestion: handleApplyLayoutSuggestion,
      onApplyComponentCombination: handleApplyComponentCombination,
      onComponentAdd: onComponentAdd,
      context: {
        selectedComponent: selectedComponent
      }
    }));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: null,
    placement: placement,
    width: width,
    onClose: onClose,
    open: visible,
    headerStyle: {
      display: 'none'
    },
    bodyStyle: {
      padding: 0
    },
    className: "ai-suggestions-panel"
  }, renderHeader(), renderSettings(), renderQuickStats(), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      height: 'calc(100vh - 200px)',
      overflowY: 'auto',
      paddingBottom: '16px'
    }
  }, renderSuggestionsContent()));
};
AISuggestionsPanel.propTypes = {
  visible: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool).isRequired,
  onClose: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func).isRequired,
  components: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().array),
  layouts: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().array),
  selectedComponent: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object),
  onApplyLayoutSuggestion: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func),
  onApplyComponentCombination: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func),
  onComponentAdd: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().func),
  placement: prop_types__WEBPACK_IMPORTED_MODULE_3___default().oneOf(['left', 'right', 'top', 'bottom']),
  width: prop_types__WEBPACK_IMPORTED_MODULE_3___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_3___default().number), (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string)]),
  autoShow: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),
  showSettings: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AISuggestionsPanel);

/***/ })

}]);