"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[7435],{

/***/ 37435:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(83590);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(63710);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(70572);
/* harmony import */ var _styles_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(57749);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;








var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU.TabPane;

// Styled components
var ProfileContainer = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 24px;\n  max-width: 800px;\n  margin: 0 auto;\n"])));
var ProfileHeader = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 24px;\n  \n  @media (max-width: 576px) {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n"])));
var ProfileAvatar = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Avatar */ .eu)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-right: 24px;\n  background-color: ", ";\n  \n  @media (max-width: 576px) {\n    margin-right: 0;\n    margin-bottom: 16px;\n  }\n"])), function (props) {
  return props.theme.colorPalette.primary;
});
var ProfileInfo = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  flex: 1;\n"])));
var ProfileForm = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"])));

/**
 * ProfilePage component
 * Allows users to view and edit their profile
 */
var ProfilePage = function ProfilePage() {
  var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__/* .useAuth */ .As)(),
    user = _useAuth.user;
  var _useTranslation = (0,react_i18next__WEBPACK_IMPORTED_MODULE_8__/* .useTranslation */ .Bd)(),
    t = _useTranslation.t;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    success = _useState6[0],
    setSuccess = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('1'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    activeTab = _useState8[0],
    setActiveTab = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    editMode = _useState0[0],
    setEditMode = _useState0[1];

  // Handle profile update
  var handleProfileUpdate = /*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(values) {
      var _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setLoading(true);
            setError(null);
            setSuccess(null);
            _context.prev = 1;
            _context.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 1000);
            });
          case 2:
            setSuccess(t('profile.updateSuccess'));
            setEditMode(false);
            _context.next = 4;
            break;
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            setError(_t.message || t('profile.updateError'));
          case 4:
            _context.prev = 4;
            setLoading(false);
            return _context.finish(4);
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3, 4, 5]]);
    }));
    return function handleProfileUpdate(_x) {
      return _ref.apply(this, arguments);
    };
  }();

  // Handle password change
  var handlePasswordChange = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(values) {
      var _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            setLoading(true);
            setError(null);
            setSuccess(null);
            _context2.prev = 1;
            _context2.next = 2;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 1000);
            });
          case 2:
            setSuccess(t('profile.passwordChangeSuccess'));

            // Reset form
            passwordForm.resetFields();
            _context2.next = 4;
            break;
          case 3:
            _context2.prev = 3;
            _t2 = _context2["catch"](1);
            setError(_t2.message || t('profile.passwordChangeError'));
          case 4:
            _context2.prev = 4;
            setLoading(false);
            return _context2.finish(4);
          case 5:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 3, 4, 5]]);
    }));
    return function handlePasswordChange(_x2) {
      return _ref2.apply(this, arguments);
    };
  }();

  // Handle avatar upload
  var handleAvatarUpload = function handleAvatarUpload(info) {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success(t('profile.avatarUploadSuccess'));
    } else if (info.file.status === 'error') {
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error(t('profile.avatarUploadError'));
    }
  };

  // Create form instances
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm, 1),
    profileForm = _Form$useForm2[0];
  var _Form$useForm3 = antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.useForm(),
    _Form$useForm4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm3, 1),
    passwordForm = _Form$useForm4[0];

  // Initialize profile form with user data
  react__WEBPACK_IMPORTED_MODULE_4__.useEffect(function () {
    if (user) {
      profileForm.setFieldsValue({
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone
      });
    }
  }, [user, profileForm]);
  if (!user) {
    return null;
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ProfileContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_styles_components__WEBPACK_IMPORTED_MODULE_10__/* .PageTitle */ .sT, {
    level: 2
  }, t('profile.title')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ProfileHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ProfileAvatar, {
    size: 100,
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UserOutlined */ .qmv, null),
    src: user.avatar
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ProfileInfo, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 3
  }, user.firstName, " ", user.lastName), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary"
  }, user.email), user.roles && user.roles.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: '8px'
    }
  }, user.roles.map(function (role) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_styles_components__WEBPACK_IMPORTED_MODULE_10__/* .StyledTag */ .ih, {
      key: role,
      color: "blue"
    }, role);
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UserOutlined */ .qmv, null), t('profile.tabs.profile')),
    key: "1"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_styles_components__WEBPACK_IMPORTED_MODULE_10__/* .StyledCard */ .ee, {
    title: t('profile.profileInfo'),
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      type: editMode ? 'primary' : 'default',
      icon: editMode ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SaveOutlined */ .ylI, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EditOutlined */ .xjh, null),
      onClick: function onClick() {
        if (editMode) {
          profileForm.submit();
        } else {
          setEditMode(true);
        }
      }
    }, editMode ? t('profile.save') : t('profile.edit'))
  }, error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: t('profile.error'),
    description: error,
    type: "error",
    showIcon: true,
    style: {
      marginBottom: '24px'
    }
  }), success && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: t('profile.success'),
    description: success,
    type: "success",
    showIcon: true,
    style: {
      marginBottom: '24px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ProfileForm, {
    form: profileForm,
    layout: "vertical",
    onFinish: handleProfileUpdate,
    disabled: !editMode
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('profile.username'),
    name: "username",
    rules: [{
      required: true,
      message: t('profile.usernameRequired')
    }, {
      min: 3,
      message: t('profile.usernameTooShort')
    }, {
      max: 20,
      message: t('profile.usernameTooLong')
    }, {
      pattern: /^[a-zA-Z0-9_]+$/,
      message: t('profile.usernameInvalid')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UserOutlined */ .qmv, null),
    placeholder: t('profile.usernamePlaceholder')
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('profile.email'),
    name: "email",
    rules: [{
      required: true,
      message: t('profile.emailRequired')
    }, {
      type: 'email',
      message: t('profile.emailInvalid')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .MailOutlined */ ._Wu, null),
    placeholder: t('profile.emailPlaceholder')
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('profile.firstName'),
    name: "firstName",
    rules: [{
      required: true,
      message: t('profile.firstNameRequired')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    placeholder: t('profile.firstNamePlaceholder')
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('profile.lastName'),
    name: "lastName",
    rules: [{
      required: true,
      message: t('profile.lastNameRequired')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    placeholder: t('profile.lastNamePlaceholder')
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('profile.phone'),
    name: "phone",
    rules: [{
      required: true,
      message: t('profile.phoneRequired')
    }, {
      pattern: /^\+?[0-9]{10,15}$/,
      message: t('profile.phoneInvalid')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PhoneOutlined */ .NW5, null),
    placeholder: t('profile.phonePlaceholder')
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LockOutlined */ .sXv, null), t('profile.tabs.security')),
    key: "2"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_styles_components__WEBPACK_IMPORTED_MODULE_10__/* .StyledCard */ .ee, {
    title: t('profile.changePassword')
  }, error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: t('profile.error'),
    description: error,
    type: "error",
    showIcon: true,
    style: {
      marginBottom: '24px'
    }
  }), success && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: t('profile.success'),
    description: success,
    type: "success",
    showIcon: true,
    style: {
      marginBottom: '24px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ProfileForm, {
    form: passwordForm,
    layout: "vertical",
    onFinish: handlePasswordChange
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('profile.currentPassword'),
    name: "currentPassword",
    rules: [{
      required: true,
      message: t('profile.currentPasswordRequired')
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LockOutlined */ .sXv, null),
    placeholder: t('profile.currentPasswordPlaceholder')
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('profile.newPassword'),
    name: "newPassword",
    rules: [{
      required: true,
      message: t('profile.newPasswordRequired')
    }, {
      min: 8,
      message: t('profile.passwordTooShort')
    }, {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,
      message: t('profile.passwordInvalid')
    }],
    hasFeedback: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LockOutlined */ .sXv, null),
    placeholder: t('profile.newPasswordPlaceholder')
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: t('profile.confirmPassword'),
    name: "confirmPassword",
    dependencies: ['newPassword'],
    hasFeedback: true,
    rules: [{
      required: true,
      message: t('profile.confirmPasswordRequired')
    }, function (_ref3) {
      var getFieldValue = _ref3.getFieldValue;
      return {
        validator: function validator(_, value) {
          if (!value || getFieldValue('newPassword') === value) {
            return Promise.resolve();
          }
          return Promise.reject(new Error(t('profile.passwordsMismatch')));
        }
      };
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.Password, {
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LockOutlined */ .sXv, null),
    placeholder: t('profile.confirmPasswordPlaceholder')
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    htmlType: "submit",
    loading: loading
  }, t('profile.changePassword')))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UploadOutlined */ .qvO, null), t('profile.tabs.avatar')),
    key: "3"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_styles_components__WEBPACK_IMPORTED_MODULE_10__/* .StyledCard */ .ee, {
    title: t('profile.changeAvatar')
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      textAlign: 'center',
      marginBottom: '24px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ProfileAvatar, {
    size: 150,
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UserOutlined */ .qmv, null),
    src: user.avatar
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Upload */ ._O, {
    name: "avatar",
    listType: "picture",
    showUploadList: false,
    action: "/api/upload" // TODO: Replace with actual upload endpoint
    ,
    onChange: handleAvatarUpload
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UploadOutlined */ .qvO, null)
  }, t('profile.uploadAvatar')))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfilePage);

/***/ })

}]);