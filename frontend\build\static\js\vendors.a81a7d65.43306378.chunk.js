"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[645],{

/***/ 3057:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


var refractorRuby = __webpack_require__(54264)
var refractorMarkupTemplating = __webpack_require__(56876)
module.exports = erb
erb.displayName = 'erb'
erb.aliases = []
function erb(Prism) {
  Prism.register(refractorRuby)
  Prism.register(refractorMarkupTemplating)
  ;(function (Prism) {
    Prism.languages.erb = {
      delimiter: {
        pattern: /^(\s*)<%=?|%>(?=\s*$)/,
        lookbehind: true,
        alias: 'punctuation'
      },
      ruby: {
        pattern: /\s*\S[\s\S]*/,
        alias: 'language-ruby',
        inside: Prism.languages.ruby
      }
    }
    Prism.hooks.add('before-tokenize', function (env) {
      var erbPattern =
        /<%=?(?:[^\r\n]|[\r\n](?!=begin)|[\r\n]=begin\s(?:[^\r\n]|[\r\n](?!=end))*[\r\n]=end)+?%>/g
      Prism.languages['markup-templating'].buildPlaceholders(
        env,
        'erb',
        erbPattern
      )
    })
    Prism.hooks.add('after-tokenize', function (env) {
      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'erb')
    })
  })(Prism)
}


/***/ }),

/***/ 12551:
/***/ ((module) => {



module.exports = diff
diff.displayName = 'diff'
diff.aliases = []
function diff(Prism) {
  ;(function (Prism) {
    Prism.languages.diff = {
      coord: [
        // Match all kinds of coord lines (prefixed by "+++", "---" or "***").
        /^(?:\*{3}|-{3}|\+{3}).*$/m, // Match "@@ ... @@" coord lines in unified diff.
        /^@@.*@@$/m, // Match coord lines in normal diff (starts with a number).
        /^\d.*$/m
      ] // deleted, inserted, unchanged, diff
    }
    /**
     * A map from the name of a block to its line prefix.
     *
     * @type {Object<string, string>}
     */
    var PREFIXES = {
      'deleted-sign': '-',
      'deleted-arrow': '<',
      'inserted-sign': '+',
      'inserted-arrow': '>',
      unchanged: ' ',
      diff: '!'
    } // add a token for each prefix
    Object.keys(PREFIXES).forEach(function (name) {
      var prefix = PREFIXES[name]
      var alias = []
      if (!/^\w+$/.test(name)) {
        // "deleted-sign" -> "deleted"
        alias.push(/\w+/.exec(name)[0])
      }
      if (name === 'diff') {
        alias.push('bold')
      }
      Prism.languages.diff[name] = {
        pattern: RegExp(
          '^(?:[' + prefix + '].*(?:\r\n?|\n|(?![\\s\\S])))+',
          'm'
        ),
        alias: alias,
        inside: {
          line: {
            pattern: /(.)(?=[\s\S]).*(?:\r\n?|\n)?/,
            lookbehind: true
          },
          prefix: {
            pattern: /[\s\S]/,
            alias: /\w+/.exec(name)[0]
          }
        }
      }
    }) // make prefixes available to Diff plugin
    Object.defineProperty(Prism.languages.diff, 'PREFIXES', {
      value: PREFIXES
    })
  })(Prism)
}


/***/ }),

/***/ 21711:
/***/ ((module) => {



module.exports = editorconfig
editorconfig.displayName = 'editorconfig'
editorconfig.aliases = []
function editorconfig(Prism) {
  Prism.languages.editorconfig = {
    // https://editorconfig-specification.readthedocs.io
    comment: /[;#].*/,
    section: {
      pattern: /(^[ \t]*)\[.+\]/m,
      lookbehind: true,
      alias: 'selector',
      inside: {
        regex: /\\\\[\[\]{},!?.*]/,
        // Escape special characters with '\\'
        operator: /[!?]|\.\.|\*{1,2}/,
        punctuation: /[\[\]{},]/
      }
    },
    key: {
      pattern: /(^[ \t]*)[^\s=]+(?=[ \t]*=)/m,
      lookbehind: true,
      alias: 'attr-name'
    },
    value: {
      pattern: /=.*/,
      alias: 'attr-value',
      inside: {
        punctuation: /^=/
      }
    }
  }
}


/***/ }),

/***/ 22339:
/***/ ((module) => {



module.exports = erlang
erlang.displayName = 'erlang'
erlang.aliases = []
function erlang(Prism) {
  Prism.languages.erlang = {
    comment: /%.+/,
    string: {
      pattern: /"(?:\\.|[^\\"\r\n])*"/,
      greedy: true
    },
    'quoted-function': {
      pattern: /'(?:\\.|[^\\'\r\n])+'(?=\()/,
      alias: 'function'
    },
    'quoted-atom': {
      pattern: /'(?:\\.|[^\\'\r\n])+'/,
      alias: 'atom'
    },
    boolean: /\b(?:false|true)\b/,
    keyword: /\b(?:after|case|catch|end|fun|if|of|receive|try|when)\b/,
    number: [
      /\$\\?./,
      /\b\d+#[a-z0-9]+/i,
      /(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i
    ],
    function: /\b[a-z][\w@]*(?=\()/,
    variable: {
      // Look-behind is used to prevent wrong highlighting of atoms containing "@"
      pattern: /(^|[^@])(?:\b|\?)[A-Z_][\w@]*/,
      lookbehind: true
    },
    operator: [
      /[=\/<>:]=|=[:\/]=|\+\+?|--?|[=*\/!]|\b(?:and|andalso|band|bnot|bor|bsl|bsr|bxor|div|not|or|orelse|rem|xor)\b/,
      {
        // We don't want to match <<
        pattern: /(^|[^<])<(?!<)/,
        lookbehind: true
      },
      {
        // We don't want to match >>
        pattern: /(^|[^>])>(?!>)/,
        lookbehind: true
      }
    ],
    atom: /\b[a-z][\w@]*/,
    punctuation: /[()[\]{}:;,.#|]|<<|>>/
  }
}


/***/ }),

/***/ 23507:
/***/ ((module) => {



module.exports = dnsZoneFile
dnsZoneFile.displayName = 'dnsZoneFile'
dnsZoneFile.aliases = []
function dnsZoneFile(Prism) {
  Prism.languages['dns-zone-file'] = {
    comment: /;.*/,
    string: {
      pattern: /"(?:\\.|[^"\\\r\n])*"/,
      greedy: true
    },
    variable: [
      {
        pattern: /(^\$ORIGIN[ \t]+)\S+/m,
        lookbehind: true
      },
      {
        pattern: /(^|\s)@(?=\s|$)/,
        lookbehind: true
      }
    ],
    keyword: /^\$(?:INCLUDE|ORIGIN|TTL)(?=\s|$)/m,
    class: {
      // https://tools.ietf.org/html/rfc1035#page-13
      pattern: /(^|\s)(?:CH|CS|HS|IN)(?=\s|$)/,
      lookbehind: true,
      alias: 'keyword'
    },
    type: {
      // https://en.wikipedia.org/wiki/List_of_DNS_record_types
      pattern:
        /(^|\s)(?:A|A6|AAAA|AFSDB|APL|ATMA|CAA|CDNSKEY|CDS|CERT|CNAME|DHCID|DLV|DNAME|DNSKEY|DS|EID|GID|GPOS|HINFO|HIP|IPSECKEY|ISDN|KEY|KX|LOC|MAILA|MAILB|MB|MD|MF|MG|MINFO|MR|MX|NAPTR|NB|NBSTAT|NIMLOC|NINFO|NS|NSAP|NSAP-PTR|NSEC|NSEC3|NSEC3PARAM|NULL|NXT|OPENPGPKEY|PTR|PX|RKEY|RP|RRSIG|RT|SIG|SINK|SMIMEA|SOA|SPF|SRV|SSHFP|TA|TKEY|TLSA|TSIG|TXT|UID|UINFO|UNSPEC|URI|WKS|X25)(?=\s|$)/,
      lookbehind: true,
      alias: 'keyword'
    },
    punctuation: /[()]/
  }
  Prism.languages['dns-zone'] = Prism.languages['dns-zone-file']
}


/***/ }),

/***/ 30325:
/***/ ((module) => {



module.exports = $false
$false.displayName = '$false'
$false.aliases = []
function $false(Prism) {
  ;(function (Prism) {
    /**
     * Based on the manual by Wouter van Oortmerssen.
     *
     * @see {@link https://github.com/PrismJS/prism/issues/2801#issue-829717504}
     */
    Prism.languages['false'] = {
      comment: {
        pattern: /\{[^}]*\}/
      },
      string: {
        pattern: /"[^"]*"/,
        greedy: true
      },
      'character-code': {
        pattern: /'(?:[^\r]|\r\n?)/,
        alias: 'number'
      },
      'assembler-code': {
        pattern: /\d+`/,
        alias: 'important'
      },
      number: /\d+/,
      operator: /[-!#$%&'*+,./:;=>?@\\^_`|~ßø]/,
      punctuation: /\[|\]/,
      variable: /[a-z]/,
      'non-standard': {
        pattern: /[()<BDO®]/,
        alias: 'bold'
      }
    }
  })(Prism)
}


/***/ }),

/***/ 42986:
/***/ ((module) => {



module.exports = excelFormula
excelFormula.displayName = 'excelFormula'
excelFormula.aliases = []
function excelFormula(Prism) {
  Prism.languages['excel-formula'] = {
    comment: {
      pattern: /(\bN\(\s*)"(?:[^"]|"")*"(?=\s*\))/i,
      lookbehind: true,
      greedy: true
    },
    string: {
      pattern: /"(?:[^"]|"")*"(?!")/,
      greedy: true
    },
    reference: {
      // https://www.ablebits.com/office-addins-blog/2015/12/08/excel-reference-another-sheet-workbook/
      // Sales!B2
      // 'Winter sales'!B2
      // [Sales.xlsx]Jan!B2:B5
      // D:\Reports\[Sales.xlsx]Jan!B2:B5
      // '[Sales.xlsx]Jan sales'!B2:B5
      // 'D:\Reports\[Sales.xlsx]Jan sales'!B2:B5
      pattern:
        /(?:'[^']*'|(?:[^\s()[\]{}<>*?"';,$&]*\[[^^\s()[\]{}<>*?"']+\])?\w+)!/,
      greedy: true,
      alias: 'string',
      inside: {
        operator: /!$/,
        punctuation: /'/,
        sheet: {
          pattern: /[^[\]]+$/,
          alias: 'function'
        },
        file: {
          pattern: /\[[^[\]]+\]$/,
          inside: {
            punctuation: /[[\]]/
          }
        },
        path: /[\s\S]+/
      }
    },
    'function-name': {
      pattern: /\b[A-Z]\w*(?=\()/i,
      alias: 'keyword'
    },
    range: {
      pattern:
        /\$?\b(?:[A-Z]+\$?\d+:\$?[A-Z]+\$?\d+|[A-Z]+:\$?[A-Z]+|\d+:\$?\d+)\b/i,
      alias: 'property',
      inside: {
        operator: /:/,
        cell: /\$?[A-Z]+\$?\d+/i,
        column: /\$?[A-Z]+/i,
        row: /\$?\d+/
      }
    },
    cell: {
      // Excel is case insensitive, so the string "foo1" could be either a variable or a cell.
      // To combat this, we match cells case insensitive, if the contain at least one "$", and case sensitive otherwise.
      pattern: /\b[A-Z]+\d+\b|\$[A-Za-z]+\$?\d+\b|\b[A-Za-z]+\$\d+\b/,
      alias: 'property'
    },
    number: /(?:\b\d+(?:\.\d+)?|\B\.\d+)(?:e[+-]?\d+)?\b/i,
    boolean: /\b(?:FALSE|TRUE)\b/i,
    operator: /[-+*/^%=&,]|<[=>]?|>=?/,
    punctuation: /[[\]();{}|]/
  }
  Prism.languages['xlsx'] = Prism.languages['xls'] =
    Prism.languages['excel-formula']
}


/***/ }),

/***/ 47603:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


var refractorLua = __webpack_require__(50347)
var refractorMarkupTemplating = __webpack_require__(56876)
module.exports = etlua
etlua.displayName = 'etlua'
etlua.aliases = []
function etlua(Prism) {
  Prism.register(refractorLua)
  Prism.register(refractorMarkupTemplating)
  ;(function (Prism) {
    Prism.languages.etlua = {
      delimiter: {
        pattern: /^<%[-=]?|-?%>$/,
        alias: 'punctuation'
      },
      'language-lua': {
        pattern: /[\s\S]+/,
        inside: Prism.languages.lua
      }
    }
    Prism.hooks.add('before-tokenize', function (env) {
      var pattern = /<%[\s\S]+?%>/g
      Prism.languages['markup-templating'].buildPlaceholders(
        env,
        'etlua',
        pattern
      )
    })
    Prism.hooks.add('after-tokenize', function (env) {
      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'etlua')
    })
  })(Prism)
}


/***/ }),

/***/ 51371:
/***/ ((module) => {



module.exports = elixir
elixir.displayName = 'elixir'
elixir.aliases = []
function elixir(Prism) {
  Prism.languages.elixir = {
    doc: {
      pattern:
        /@(?:doc|moduledoc)\s+(?:("""|''')[\s\S]*?\1|("|')(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2)/,
      inside: {
        attribute: /^@\w+/,
        string: /['"][\s\S]+/
      }
    },
    comment: {
      pattern: /#.*/,
      greedy: true
    },
    // ~r"""foo""" (multi-line), ~r'''foo''' (multi-line), ~r/foo/, ~r|foo|, ~r"foo", ~r'foo', ~r(foo), ~r[foo], ~r{foo}, ~r<foo>
    regex: {
      pattern:
        /~[rR](?:("""|''')(?:\\[\s\S]|(?!\1)[^\\])+\1|([\/|"'])(?:\\.|(?!\2)[^\\\r\n])+\2|\((?:\\.|[^\\)\r\n])+\)|\[(?:\\.|[^\\\]\r\n])+\]|\{(?:\\.|[^\\}\r\n])+\}|<(?:\\.|[^\\>\r\n])+>)[uismxfr]*/,
      greedy: true
    },
    string: [
      {
        // ~s"""foo""" (multi-line), ~s'''foo''' (multi-line), ~s/foo/, ~s|foo|, ~s"foo", ~s'foo', ~s(foo), ~s[foo], ~s{foo} (with interpolation care), ~s<foo>
        pattern:
          /~[cCsSwW](?:("""|''')(?:\\[\s\S]|(?!\1)[^\\])+\1|([\/|"'])(?:\\.|(?!\2)[^\\\r\n])+\2|\((?:\\.|[^\\)\r\n])+\)|\[(?:\\.|[^\\\]\r\n])+\]|\{(?:\\.|#\{[^}]+\}|#(?!\{)|[^#\\}\r\n])+\}|<(?:\\.|[^\\>\r\n])+>)[csa]?/,
        greedy: true,
        inside: {
          // See interpolation below
        }
      },
      {
        pattern: /("""|''')[\s\S]*?\1/,
        greedy: true,
        inside: {
          // See interpolation below
        }
      },
      {
        // Multi-line strings are allowed
        pattern: /("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,
        greedy: true,
        inside: {
          // See interpolation below
        }
      }
    ],
    atom: {
      // Look-behind prevents bad highlighting of the :: operator
      pattern: /(^|[^:]):\w+/,
      lookbehind: true,
      alias: 'symbol'
    },
    module: {
      pattern: /\b[A-Z]\w*\b/,
      alias: 'class-name'
    },
    // Look-ahead prevents bad highlighting of the :: operator
    'attr-name': /\b\w+\??:(?!:)/,
    argument: {
      // Look-behind prevents bad highlighting of the && operator
      pattern: /(^|[^&])&\d+/,
      lookbehind: true,
      alias: 'variable'
    },
    attribute: {
      pattern: /@\w+/,
      alias: 'variable'
    },
    function: /\b[_a-zA-Z]\w*[?!]?(?:(?=\s*(?:\.\s*)?\()|(?=\/\d))/,
    number: /\b(?:0[box][a-f\d_]+|\d[\d_]*)(?:\.[\d_]+)?(?:e[+-]?[\d_]+)?\b/i,
    keyword:
      /\b(?:after|alias|and|case|catch|cond|def(?:callback|delegate|exception|impl|macro|module|n|np|p|protocol|struct)?|do|else|end|fn|for|if|import|not|or|quote|raise|require|rescue|try|unless|unquote|use|when)\b/,
    boolean: /\b(?:false|nil|true)\b/,
    operator: [
      /\bin\b|&&?|\|[|>]?|\\\\|::|\.\.\.?|\+\+?|-[->]?|<[-=>]|>=|!==?|\B!|=(?:==?|[>~])?|[*\/^]/,
      {
        // We don't want to match <<
        pattern: /([^<])<(?!<)/,
        lookbehind: true
      },
      {
        // We don't want to match >>
        pattern: /([^>])>(?!>)/,
        lookbehind: true
      }
    ],
    punctuation: /<<|>>|[.,%\[\]{}()]/
  }
  Prism.languages.elixir.string.forEach(function (o) {
    o.inside = {
      interpolation: {
        pattern: /#\{[^}]+\}/,
        inside: {
          delimiter: {
            pattern: /^#\{|\}$/,
            alias: 'punctuation'
          },
          rest: Prism.languages.elixir
        }
      }
    }
  })
}


/***/ }),

/***/ 53231:
/***/ ((module) => {



module.exports = eiffel
eiffel.displayName = 'eiffel'
eiffel.aliases = []
function eiffel(Prism) {
  Prism.languages.eiffel = {
    comment: /--.*/,
    string: [
      // Aligned-verbatim-strings
      {
        pattern: /"([^[]*)\[[\s\S]*?\]\1"/,
        greedy: true
      }, // Non-aligned-verbatim-strings
      {
        pattern: /"([^{]*)\{[\s\S]*?\}\1"/,
        greedy: true
      }, // Single-line string
      {
        pattern: /"(?:%(?:(?!\n)\s)*\n\s*%|%\S|[^%"\r\n])*"/,
        greedy: true
      }
    ],
    // normal char | special char | char code
    char: /'(?:%.|[^%'\r\n])+'/,
    keyword:
      /\b(?:across|agent|alias|all|and|as|assign|attached|attribute|check|class|convert|create|Current|debug|deferred|detachable|do|else|elseif|end|ensure|expanded|export|external|feature|from|frozen|if|implies|inherit|inspect|invariant|like|local|loop|not|note|obsolete|old|once|or|Precursor|redefine|rename|require|rescue|Result|retry|select|separate|some|then|undefine|until|variant|Void|when|xor)\b/i,
    boolean: /\b(?:False|True)\b/i,
    // Convention: class-names are always all upper-case characters
    'class-name': /\b[A-Z][\dA-Z_]*\b/,
    number: [
      // hexa | octal | bin
      /\b0[xcb][\da-f](?:_*[\da-f])*\b/i, // Decimal
      /(?:\b\d(?:_*\d)*)?\.(?:(?:\d(?:_*\d)*)?e[+-]?)?\d(?:_*\d)*\b|\b\d(?:_*\d)*\b\.?/i
    ],
    punctuation: /:=|<<|>>|\(\||\|\)|->|\.(?=\w)|[{}[\];(),:?]/,
    operator: /\\\\|\|\.\.\||\.\.|\/[~\/=]?|[><]=?|[-+*^=~]/
  }
}


/***/ }),

/***/ 53558:
/***/ ((module) => {



module.exports = elm
elm.displayName = 'elm'
elm.aliases = []
function elm(Prism) {
  Prism.languages.elm = {
    comment: /--.*|\{-[\s\S]*?-\}/,
    char: {
      pattern:
        /'(?:[^\\'\r\n]|\\(?:[abfnrtv\\']|\d+|x[0-9a-fA-F]+|u\{[0-9a-fA-F]+\}))'/,
      greedy: true
    },
    string: [
      {
        // Multiline strings are wrapped in triple ". Quotes may appear unescaped.
        pattern: /"""[\s\S]*?"""/,
        greedy: true
      },
      {
        pattern: /"(?:[^\\"\r\n]|\\.)*"/,
        greedy: true
      }
    ],
    'import-statement': {
      // The imported or hidden names are not included in this import
      // statement. This is because we want to highlight those exactly like
      // we do for the names in the program.
      pattern:
        /(^[\t ]*)import\s+[A-Z]\w*(?:\.[A-Z]\w*)*(?:\s+as\s+(?:[A-Z]\w*)(?:\.[A-Z]\w*)*)?(?:\s+exposing\s+)?/m,
      lookbehind: true,
      inside: {
        keyword: /\b(?:as|exposing|import)\b/
      }
    },
    keyword:
      /\b(?:alias|as|case|else|exposing|if|in|infixl|infixr|let|module|of|then|type)\b/,
    // These are builtin variables only. Constructors are highlighted later as a constant.
    builtin:
      /\b(?:abs|acos|always|asin|atan|atan2|ceiling|clamp|compare|cos|curry|degrees|e|flip|floor|fromPolar|identity|isInfinite|isNaN|logBase|max|min|negate|never|not|pi|radians|rem|round|sin|sqrt|tan|toFloat|toPolar|toString|truncate|turns|uncurry|xor)\b/,
    // decimal integers and floating point numbers | hexadecimal integers
    number: /\b(?:\d+(?:\.\d+)?(?:e[+-]?\d+)?|0x[0-9a-f]+)\b/i,
    // Most of this is needed because of the meaning of a single '.'.
    // If it stands alone freely, it is the function composition.
    // It may also be a separator between a module name and an identifier => no
    // operator. If it comes together with other special characters it is an
    // operator too.
    // Valid operator characters in 0.18: +-/*=.$<>:&|^?%#@~!
    // Ref: https://groups.google.com/forum/#!msg/elm-dev/0AHSnDdkSkQ/E0SVU70JEQAJ
    operator: /\s\.\s|[+\-/*=.$<>:&|^?%#@~!]{2,}|[+\-/*=$<>:&|^?%#@~!]/,
    // In Elm, nearly everything is a variable, do not highlight these.
    hvariable: /\b(?:[A-Z]\w*\.)*[a-z]\w*\b/,
    constant: /\b(?:[A-Z]\w*\.)*[A-Z]\w*\b/,
    punctuation: /[{}[\]|(),.:]/
  }
}


/***/ }),

/***/ 56489:
/***/ ((module) => {



module.exports = dot
dot.displayName = 'dot'
dot.aliases = ['gv']
function dot(Prism) {
  // https://www.graphviz.org/doc/info/lang.html
  ;(function (Prism) {
    var ID =
      '(?:' +
      [
        // an identifier
        /[a-zA-Z_\x80-\uFFFF][\w\x80-\uFFFF]*/.source, // a number
        /-?(?:\.\d+|\d+(?:\.\d*)?)/.source, // a double-quoted string
        /"[^"\\]*(?:\\[\s\S][^"\\]*)*"/.source, // HTML-like string
        /<(?:[^<>]|(?!<!--)<(?:[^<>"']|"[^"]*"|'[^']*')+>|<!--(?:[^-]|-(?!->))*-->)*>/
          .source
      ].join('|') +
      ')'
    var IDInside = {
      markup: {
        pattern: /(^<)[\s\S]+(?=>$)/,
        lookbehind: true,
        alias: ['language-markup', 'language-html', 'language-xml'],
        inside: Prism.languages.markup
      }
    }
    /**
     * @param {string} source
     * @param {string} flags
     * @returns {RegExp}
     */
    function withID(source, flags) {
      return RegExp(
        source.replace(/<ID>/g, function () {
          return ID
        }),
        flags
      )
    }
    Prism.languages.dot = {
      comment: {
        pattern: /\/\/.*|\/\*[\s\S]*?\*\/|^#.*/m,
        greedy: true
      },
      'graph-name': {
        pattern: withID(
          /(\b(?:digraph|graph|subgraph)[ \t\r\n]+)<ID>/.source,
          'i'
        ),
        lookbehind: true,
        greedy: true,
        alias: 'class-name',
        inside: IDInside
      },
      'attr-value': {
        pattern: withID(/(=[ \t\r\n]*)<ID>/.source),
        lookbehind: true,
        greedy: true,
        inside: IDInside
      },
      'attr-name': {
        pattern: withID(/([\[;, \t\r\n])<ID>(?=[ \t\r\n]*=)/.source),
        lookbehind: true,
        greedy: true,
        inside: IDInside
      },
      keyword: /\b(?:digraph|edge|graph|node|strict|subgraph)\b/i,
      'compass-point': {
        pattern: /(:[ \t\r\n]*)(?:[ewc_]|[ns][ew]?)(?![\w\x80-\uFFFF])/,
        lookbehind: true,
        alias: 'builtin'
      },
      node: {
        pattern: withID(/(^|[^-.\w\x80-\uFFFF\\])<ID>/.source),
        lookbehind: true,
        greedy: true,
        inside: IDInside
      },
      operator: /[=:]|-[->]/,
      punctuation: /[\[\]{};,]/
    }
    Prism.languages.gv = Prism.languages.dot
  })(Prism)
}


/***/ }),

/***/ 58245:
/***/ ((module) => {



module.exports = dhall
dhall.displayName = 'dhall'
dhall.aliases = []
function dhall(Prism) {
  // ABNF grammar:
  // https://github.com/dhall-lang/dhall-lang/blob/master/standard/dhall.abnf
  Prism.languages.dhall = {
    // Multi-line comments can be nested. E.g. {- foo {- bar -} -}
    // The multi-line pattern is essentially this:
    //   \{-(?:[^-{]|-(?!\})|\{(?!-)|<SELF>)*-\}
    comment:
      /--.*|\{-(?:[^-{]|-(?!\})|\{(?!-)|\{-(?:[^-{]|-(?!\})|\{(?!-))*-\})*-\}/,
    string: {
      pattern: /"(?:[^"\\]|\\.)*"|''(?:[^']|'(?!')|'''|''\$\{)*''(?!'|\$)/,
      greedy: true,
      inside: {
        interpolation: {
          pattern: /\$\{[^{}]*\}/,
          inside: {
            expression: {
              pattern: /(^\$\{)[\s\S]+(?=\}$)/,
              lookbehind: true,
              alias: 'language-dhall',
              inside: null // see blow
            },
            punctuation: /\$\{|\}/
          }
        }
      }
    },
    label: {
      pattern: /`[^`]*`/,
      greedy: true
    },
    url: {
      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L596
      pattern:
        /\bhttps?:\/\/[\w.:%!$&'*+;=@~-]+(?:\/[\w.:%!$&'*+;=@~-]*)*(?:\?[/?\w.:%!$&'*+;=@~-]*)?/,
      greedy: true
    },
    env: {
      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L661
      pattern: /\benv:(?:(?!\d)\w+|"(?:[^"\\=]|\\.)*")/,
      greedy: true,
      inside: {
        function: /^env/,
        operator: /^:/,
        variable: /[\s\S]+/
      }
    },
    hash: {
      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L725
      pattern: /\bsha256:[\da-fA-F]{64}\b/,
      inside: {
        function: /sha256/,
        operator: /:/,
        number: /[\da-fA-F]{64}/
      }
    },
    // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L359
    keyword:
      /\b(?:as|assert|else|forall|if|in|let|merge|missing|then|toMap|using|with)\b|\u2200/,
    builtin: /\b(?:None|Some)\b/,
    boolean: /\b(?:False|True)\b/,
    number:
      /\bNaN\b|-?\bInfinity\b|[+-]?\b(?:0x[\da-fA-F]+|\d+(?:\.\d+)?(?:e[+-]?\d+)?)\b/,
    operator:
      /\/\\|\/\/\\\\|&&|\|\||===|[!=]=|\/\/|->|\+\+|::|[+*#@=:?<>|\\\u2227\u2a53\u2261\u2afd\u03bb\u2192]/,
    punctuation: /\.\.|[{}\[\](),./]/,
    // we'll just assume that every capital word left is a type name
    'class-name': /\b[A-Z]\w*\b/
  }
  Prism.languages.dhall.string.inside.interpolation.inside.expression.inside =
    Prism.languages.dhall
}


/***/ }),

/***/ 60422:
/***/ ((module) => {



module.exports = d
d.displayName = 'd'
d.aliases = []
function d(Prism) {
  Prism.languages.d = Prism.languages.extend('clike', {
    comment: [
      {
        // Shebang
        pattern: /^\s*#!.+/,
        greedy: true
      },
      {
        pattern: RegExp(
          /(^|[^\\])/.source +
            '(?:' +
            [
              // /+ comment +/
              // Allow one level of nesting
              /\/\+(?:\/\+(?:[^+]|\+(?!\/))*\+\/|(?!\/\+)[\s\S])*?\+\//.source, // // comment
              /\/\/.*/.source, // /* comment */
              /\/\*[\s\S]*?\*\//.source
            ].join('|') +
            ')'
        ),
        lookbehind: true,
        greedy: true
      }
    ],
    string: [
      {
        pattern: RegExp(
          [
            // r"", x""
            /\b[rx]"(?:\\[\s\S]|[^\\"])*"[cwd]?/.source, // q"[]", q"()", q"<>", q"{}"
            /\bq"(?:\[[\s\S]*?\]|\([\s\S]*?\)|<[\s\S]*?>|\{[\s\S]*?\})"/.source, // q"IDENT
            // ...
            // IDENT"
            /\bq"((?!\d)\w+)$[\s\S]*?^\1"/.source, // q"//", q"||", etc.
            // eslint-disable-next-line regexp/strict
            /\bq"(.)[\s\S]*?\2"/.source, // eslint-disable-next-line regexp/strict
            /(["`])(?:\\[\s\S]|(?!\3)[^\\])*\3[cwd]?/.source
          ].join('|'),
          'm'
        ),
        greedy: true
      },
      {
        pattern: /\bq\{(?:\{[^{}]*\}|[^{}])*\}/,
        greedy: true,
        alias: 'token-string'
      }
    ],
    // In order: $, keywords and special tokens, globally defined symbols
    keyword:
      /\$|\b(?:__(?:(?:DATE|EOF|FILE|FUNCTION|LINE|MODULE|PRETTY_FUNCTION|TIMESTAMP|TIME|VENDOR|VERSION)__|gshared|parameters|traits|vector)|abstract|alias|align|asm|assert|auto|body|bool|break|byte|case|cast|catch|cdouble|cent|cfloat|char|class|const|continue|creal|dchar|debug|default|delegate|delete|deprecated|do|double|dstring|else|enum|export|extern|false|final|finally|float|for|foreach|foreach_reverse|function|goto|idouble|if|ifloat|immutable|import|inout|int|interface|invariant|ireal|lazy|long|macro|mixin|module|new|nothrow|null|out|override|package|pragma|private|protected|ptrdiff_t|public|pure|real|ref|return|scope|shared|short|size_t|static|string|struct|super|switch|synchronized|template|this|throw|true|try|typedef|typeid|typeof|ubyte|ucent|uint|ulong|union|unittest|ushort|version|void|volatile|wchar|while|with|wstring)\b/,
    number: [
      // The lookbehind and the negative look-ahead try to prevent bad highlighting of the .. operator
      // Hexadecimal numbers must be handled separately to avoid problems with exponent "e"
      /\b0x\.?[a-f\d_]+(?:(?!\.\.)\.[a-f\d_]*)?(?:p[+-]?[a-f\d_]+)?[ulfi]{0,4}/i,
      {
        pattern:
          /((?:\.\.)?)(?:\b0b\.?|\b|\.)\d[\d_]*(?:(?!\.\.)\.[\d_]*)?(?:e[+-]?\d[\d_]*)?[ulfi]{0,4}/i,
        lookbehind: true
      }
    ],
    operator:
      /\|[|=]?|&[&=]?|\+[+=]?|-[-=]?|\.?\.\.|=[>=]?|!(?:i[ns]\b|<>?=?|>=?|=)?|\bi[ns]\b|(?:<[<>]?|>>?>?|\^\^|[*\/%^~])=?/
  })
  Prism.languages.insertBefore('d', 'string', {
    // Characters
    // 'a', '\\', '\n', '\xFF', '\377', '\uFFFF', '\U0010FFFF', '\quot'
    char: /'(?:\\(?:\W|\w+)|[^\\])'/
  })
  Prism.languages.insertBefore('d', 'keyword', {
    property: /\B@\w*/
  })
  Prism.languages.insertBefore('d', 'function', {
    register: {
      // Iasm registers
      pattern:
        /\b(?:[ABCD][LHX]|E?(?:BP|DI|SI|SP)|[BS]PL|[ECSDGF]S|CR[0234]|[DS]IL|DR[012367]|E[ABCD]X|X?MM[0-7]|R(?:1[0-5]|[89])[BWD]?|R[ABCD]X|R[BS]P|R[DS]I|TR[3-7]|XMM(?:1[0-5]|[89])|YMM(?:1[0-5]|\d))\b|\bST(?:\([0-7]\)|\b)/,
      alias: 'variable'
    }
  })
}


/***/ }),

/***/ 63507:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


var refractorMarkupTemplating = __webpack_require__(56876)
module.exports = django
django.displayName = 'django'
django.aliases = ['jinja2']
function django(Prism) {
  Prism.register(refractorMarkupTemplating)
  // Django/Jinja2 syntax definition for Prism.js <http://prismjs.com> syntax highlighter.
  // Mostly it works OK but can paint code incorrectly on complex html/template tag combinations.
  ;(function (Prism) {
    Prism.languages.django = {
      comment: /^\{#[\s\S]*?#\}$/,
      tag: {
        pattern: /(^\{%[+-]?\s*)\w+/,
        lookbehind: true,
        alias: 'keyword'
      },
      delimiter: {
        pattern: /^\{[{%][+-]?|[+-]?[}%]\}$/,
        alias: 'punctuation'
      },
      string: {
        pattern: /("|')(?:\\.|(?!\1)[^\\\r\n])*\1/,
        greedy: true
      },
      filter: {
        pattern: /(\|)\w+/,
        lookbehind: true,
        alias: 'function'
      },
      test: {
        pattern: /(\bis\s+(?:not\s+)?)(?!not\b)\w+/,
        lookbehind: true,
        alias: 'function'
      },
      function: /\b[a-z_]\w+(?=\s*\()/i,
      keyword:
        /\b(?:and|as|by|else|for|if|import|in|is|loop|not|or|recursive|with|without)\b/,
      operator: /[-+%=]=?|!=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,
      number: /\b\d+(?:\.\d+)?\b/,
      boolean: /[Ff]alse|[Nn]one|[Tt]rue/,
      variable: /\b\w+\b/,
      punctuation: /[{}[\](),.:;]/
    }
    var pattern = /\{\{[\s\S]*?\}\}|\{%[\s\S]*?%\}|\{#[\s\S]*?#\}/g
    var markupTemplating = Prism.languages['markup-templating']
    Prism.hooks.add('before-tokenize', function (env) {
      markupTemplating.buildPlaceholders(env, 'django', pattern)
    })
    Prism.hooks.add('after-tokenize', function (env) {
      markupTemplating.tokenizePlaceholders(env, 'django')
    }) // Add an Jinja2 alias
    Prism.languages.jinja2 = Prism.languages.django
    Prism.hooks.add('before-tokenize', function (env) {
      markupTemplating.buildPlaceholders(env, 'jinja2', pattern)
    })
    Prism.hooks.add('after-tokenize', function (env) {
      markupTemplating.tokenizePlaceholders(env, 'jinja2')
    })
  })(Prism)
}


/***/ }),

/***/ 67012:
/***/ ((module) => {



module.exports = dataweave
dataweave.displayName = 'dataweave'
dataweave.aliases = []
function dataweave(Prism) {
  ;(function (Prism) {
    Prism.languages.dataweave = {
      url: /\b[A-Za-z]+:\/\/[\w/:.?=&-]+|\burn:[\w:.?=&-]+/,
      property: {
        pattern: /(?:\b\w+#)?(?:"(?:\\.|[^\\"\r\n])*"|\b\w+)(?=\s*[:@])/,
        greedy: true
      },
      string: {
        pattern: /(["'`])(?:\\[\s\S]|(?!\1)[^\\])*\1/,
        greedy: true
      },
      'mime-type':
        /\b(?:application|audio|image|multipart|text|video)\/[\w+-]+/,
      date: {
        pattern: /\|[\w:+-]+\|/,
        greedy: true
      },
      comment: [
        {
          pattern: /(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,
          lookbehind: true,
          greedy: true
        },
        {
          pattern: /(^|[^\\:])\/\/.*/,
          lookbehind: true,
          greedy: true
        }
      ],
      regex: {
        pattern: /\/(?:[^\\\/\r\n]|\\[^\r\n])+\//,
        greedy: true
      },
      keyword:
        /\b(?:and|as|at|case|do|else|fun|if|input|is|match|not|ns|null|or|output|type|unless|update|using|var)\b/,
      function: /\b[A-Z_]\w*(?=\s*\()/i,
      number: /-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,
      punctuation: /[{}[\];(),.:@]/,
      operator: /<<|>>|->|[<>~=]=?|!=|--?-?|\+\+?|!|\?/,
      boolean: /\b(?:false|true)\b/
    }
  })(Prism)
}


/***/ }),

/***/ 76064:
/***/ ((module) => {



module.exports = docker
docker.displayName = 'docker'
docker.aliases = ['dockerfile']
function docker(Prism) {
  ;(function (Prism) {
    // Many of the following regexes will contain negated lookaheads like `[ \t]+(?![ \t])`. This is a trick to ensure
    // that quantifiers behave *atomically*. Atomic quantifiers are necessary to prevent exponential backtracking.
    var spaceAfterBackSlash =
      /\\[\r\n](?:\s|\\[\r\n]|#.*(?!.))*(?![\s#]|\\[\r\n])/.source // At least one space, comment, or line break
    var space = /(?:[ \t]+(?![ \t])(?:<SP_BS>)?|<SP_BS>)/.source.replace(
      /<SP_BS>/g,
      function () {
        return spaceAfterBackSlash
      }
    )
    var string =
      /"(?:[^"\\\r\n]|\\(?:\r\n|[\s\S]))*"|'(?:[^'\\\r\n]|\\(?:\r\n|[\s\S]))*'/
        .source
    var option = /--[\w-]+=(?:<STR>|(?!["'])(?:[^\s\\]|\\.)+)/.source.replace(
      /<STR>/g,
      function () {
        return string
      }
    )
    var stringRule = {
      pattern: RegExp(string),
      greedy: true
    }
    var commentRule = {
      pattern: /(^[ \t]*)#.*/m,
      lookbehind: true,
      greedy: true
    }
    /**
     * @param {string} source
     * @param {string} flags
     * @returns {RegExp}
     */
    function re(source, flags) {
      source = source
        .replace(/<OPT>/g, function () {
          return option
        })
        .replace(/<SP>/g, function () {
          return space
        })
      return RegExp(source, flags)
    }
    Prism.languages.docker = {
      instruction: {
        pattern:
          /(^[ \t]*)(?:ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|ONBUILD|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR)(?=\s)(?:\\.|[^\r\n\\])*(?:\\$(?:\s|#.*$)*(?![\s#])(?:\\.|[^\r\n\\])*)*/im,
        lookbehind: true,
        greedy: true,
        inside: {
          options: {
            pattern: re(
              /(^(?:ONBUILD<SP>)?\w+<SP>)<OPT>(?:<SP><OPT>)*/.source,
              'i'
            ),
            lookbehind: true,
            greedy: true,
            inside: {
              property: {
                pattern: /(^|\s)--[\w-]+/,
                lookbehind: true
              },
              string: [
                stringRule,
                {
                  pattern: /(=)(?!["'])(?:[^\s\\]|\\.)+/,
                  lookbehind: true
                }
              ],
              operator: /\\$/m,
              punctuation: /=/
            }
          },
          keyword: [
            {
              // https://docs.docker.com/engine/reference/builder/#healthcheck
              pattern: re(
                /(^(?:ONBUILD<SP>)?HEALTHCHECK<SP>(?:<OPT><SP>)*)(?:CMD|NONE)\b/
                  .source,
                'i'
              ),
              lookbehind: true,
              greedy: true
            },
            {
              // https://docs.docker.com/engine/reference/builder/#from
              pattern: re(
                /(^(?:ONBUILD<SP>)?FROM<SP>(?:<OPT><SP>)*(?!--)[^ \t\\]+<SP>)AS/
                  .source,
                'i'
              ),
              lookbehind: true,
              greedy: true
            },
            {
              // https://docs.docker.com/engine/reference/builder/#onbuild
              pattern: re(/(^ONBUILD<SP>)\w+/.source, 'i'),
              lookbehind: true,
              greedy: true
            },
            {
              pattern: /^\w+/,
              greedy: true
            }
          ],
          comment: commentRule,
          string: stringRule,
          variable: /\$(?:\w+|\{[^{}"'\\]*\})/,
          operator: /\\$/m
        }
      },
      comment: commentRule
    }
    Prism.languages.dockerfile = Prism.languages.docker
  })(Prism)
}


/***/ }),

/***/ 79584:
/***/ ((module) => {



module.exports = firestoreSecurityRules
firestoreSecurityRules.displayName = 'firestoreSecurityRules'
firestoreSecurityRules.aliases = []
function firestoreSecurityRules(Prism) {
  Prism.languages['firestore-security-rules'] = Prism.languages.extend(
    'clike',
    {
      comment: /\/\/.*/,
      keyword:
        /\b(?:allow|function|if|match|null|return|rules_version|service)\b/,
      operator: /&&|\|\||[<>!=]=?|[-+*/%]|\b(?:in|is)\b/
    }
  )
  delete Prism.languages['firestore-security-rules']['class-name']
  Prism.languages.insertBefore('firestore-security-rules', 'keyword', {
    path: {
      pattern:
        /(^|[\s(),])(?:\/(?:[\w\xA0-\uFFFF]+|\{[\w\xA0-\uFFFF]+(?:=\*\*)?\}|\$\([\w\xA0-\uFFFF.]+\)))+/,
      lookbehind: true,
      greedy: true,
      inside: {
        variable: {
          pattern: /\{[\w\xA0-\uFFFF]+(?:=\*\*)?\}|\$\([\w\xA0-\uFFFF.]+\)/,
          inside: {
            operator: /=/,
            keyword: /\*\*/,
            punctuation: /[.$(){}]/
          }
        },
        punctuation: /\//
      }
    },
    method: {
      // to make the pattern shorter, the actual method names are omitted
      pattern: /(\ballow\s+)[a-z]+(?:\s*,\s*[a-z]+)*(?=\s*[:;])/,
      lookbehind: true,
      alias: 'builtin',
      inside: {
        punctuation: /,/
      }
    }
  })
}


/***/ }),

/***/ 79962:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


var refractorMarkupTemplating = __webpack_require__(56876)
module.exports = ejs
ejs.displayName = 'ejs'
ejs.aliases = ['eta']
function ejs(Prism) {
  Prism.register(refractorMarkupTemplating)
  ;(function (Prism) {
    Prism.languages.ejs = {
      delimiter: {
        pattern: /^<%[-_=]?|[-_]?%>$/,
        alias: 'punctuation'
      },
      comment: /^#[\s\S]*/,
      'language-javascript': {
        pattern: /[\s\S]+/,
        inside: Prism.languages.javascript
      }
    }
    Prism.hooks.add('before-tokenize', function (env) {
      var ejsPattern = /<%(?!%)[\s\S]+?%>/g
      Prism.languages['markup-templating'].buildPlaceholders(
        env,
        'ejs',
        ejsPattern
      )
    })
    Prism.hooks.add('after-tokenize', function (env) {
      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'ejs')
    })
    Prism.languages.eta = Prism.languages.ejs
  })(Prism)
}


/***/ }),

/***/ 84125:
/***/ ((module) => {



module.exports = dart
dart.displayName = 'dart'
dart.aliases = []
function dart(Prism) {
  ;(function (Prism) {
    var keywords = [
      /\b(?:async|sync|yield)\*/,
      /\b(?:abstract|assert|async|await|break|case|catch|class|const|continue|covariant|default|deferred|do|dynamic|else|enum|export|extends|extension|external|factory|final|finally|for|get|hide|if|implements|import|in|interface|library|mixin|new|null|on|operator|part|rethrow|return|set|show|static|super|switch|sync|this|throw|try|typedef|var|void|while|with|yield)\b/
    ] // Handles named imports, such as http.Client
    var packagePrefix = /(^|[^\w.])(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/
      .source // based on the dart naming conventions
    var className = {
      pattern: RegExp(packagePrefix + /[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),
      lookbehind: true,
      inside: {
        namespace: {
          pattern: /^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,
          inside: {
            punctuation: /\./
          }
        }
      }
    }
    Prism.languages.dart = Prism.languages.extend('clike', {
      'class-name': [
        className,
        {
          // variables and parameters
          // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)
          pattern: RegExp(
            packagePrefix + /[A-Z]\w*(?=\s+\w+\s*[;,=()])/.source
          ),
          lookbehind: true,
          inside: className.inside
        }
      ],
      keyword: keywords,
      operator:
        /\bis!|\b(?:as|is)\b|\+\+|--|&&|\|\||<<=?|>>=?|~(?:\/=?)?|[+\-*\/%&^|=!<>]=?|\?/
    })
    Prism.languages.insertBefore('dart', 'string', {
      'string-literal': {
        pattern:
          /r?(?:("""|''')[\s\S]*?\1|(["'])(?:\\.|(?!\2)[^\\\r\n])*\2(?!\2))/,
        greedy: true,
        inside: {
          interpolation: {
            pattern:
              /((?:^|[^\\])(?:\\{2})*)\$(?:\w+|\{(?:[^{}]|\{[^{}]*\})*\})/,
            lookbehind: true,
            inside: {
              punctuation: /^\$\{?|\}$/,
              expression: {
                pattern: /[\s\S]+/,
                inside: Prism.languages.dart
              }
            }
          },
          string: /[\s\S]+/
        }
      },
      string: undefined
    })
    Prism.languages.insertBefore('dart', 'class-name', {
      metadata: {
        pattern: /@\w+/,
        alias: 'function'
      }
    })
    Prism.languages.insertBefore('dart', 'class-name', {
      generics: {
        pattern:
          /<(?:[\w\s,.&?]|<(?:[\w\s,.&?]|<(?:[\w\s,.&?]|<[\w\s,.&?]*>)*>)*>)*>/,
        inside: {
          'class-name': className,
          keyword: keywords,
          punctuation: /[<>(),.:]/,
          operator: /[?&|]/
        }
      }
    })
  })(Prism)
}


/***/ }),

/***/ 84771:
/***/ ((module) => {



module.exports = ebnf
ebnf.displayName = 'ebnf'
ebnf.aliases = []
function ebnf(Prism) {
  Prism.languages.ebnf = {
    comment: /\(\*[\s\S]*?\*\)/,
    string: {
      pattern: /"[^"\r\n]*"|'[^'\r\n]*'/,
      greedy: true
    },
    special: {
      pattern: /\?[^?\r\n]*\?/,
      greedy: true,
      alias: 'class-name'
    },
    definition: {
      pattern: /^([\t ]*)[a-z]\w*(?:[ \t]+[a-z]\w*)*(?=\s*=)/im,
      lookbehind: true,
      alias: ['rule', 'keyword']
    },
    rule: /\b[a-z]\w*(?:[ \t]+[a-z]\w*)*\b/i,
    punctuation: /\([:/]|[:/]\)|[.,;()[\]{}]/,
    operator: /[-=|*/!]/
  }
}


/***/ }),

/***/ 89155:
/***/ ((module) => {



module.exports = dax
dax.displayName = 'dax'
dax.aliases = []
function dax(Prism) {
  Prism.languages.dax = {
    comment: {
      pattern: /(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/).*)/,
      lookbehind: true
    },
    'data-field': {
      pattern:
        /'(?:[^']|'')*'(?!')(?:\[[ \w\xA0-\uFFFF]+\])?|\w+\[[ \w\xA0-\uFFFF]+\]/,
      alias: 'symbol'
    },
    measure: {
      pattern: /\[[ \w\xA0-\uFFFF]+\]/,
      alias: 'constant'
    },
    string: {
      pattern: /"(?:[^"]|"")*"(?!")/,
      greedy: true
    },
    function:
      /\b(?:ABS|ACOS|ACOSH|ACOT|ACOTH|ADDCOLUMNS|ADDMISSINGITEMS|ALL|ALLCROSSFILTERED|ALLEXCEPT|ALLNOBLANKROW|ALLSELECTED|AND|APPROXIMATEDISTINCTCOUNT|ASIN|ASINH|ATAN|ATANH|AVERAGE|AVERAGEA|AVERAGEX|BETA\.DIST|BETA\.INV|BLANK|CALCULATE|CALCULATETABLE|CALENDAR|CALENDARAUTO|CEILING|CHISQ\.DIST|CHISQ\.DIST\.RT|CHISQ\.INV|CHISQ\.INV\.RT|CLOSINGBALANCEMONTH|CLOSINGBALANCEQUARTER|CLOSINGBALANCEYEAR|COALESCE|COMBIN|COMBINA|COMBINEVALUES|CONCATENATE|CONCATENATEX|CONFIDENCE\.NORM|CONFIDENCE\.T|CONTAINS|CONTAINSROW|CONTAINSSTRING|CONTAINSSTRINGEXACT|CONVERT|COS|COSH|COT|COTH|COUNT|COUNTA|COUNTAX|COUNTBLANK|COUNTROWS|COUNTX|CROSSFILTER|CROSSJOIN|CURRENCY|CURRENTGROUP|CUSTOMDATA|DATATABLE|DATE|DATEADD|DATEDIFF|DATESBETWEEN|DATESINPERIOD|DATESMTD|DATESQTD|DATESYTD|DATEVALUE|DAY|DEGREES|DETAILROWS|DISTINCT|DISTINCTCOUNT|DISTINCTCOUNTNOBLANK|DIVIDE|EARLIER|EARLIEST|EDATE|ENDOFMONTH|ENDOFQUARTER|ENDOFYEAR|EOMONTH|ERROR|EVEN|EXACT|EXCEPT|EXP|EXPON\.DIST|FACT|FALSE|FILTER|FILTERS|FIND|FIRSTDATE|FIRSTNONBLANK|FIRSTNONBLANKVALUE|FIXED|FLOOR|FORMAT|GCD|GENERATE|GENERATEALL|GENERATESERIES|GEOMEAN|GEOMEANX|GROUPBY|HASONEFILTER|HASONEVALUE|HOUR|IF|IF\.EAGER|IFERROR|IGNORE|INT|INTERSECT|ISBLANK|ISCROSSFILTERED|ISEMPTY|ISERROR|ISEVEN|ISFILTERED|ISINSCOPE|ISLOGICAL|ISNONTEXT|ISNUMBER|ISO\.CEILING|ISODD|ISONORAFTER|ISSELECTEDMEASURE|ISSUBTOTAL|ISTEXT|KEEPFILTERS|KEYWORDMATCH|LASTDATE|LASTNONBLANK|LASTNONBLANKVALUE|LCM|LEFT|LEN|LN|LOG|LOG10|LOOKUPVALUE|LOWER|MAX|MAXA|MAXX|MEDIAN|MEDIANX|MID|MIN|MINA|MINUTE|MINX|MOD|MONTH|MROUND|NATURALINNERJOIN|NATURALLEFTOUTERJOIN|NEXTDAY|NEXTMONTH|NEXTQUARTER|NEXTYEAR|NONVISUAL|NORM\.DIST|NORM\.INV|NORM\.S\.DIST|NORM\.S\.INV|NOT|NOW|ODD|OPENINGBALANCEMONTH|OPENINGBALANCEQUARTER|OPENINGBALANCEYEAR|OR|PARALLELPERIOD|PATH|PATHCONTAINS|PATHITEM|PATHITEMREVERSE|PATHLENGTH|PERCENTILE\.EXC|PERCENTILE\.INC|PERCENTILEX\.EXC|PERCENTILEX\.INC|PERMUT|PI|POISSON\.DIST|POWER|PREVIOUSDAY|PREVIOUSMONTH|PREVIOUSQUARTER|PREVIOUSYEAR|PRODUCT|PRODUCTX|QUARTER|QUOTIENT|RADIANS|RAND|RANDBETWEEN|RANK\.EQ|RANKX|RELATED|RELATEDTABLE|REMOVEFILTERS|REPLACE|REPT|RIGHT|ROLLUP|ROLLUPADDISSUBTOTAL|ROLLUPGROUP|ROLLUPISSUBTOTAL|ROUND|ROUNDDOWN|ROUNDUP|ROW|SAMEPERIODLASTYEAR|SAMPLE|SEARCH|SECOND|SELECTCOLUMNS|SELECTEDMEASURE|SELECTEDMEASUREFORMATSTRING|SELECTEDMEASURENAME|SELECTEDVALUE|SIGN|SIN|SINH|SQRT|SQRTPI|STARTOFMONTH|STARTOFQUARTER|STARTOFYEAR|STDEV\.P|STDEV\.S|STDEVX\.P|STDEVX\.S|SUBSTITUTE|SUBSTITUTEWITHINDEX|SUM|SUMMARIZE|SUMMARIZECOLUMNS|SUMX|SWITCH|T\.DIST|T\.DIST\.2T|T\.DIST\.RT|T\.INV|T\.INV\.2T|TAN|TANH|TIME|TIMEVALUE|TODAY|TOPN|TOPNPERLEVEL|TOPNSKIP|TOTALMTD|TOTALQTD|TOTALYTD|TREATAS|TRIM|TRUE|TRUNC|UNICHAR|UNICODE|UNION|UPPER|USERELATIONSHIP|USERNAME|USEROBJECTID|USERPRINCIPALNAME|UTCNOW|UTCTODAY|VALUE|VALUES|VAR\.P|VAR\.S|VARX\.P|VARX\.S|WEEKDAY|WEEKNUM|XIRR|XNPV|YEAR|YEARFRAC)(?=\s*\()/i,
    keyword:
      /\b(?:DEFINE|EVALUATE|MEASURE|ORDER\s+BY|RETURN|VAR|START\s+AT|ASC|DESC)\b/i,
    boolean: {
      pattern: /\b(?:FALSE|NULL|TRUE)\b/i,
      alias: 'constant'
    },
    number: /\b\d+(?:\.\d*)?|\B\.\d+\b/,
    operator: /:=|[-+*\/=^]|&&?|\|\||<(?:=>?|<|>)?|>[>=]?|\b(?:IN|NOT)\b/i,
    punctuation: /[;\[\](){}`,.]/
  }
}


/***/ }),

/***/ 90027:
/***/ ((module) => {



module.exports = factor
factor.displayName = 'factor'
factor.aliases = []
function factor(Prism) {
  ;(function (Prism) {
    var comment_inside = {
      function:
        /\b(?:BUGS?|FIX(?:MES?)?|NOTES?|TODOS?|XX+|HACKS?|WARN(?:ING)?|\?{2,}|!{2,})\b/
    }
    var string_inside = {
      number: /\\[^\s']|%\w/
    }
    var factor = {
      comment: [
        {
          // ! single-line exclamation point comments with whitespace after/around the !
          pattern: /(^|\s)(?:! .*|!$)/,
          lookbehind: true,
          inside: comment_inside
        },
        /* from basis/multiline: */
        {
          // /* comment */, /* comment*/
          pattern: /(^|\s)\/\*\s[\s\S]*?\*\/(?=\s|$)/,
          lookbehind: true,
          greedy: true,
          inside: comment_inside
        },
        {
          // ![[ comment ]] , ![===[ comment]===]
          pattern: /(^|\s)!\[(={0,6})\[\s[\s\S]*?\]\2\](?=\s|$)/,
          lookbehind: true,
          greedy: true,
          inside: comment_inside
        }
      ],
      number: [
        {
          // basic base 10 integers 9, -9
          pattern: /(^|\s)[+-]?\d+(?=\s|$)/,
          lookbehind: true
        },
        {
          // base prefix integers 0b010 0o70 0xad 0d10 0XAD -0xa9
          pattern: /(^|\s)[+-]?0(?:b[01]+|o[0-7]+|d\d+|x[\dA-F]+)(?=\s|$)/i,
          lookbehind: true
        },
        {
          // fractional ratios 1/5 -1/5 and the literal float approximations 1/5. -1/5.
          pattern: /(^|\s)[+-]?\d+\/\d+\.?(?=\s|$)/,
          lookbehind: true
        },
        {
          // positive mixed numbers 23+1/5 +23+1/5
          pattern: /(^|\s)\+?\d+\+\d+\/\d+(?=\s|$)/,
          lookbehind: true
        },
        {
          // negative mixed numbers -23-1/5
          pattern: /(^|\s)-\d+-\d+\/\d+(?=\s|$)/,
          lookbehind: true
        },
        {
          // basic decimal floats -0.01 0. .0 .1 -.1 -1. -12.13 +12.13
          // and scientific notation with base 10 exponents 3e4 3e-4 .3e-4
          pattern:
            /(^|\s)[+-]?(?:\d*\.\d+|\d+\.\d*|\d+)(?:e[+-]?\d+)?(?=\s|$)/i,
          lookbehind: true
        },
        {
          // NAN literal syntax NAN: 80000deadbeef, NAN: a
          pattern: /(^|\s)NAN:\s+[\da-fA-F]+(?=\s|$)/,
          lookbehind: true
        },
        {
          /*
base prefix floats 0x1.0p3 (8.0) 0b1.010p2 (5.0) 0x1.p1 0b1.11111111p11111...
"The normalized hex form ±0x1.MMMMMMMMMMMMM[pP]±EEEE allows any floating-point number to be specified precisely.
The values of MMMMMMMMMMMMM and EEEE map directly to the mantissa and exponent fields of the binary IEEE 754 representation."
<https://docs.factorcode.org/content/article-syntax-floats.html>
*/
          pattern:
            /(^|\s)[+-]?0(?:b1\.[01]*|o1\.[0-7]*|d1\.\d*|x1\.[\dA-F]*)p\d+(?=\s|$)/i,
          lookbehind: true
        }
      ],
      // R/ regexp?\/\\/
      regexp: {
        pattern:
          /(^|\s)R\/\s(?:\\\S|[^\\/])*\/(?:[idmsr]*|[idmsr]+-[idmsr]+)(?=\s|$)/,
        lookbehind: true,
        alias: 'number',
        inside: {
          variable: /\\\S/,
          keyword: /[+?*\[\]^$(){}.|]/,
          operator: {
            pattern: /(\/)[idmsr]+(?:-[idmsr]+)?/,
            lookbehind: true
          }
        }
      },
      boolean: {
        pattern: /(^|\s)[tf](?=\s|$)/,
        lookbehind: true
      },
      // SBUF" asd", URL" ://...", P" /etc/"
      'custom-string': {
        pattern: /(^|\s)[A-Z0-9\-]+"\s(?:\\\S|[^"\\])*"/,
        lookbehind: true,
        greedy: true,
        alias: 'string',
        inside: {
          number: /\\\S|%\w|\//
        }
      },
      'multiline-string': [
        {
          // STRING: name \n content \n ; -> CONSTANT: name "content" (symbol)
          pattern: /(^|\s)STRING:\s+\S+(?:\n|\r\n).*(?:\n|\r\n)\s*;(?=\s|$)/,
          lookbehind: true,
          greedy: true,
          alias: 'string',
          inside: {
            number: string_inside.number,
            // trailing semicolon on its own line
            'semicolon-or-setlocal': {
              pattern: /([\r\n][ \t]*);(?=\s|$)/,
              lookbehind: true,
              alias: 'function'
            }
          }
        },
        {
          // HEREDOC: marker \n content \n marker ; -> "content" (immediate)
          pattern: /(^|\s)HEREDOC:\s+\S+(?:\n|\r\n).*(?:\n|\r\n)\s*\S+(?=\s|$)/,
          lookbehind: true,
          greedy: true,
          alias: 'string',
          inside: string_inside
        },
        {
          // [[ string ]], [==[ string]==]
          pattern: /(^|\s)\[(={0,6})\[\s[\s\S]*?\]\2\](?=\s|$)/,
          lookbehind: true,
          greedy: true,
          alias: 'string',
          inside: string_inside
        }
      ],
      'special-using': {
        pattern: /(^|\s)USING:(?:\s\S+)*(?=\s+;(?:\s|$))/,
        lookbehind: true,
        alias: 'function',
        inside: {
          // this is essentially a regex for vocab names, which i don't want to specify
          // but the USING: gets picked up as a vocab name
          string: {
            pattern: /(\s)[^:\s]+/,
            lookbehind: true
          }
        }
      },
      /* this description of stack effect literal syntax is not complete and not as specific as theoretically possible
trying to do better is more work and regex-computation-time than it's worth though.
- we'd like to have the "delimiter" parts of the stack effect [ (, --, and ) ] be a different (less-important or comment-like) colour to the stack effect contents
- we'd like if nested stack effects were treated as such rather than just appearing flat (with `inside`)
- we'd like if the following variable name conventions were recognised specifically:
special row variables = ..a b..
type and stack effect annotations end with a colon = ( quot: ( a: ( -- ) -- b ) -- x ), ( x: number -- )
word throws unconditional error = *
any other word-like variable name = a ? q' etc
https://docs.factorcode.org/content/article-effects.html
these are pretty complicated to highlight properly without a real parser, and therefore out of scope
the old pattern, which may be later useful, was: (^|\s)(?:call|execute|eval)?\((?:\s+[^"\r\n\t ]\S*)*?\s+--(?:\s+[^"\n\t ]\S*)*?\s+\)(?=\s|$)
*/
      // current solution is not great
      'stack-effect-delimiter': [
        {
          // opening parenthesis
          pattern: /(^|\s)(?:call|eval|execute)?\((?=\s)/,
          lookbehind: true,
          alias: 'operator'
        },
        {
          // middle --
          pattern: /(\s)--(?=\s)/,
          lookbehind: true,
          alias: 'operator'
        },
        {
          // closing parenthesis
          pattern: /(\s)\)(?=\s|$)/,
          lookbehind: true,
          alias: 'operator'
        }
      ],
      combinators: {
        pattern: null,
        lookbehind: true,
        alias: 'keyword'
      },
      'kernel-builtin': {
        pattern: null,
        lookbehind: true,
        alias: 'variable'
      },
      'sequences-builtin': {
        pattern: null,
        lookbehind: true,
        alias: 'variable'
      },
      'math-builtin': {
        pattern: null,
        lookbehind: true,
        alias: 'variable'
      },
      'constructor-word': {
        // <array> but not <=>
        pattern: /(^|\s)<(?!=+>|-+>)\S+>(?=\s|$)/,
        lookbehind: true,
        alias: 'keyword'
      },
      'other-builtin-syntax': {
        pattern: null,
        lookbehind: true,
        alias: 'operator'
      },
      /*
full list of supported word naming conventions: (the convention appears outside of the [brackets])
set-[x]
change-[x]
with-[x]
new-[x]
>[string]
[base]>
[string]>[number]
+[symbol]+
[boolean-word]?
?[of]
[slot-reader]>>
>>[slot-setter]
[slot-writer]<<
([implementation-detail])
[mutater]!
[variant]*
[prettyprint].
$[help-markup]
<constructors>, SYNTAX:, etc are supported by their own patterns.
`with` and `new` from `kernel` are their own builtins.
see <https://docs.factorcode.org/content/article-conventions.html>
*/
      'conventionally-named-word': {
        pattern:
          /(^|\s)(?!")(?:(?:change|new|set|with)-\S+|\$\S+|>[^>\s]+|[^:>\s]+>|[^>\s]+>[^>\s]+|\+[^+\s]+\+|[^?\s]+\?|\?[^?\s]+|[^>\s]+>>|>>[^>\s]+|[^<\s]+<<|\([^()\s]+\)|[^!\s]+!|[^*\s]\S*\*|[^.\s]\S*\.)(?=\s|$)/,
        lookbehind: true,
        alias: 'keyword'
      },
      'colon-syntax': {
        pattern: /(^|\s)(?:[A-Z0-9\-]+#?)?:{1,2}\s+(?:;\S+|(?!;)\S+)(?=\s|$)/,
        lookbehind: true,
        greedy: true,
        alias: 'function'
      },
      'semicolon-or-setlocal': {
        pattern: /(\s)(?:;|:>)(?=\s|$)/,
        lookbehind: true,
        alias: 'function'
      },
      // do not highlight leading } or trailing X{ at the begin/end of the file as it's invalid syntax
      'curly-brace-literal-delimiter': [
        {
          // opening
          pattern: /(^|\s)[a-z]*\{(?=\s)/i,
          lookbehind: true,
          alias: 'operator'
        },
        {
          // closing
          pattern: /(\s)\}(?=\s|$)/,
          lookbehind: true,
          alias: 'operator'
        }
      ],
      // do not highlight leading ] or trailing [ at the begin/end of the file as it's invalid syntax
      'quotation-delimiter': [
        {
          // opening
          pattern: /(^|\s)\[(?=\s)/,
          lookbehind: true,
          alias: 'operator'
        },
        {
          // closing
          pattern: /(\s)\](?=\s|$)/,
          lookbehind: true,
          alias: 'operator'
        }
      ],
      'normal-word': {
        pattern: /(^|\s)[^"\s]\S*(?=\s|$)/,
        lookbehind: true
      },
      /*
basic first-class string "a"
with escaped double-quote "a\""
escaped backslash "\\"
and general escapes since Factor has so many "\N"
syntax that works in the reference implementation that isn't fully
supported because it's an implementation detail:
"string 1""string 2" -> 2 strings (works anyway)
"string"5 -> string, 5
"string"[ ] -> string, quotation
{ "a"} -> array<string>
the rest of those examples all properly recognise the string, but not
the other object (number, quotation, etc)
this is fine for a regex-only implementation.
*/
      string: {
        pattern: /"(?:\\\S|[^"\\])*"/,
        greedy: true,
        inside: string_inside
      }
    }
    var escape = function (str) {
      return (str + '').replace(/([.?*+\^$\[\]\\(){}|\-])/g, '\\$1')
    }
    var arrToWordsRegExp = function (arr) {
      return new RegExp('(^|\\s)(?:' + arr.map(escape).join('|') + ')(?=\\s|$)')
    }
    var builtins = {
      'kernel-builtin': [
        'or',
        '2nipd',
        '4drop',
        'tuck',
        'wrapper',
        'nip',
        'wrapper?',
        'callstack>array',
        'die',
        'dupd',
        'callstack',
        'callstack?',
        '3dup',
        'hashcode',
        'pick',
        '4nip',
        'build',
        '>boolean',
        'nipd',
        'clone',
        '5nip',
        'eq?',
        '?',
        '=',
        'swapd',
        '2over',
        'clear',
        '2dup',
        'get-retainstack',
        'not',
        'tuple?',
        'dup',
        '3nipd',
        'call',
        '-rotd',
        'object',
        'drop',
        'assert=',
        'assert?',
        '-rot',
        'execute',
        'boa',
        'get-callstack',
        'curried?',
        '3drop',
        'pickd',
        'overd',
        'over',
        'roll',
        '3nip',
        'swap',
        'and',
        '2nip',
        'rotd',
        'throw',
        '(clone)',
        'hashcode*',
        'spin',
        'reach',
        '4dup',
        'equal?',
        'get-datastack',
        'assert',
        '2drop',
        '<wrapper>',
        'boolean?',
        'identity-hashcode',
        'identity-tuple?',
        'null',
        'composed?',
        'new',
        '5drop',
        'rot',
        '-roll',
        'xor',
        'identity-tuple',
        'boolean'
      ],
      'other-builtin-syntax': [
        // syntax
        '=======',
        'recursive',
        'flushable',
        '>>',
        '<<<<<<',
        'M\\',
        'B',
        'PRIVATE>',
        '\\',
        '======',
        'final',
        'inline',
        'delimiter',
        'deprecated',
        '<PRIVATE',
        '>>>>>>',
        '<<<<<<<',
        'parse-complex',
        'malformed-complex',
        'read-only',
        '>>>>>>>',
        'call-next-method',
        '<<',
        'foldable', // literals
        '$',
        '$[',
        '${'
      ],
      'sequences-builtin': [
        'member-eq?',
        'mismatch',
        'append',
        'assert-sequence=',
        'longer',
        'repetition',
        'clone-like',
        '3sequence',
        'assert-sequence?',
        'last-index-from',
        'reversed',
        'index-from',
        'cut*',
        'pad-tail',
        'join-as',
        'remove-eq!',
        'concat-as',
        'but-last',
        'snip',
        'nths',
        'nth',
        'sequence',
        'longest',
        'slice?',
        '<slice>',
        'remove-nth',
        'tail-slice',
        'empty?',
        'tail*',
        'member?',
        'virtual-sequence?',
        'set-length',
        'drop-prefix',
        'iota',
        'unclip',
        'bounds-error?',
        'unclip-last-slice',
        'non-negative-integer-expected',
        'non-negative-integer-expected?',
        'midpoint@',
        'longer?',
        '?set-nth',
        '?first',
        'rest-slice',
        'prepend-as',
        'prepend',
        'fourth',
        'sift',
        'subseq-start',
        'new-sequence',
        '?last',
        'like',
        'first4',
        '1sequence',
        'reverse',
        'slice',
        'virtual@',
        'repetition?',
        'set-last',
        'index',
        '4sequence',
        'max-length',
        'set-second',
        'immutable-sequence',
        'first2',
        'first3',
        'supremum',
        'unclip-slice',
        'suffix!',
        'insert-nth',
        'tail',
        '3append',
        'short',
        'suffix',
        'concat',
        'flip',
        'immutable?',
        'reverse!',
        '2sequence',
        'sum',
        'delete-all',
        'indices',
        'snip-slice',
        '<iota>',
        'check-slice',
        'sequence?',
        'head',
        'append-as',
        'halves',
        'sequence=',
        'collapse-slice',
        '?second',
        'slice-error?',
        'product',
        'bounds-check?',
        'bounds-check',
        'immutable',
        'virtual-exemplar',
        'harvest',
        'remove',
        'pad-head',
        'last',
        'set-fourth',
        'cartesian-product',
        'remove-eq',
        'shorten',
        'shorter',
        'reversed?',
        'shorter?',
        'shortest',
        'head-slice',
        'pop*',
        'tail-slice*',
        'but-last-slice',
        'iota?',
        'append!',
        'cut-slice',
        'new-resizable',
        'head-slice*',
        'sequence-hashcode',
        'pop',
        'set-nth',
        '?nth',
        'second',
        'join',
        'immutable-sequence?',
        '<reversed>',
        '3append-as',
        'virtual-sequence',
        'subseq?',
        'remove-nth!',
        'length',
        'last-index',
        'lengthen',
        'assert-sequence',
        'copy',
        'move',
        'third',
        'first',
        'tail?',
        'set-first',
        'prefix',
        'bounds-error',
        '<repetition>',
        'exchange',
        'surround',
        'cut',
        'min-length',
        'set-third',
        'push-all',
        'head?',
        'subseq-start-from',
        'delete-slice',
        'rest',
        'sum-lengths',
        'head*',
        'infimum',
        'remove!',
        'glue',
        'slice-error',
        'subseq',
        'push',
        'replace-slice',
        'subseq-as',
        'unclip-last'
      ],
      'math-builtin': [
        'number=',
        'next-power-of-2',
        '?1+',
        'fp-special?',
        'imaginary-part',
        'float>bits',
        'number?',
        'fp-infinity?',
        'bignum?',
        'fp-snan?',
        'denominator',
        'gcd',
        '*',
        '+',
        'fp-bitwise=',
        '-',
        'u>=',
        '/',
        '>=',
        'bitand',
        'power-of-2?',
        'log2-expects-positive',
        'neg?',
        '<',
        'log2',
        '>',
        'integer?',
        'number',
        'bits>double',
        '2/',
        'zero?',
        'bits>float',
        'float?',
        'shift',
        'ratio?',
        'rect>',
        'even?',
        'ratio',
        'fp-sign',
        'bitnot',
        '>fixnum',
        'complex?',
        '/i',
        'integer>fixnum',
        '/f',
        'sgn',
        '>bignum',
        'next-float',
        'u<',
        'u>',
        'mod',
        'recip',
        'rational',
        '>float',
        '2^',
        'integer',
        'fixnum?',
        'neg',
        'fixnum',
        'sq',
        'bignum',
        '>rect',
        'bit?',
        'fp-qnan?',
        'simple-gcd',
        'complex',
        '<fp-nan>',
        'real',
        '>fraction',
        'double>bits',
        'bitor',
        'rem',
        'fp-nan-payload',
        'real-part',
        'log2-expects-positive?',
        'prev-float',
        'align',
        'unordered?',
        'float',
        'fp-nan?',
        'abs',
        'bitxor',
        'integer>fixnum-strict',
        'u<=',
        'odd?',
        '<=',
        '/mod',
        '>integer',
        'real?',
        'rational?',
        'numerator'
      ] // that's all for now
    }
    Object.keys(builtins).forEach(function (k) {
      factor[k].pattern = arrToWordsRegExp(builtins[k])
    })
    var combinators = [
      // kernel
      '2bi',
      'while',
      '2tri',
      'bi*',
      '4dip',
      'both?',
      'same?',
      'tri@',
      'curry',
      'prepose',
      '3bi',
      '?if',
      'tri*',
      '2keep',
      '3keep',
      'curried',
      '2keepd',
      'when',
      '2bi*',
      '2tri*',
      '4keep',
      'bi@',
      'keepdd',
      'do',
      'unless*',
      'tri-curry',
      'if*',
      'loop',
      'bi-curry*',
      'when*',
      '2bi@',
      '2tri@',
      'with',
      '2with',
      'either?',
      'bi',
      'until',
      '3dip',
      '3curry',
      'tri-curry*',
      'tri-curry@',
      'bi-curry',
      'keepd',
      'compose',
      '2dip',
      'if',
      '3tri',
      'unless',
      'tuple',
      'keep',
      '2curry',
      'tri',
      'most',
      'while*',
      'dip',
      'composed',
      'bi-curry@', // sequences
      'find-last-from',
      'trim-head-slice',
      'map-as',
      'each-from',
      'none?',
      'trim-tail',
      'partition',
      'if-empty',
      'accumulate*',
      'reject!',
      'find-from',
      'accumulate-as',
      'collector-for-as',
      'reject',
      'map',
      'map-sum',
      'accumulate!',
      '2each-from',
      'follow',
      'supremum-by',
      'map!',
      'unless-empty',
      'collector',
      'padding',
      'reduce-index',
      'replicate-as',
      'infimum-by',
      'trim-tail-slice',
      'count',
      'find-index',
      'filter',
      'accumulate*!',
      'reject-as',
      'map-integers',
      'map-find',
      'reduce',
      'selector',
      'interleave',
      '2map',
      'filter-as',
      'binary-reduce',
      'map-index-as',
      'find',
      'produce',
      'filter!',
      'replicate',
      'cartesian-map',
      'cartesian-each',
      'find-index-from',
      'map-find-last',
      '3map-as',
      '3map',
      'find-last',
      'selector-as',
      '2map-as',
      '2map-reduce',
      'accumulate',
      'each',
      'each-index',
      'accumulate*-as',
      'when-empty',
      'all?',
      'collector-as',
      'push-either',
      'new-like',
      'collector-for',
      '2selector',
      'push-if',
      '2all?',
      'map-reduce',
      '3each',
      'any?',
      'trim-slice',
      '2reduce',
      'change-nth',
      'produce-as',
      '2each',
      'trim',
      'trim-head',
      'cartesian-find',
      'map-index', // math
      'if-zero',
      'each-integer',
      'unless-zero',
      '(find-integer)',
      'when-zero',
      'find-last-integer',
      '(all-integers?)',
      'times',
      '(each-integer)',
      'find-integer',
      'all-integers?', // math.combinators
      'unless-negative',
      'if-positive',
      'when-positive',
      'when-negative',
      'unless-positive',
      'if-negative', // combinators
      'case',
      '2cleave',
      'cond>quot',
      'case>quot',
      '3cleave',
      'wrong-values',
      'to-fixed-point',
      'alist>quot',
      'cond',
      'cleave',
      'call-effect',
      'recursive-hashcode',
      'spread',
      'deep-spread>quot', // combinators.short-circuit
      '2||',
      '0||',
      'n||',
      '0&&',
      '2&&',
      '3||',
      '1||',
      '1&&',
      'n&&',
      '3&&', // combinators.smart
      'smart-unless*',
      'keep-inputs',
      'reduce-outputs',
      'smart-when*',
      'cleave>array',
      'smart-with',
      'smart-apply',
      'smart-if',
      'inputs/outputs',
      'output>sequence-n',
      'map-outputs',
      'map-reduce-outputs',
      'dropping',
      'output>array',
      'smart-map-reduce',
      'smart-2map-reduce',
      'output>array-n',
      'nullary',
      'input<sequence',
      'append-outputs',
      'drop-inputs',
      'inputs',
      'smart-2reduce',
      'drop-outputs',
      'smart-reduce',
      'preserving',
      'smart-when',
      'outputs',
      'append-outputs-as',
      'smart-unless',
      'smart-if*',
      'sum-outputs',
      'input<sequence-unsafe',
      'output>sequence' // tafn
    ]
    factor.combinators.pattern = arrToWordsRegExp(combinators)
    Prism.languages.factor = factor
  })(Prism)
}


/***/ })

}]);