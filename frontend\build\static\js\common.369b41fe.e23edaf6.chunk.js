"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4666],{

/***/ 17050:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(70405);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/**
 * Custom hook for managing responsive preview functionality
 * Handles device switching, breakpoints, and responsive behavior
 */
var useResponsivePreview = function useResponsivePreview(_ref) {
  var _DEVICE_PRESETS$initi;
  var _ref$initialDevice = _ref.initialDevice,
    initialDevice = _ref$initialDevice === void 0 ? 'desktop' : _ref$initialDevice,
    _ref$initialVariant = _ref.initialVariant,
    initialVariant = _ref$initialVariant === void 0 ? null : _ref$initialVariant,
    _ref$enableBreakpoint = _ref.enableBreakpointDetection,
    enableBreakpointDetection = _ref$enableBreakpoint === void 0 ? true : _ref$enableBreakpoint,
    _ref$customBreakpoint = _ref.customBreakpoints,
    customBreakpoints = _ref$customBreakpoint === void 0 ? null : _ref$customBreakpoint;
  // State management
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialDevice),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    currentDevice = _useState2[0],
    setCurrentDevice = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialVariant || ((_DEVICE_PRESETS$initi = _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c[initialDevice]) === null || _DEVICE_PRESETS$initi === void 0 ? void 0 : _DEVICE_PRESETS$initi.defaultVariant)),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    currentVariant = _useState4[0],
    setCurrentVariant = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('portrait'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    orientation = _useState6[0],
    setOrientation = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    isFullscreen = _useState8[0],
    setIsFullscreen = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      width: 0,
      height: 0
    }),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    viewportSize = _useState0[0],
    setViewportSize = _useState0[1];

  // Default breakpoints (can be overridden)
  var defaultBreakpoints = {
    mobile: {
      min: 0,
      max: 767
    },
    tablet: {
      min: 768,
      max: 1023
    },
    desktop: {
      min: 1024,
      max: Infinity
    }
  };
  var breakpoints = customBreakpoints || defaultBreakpoints;

  // Get current device configuration
  var deviceConfig = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    var device = _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c[currentDevice];
    if (!device) return null;
    var variant = device.variants[currentVariant];
    if (!variant) return null;
    return _objectSpread(_objectSpread(_objectSpread({}, device), variant), {}, {
      category: device.category,
      orientation: orientation
    });
  }, [currentDevice, currentVariant, orientation]);

  // Get responsive styles based on current device
  var responsiveStyles = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    if (!deviceConfig) return {};
    var baseStyles = {
      fontSize: deviceConfig.category === 'mobile' ? '14px' : deviceConfig.category === 'tablet' ? '15px' : '16px',
      padding: deviceConfig.category === 'mobile' ? '8px' : deviceConfig.category === 'tablet' ? '12px' : '16px',
      margin: deviceConfig.category === 'mobile' ? '4px' : '8px',
      borderRadius: deviceConfig.category === 'mobile' ? '4px' : '6px'
    };

    // Add device-specific styles
    var deviceSpecificStyles = {
      mobile: {
        maxWidth: '100%',
        touchAction: 'manipulation',
        WebkitTapHighlightColor: 'transparent'
      },
      tablet: {
        maxWidth: '100%',
        touchAction: 'manipulation'
      },
      desktop: {
        cursor: 'pointer',
        userSelect: 'none'
      }
    };
    return _objectSpread(_objectSpread({}, baseStyles), deviceSpecificStyles[deviceConfig.category]);
  }, [deviceConfig]);

  // Get component size based on device
  var getComponentSize = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (componentType) {
    var _sizeMap$deviceConfig;
    if (!deviceConfig) return 'middle';
    var sizeMap = {
      mobile: {
        button: 'small',
        input: 'small',
        card: 'small',
        table: 'small',
        form: 'small'
      },
      tablet: {
        button: 'middle',
        input: 'middle',
        card: 'default',
        table: 'middle',
        form: 'middle'
      },
      desktop: {
        button: 'middle',
        input: 'middle',
        card: 'default',
        table: 'middle',
        form: 'middle'
      }
    };
    return ((_sizeMap$deviceConfig = sizeMap[deviceConfig.category]) === null || _sizeMap$deviceConfig === void 0 ? void 0 : _sizeMap$deviceConfig[componentType]) || 'middle';
  }, [deviceConfig]);

  // Detect viewport size changes
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enableBreakpointDetection) return;
    var updateViewportSize = function updateViewportSize() {
      setViewportSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };
    updateViewportSize();
    window.addEventListener('resize', updateViewportSize);
    return function () {
      window.removeEventListener('resize', updateViewportSize);
    };
  }, [enableBreakpointDetection]);

  // Auto-detect device based on viewport size
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var _Object$entries$find;
    if (!enableBreakpointDetection || viewportSize.width === 0) return;
    var detectedDevice = (_Object$entries$find = Object.entries(breakpoints).find(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref2, 2),
        _ = _ref3[0],
        range = _ref3[1];
      return viewportSize.width >= range.min && viewportSize.width <= range.max;
    })) === null || _Object$entries$find === void 0 ? void 0 : _Object$entries$find[0];
    if (detectedDevice && detectedDevice !== currentDevice) {
      var _DEVICE_PRESETS$detec;
      setCurrentDevice(detectedDevice);
      setCurrentVariant((_DEVICE_PRESETS$detec = _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c[detectedDevice]) === null || _DEVICE_PRESETS$detec === void 0 ? void 0 : _DEVICE_PRESETS$detec.defaultVariant);
    }
  }, [viewportSize, breakpoints, enableBreakpointDetection, currentDevice]);

  // Handle device change
  var handleDeviceChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (newDevice) {
    var _DEVICE_PRESETS$newDe;
    var newVariant = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    setCurrentDevice(newDevice);
    var variant = newVariant || ((_DEVICE_PRESETS$newDe = _components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c[newDevice]) === null || _DEVICE_PRESETS$newDe === void 0 ? void 0 : _DEVICE_PRESETS$newDe.defaultVariant);
    setCurrentVariant(variant);

    // Reset orientation for desktop
    if (newDevice === 'desktop') {
      setOrientation('portrait');
    }
  }, []);

  // Handle variant change
  var handleVariantChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (newVariant) {
    setCurrentVariant(newVariant);
  }, []);

  // Handle orientation change
  var handleOrientationChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if ((deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'desktop') return;
    setOrientation(function (prev) {
      return prev === 'portrait' ? 'landscape' : 'portrait';
    });
  }, [deviceConfig]);

  // Handle fullscreen toggle
  var handleFullscreenToggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setIsFullscreen(function (prev) {
      return !prev;
    });
  }, []);

  // Get media queries for CSS
  var getMediaQueries = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return {
      mobile: "@media (max-width: ".concat(breakpoints.mobile.max, "px)"),
      tablet: "@media (min-width: ".concat(breakpoints.tablet.min, "px) and (max-width: ").concat(breakpoints.tablet.max, "px)"),
      desktop: "@media (min-width: ".concat(breakpoints.desktop.min, "px)"),
      isMobile: "(max-width: ".concat(breakpoints.mobile.max, "px)"),
      isTablet: "(min-width: ".concat(breakpoints.tablet.min, "px) and (max-width: ").concat(breakpoints.tablet.max, "px)"),
      isDesktop: "(min-width: ".concat(breakpoints.desktop.min, "px)")
    };
  }, [breakpoints]);

  // Check if current device matches a category
  var isDevice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (deviceCategory) {
    return (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === deviceCategory;
  }, [deviceConfig]);

  // Get responsive props for Ant Design components
  var getResponsiveProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (componentType) {
    var size = getComponentSize(componentType);
    var props = {
      size: size
    };

    // Add device-specific props
    if ((deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'mobile') {
      if (componentType === 'table') {
        props.scroll = {
          x: true
        };
        props.pagination = {
          simple: true,
          size: 'small'
        };
      }
      if (componentType === 'form') {
        props.layout = 'vertical';
      }
    }
    return props;
  }, [deviceConfig, getComponentSize]);

  // Get current dimensions (considering orientation)
  var getCurrentDimensions = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!deviceConfig) return {
      width: 0,
      height: 0
    };
    var width = deviceConfig.width,
      height = deviceConfig.height;
    if (orientation === 'landscape' && deviceConfig.category !== 'desktop') {
      return {
        width: height,
        height: width
      };
    }
    return {
      width: width,
      height: height
    };
  }, [deviceConfig, orientation]);

  // Preview state utilities
  var previewUtils = {
    // Check if component should be hidden on current device
    shouldHideComponent: function shouldHideComponent(componentConfig) {
      var _componentConfig$resp;
      var hideOn = (componentConfig === null || componentConfig === void 0 || (_componentConfig$resp = componentConfig.responsive) === null || _componentConfig$resp === void 0 ? void 0 : _componentConfig$resp.hideOn) || [];
      return hideOn.includes(deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category);
    },
    // Get component-specific responsive styles
    getComponentStyles: function getComponentStyles(componentConfig) {
      var responsive = (componentConfig === null || componentConfig === void 0 ? void 0 : componentConfig.responsive) || {};
      var deviceStyles = responsive[deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category] || {};
      return _objectSpread(_objectSpread({}, responsiveStyles), deviceStyles);
    },
    // Check if feature is supported on current device
    isFeatureSupported: function isFeatureSupported(feature) {
      var supportMap = {
        hover: (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'desktop',
        touch: (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) !== 'desktop',
        keyboard: (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'desktop',
        contextMenu: (deviceConfig === null || deviceConfig === void 0 ? void 0 : deviceConfig.category) === 'desktop'
      };
      return supportMap[feature] !== false;
    }
  };
  return _objectSpread(_objectSpread({
    // Current state
    currentDevice: currentDevice,
    currentVariant: currentVariant,
    orientation: orientation,
    isFullscreen: isFullscreen,
    deviceConfig: deviceConfig,
    viewportSize: viewportSize,
    // Computed values
    responsiveStyles: responsiveStyles,
    mediaQueries: getMediaQueries(),
    dimensions: getCurrentDimensions(),
    // Actions
    handleDeviceChange: handleDeviceChange,
    handleVariantChange: handleVariantChange,
    handleOrientationChange: handleOrientationChange,
    handleFullscreenToggle: handleFullscreenToggle,
    // Utilities
    isDevice: isDevice,
    getComponentSize: getComponentSize,
    getResponsiveProps: getResponsiveProps
  }, previewUtils), {}, {
    // Available devices and variants
    availableDevices: Object.keys(_components_preview_DevicePreviewFrame__WEBPACK_IMPORTED_MODULE_3__/* .DEVICE_PRESETS */ .c),
    availableVariants: deviceConfig ? Object.keys(deviceConfig.variants || {}) : [],
    // Breakpoints
    breakpoints: breakpoints
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useResponsivePreview);

/***/ }),

/***/ 48860:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2543);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/**
 * Custom hook for optimizing preview performance
 * Handles virtual rendering, component caching, and performance monitoring
 */
var usePreviewPerformance = function usePreviewPerformance(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$containerHeight = _ref.containerHeight,
    containerHeight = _ref$containerHeight === void 0 ? 600 : _ref$containerHeight,
    _ref$itemHeight = _ref.itemHeight,
    itemHeight = _ref$itemHeight === void 0 ? 100 : _ref$itemHeight,
    _ref$overscan = _ref.overscan,
    overscan = _ref$overscan === void 0 ? 5 : _ref$overscan,
    _ref$enableVirtualiza = _ref.enableVirtualization,
    enableVirtualization = _ref$enableVirtualiza === void 0 ? true : _ref$enableVirtualiza,
    _ref$enablePerformanc = _ref.enablePerformanceMonitoring,
    enablePerformanceMonitoring = _ref$enablePerformanc === void 0 ? true : _ref$enablePerformanc;
  // State for virtualization
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    scrollTop = _useState2[0],
    setScrollTop = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    containerRef = _useState4[0],
    setContainerRef = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      start: 0,
      end: 0
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    visibleRange = _useState6[0],
    setVisibleRange = _useState6[1];

  // Performance monitoring state
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    renderTime = _useState8[0],
    setRenderTime = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(60),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    frameRate = _useState0[0],
    setFrameRate = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    memoryUsage = _useState10[0],
    setMemoryUsage = _useState10[1];

  // Refs for performance tracking
  var renderStartTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var frameCount = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var lastFrameTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(performance.now());
  var componentCache = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(new Map());
  var intersectionObserver = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);

  // Calculate visible items for virtualization
  var calculateVisibleRange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization || !containerRef || components.length === 0) {
      return {
        start: 0,
        end: components.length
      };
    }
    var startIndex = Math.floor(scrollTop / itemHeight);
    var endIndex = Math.min(startIndex + Math.ceil(containerHeight / itemHeight) + overscan, components.length);
    return {
      start: Math.max(0, startIndex - overscan),
      end: endIndex
    };
  }, [scrollTop, itemHeight, containerHeight, overscan, components.length, enableVirtualization, containerRef]);

  // Update visible range when scroll changes
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var newRange = calculateVisibleRange();
    setVisibleRange(newRange);
  }, [calculateVisibleRange]);

  // Throttled scroll handler
  var handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_3__.throttle)(function (event) {
    if (event.target) {
      setScrollTop(event.target.scrollTop);
    }
  }, 16),
  // ~60fps
  []);

  // Get visible components for rendering
  var visibleComponents = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    if (!enableVirtualization) {
      return components.map(function (component, index) {
        return {
          component: component,
          index: index
        };
      });
    }
    return components.slice(visibleRange.start, visibleRange.end).map(function (component, relativeIndex) {
      return {
        component: component,
        index: visibleRange.start + relativeIndex
      };
    });
  }, [components, visibleRange, enableVirtualization]);

  // Component caching for performance
  var getCachedComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (componentId, renderFunction) {
    var cacheKey = "".concat(componentId, "_").concat(JSON.stringify(components.find(function (c) {
      return c.id === componentId;
    })));
    if (componentCache.current.has(cacheKey)) {
      return componentCache.current.get(cacheKey);
    }
    var renderedComponent = renderFunction();
    componentCache.current.set(cacheKey, renderedComponent);

    // Limit cache size to prevent memory leaks
    if (componentCache.current.size > 100) {
      var firstKey = componentCache.current.keys().next().value;
      componentCache.current["delete"](firstKey);
    }
    return renderedComponent;
  }, [components]);

  // Performance monitoring
  var startRenderMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (enablePerformanceMonitoring) {
      renderStartTime.current = performance.now();
    }
  }, [enablePerformanceMonitoring]);
  var endRenderMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (enablePerformanceMonitoring && renderStartTime.current > 0) {
      var renderDuration = performance.now() - renderStartTime.current;
      setRenderTime(renderDuration);
      renderStartTime.current = 0;
    }
  }, [enablePerformanceMonitoring]);

  // Frame rate monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enablePerformanceMonitoring) return;
    var animationId;
    var _measureFrameRate = function measureFrameRate() {
      var now = performance.now();
      var delta = now - lastFrameTime.current;
      if (delta >= 1000) {
        var fps = Math.round(frameCount.current * 1000 / delta);
        setFrameRate(fps);
        frameCount.current = 0;
        lastFrameTime.current = now;
      } else {
        frameCount.current++;
      }
      animationId = requestAnimationFrame(_measureFrameRate);
    };
    animationId = requestAnimationFrame(_measureFrameRate);
    return function () {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enablePerformanceMonitoring]);

  // Memory usage monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enablePerformanceMonitoring || !performance.memory) return;
    var measureMemory = function measureMemory() {
      var memoryInfo = performance.memory;
      var usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
      setMemoryUsage(usedMB);
    };
    var interval = setInterval(measureMemory, 5000);
    measureMemory(); // Initial measurement

    return function () {
      return clearInterval(interval);
    };
  }, [enablePerformanceMonitoring]);

  // Intersection Observer for lazy loading
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enableVirtualization) return;
    intersectionObserver.current = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          // Component is visible, ensure it's rendered
          var componentId = entry.target.dataset.componentId;
          if (componentId) {
            // Trigger re-render if needed
          }
        }
      });
    }, {
      root: containerRef,
      rootMargin: "".concat(overscan * itemHeight, "px"),
      threshold: 0.1
    });
    return function () {
      if (intersectionObserver.current) {
        intersectionObserver.current.disconnect();
      }
    };
  }, [containerRef, overscan, itemHeight, enableVirtualization]);

  // Clear cache when components change significantly
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var componentIds = new Set(components.map(function (c) {
      return c.id;
    }));
    var cachedIds = new Set(Array.from(componentCache.current.keys()).map(function (key) {
      return key.split('_')[0];
    }));

    // Remove cached components that no longer exist
    cachedIds.forEach(function (cachedId) {
      if (!componentIds.has(cachedId)) {
        Array.from(componentCache.current.keys()).filter(function (key) {
          return key.startsWith(cachedId);
        }).forEach(function (key) {
          return componentCache.current["delete"](key);
        });
      }
    });
  }, [components]);

  // Get container props for virtualization
  var getContainerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization) {
      return {};
    }
    return {
      ref: setContainerRef,
      onScroll: handleScroll,
      style: {
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }
    };
  }, [enableVirtualization, containerHeight, handleScroll]);

  // Get spacer props for virtual scrolling
  var getSpacerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization) {
      return {
        before: {},
        after: {}
      };
    }
    var totalHeight = components.length * itemHeight;
    var beforeHeight = visibleRange.start * itemHeight;
    var afterHeight = totalHeight - visibleRange.end * itemHeight;
    return {
      before: {
        style: {
          height: beforeHeight,
          width: '100%'
        }
      },
      after: {
        style: {
          height: afterHeight,
          width: '100%'
        }
      }
    };
  }, [enableVirtualization, components.length, itemHeight, visibleRange]);

  // Performance optimization utilities
  var optimizationUtils = {
    clearCache: function clearCache() {
      return componentCache.current.clear();
    },
    getCacheSize: function getCacheSize() {
      return componentCache.current.size;
    },
    getPerformanceMetrics: function getPerformanceMetrics() {
      return {
        renderTime: renderTime,
        frameRate: frameRate,
        memoryUsage: memoryUsage,
        cacheSize: componentCache.current.size,
        visibleComponents: visibleComponents.length,
        totalComponents: components.length
      };
    },
    shouldRender: function shouldRender(componentId) {
      // Check if component should be rendered based on visibility
      if (!enableVirtualization) return true;
      var componentIndex = components.findIndex(function (c) {
        return c.id === componentId;
      });
      return componentIndex >= visibleRange.start && componentIndex < visibleRange.end;
    }
  };
  return _objectSpread({
    // Virtualization
    visibleComponents: visibleComponents,
    visibleRange: visibleRange,
    getContainerProps: getContainerProps,
    getSpacerProps: getSpacerProps,
    // Performance monitoring
    renderTime: renderTime,
    frameRate: frameRate,
    memoryUsage: memoryUsage,
    startRenderMeasurement: startRenderMeasurement,
    endRenderMeasurement: endRenderMeasurement,
    // Caching
    getCachedComponent: getCachedComponent
  }, optimizationUtils);
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (usePreviewPerformance);

/***/ }),

/***/ 79459:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(71468);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2543);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_5__);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/**
 * Custom hook for real-time preview functionality
 * Handles instant updates, performance optimization, and WebSocket synchronization
 */
var useRealTimePreview = function useRealTimePreview(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onUpdateComponent = _ref.onUpdateComponent,
    onAddComponent = _ref.onAddComponent,
    onDeleteComponent = _ref.onDeleteComponent,
    websocketService = _ref.websocketService,
    _ref$updateDelay = _ref.updateDelay,
    updateDelay = _ref$updateDelay === void 0 ? 300 : _ref$updateDelay,
    _ref$throttleDelay = _ref.throttleDelay,
    throttleDelay = _ref$throttleDelay === void 0 ? 100 : _ref$throttleDelay,
    _ref$enableWebSocket = _ref.enableWebSocket,
    enableWebSocket = _ref$enableWebSocket === void 0 ? true : _ref$enableWebSocket;
  // State for tracking updates
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    isUpdating = _useState2[0],
    setIsUpdating = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    lastUpdateTime = _useState4[0],
    setLastUpdateTime = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Map()),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    pendingUpdates = _useState6[0],
    setPendingUpdates = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    updateQueue = _useState8[0],
    setUpdateQueue = _useState8[1];

  // Refs for cleanup and performance
  var updateTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);
  var websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(websocketService);
  var componentCacheRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(new Map());

  // Redux state
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__/* .useDispatch */ .wA)();
  var websocketConnected = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__/* .useSelector */ .d4)(function (state) {
    try {
      var _state$websocket;
      return (state === null || state === void 0 || (_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
    } catch (error) {
      console.warn('Error accessing websocket state:', error);
      return false;
    }
  });

  // Update WebSocket reference when service changes
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    websocketRef.current = websocketService;
  }, [websocketService]);

  // Debounced update function for batching changes
  var debouncedUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_5__.debounce)(function (updates) {
    if (updates.length === 0) return;
    setIsUpdating(true);

    // Process all pending updates
    updates.forEach(function (_ref2) {
      var type = _ref2.type,
        componentId = _ref2.componentId,
        data = _ref2.data;
      switch (type) {
        case 'update':
          if (onUpdateComponent) {
            onUpdateComponent(componentId, data);
          }
          break;
        case 'add':
          if (onAddComponent) {
            onAddComponent(data);
          }
          break;
        case 'delete':
          if (onDeleteComponent) {
            onDeleteComponent(componentId);
          }
          break;
        default:
          console.warn('Unknown update type:', type);
      }
    });

    // Send WebSocket updates if enabled and connected
    if (enableWebSocket && websocketConnected && websocketRef.current) {
      updates.forEach(function (_ref3) {
        var type = _ref3.type,
          componentId = _ref3.componentId,
          data = _ref3.data;
        websocketRef.current.send({
          type: "component_".concat(type),
          component_id: componentId,
          component_data: data,
          timestamp: new Date().toISOString()
        });
      });
    }
    setLastUpdateTime(new Date());
    setUpdateQueue([]);
    setPendingUpdates(new Map());

    // Clear updating state after a short delay
    setTimeout(function () {
      return setIsUpdating(false);
    }, 500);
  }, updateDelay), [onUpdateComponent, onAddComponent, onDeleteComponent, enableWebSocket, websocketConnected, updateDelay]);

  // Throttled function for immediate visual feedback
  var throttledVisualUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_5__.throttle)(function (componentId, updates) {
    // Update component cache for immediate visual feedback
    var currentCache = componentCacheRef.current.get(componentId) || {};
    componentCacheRef.current.set(componentId, _objectSpread(_objectSpread({}, currentCache), updates));

    // Force re-render by updating a timestamp
    setLastUpdateTime(new Date());
  }, throttleDelay), [throttleDelay]);

  // Main update function
  var updateComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId, updates) {
    var immediate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    if (!componentId) return;

    // Add to pending updates
    var currentPending = pendingUpdates.get(componentId) || {};
    var newPending = _objectSpread(_objectSpread({}, currentPending), updates);
    setPendingUpdates(function (prev) {
      return new Map(prev.set(componentId, newPending));
    });

    // Add to update queue
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.filter(function (item) {
        return !(item.type === 'update' && item.componentId === componentId);
      })), [{
        type: 'update',
        componentId: componentId,
        data: newPending
      }]);
    });

    // Immediate visual feedback
    if (immediate) {
      throttledVisualUpdate(componentId, updates);
    }

    // Trigger debounced update
    debouncedUpdate(updateQueue);
  }, [pendingUpdates, updateQueue, debouncedUpdate, throttledVisualUpdate]);

  // Add component function
  var addComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentData) {
    var immediate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var componentId = componentData.id || Date.now().toString();
    var newComponent = _objectSpread(_objectSpread({}, componentData), {}, {
      id: componentId
    });
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [{
        type: 'add',
        componentId: componentId,
        data: newComponent
      }]);
    });
    if (immediate) {
      componentCacheRef.current.set(componentId, newComponent);
      setLastUpdateTime(new Date());
    }
    debouncedUpdate(updateQueue);
    return componentId;
  }, [updateQueue, debouncedUpdate]);

  // Delete component function
  var deleteComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId) {
    var immediate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    if (!componentId) return;
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [{
        type: 'delete',
        componentId: componentId
      }]);
    });
    if (immediate) {
      componentCacheRef.current["delete"](componentId);
      setLastUpdateTime(new Date());
    }
    debouncedUpdate(updateQueue);
  }, [updateQueue, debouncedUpdate]);

  // Get component with cached updates
  var getComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId) {
    var originalComponent = components.find(function (c) {
      return c.id === componentId;
    });
    var cachedUpdates = componentCacheRef.current.get(componentId);
    var pendingUpdate = pendingUpdates.get(componentId);
    return _objectSpread(_objectSpread(_objectSpread({}, originalComponent), cachedUpdates), pendingUpdate);
  }, [components, pendingUpdates]);

  // Get all components with cached updates
  var getAllComponents = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    return components.map(function (component) {
      return getComponent(component.id);
    });
  }, [components, getComponent]);

  // Force update function for manual refresh
  var forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    debouncedUpdate.flush();
    componentCacheRef.current.clear();
    setPendingUpdates(new Map());
    setUpdateQueue([]);
  }, [debouncedUpdate]);

  // WebSocket message handler
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!enableWebSocket || !websocketRef.current) return;
    var handleWebSocketMessage = function handleWebSocketMessage(message) {
      var _message$type;
      if ((_message$type = message.type) !== null && _message$type !== void 0 && _message$type.startsWith('component_')) {
        var component_id = message.component_id,
          component_data = message.component_data,
          timestamp = message.timestamp;

        // Update component cache with remote changes
        if (component_data && component_id) {
          componentCacheRef.current.set(component_id, component_data);
          setLastUpdateTime(new Date(timestamp));
        }
      }
    };
    websocketRef.current.addEventListener('message', handleWebSocketMessage);
    return function () {
      if (websocketRef.current) {
        websocketRef.current.removeEventListener('message', handleWebSocketMessage);
      }
    };
  }, [enableWebSocket]);

  // Cleanup on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    return function () {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      debouncedUpdate.cancel();
      throttledVisualUpdate.cancel();
    };
  }, [debouncedUpdate, throttledVisualUpdate]);
  return {
    // State
    isUpdating: isUpdating,
    lastUpdateTime: lastUpdateTime,
    websocketConnected: websocketConnected,
    hasPendingUpdates: pendingUpdates.size > 0,
    // Functions
    updateComponent: updateComponent,
    addComponent: addComponent,
    deleteComponent: deleteComponent,
    getComponent: getComponent,
    getAllComponents: getAllComponents,
    forceUpdate: forceUpdate,
    // Utilities
    clearCache: function clearCache() {
      return componentCacheRef.current.clear();
    },
    getPendingUpdates: function getPendingUpdates() {
      return Array.from(pendingUpdates.entries());
    },
    getUpdateQueueSize: function getUpdateQueueSize() {
      return updateQueue.length;
    }
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRealTimePreview);

/***/ }),

/***/ 81415:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* unused harmony export usePerformanceOptimization */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);




/**
 * Custom hook for performance optimization
 * Provides utilities for measuring and optimizing performance
 */
var usePerformanceOptimization = function usePerformanceOptimization() {
  var _useState = useState(0),
    _useState2 = _slicedToArray(_useState, 2),
    renderCount = _useState2[0],
    setRenderCount = _useState2[1];
  var _useState3 = useState(0),
    _useState4 = _slicedToArray(_useState3, 2),
    lastRenderTime = _useState4[0],
    setLastRenderTime = _useState4[1];
  var _useState5 = useState([]),
    _useState6 = _slicedToArray(_useState5, 2),
    renderTimes = _useState6[0],
    setRenderTimes = _useState6[1];
  var componentMountTime = useRef(performance.now());

  // Increment render count on each render
  useEffect(function () {
    var now = performance.now();
    setRenderCount(function (prev) {
      return prev + 1;
    });
    if (lastRenderTime > 0) {
      var renderTime = now - lastRenderTime;
      setRenderTimes(function (prev) {
        return [].concat(_toConsumableArray(prev), [renderTime]).slice(-10);
      }); // Keep last 10 render times
    }
    setLastRenderTime(now);

    // Mark the render in the performance timeline
    if (window.performance && window.performance.mark) {
      window.performance.mark("render-".concat(renderCount));
    }
  });

  // Calculate average render time
  var averageRenderTime = useMemo(function () {
    if (renderTimes.length === 0) return 0;
    return renderTimes.reduce(function (sum, time) {
      return sum + time;
    }, 0) / renderTimes.length;
  }, [renderTimes]);

  // Calculate time since mount
  var timeSinceMount = useMemo(function () {
    return performance.now() - componentMountTime.current;
  }, [renderCount]);

  // Measure function execution time
  var measureExecutionTime = useCallback(function (fn) {
    var start = performance.now();
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    var result = fn.apply(void 0, args);
    var end = performance.now();
    return {
      result: result,
      executionTime: end - start
    };
  }, []);

  // Debounce function
  var debounce = useCallback(function (fn, delay) {
    var timeoutId;
    return function () {
      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
        args[_key2] = arguments[_key2];
      }
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(function () {
        fn.apply(void 0, args);
      }, delay);
    };
  }, []);

  // Throttle function
  var throttle = useCallback(function (fn, limit) {
    var lastCall = 0;
    return function () {
      var now = performance.now();
      if (now - lastCall < limit) return;
      lastCall = now;
      return fn.apply(void 0, arguments);
    };
  }, []);

  // Memoize expensive function
  var memoize = useCallback(function (fn) {
    var cache = new Map();
    return function () {
      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        args[_key3] = arguments[_key3];
      }
      var key = JSON.stringify(args);
      if (cache.has(key)) return cache.get(key);
      var result = fn.apply(void 0, args);
      cache.set(key, result);
      return result;
    };
  }, []);

  // Log performance metrics
  var logPerformanceMetrics = useCallback(function () {
    console.log('Performance Metrics:');
    console.log("Render Count: ".concat(renderCount));
    console.log("Average Render Time: ".concat(averageRenderTime.toFixed(2), "ms"));
    console.log("Time Since Mount: ".concat(timeSinceMount.toFixed(2), "ms"));
    console.log("Render Times: ".concat(renderTimes.map(function (t) {
      return t.toFixed(2);
    }).join(', '), "ms"));

    // Get performance entries
    if (window.performance && window.performance.getEntriesByType) {
      var paintMetrics = window.performance.getEntriesByType('paint');
      console.log('Paint Metrics:', paintMetrics);
      var navigationMetrics = window.performance.getEntriesByType('navigation');
      console.log('Navigation Metrics:', navigationMetrics);
      var resourceMetrics = window.performance.getEntriesByType('resource');
      console.log('Resource Metrics:', resourceMetrics);
    }
  }, [renderCount, averageRenderTime, timeSinceMount, renderTimes]);
  return {
    renderCount: renderCount,
    averageRenderTime: averageRenderTime,
    timeSinceMount: timeSinceMount,
    renderTimes: renderTimes,
    measureExecutionTime: measureExecutionTime,
    debounce: debounce,
    throttle: throttle,
    memoize: memoize,
    logPerformanceMetrics: logPerformanceMetrics
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (usePerformanceOptimization)));

/***/ })

}]);