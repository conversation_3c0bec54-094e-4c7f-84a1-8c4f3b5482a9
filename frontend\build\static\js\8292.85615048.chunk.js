"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8292],{

/***/ 31103:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(70572);

var _templateObject, _templateObject2, _templateObject3, _templateObject4;




var ContextMenuContainer = styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  position: fixed;\n  z-index: 10000;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border: 1px solid #e8e8e8;\n  min-width: 180px;\n  overflow: hidden;\n  \n  .ant-menu {\n    border: none;\n    box-shadow: none;\n  }\n  \n  .ant-menu-item {\n    margin: 0;\n    padding: 8px 16px;\n    height: auto;\n    line-height: 1.4;\n    \n    &:hover {\n      background: #f0f2f5;\n    }\n    \n    &.ant-menu-item-disabled {\n      color: #bfbfbf;\n      cursor: not-allowed;\n      \n      &:hover {\n        background: transparent;\n      }\n    }\n  }\n  \n  .ant-menu-item-icon {\n    margin-right: 8px;\n    font-size: 14px;\n  }\n"])));
var MenuSection = styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: 4px 0;\n  \n  &:not(:last-child) {\n    border-bottom: 1px solid #f0f0f0;\n  }\n"])));
var MenuItemContent = styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n"])));
var MenuItemShortcut = styled_components__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay.span(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  font-size: 11px;\n  color: #999;\n  margin-left: 16px;\n"])));
var ContextualMenu = function ContextualMenu(_ref) {
  var visible = _ref.visible,
    x = _ref.x,
    y = _ref.y,
    onClose = _ref.onClose,
    selectedComponent = _ref.selectedComponent,
    _ref$selectedComponen = _ref.selectedComponents,
    selectedComponents = _ref$selectedComponen === void 0 ? [] : _ref$selectedComponen,
    onCopy = _ref.onCopy,
    onPaste = _ref.onPaste,
    onDelete = _ref.onDelete,
    onEdit = _ref.onEdit,
    onDuplicate = _ref.onDuplicate,
    onMoveUp = _ref.onMoveUp,
    onMoveDown = _ref.onMoveDown,
    onToggleVisibility = _ref.onToggleVisibility,
    onToggleLock = _ref.onToggleLock,
    onGroup = _ref.onGroup,
    onUngroup = _ref.onUngroup,
    onCopyStyle = _ref.onCopyStyle,
    onPasteStyle = _ref.onPasteStyle,
    onProperties = _ref.onProperties,
    _ref$clipboardHasData = _ref.clipboardHasData,
    clipboardHasData = _ref$clipboardHasData === void 0 ? false : _ref$clipboardHasData,
    _ref$canMoveUp = _ref.canMoveUp,
    canMoveUp = _ref$canMoveUp === void 0 ? true : _ref$canMoveUp,
    _ref$canMoveDown = _ref.canMoveDown,
    canMoveDown = _ref$canMoveDown === void 0 ? true : _ref$canMoveDown,
    _ref$canGroup = _ref.canGroup,
    canGroup = _ref$canGroup === void 0 ? false : _ref$canGroup,
    _ref$canUngroup = _ref.canUngroup,
    canUngroup = _ref$canUngroup === void 0 ? false : _ref$canUngroup;
  var menuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);

  // Position menu and handle viewport boundaries
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (visible && menuRef.current) {
      var menu = menuRef.current;
      var rect = menu.getBoundingClientRect();
      var viewportWidth = window.innerWidth;
      var viewportHeight = window.innerHeight;
      var adjustedX = x;
      var adjustedY = y;

      // Adjust horizontal position if menu would overflow
      if (x + rect.width > viewportWidth) {
        adjustedX = viewportWidth - rect.width - 10;
      }

      // Adjust vertical position if menu would overflow
      if (y + rect.height > viewportHeight) {
        adjustedY = viewportHeight - rect.height - 10;
      }
      menu.style.left = "".concat(Math.max(10, adjustedX), "px");
      menu.style.top = "".concat(Math.max(10, adjustedY), "px");
    }
  }, [visible, x, y]);

  // Close menu on escape key
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var handleKeyDown = function handleKeyDown(e) {
      if (e.key === 'Escape' && visible) {
        onClose();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return function () {
      return document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, onClose]);
  if (!visible) return null;
  var isMultipleSelection = selectedComponents.length > 1;
  var hasSelection = selectedComponent || selectedComponents.length > 0;
  var menuItems = [
  // Edit section
  {
    section: 'edit',
    items: [{
      key: 'edit',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .EditOutlined */ .xjh, null),
      label: 'Edit Properties',
      shortcut: 'Enter',
      disabled: !hasSelection || isMultipleSelection,
      onClick: function onClick() {
        onEdit === null || onEdit === void 0 || onEdit(selectedComponent);
        onClose();
      }
    }, {
      key: 'copy',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .CopyOutlined */ .wq3, null),
      label: isMultipleSelection ? "Copy ".concat(selectedComponents.length, " Components") : 'Copy',
      shortcut: 'Ctrl+C',
      disabled: !hasSelection,
      onClick: function onClick() {
        onCopy === null || onCopy === void 0 || onCopy(isMultipleSelection ? selectedComponents : selectedComponent);
        onClose();
      }
    }, {
      key: 'paste',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .CopyOutlined */ .wq3, {
        style: {
          transform: 'scaleX(-1)'
        }
      }),
      label: 'Paste',
      shortcut: 'Ctrl+V',
      disabled: !clipboardHasData,
      onClick: function onClick() {
        onPaste === null || onPaste === void 0 || onPaste();
        onClose();
      }
    }, {
      key: 'duplicate',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .CopyOutlined */ .wq3, null),
      label: isMultipleSelection ? 'Duplicate Selection' : 'Duplicate',
      shortcut: 'Ctrl+D',
      disabled: !hasSelection,
      onClick: function onClick() {
        onDuplicate === null || onDuplicate === void 0 || onDuplicate(isMultipleSelection ? selectedComponents : selectedComponent);
        onClose();
      }
    }]
  },
  // Arrange section
  {
    section: 'arrange',
    items: [{
      key: 'move-up',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .ArrowUpOutlined */ .lu9, null),
      label: 'Move Up',
      shortcut: 'Ctrl+↑',
      disabled: !hasSelection || !canMoveUp,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onMoveUp === null || onMoveUp === void 0 ? void 0 : onMoveUp(comp);
          });
        } else {
          onMoveUp === null || onMoveUp === void 0 || onMoveUp(selectedComponent);
        }
        onClose();
      }
    }, {
      key: 'move-down',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .ArrowDownOutlined */ .Axk, null),
      label: 'Move Down',
      shortcut: 'Ctrl+↓',
      disabled: !hasSelection || !canMoveDown,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onMoveDown === null || onMoveDown === void 0 ? void 0 : onMoveDown(comp);
          });
        } else {
          onMoveDown === null || onMoveDown === void 0 || onMoveDown(selectedComponent);
        }
        onClose();
      }
    }]
  },
  // Visibility section
  {
    section: 'visibility',
    items: [{
      key: 'toggle-visibility',
      icon: (selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.visible) !== false ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .EyeInvisibleOutlined */ .LCF, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .EyeOutlined */ .Om2, null),
      label: (selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.visible) !== false ? 'Hide' : 'Show',
      shortcut: 'Ctrl+H',
      disabled: !hasSelection,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onToggleVisibility === null || onToggleVisibility === void 0 ? void 0 : onToggleVisibility(comp);
          });
        } else {
          onToggleVisibility === null || onToggleVisibility === void 0 || onToggleVisibility(selectedComponent);
        }
        onClose();
      }
    }, {
      key: 'toggle-lock',
      icon: selectedComponent !== null && selectedComponent !== void 0 && selectedComponent.locked ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .UnlockOutlined */ .Rrh, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .LockOutlined */ .sXv, null),
      label: selectedComponent !== null && selectedComponent !== void 0 && selectedComponent.locked ? 'Unlock' : 'Lock',
      shortcut: 'Ctrl+L',
      disabled: !hasSelection,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onToggleLock === null || onToggleLock === void 0 ? void 0 : onToggleLock(comp);
          });
        } else {
          onToggleLock === null || onToggleLock === void 0 || onToggleLock(selectedComponent);
        }
        onClose();
      }
    }]
  },
  // Group section
  {
    section: 'group',
    items: [{
      key: 'group',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .GroupOutlined */ .WT5, null),
      label: 'Group',
      shortcut: 'Ctrl+G',
      disabled: !canGroup || selectedComponents.length < 2,
      onClick: function onClick() {
        onGroup === null || onGroup === void 0 || onGroup(selectedComponents);
        onClose();
      }
    }, {
      key: 'ungroup',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .UngroupOutlined */ .T6x, null),
      label: 'Ungroup',
      shortcut: 'Ctrl+Shift+G',
      disabled: !canUngroup,
      onClick: function onClick() {
        onUngroup === null || onUngroup === void 0 || onUngroup(selectedComponent);
        onClose();
      }
    }]
  },
  // Style section
  {
    section: 'style',
    items: [{
      key: 'copy-style',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .FormatPainterOutlined */ .tBh, null),
      label: 'Copy Style',
      disabled: !hasSelection || isMultipleSelection,
      onClick: function onClick() {
        onCopyStyle === null || onCopyStyle === void 0 || onCopyStyle(selectedComponent);
        onClose();
      }
    }, {
      key: 'paste-style',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .FormatPainterOutlined */ .tBh, {
        style: {
          transform: 'scaleX(-1)'
        }
      }),
      label: 'Paste Style',
      disabled: !hasSelection,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onPasteStyle === null || onPasteStyle === void 0 ? void 0 : onPasteStyle(comp);
          });
        } else {
          onPasteStyle === null || onPasteStyle === void 0 || onPasteStyle(selectedComponent);
        }
        onClose();
      }
    }]
  },
  // Actions section
  {
    section: 'actions',
    items: [{
      key: 'properties',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .SettingOutlined */ .JO7, null),
      label: 'Properties',
      shortcut: 'F4',
      disabled: !hasSelection || isMultipleSelection,
      onClick: function onClick() {
        onProperties === null || onProperties === void 0 || onProperties(selectedComponent);
        onClose();
      }
    }, {
      key: 'delete',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .DeleteOutlined */ .SUY, null),
      label: isMultipleSelection ? "Delete ".concat(selectedComponents.length, " Components") : 'Delete',
      shortcut: 'Delete',
      disabled: !hasSelection,
      danger: true,
      onClick: function onClick() {
        if (isMultipleSelection) {
          selectedComponents.forEach(function (comp) {
            return onDelete === null || onDelete === void 0 ? void 0 : onDelete(comp);
          });
        } else {
          onDelete === null || onDelete === void 0 || onDelete(selectedComponent);
        }
        onClose();
      }
    }]
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ContextMenuContainer, {
    ref: menuRef,
    style: {
      left: x,
      top: y
    },
    onClick: function onClick(e) {
      return e.stopPropagation();
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Menu */ .W1, {
    mode: "vertical",
    selectable: false
  }, menuItems.map(function (section, sectionIndex) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(MenuSection, {
      key: section.section
    }, section.items.map(function (item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Menu */ .W1.Item, {
        key: item.key,
        icon: item.icon,
        disabled: item.disabled,
        danger: item.danger,
        onClick: item.onClick
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(MenuItemContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("span", null, item.label), item.shortcut && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(MenuItemShortcut, null, item.shortcut)));
    }));
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContextualMenu);

/***/ }),

/***/ 89926:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports DropIndicatorLine, DropZoneOverlayComponent, DragGhost, HoverIndicatorComponent, SuccessIndicatorComponent */
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(70572);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(35346);

var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12;




// Keyframe animations
var pulse = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__/* .keyframes */ .i7)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.05);\n  }\n"])));
var shake = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__/* .keyframes */ .i7)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  0%, 100% { transform: translateX(0); }\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }\n  20%, 40%, 60%, 80% { transform: translateX(3px); }\n"])));
var bounce = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__/* .keyframes */ .i7)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0, 0, 0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -8px, 0);\n  }\n  70% {\n    transform: translate3d(0, -4px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n"])));
var fadeIn = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__/* .keyframes */ .i7)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n"])));
var glow = (0,styled_components__WEBPACK_IMPORTED_MODULE_2__/* .keyframes */ .i7)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);\n  }\n"])));

// Styled components
var DropIndicator = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  position: absolute;\n  width: 100%;\n  height: 4px;\n  background: ", ";\n  border-radius: 2px;\n  z-index: 1000;\n  animation: ", " 1.5s ease-in-out infinite;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ", ";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    right: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ", ";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n"])), function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
}, pulse, function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
}, function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
});
var DropZoneOverlay = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 3px dashed ", ";\n  border-radius: 12px;\n  background: ", ";\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n  animation: ", " 0.3s ease-out;\n  backdrop-filter: blur(4px);\n  box-shadow: ", ";\n\n  ", "\n\n  ", "\n"])), function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
}, function (props) {
  return props.isValid ? 'linear-gradient(135deg, rgba(82, 196, 26, 0.08) 0%, rgba(82, 196, 26, 0.15) 100%)' : 'linear-gradient(135deg, rgba(255, 77, 79, 0.08) 0%, rgba(255, 77, 79, 0.15) 100%)';
}, fadeIn, function (props) {
  return props.isValid ? '0 8px 32px rgba(82, 196, 26, 0.2)' : '0 8px 32px rgba(255, 77, 79, 0.2)';
}, function (props) {
  return props.isValid && (0,styled_components__WEBPACK_IMPORTED_MODULE_2__/* .css */ .AH)(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n    animation: ", " 1.5s ease-in-out infinite;\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: -6px;\n      left: -6px;\n      right: -6px;\n      bottom: -6px;\n      border: 2px solid rgba(82, 196, 26, 0.3);\n      border-radius: 16px;\n      animation: ", " 2s ease-in-out infinite reverse;\n    }\n  "])), pulse, pulse);
}, function (props) {
  return !props.isValid && (0,styled_components__WEBPACK_IMPORTED_MODULE_2__/* .css */ .AH)(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n    animation: ", " 0.5s ease-in-out;\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: -6px;\n      left: -6px;\n      right: -6px;\n      bottom: -6px;\n      border: 2px solid rgba(255, 77, 79, 0.3);\n      border-radius: 16px;\n    }\n  "])), shake);
});
var DropZoneMessage = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 16px 24px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\n  font-weight: 600;\n  font-size: 16px;\n  color: ", ";\n  animation: ", " 0.3s ease-out 0.1s both;\n  backdrop-filter: blur(8px);\n  border: 1px solid ", ";\n\n  svg {\n    font-size: 20px;\n  }\n\n  /* Add subtle hint text */\n  &::after {\n    content: '", "';\n    display: block;\n    font-size: 12px;\n    font-weight: 400;\n    color: ", ";\n    margin-top: 4px;\n  }\n"])), function (props) {
  return props.isValid ? '#52c41a' : '#ff4d4f';
}, fadeIn, function (props) {
  return props.isValid ? 'rgba(82, 196, 26, 0.2)' : 'rgba(255, 77, 79, 0.2)';
}, function (props) {
  return props.isValid ? 'Release to add' : 'Choose valid location';
}, function (props) {
  return props.isValid ? 'rgba(82, 196, 26, 0.7)' : 'rgba(255, 77, 79, 0.7)';
});
var GhostElement = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  position: fixed;\n  pointer-events: none;\n  z-index: 9999;\n  opacity: 0.8;\n  transform: rotate(3deg) scale(0.95);\n  filter: blur(0.5px);\n  transition: all 0.15s ease-out;\n  border: 2px solid #1890ff;\n  border-radius: 8px;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);\n  box-shadow: 0 12px 40px rgba(24, 144, 255, 0.4);\n  backdrop-filter: blur(4px);\n\n  /* Add floating animation */\n  animation: ", " 2s ease-in-out infinite;\n\n  /* Enhanced visual depth */\n  &::before {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    border: 1px solid rgba(24, 144, 255, 0.3);\n    border-radius: 10px;\n    animation: ", " 1.5s ease-in-out infinite;\n  }\n"])), glow, pulse);
var DragPreview = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject10 || (_templateObject10 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: 12px 16px;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);\n  border: 2px solid #1890ff;\n  border-radius: 8px;\n  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.3);\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-weight: 600;\n  font-size: 14px;\n  color: #1890ff;\n  animation: ", " 2s ease-in-out infinite;\n  backdrop-filter: blur(8px);\n  min-width: 120px;\n\n  /* Add component type indicator */\n  &::before {\n    content: '';\n    width: 8px;\n    height: 8px;\n    background: #1890ff;\n    border-radius: 50%;\n    animation: ", " 1s ease-in-out infinite;\n  }\n\n  svg {\n    font-size: 16px;\n  }\n"])), glow, pulse);
var HoverIndicator = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject11 || (_templateObject11 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  background: rgba(24, 144, 255, 0.05);\n  pointer-events: none;\n  z-index: 10;\n  animation: ", " 0.2s ease-out;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    border: 1px solid rgba(24, 144, 255, 0.3);\n    border-radius: 8px;\n    animation: ", " 2s ease-in-out infinite;\n  }\n"])), fadeIn, pulse);
var SuccessIndicator = styled_components__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay.div(_templateObject12 || (_templateObject12 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: #52c41a;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-weight: 600;\n  z-index: 1001;\n  animation: ", " 0.6s ease-out;\n  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);\n"])), bounce);

// Component for drop indicator line
var DropIndicatorLine = function DropIndicatorLine(_ref) {
  var position = _ref.position,
    _ref$isValid = _ref.isValid,
    isValid = _ref$isValid === void 0 ? true : _ref$isValid,
    _ref$visible = _ref.visible,
    visible = _ref$visible === void 0 ? false : _ref$visible;
  if (!visible || !position) return null;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(DropIndicator, {
    isValid: isValid,
    style: {
      top: position.y,
      left: position.x,
      width: position.width || '100%'
    }
  });
};

// Component for drop zone overlay
var DropZoneOverlayComponent = function DropZoneOverlayComponent(_ref2) {
  var _ref2$isValid = _ref2.isValid,
    isValid = _ref2$isValid === void 0 ? true : _ref2$isValid,
    _ref2$visible = _ref2.visible,
    visible = _ref2$visible === void 0 ? false : _ref2$visible,
    message = _ref2.message;
  if (!visible) return null;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(DropZoneOverlay, {
    isValid: isValid
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(DropZoneMessage, {
    isValid: isValid
  }, isValid ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .CheckCircleOutlined */ .hWy, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .CloseCircleOutlined */ .bBN, null), message || (isValid ? 'Drop here to add component' : 'Invalid drop target')));
};

// Component for ghost element during drag
var DragGhost = function DragGhost(_ref3) {
  var _ref3$visible = _ref3.visible,
    visible = _ref3$visible === void 0 ? false : _ref3$visible,
    position = _ref3.position,
    children = _ref3.children,
    componentType = _ref3.componentType;
  var ghostRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (ghostRef.current && visible && position) {
      ghostRef.current.style.left = "".concat(position.x, "px");
      ghostRef.current.style.top = "".concat(position.y, "px");
    }
  }, [visible, position]);
  if (!visible) return null;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(GhostElement, {
    ref: ghostRef
  }, children || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(DragPreview, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .DragOutlined */ .duJ, null), componentType || 'Component'));
};

// Component for hover indicator
var HoverIndicatorComponent = function HoverIndicatorComponent(_ref4) {
  var _ref4$visible = _ref4.visible,
    visible = _ref4$visible === void 0 ? false : _ref4$visible,
    targetRef = _ref4.targetRef;
  var indicatorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    if (indicatorRef.current && targetRef !== null && targetRef !== void 0 && targetRef.current && visible) {
      var rect = targetRef.current.getBoundingClientRect();
      var indicator = indicatorRef.current;
      indicator.style.position = 'fixed';
      indicator.style.top = "".concat(rect.top, "px");
      indicator.style.left = "".concat(rect.left, "px");
      indicator.style.width = "".concat(rect.width, "px");
      indicator.style.height = "".concat(rect.height, "px");
    }
  }, [visible, targetRef]);
  if (!visible) return null;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(HoverIndicator, {
    ref: indicatorRef
  });
};

// Component for success indicator
var SuccessIndicatorComponent = function SuccessIndicatorComponent(_ref5) {
  var _ref5$visible = _ref5.visible,
    visible = _ref5$visible === void 0 ? false : _ref5$visible,
    _ref5$message = _ref5.message,
    message = _ref5$message === void 0 ? 'Component added!' : _ref5$message;
  if (!visible) return null;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(SuccessIndicator, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .CheckCircleOutlined */ .hWy, null), message);
};

// Main visual feedback component
var DragVisualFeedback = function DragVisualFeedback(_ref6) {
  var _ref6$isDragging = _ref6.isDragging,
    isDragging = _ref6$isDragging === void 0 ? false : _ref6$isDragging,
    _ref6$isOver = _ref6.isOver,
    isOver = _ref6$isOver === void 0 ? false : _ref6$isOver,
    _ref6$isValid = _ref6.isValid,
    isValid = _ref6$isValid === void 0 ? true : _ref6$isValid,
    dropPosition = _ref6.dropPosition,
    ghostPosition = _ref6.ghostPosition,
    hoveredElement = _ref6.hoveredElement,
    draggedComponent = _ref6.draggedComponent,
    _ref6$showSuccess = _ref6.showSuccess,
    showSuccess = _ref6$showSuccess === void 0 ? false : _ref6$showSuccess,
    successMessage = _ref6.successMessage,
    dropMessage = _ref6.dropMessage,
    children = _ref6.children;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(DropIndicatorLine, {
    position: dropPosition,
    isValid: isValid,
    visible: isDragging && isOver && dropPosition
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(DropZoneOverlayComponent, {
    isValid: isValid,
    visible: isDragging && isOver,
    message: dropMessage
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(DragGhost, {
    visible: isDragging,
    position: ghostPosition,
    componentType: draggedComponent === null || draggedComponent === void 0 ? void 0 : draggedComponent.type
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(HoverIndicatorComponent, {
    visible: !isDragging && hoveredElement,
    targetRef: hoveredElement
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(SuccessIndicatorComponent, {
    visible: showSuccess,
    message: successMessage
  }), children);
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DragVisualFeedback);

/***/ })

}]);