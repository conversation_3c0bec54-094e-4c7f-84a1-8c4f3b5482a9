"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3879],{

/***/ 18563:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AH: () => (/* reexport safe */ styled_components__WEBPACK_IMPORTED_MODULE_0__.AH),
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   DU: () => (/* reexport safe */ styled_components__WEBPACK_IMPORTED_MODULE_0__.DU),
/* harmony export */   I4: () => (/* reexport safe */ styled_components__WEBPACK_IMPORTED_MODULE_0__.Ay),
/* harmony export */   NP: () => (/* reexport safe */ styled_components__WEBPACK_IMPORTED_MODULE_0__.NP),
/* harmony export */   i7: () => (/* reexport safe */ styled_components__WEBPACK_IMPORTED_MODULE_0__.i7)
/* harmony export */ });
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(70572);
/**
 * Re-export styled-components
 * This file re-exports styled-components to provide a consistent API
 */



// Export all styled-components exports


// Export styled as default for convenience
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (styled_components__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Ay);

/***/ }),

/***/ 30403:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(56822);
/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(53954);
/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85501);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(96540);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(5556);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _design_system_styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(18563);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(86020);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(79146);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(35346);
/* harmony import */ var _utils_errorTracker__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(17648);







var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;
function _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }







var ErrorContainer = _design_system_styled_components__WEBPACK_IMPORTED_MODULE_9__/* .styled */ .I4.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ", ";\n  background-color: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  max-width: 800px;\n  margin: 0 auto;\n  text-align: center;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[6], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.neutral[100], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].borderRadius */ .Ay.borderRadius.lg, _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].shadows */ .Ay.shadows.md);
var ErrorIcon = _design_system_styled_components__WEBPACK_IMPORTED_MODULE_9__/* .styled */ .I4.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(["\n  font-size: 48px;\n  color: ", ";\n  margin-bottom: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.error.main, _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[4]);
var ErrorTitle = _design_system_styled_components__WEBPACK_IMPORTED_MODULE_9__/* .styled */ .I4.h2(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(["\n  font-size: ", ";\n  color: ", ";\n  margin-bottom: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].typography */ .Ay.typography.fontSize.xl, _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.neutral[900], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[3]);
var ErrorMessage = _design_system_styled_components__WEBPACK_IMPORTED_MODULE_9__/* .styled */ .I4.p(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(["\n  font-size: ", ";\n  color: ", ";\n  margin-bottom: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].typography */ .Ay.typography.fontSize.md, _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.neutral[700], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[4]);
var ErrorDetails = _design_system_styled_components__WEBPACK_IMPORTED_MODULE_9__/* .styled */ .I4.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(["\n  background-color: ", ";\n  padding: ", ";\n  border-radius: ", ";\n  margin-bottom: ", ";\n  text-align: left;\n  overflow: auto;\n  max-height: 200px;\n  width: 100%;\n  font-family: ", ";\n  font-size: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.neutral[200], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[3], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].borderRadius */ .Ay.borderRadius.md, _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[4], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].typography */ .Ay.typography.fontFamily.code, _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].typography */ .Ay.typography.fontSize.sm);
var ActionButtons = _design_system_styled_components__WEBPACK_IMPORTED_MODULE_9__/* .styled */ .I4.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(["\n  display: flex;\n  gap: ", ";\n  margin-top: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[3], _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[4]);

/**
 * Enhanced Error Boundary component
 * Catches JavaScript errors in child components and displays a fallback UI
 */
var EnhancedErrorBoundary = /*#__PURE__*/function (_React$Component) {
  function EnhancedErrorBoundary(props) {
    var _this;
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this, EnhancedErrorBoundary);
    _this = _callSuper(this, EnhancedErrorBoundary, [props]);
    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(_this, "handleReload", function () {
      // Reload the current page
      window.location.reload();
    });
    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(_this, "handleReset", function () {
      // Reset the error state to try rendering the children again
      _this.setState({
        hasError: false,
        error: null,
        errorInfo: null
      });
    });
    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(_this, "handleGoHome", function () {
      // Navigate to the home page
      window.location.href = '/';
    });
    _this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorCount: 0
    };
    return _this;
  }
  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(EnhancedErrorBoundary, _React$Component);
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(EnhancedErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      // Log the error to an error reporting service
      console.error('Error caught by ErrorBoundary:', error, errorInfo);

      // Track the error with our error tracker
      _utils_errorTracker__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A.trackError(error, {
        componentStack: errorInfo.componentStack,
        source: 'react_error_boundary',
        component: this.constructor.name,
        props: JSON.stringify(this.props)
      });

      // Add a breadcrumb for the error
      _utils_errorTracker__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A.addBreadcrumb("Error in component: ".concat(error.message), 'error_boundary', {
        componentStack: errorInfo.componentStack
      });

      // Update state with error details
      this.setState(function (prevState) {
        return {
          errorInfo: errorInfo,
          errorCount: prevState.errorCount + 1
        };
      });

      // Send error to analytics or error tracking service
      if (this.props.onError) {
        this.props.onError(error, errorInfo);
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this$state = this.state,
        hasError = _this$state.hasError,
        error = _this$state.error,
        errorInfo = _this$state.errorInfo,
        errorCount = _this$state.errorCount;
      var _this$props = this.props,
        fallback = _this$props.fallback,
        children = _this$props.children;

      // If there's no error, render children normally
      if (!hasError) {
        return children;
      }

      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback(error, errorInfo, this.handleReset);
      }

      // Otherwise, use the default error UI
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ErrorContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ErrorIcon, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__/* .WarningOutlined */ .v7y, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ErrorTitle, null, "Something went wrong"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ErrorMessage, null, "We're sorry, but an error occurred while rendering this component.", errorCount > 1 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          marginTop: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[2],
          color: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].colors */ .Ay.colors.error.main
        }
      }, "Multiple errors detected (", errorCount, "). You may need to reload the page.")), error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ErrorDetails, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("strong", null, "Error:"), " ", error.toString(), errorInfo && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          marginTop: _design_system_theme__WEBPACK_IMPORTED_MODULE_10__/* ["default"].spacing */ .Ay.spacing[2]
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("strong", null, "Component Stack:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("pre", null, errorInfo.componentStack))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ActionButtons, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_11__.Button, {
        onClick: this.handleReset,
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__/* .BugOutlined */ .NhG, null),
        variant: "outline"
      }, "Try Again"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_11__.Button, {
        onClick: this.handleReload,
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__/* .ReloadOutlined */ .KF4, null)
      }, "Reload Page"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_design_system__WEBPACK_IMPORTED_MODULE_11__.Button, {
        onClick: this.handleGoHome,
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__/* .HomeOutlined */ .aod, null),
        variant: "outline"
      }, "Go to Home")));
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      // Update state so the next render will show the fallback UI
      return {
        hasError: true,
        error: error
      };
    }
  }]);
}(react__WEBPACK_IMPORTED_MODULE_7__.Component);
EnhancedErrorBoundary.propTypes = {
  children: (prop_types__WEBPACK_IMPORTED_MODULE_8___default().node).isRequired,
  fallback: (prop_types__WEBPACK_IMPORTED_MODULE_8___default().func),
  onError: (prop_types__WEBPACK_IMPORTED_MODULE_8___default().func)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedErrorBoundary);

/***/ }),

/***/ 33246:
/***/ (() => {

// extracted by mini-css-extract-plugin


/***/ }),

/***/ 48751:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   st: () => (/* binding */ useAnalytics),
/* harmony export */   yP: () => (/* binding */ AnalyticsProvider)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(96540);


// Create analytics context
var AnalyticsContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({
  trackEvent: function trackEvent() {},
  trackPageView: function trackPageView() {},
  trackError: function trackError() {}
});

/**
 * Analytics provider component
 * Provides analytics tracking functions throughout the application
 */
var AnalyticsProvider = function AnalyticsProvider(_ref) {
  var children = _ref.children;
  // Track event
  var trackEvent = function trackEvent(eventName) {
    var eventData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    // In a real application, this would send data to an analytics service
    console.log("[Analytics] Event: ".concat(eventName), eventData);
  };

  // Track page view
  var trackPageView = function trackPageView(pageName) {
    var pageData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    // In a real application, this would send data to an analytics service
    console.log("[Analytics] Page View: ".concat(pageName), pageData);
  };

  // Track error
  var trackError = function trackError(errorName) {
    var errorData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    // In a real application, this would send data to an analytics service
    console.log("[Analytics] Error: ".concat(errorName), errorData);
  };

  // Create context value
  var contextValue = {
    trackEvent: trackEvent,
    trackPageView: trackPageView,
    trackError: trackError
  };
  return /*#__PURE__*/React.createElement(AnalyticsContext.Provider, {
    value: contextValue
  }, children);
};

/**
 * Custom hook for using analytics
 * @returns {Object} Analytics context
 */
var useAnalytics = function useAnalytics() {
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(AnalyticsContext);
};

// Default export for convenience
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  trackEvent: function trackEvent(eventName) {
    var eventData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    console.log("[Analytics] Event: ".concat(eventName), eventData);
  },
  trackPageView: function trackPageView(pageName) {
    var pageData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    console.log("[Analytics] Page View: ".concat(pageName), pageData);
  },
  trackError: function trackError(errorName) {
    var errorData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    console.log("[Analytics] Error: ".concat(errorName), errorData);
  }
});

/***/ }),

/***/ 79146:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Button: () => (/* reexport */ design_system_Button),
  Card: () => (/* reexport */ design_system_Card),
  Input: () => (/* reexport */ design_system_Input),
  Select: () => (/* reexport */ design_system_Select),
  Text: () => (/* reexport */ components_Text),
  ThemeProvider: () => (/* reexport */ styled_components/* ThemeProvider */.NP),
  VERSION: () => (/* binding */ VERSION),
  a11yUtils: () => (/* reexport */ a11yUtils),
  accessibility: () => (/* reexport */ theme/* accessibility */.Ti),
  animationUtils: () => (/* reexport */ animationUtils),
  animations: () => (/* reexport */ theme/* animations */.WT),
  borderRadius: () => (/* reexport */ theme/* borderRadius */.Vq),
  breakpoints: () => (/* reexport */ theme/* breakpoints */.fi),
  colorUtils: () => (/* reexport */ colorUtils),
  colors: () => (/* reexport */ theme/* colors */.Tj),
  componentTokens: () => (/* reexport */ theme/* components */.dK),
  componentUtils: () => (/* reexport */ componentUtils),
  config: () => (/* binding */ config),
  createGlobalStyle: () => (/* reexport */ styled_components/* createGlobalStyle */.DU),
  css: () => (/* reexport */ styled_components/* css */.AH),
  designUtils: () => (/* reexport */ utils),
  globalStyles: () => (/* binding */ globalStyles),
  hierarchyUtils: () => (/* reexport */ hierarchyUtils),
  informationArchitecture: () => (/* reexport */ informationArchitecture),
  interactionPatterns: () => (/* reexport */ interactionPatterns),
  keyframes: () => (/* reexport */ styled_components/* keyframes */.i7),
  mediaQueries: () => (/* reexport */ theme/* mediaQueries */.HP),
  responsiveUtils: () => (/* reexport */ responsiveUtils),
  shadows: () => (/* reexport */ theme/* shadows */.Eo),
  spacing: () => (/* reexport */ theme/* spacing */.YK),
  spacingHierarchy: () => (/* reexport */ spacingHierarchy),
  spacingUtils: () => (/* reexport */ spacingUtils),
  styled: () => (/* reexport */ styled_components/* default */.Ay),
  theme: () => (/* reexport */ theme/* default */.Ay),
  transitions: () => (/* reexport */ theme/* transitions */.bm),
  typography: () => (/* reexport */ theme/* typography */.Il),
  typographyUtils: () => (/* reexport */ typographyUtils),
  visualGrouping: () => (/* reexport */ visualGrouping),
  visualHierarchy: () => (/* reexport */ typographyHierarchy),
  zIndex: () => (/* reexport */ theme/* zIndex */.fE)
});

// EXTERNAL MODULE: ./src/design-system/theme.js
var theme = __webpack_require__(86020);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
;// ./src/design-system/visual-hierarchy.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Visual Hierarchy System
 * 
 * Comprehensive system for implementing clear information architecture
 * with proper spacing, typography scales, visual grouping, and consistent
 * interaction patterns throughout the application.
 */



/**
 * Typography Hierarchy
 * Semantic typography scales for consistent information hierarchy
 */
var typographyHierarchy = {
  // Display text for hero sections and major headings
  display: {
    large: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize['6xl'],
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.bold,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.tight,
      letterSpacing: '-0.025em',
      marginBottom: theme/* default.spacing */.Ay.spacing[6],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    medium: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize['5xl'],
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.bold,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.tight,
      letterSpacing: '-0.025em',
      marginBottom: theme/* default.spacing */.Ay.spacing[5],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    small: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize['4xl'],
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.semibold,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.tight,
      letterSpacing: '-0.025em',
      marginBottom: theme/* default.spacing */.Ay.spacing[4],
      color: theme/* default.colors */.Ay.colors.text.primary
    }
  },
  // Headings for sections and subsections
  heading: {
    h1: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize['3xl'],
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.bold,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.tight,
      marginBottom: theme/* default.spacing */.Ay.spacing[4],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    h2: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize['2xl'],
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.semibold,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.snug,
      marginBottom: theme/* default.spacing */.Ay.spacing[3],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    h3: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.xl,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.semibold,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.snug,
      marginBottom: theme/* default.spacing */.Ay.spacing[3],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    h4: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.lg,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.normal,
      marginBottom: theme/* default.spacing */.Ay.spacing[2],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    h5: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.base,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.normal,
      marginBottom: theme/* default.spacing */.Ay.spacing[2],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    h6: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.sm,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.normal,
      marginBottom: theme/* default.spacing */.Ay.spacing[1],
      color: theme/* default.colors */.Ay.colors.text.secondary,
      textTransform: 'uppercase',
      letterSpacing: '0.05em'
    }
  },
  // Body text for content
  body: {
    large: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.lg,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.regular,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.relaxed,
      marginBottom: theme/* default.spacing */.Ay.spacing[4],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    medium: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.base,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.regular,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.relaxed,
      marginBottom: theme/* default.spacing */.Ay.spacing[3],
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    small: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.sm,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.regular,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.normal,
      marginBottom: theme/* default.spacing */.Ay.spacing[2],
      color: theme/* default.colors */.Ay.colors.text.secondary
    }
  },
  // Labels and captions
  label: {
    large: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.sm,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.normal,
      color: theme/* default.colors */.Ay.colors.text.primary
    },
    medium: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.xs,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.normal,
      color: theme/* default.colors */.Ay.colors.text.secondary
    },
    small: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.xs,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.regular,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.normal,
      color: theme/* default.colors */.Ay.colors.text.tertiary
    }
  },
  // Interactive elements
  interactive: {
    button: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.sm,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.tight,
      letterSpacing: '0.025em'
    },
    link: {
      fontSize: 'inherit',
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      lineHeight: 'inherit',
      color: theme/* default.colors */.Ay.colors.primary.main,
      textDecoration: 'none'
    },
    tab: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.sm,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      lineHeight: theme/* default.typography */.Ay.typography.lineHeight.normal
    }
  }
};

/**
 * Spacing Hierarchy
 * Consistent spacing system for visual grouping and rhythm
 */
var spacingHierarchy = {
  // Component internal spacing
  component: {
    tight: theme/* default.spacing */.Ay.spacing[1],
    // 4px - Very tight spacing within components
    normal: theme/* default.spacing */.Ay.spacing[2],
    // 8px - Normal internal spacing
    comfortable: theme/* default.spacing */.Ay.spacing[3],
    // 12px - Comfortable internal spacing
    loose: theme/* default.spacing */.Ay.spacing[4] // 16px - Loose internal spacing
  },
  // Element spacing (between related elements)
  element: {
    tight: theme/* default.spacing */.Ay.spacing[2],
    // 8px - Related elements
    normal: theme/* default.spacing */.Ay.spacing[3],
    // 12px - Standard element spacing
    comfortable: theme/* default.spacing */.Ay.spacing[4],
    // 16px - Comfortable element spacing
    loose: theme/* default.spacing */.Ay.spacing[6] // 24px - Loose element spacing
  },
  // Section spacing (between content sections)
  section: {
    tight: theme/* default.spacing */.Ay.spacing[6],
    // 24px - Related sections
    normal: theme/* default.spacing */.Ay.spacing[8],
    // 32px - Standard section spacing
    comfortable: theme/* default.spacing */.Ay.spacing[12],
    // 48px - Comfortable section spacing
    loose: theme/* default.spacing */.Ay.spacing[16] // 64px - Major section breaks
  },
  // Layout spacing (major layout areas)
  layout: {
    tight: theme/* default.spacing */.Ay.spacing[8],
    // 32px - Tight layout spacing
    normal: theme/* default.spacing */.Ay.spacing[12],
    // 48px - Standard layout spacing
    comfortable: theme/* default.spacing */.Ay.spacing[16],
    // 64px - Comfortable layout spacing
    loose: theme/* default.spacing */.Ay.spacing[24] // 96px - Loose layout spacing
  }
};

/**
 * Visual Grouping System
 * Consistent patterns for grouping related content
 */
var visualGrouping = {
  // Card-based grouping
  card: {
    minimal: {
      background: theme/* default.colors */.Ay.colors.background.paper,
      border: "1px solid ".concat(theme/* default.colors */.Ay.colors.border.light),
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.md,
      padding: theme/* default.spacing */.Ay.spacing[4],
      boxShadow: 'none'
    },
    elevated: {
      background: theme/* default.colors */.Ay.colors.background.paper,
      border: "1px solid ".concat(theme/* default.colors */.Ay.colors.border.light),
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.lg,
      padding: theme/* default.spacing */.Ay.spacing[6],
      boxShadow: theme/* default.shadows */.Ay.shadows.md
    },
    prominent: {
      background: theme/* default.colors */.Ay.colors.background.paper,
      border: "1px solid ".concat(theme/* default.colors */.Ay.colors.border.light),
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.lg,
      padding: theme/* default.spacing */.Ay.spacing[8],
      boxShadow: theme/* default.shadows */.Ay.shadows.lg
    }
  },
  // Section-based grouping
  section: {
    subtle: {
      background: theme/* default.colors */.Ay.colors.background.secondary,
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.md,
      padding: theme/* default.spacing */.Ay.spacing[4]
    },
    defined: {
      background: theme/* default.colors */.Ay.colors.background.paper,
      border: "1px solid ".concat(theme/* default.colors */.Ay.colors.border.light),
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.md,
      padding: theme/* default.spacing */.Ay.spacing[6]
    },
    prominent: {
      background: theme/* default.colors */.Ay.colors.background.paper,
      border: "2px solid ".concat(theme/* default.colors */.Ay.colors.primary.light),
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.lg,
      padding: theme/* default.spacing */.Ay.spacing[8]
    }
  },
  // List-based grouping
  list: {
    simple: {
      gap: theme/* default.spacing */.Ay.spacing[2],
      divider: 'none'
    },
    divided: {
      gap: theme/* default.spacing */.Ay.spacing[3],
      divider: "1px solid ".concat(theme/* default.colors */.Ay.colors.border.light)
    },
    spaced: {
      gap: theme/* default.spacing */.Ay.spacing[4],
      divider: 'none'
    }
  }
};

/**
 * Interaction Patterns
 * Consistent interaction states and feedback
 */
var interactionPatterns = {
  // Button states
  button: {
    primary: {
      "default": {
        background: theme/* default.colors */.Ay.colors.primary.main,
        color: theme/* default.colors */.Ay.colors.primary.contrastText,
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
        boxShadow: 'none'
      },
      hover: {
        background: theme/* default.colors */.Ay.colors.primary.dark,
        color: theme/* default.colors */.Ay.colors.primary.contrastText,
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.dark),
        boxShadow: theme/* default.shadows */.Ay.shadows.sm,
        transform: 'translateY(-1px)'
      },
      active: {
        background: theme/* default.colors */.Ay.colors.primary.dark,
        color: theme/* default.colors */.Ay.colors.primary.contrastText,
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.dark),
        boxShadow: theme/* default.shadows */.Ay.shadows.inner,
        transform: 'translateY(0)'
      },
      focus: {
        outline: "2px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
        outlineOffset: '2px'
      },
      disabled: {
        background: theme/* default.colors */.Ay.colors.neutral[300],
        color: theme/* default.colors */.Ay.colors.neutral[500],
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.neutral[300]),
        cursor: 'not-allowed',
        opacity: 0.6
      }
    },
    secondary: {
      "default": {
        background: 'transparent',
        color: theme/* default.colors */.Ay.colors.primary.main,
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
        boxShadow: 'none'
      },
      hover: {
        background: theme/* default.colors */.Ay.colors.primary.light,
        color: theme/* default.colors */.Ay.colors.primary.dark,
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
        boxShadow: theme/* default.shadows */.Ay.shadows.sm
      },
      active: {
        background: theme/* default.colors */.Ay.colors.primary.light,
        color: theme/* default.colors */.Ay.colors.primary.dark,
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.dark),
        boxShadow: theme/* default.shadows */.Ay.shadows.inner
      },
      focus: {
        outline: "2px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
        outlineOffset: '2px'
      }
    }
  },
  // Interactive element states
  interactive: {
    "default": {
      cursor: 'pointer',
      transition: theme/* default.transitions */.Ay.transitions["default"]
    },
    hover: {
      background: theme/* default.colors */.Ay.colors.interactive.hover,
      transform: 'translateY(-1px)',
      boxShadow: theme/* default.shadows */.Ay.shadows.sm
    },
    active: {
      background: theme/* default.colors */.Ay.colors.interactive.pressed,
      transform: 'translateY(0)',
      boxShadow: theme/* default.shadows */.Ay.shadows.inner
    },
    focus: {
      outline: "2px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
      outlineOffset: '2px'
    },
    selected: {
      background: theme/* default.colors */.Ay.colors.interactive.selected,
      border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.main)
    },
    disabled: {
      background: theme/* default.colors */.Ay.colors.interactive.disabled,
      cursor: 'not-allowed',
      opacity: 0.6
    }
  },
  // Drag and drop states
  dragDrop: {
    draggable: {
      cursor: 'grab',
      transition: theme/* default.transitions */.Ay.transitions["default"]
    },
    dragging: {
      cursor: 'grabbing',
      opacity: 0.8,
      transform: 'rotate(2deg) scale(1.02)',
      boxShadow: theme/* default.shadows */.Ay.shadows.lg,
      zIndex: theme/* default.zIndex */.Ay.zIndex.dragOverlay
    },
    dropTarget: {
      background: theme/* default.colors */.Ay.colors.success.light,
      border: "2px dashed ".concat(theme/* default.colors */.Ay.colors.success.main),
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.md
    },
    dropActive: {
      background: theme/* default.colors */.Ay.colors.success.light,
      border: "2px solid ".concat(theme/* default.colors */.Ay.colors.success.main),
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.md,
      boxShadow: "0 0 0 4px ".concat(theme/* default.colors */.Ay.colors.success.main, "20")
    }
  }
};

/**
 * Information Architecture Patterns
 * Consistent patterns for organizing and presenting information
 */
var informationArchitecture = {
  // Navigation patterns
  navigation: {
    primary: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.base,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      spacing: theme/* default.spacing */.Ay.spacing[6],
      hierarchy: 'horizontal'
    },
    secondary: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.sm,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.regular,
      spacing: theme/* default.spacing */.Ay.spacing[4],
      hierarchy: 'vertical'
    },
    breadcrumb: {
      fontSize: theme/* default.typography */.Ay.typography.fontSize.sm,
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.regular,
      spacing: theme/* default.spacing */.Ay.spacing[2],
      separator: '/'
    }
  },
  // Content organization
  content: {
    hero: {
      alignment: 'center',
      spacing: spacingHierarchy.layout.comfortable,
      typography: typographyHierarchy.display.large
    },
    section: {
      alignment: 'left',
      spacing: spacingHierarchy.section.normal,
      typography: typographyHierarchy.heading.h2
    },
    subsection: {
      alignment: 'left',
      spacing: spacingHierarchy.element.comfortable,
      typography: typographyHierarchy.heading.h3
    },
    paragraph: {
      alignment: 'left',
      spacing: spacingHierarchy.element.normal,
      typography: typographyHierarchy.body.medium
    }
  },
  // Form organization
  form: {
    fieldGroup: {
      spacing: spacingHierarchy.element.comfortable,
      grouping: visualGrouping.section.subtle
    },
    field: {
      spacing: spacingHierarchy.element.normal,
      labelSpacing: theme/* default.spacing */.Ay.spacing[1]
    },
    actions: {
      spacing: spacingHierarchy.element.normal,
      alignment: 'right',
      grouping: 'horizontal'
    }
  },
  // Data presentation
  data: {
    table: {
      headerSpacing: spacingHierarchy.component.comfortable,
      cellSpacing: spacingHierarchy.component.normal,
      rowSpacing: theme/* default.spacing */.Ay.spacing[2]
    },
    list: {
      itemSpacing: spacingHierarchy.element.normal,
      groupSpacing: spacingHierarchy.section.tight
    },
    card: {
      contentSpacing: spacingHierarchy.component.comfortable,
      actionSpacing: spacingHierarchy.element.normal
    }
  }
};

/**
 * Utility functions for applying visual hierarchy
 */
var hierarchyUtils = {
  /**
   * Get typography styles by semantic level
   */
  getTypography: function getTypography(category, level) {
    var _typographyHierarchy$;
    return ((_typographyHierarchy$ = typographyHierarchy[category]) === null || _typographyHierarchy$ === void 0 ? void 0 : _typographyHierarchy$[level]) || {};
  },
  /**
   * Get spacing by context and level
   */
  getSpacing: function getSpacing(context, level) {
    var _spacingHierarchy$con;
    return ((_spacingHierarchy$con = spacingHierarchy[context]) === null || _spacingHierarchy$con === void 0 ? void 0 : _spacingHierarchy$con[level]) || theme/* default.spacing */.Ay.spacing[4];
  },
  /**
   * Get visual grouping styles
   */
  getGrouping: function getGrouping(type, level) {
    var _visualGrouping$type;
    return ((_visualGrouping$type = visualGrouping[type]) === null || _visualGrouping$type === void 0 ? void 0 : _visualGrouping$type[level]) || {};
  },
  /**
   * Get interaction pattern styles
   */
  getInteraction: function getInteraction(element, state) {
    var _interactionPatterns$;
    return ((_interactionPatterns$ = interactionPatterns[element]) === null || _interactionPatterns$ === void 0 ? void 0 : _interactionPatterns$[state]) || {};
  },
  /**
   * Create responsive typography
   */
  createResponsiveTypography: function createResponsiveTypography(desktop, tablet, mobile) {
    return _objectSpread(_objectSpread({}, hierarchyUtils.getTypography.apply(hierarchyUtils, (0,toConsumableArray/* default */.A)(desktop))), {}, (0,defineProperty/* default */.A)((0,defineProperty/* default */.A)({}, theme/* default.mediaQueries */.Ay.mediaQueries.maxLg, hierarchyUtils.getTypography.apply(hierarchyUtils, (0,toConsumableArray/* default */.A)(tablet))), theme/* default.mediaQueries */.Ay.mediaQueries.maxMd, hierarchyUtils.getTypography.apply(hierarchyUtils, (0,toConsumableArray/* default */.A)(mobile))));
  },
  /**
   * Create responsive spacing
   */
  createResponsiveSpacing: function createResponsiveSpacing(desktop, tablet, mobile) {
    return (0,defineProperty/* default */.A)((0,defineProperty/* default */.A)({
      margin: hierarchyUtils.getSpacing.apply(hierarchyUtils, (0,toConsumableArray/* default */.A)(desktop))
    }, theme/* default.mediaQueries */.Ay.mediaQueries.maxLg, {
      margin: hierarchyUtils.getSpacing.apply(hierarchyUtils, (0,toConsumableArray/* default */.A)(tablet))
    }), theme/* default.mediaQueries */.Ay.mediaQueries.maxMd, {
      margin: hierarchyUtils.getSpacing.apply(hierarchyUtils, (0,toConsumableArray/* default */.A)(mobile))
    });
  }
};

/**
 * Component-specific hierarchy patterns
 * Pre-configured patterns for common UI components
 */
var componentHierarchy = {
  // App Builder specific patterns
  appBuilder: {
    palette: {
      header: hierarchyUtils.getTypography('heading', 'h4'),
      categoryTitle: hierarchyUtils.getTypography('heading', 'h5'),
      componentLabel: hierarchyUtils.getTypography('label', 'medium'),
      componentDescription: hierarchyUtils.getTypography('label', 'small'),
      spacing: {
        header: spacingHierarchy.section.tight,
        category: spacingHierarchy.element.comfortable,
        component: spacingHierarchy.element.normal
      }
    },
    propertyEditor: {
      header: hierarchyUtils.getTypography('heading', 'h4'),
      groupTitle: hierarchyUtils.getTypography('heading', 'h6'),
      fieldLabel: hierarchyUtils.getTypography('label', 'large'),
      fieldHelp: hierarchyUtils.getTypography('label', 'small'),
      spacing: {
        header: spacingHierarchy.section.tight,
        group: spacingHierarchy.element.comfortable,
        field: spacingHierarchy.element.normal
      }
    },
    preview: {
      toolbar: hierarchyUtils.getTypography('label', 'medium'),
      deviceLabel: hierarchyUtils.getTypography('label', 'small'),
      statusText: hierarchyUtils.getTypography('label', 'small'),
      spacing: {
        toolbar: spacingHierarchy.component.comfortable,
        controls: spacingHierarchy.component.normal
      }
    }
  },
  // Common UI patterns
  common: {
    modal: {
      title: hierarchyUtils.getTypography('heading', 'h3'),
      content: hierarchyUtils.getTypography('body', 'medium'),
      actions: hierarchyUtils.getTypography('interactive', 'button'),
      spacing: {
        title: spacingHierarchy.section.tight,
        content: spacingHierarchy.element.comfortable,
        actions: spacingHierarchy.element.normal
      }
    },
    card: {
      title: hierarchyUtils.getTypography('heading', 'h4'),
      subtitle: hierarchyUtils.getTypography('body', 'small'),
      content: hierarchyUtils.getTypography('body', 'medium'),
      meta: hierarchyUtils.getTypography('label', 'small'),
      spacing: {
        title: spacingHierarchy.element.normal,
        content: spacingHierarchy.element.comfortable,
        actions: spacingHierarchy.element.normal
      }
    },
    form: {
      title: hierarchyUtils.getTypography('heading', 'h3'),
      sectionTitle: hierarchyUtils.getTypography('heading', 'h5'),
      fieldLabel: hierarchyUtils.getTypography('label', 'large'),
      fieldHelp: hierarchyUtils.getTypography('label', 'small'),
      errorText: _objectSpread(_objectSpread({}, hierarchyUtils.getTypography('label', 'small')), {}, {
        color: theme/* default.colors */.Ay.colors.error.main
      }),
      spacing: {
        title: spacingHierarchy.section.normal,
        section: spacingHierarchy.element.comfortable,
        field: spacingHierarchy.element.normal
      }
    }
  }
};

/**
 * Accessibility-enhanced hierarchy patterns
 * Patterns that include accessibility considerations
 */
var accessibleHierarchy = {
  // Focus management patterns
  focus: {
    ring: {
      outline: "2px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
      outlineOffset: '2px',
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.sm
    },
    highContrast: {
      '@media (prefers-contrast: high)': {
        outline: "3px solid",
        outlineOffset: '2px'
      }
    }
  },
  // Screen reader patterns
  screenReader: {
    srOnly: theme/* default.accessibility */.Ay.accessibility.srOnly,
    skipLink: {
      position: 'absolute',
      top: '-40px',
      left: theme/* default.spacing */.Ay.spacing[2],
      background: theme/* default.colors */.Ay.colors.primary.main,
      color: theme/* default.colors */.Ay.colors.primary.contrastText,
      padding: "".concat(theme/* default.spacing */.Ay.spacing[2], " ").concat(theme/* default.spacing */.Ay.spacing[4]),
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius.md,
      textDecoration: 'none',
      fontWeight: theme/* default.typography */.Ay.typography.fontWeight.medium,
      zIndex: theme/* default.zIndex */.Ay.zIndex.skipLink,
      transition: theme/* default.transitions */.Ay.transitions["default"],
      '&:focus': {
        top: theme/* default.spacing */.Ay.spacing[2]
      }
    }
  },
  // Reduced motion patterns
  reducedMotion: {
    '@media (prefers-reduced-motion: reduce)': {
      transition: 'none !important',
      animation: 'none !important',
      transform: 'none !important'
    }
  },
  // High contrast patterns
  highContrast: {
    '@media (prefers-contrast: high)': {
      border: '1px solid',
      outline: '1px solid',
      backgroundColor: 'Canvas',
      color: 'CanvasText'
    }
  }
};

/**
 * Animation and transition hierarchy
 * Consistent animation patterns for different interaction levels
 */
var animationHierarchy = {
  // Micro-interactions (button hovers, etc.)
  micro: {
    duration: '150ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    properties: ['background-color', 'border-color', 'color', 'box-shadow']
  },
  // Component transitions (panel slides, etc.)
  component: {
    duration: '250ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    properties: ['transform', 'opacity', 'width', 'height']
  },
  // Layout transitions (page changes, etc.)
  layout: {
    duration: '350ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    properties: ['transform', 'opacity']
  },
  // Attention-grabbing animations
  attention: {
    duration: '500ms',
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    properties: ['transform', 'opacity', 'scale']
  }
};

// Export the complete visual hierarchy system
/* harmony default export */ const visual_hierarchy = ({
  typography: typographyHierarchy,
  spacing: spacingHierarchy,
  grouping: visualGrouping,
  interaction: interactionPatterns,
  architecture: informationArchitecture,
  component: componentHierarchy,
  accessible: accessibleHierarchy,
  animation: animationHierarchy,
  utils: hierarchyUtils
});
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
;// ./src/design-system/utils.js



function utils_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function utils_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? utils_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : utils_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
/**
 * Design System Utilities
 * 
 * This file provides utility functions for working with the design system theme.
 * It includes functions for color manipulation, responsive design, accessibility, etc.
 */



/**
 * Color utilities
 */
var colorUtils = {
  /**
   * Get color value from theme
   * @param {string} colorPath - Path to color in theme (e.g., 'primary.main')
   * @returns {string} Color value
   */
  getColor: function getColor(colorPath) {
    var keys = colorPath.split('.');
    var color = theme/* default.colors */.Ay.colors;
    var _iterator = _createForOfIteratorHelper(keys),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var key = _step.value;
        if (color && (0,esm_typeof/* default */.A)(color) === 'object' && key in color) {
          color = color[key];
        } else {
          console.warn("Color path \"".concat(colorPath, "\" not found in theme"));
          return theme/* default.colors */.Ay.colors.neutral[500]; // Fallback color
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    return color;
  },
  /**
   * Convert hex color to rgba
   * @param {string} hex - Hex color value
   * @param {number} alpha - Alpha value (0-1)
   * @returns {string} RGBA color string
   */
  hexToRgba: function hexToRgba(hex) {
    var alpha = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
    var r = parseInt(hex.slice(1, 3), 16);
    var g = parseInt(hex.slice(3, 5), 16);
    var b = parseInt(hex.slice(5, 7), 16);
    return "rgba(".concat(r, ", ").concat(g, ", ").concat(b, ", ").concat(alpha, ")");
  },
  /**
   * Lighten a color by a percentage
   * @param {string} color - Color value
   * @param {number} amount - Amount to lighten (0-100)
   * @returns {string} Lightened color
   */
  lighten: function lighten(color, amount) {
    // Simple implementation - in production, use a color manipulation library
    var num = parseInt(color.replace('#', ''), 16);
    var amt = Math.round(2.55 * amount);
    var R = (num >> 16) + amt;
    var G = (num >> 8 & 0x00FF) + amt;
    var B = (num & 0x0000FF) + amt;
    return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 + (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 + (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  },
  /**
   * Darken a color by a percentage
   * @param {string} color - Color value
   * @param {number} amount - Amount to darken (0-100)
   * @returns {string} Darkened color
   */
  darken: function darken(color, amount) {
    return colorUtils.lighten(color, -amount);
  },
  /**
   * Check if a color meets WCAG contrast requirements
   * @param {string} foreground - Foreground color
   * @param {string} background - Background color
   * @param {string} level - WCAG level ('AA' or 'AAA')
   * @returns {boolean} Whether contrast is sufficient
   */
  checkContrast: function checkContrast(foreground, background) {
    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
    // Simplified implementation - in production, use a proper contrast checker
    var minRatio = level === 'AAA' ? 7 : 4.5;
    // This is a placeholder - implement proper contrast calculation
    return true; // For now, assume all colors pass
  }
};

/**
 * Spacing utilities
 */
var spacingUtils = {
  /**
   * Get spacing value from theme
   * @param {string|number} space - Spacing key or multiplier
   * @returns {string} Spacing value
   */
  getSpacing: function getSpacing(space) {
    if (typeof space === 'number') {
      return "".concat(space * 0.25, "rem"); // 4px base unit
    }
    return theme/* default.spacing */.Ay.spacing[space] || space;
  },
  /**
   * Create responsive spacing
   * @param {object} values - Breakpoint values
   * @returns {string} CSS with media queries
   */
  responsive: function responsive(values) {
    var css = '';
    Object.entries(values).forEach(function (_ref) {
      var _ref2 = (0,slicedToArray/* default */.A)(_ref, 2),
        breakpoint = _ref2[0],
        value = _ref2[1];
      if (breakpoint === 'base') {
        css += "".concat(spacingUtils.getSpacing(value), ";");
      } else {
        css += "".concat(theme/* default.mediaQueries */.Ay.mediaQueries[breakpoint], " { ").concat(spacingUtils.getSpacing(value), "; }");
      }
    });
    return css;
  }
};

/**
 * Typography utilities
 */
var typographyUtils = {
  /**
   * Get text style from theme
   * @param {string} style - Text style name
   * @returns {object} Style object
   */
  getTextStyle: function getTextStyle(style) {
    return theme/* default.typography */.Ay.typography.textStyles[style] || {};
  },
  /**
   * Create responsive typography
   * @param {object} values - Breakpoint values
   * @returns {object} Style object with media queries
   */
  responsiveText: function responsiveText(values) {
    var styles = {};
    Object.entries(values).forEach(function (_ref3) {
      var _ref4 = (0,slicedToArray/* default */.A)(_ref3, 2),
        breakpoint = _ref4[0],
        value = _ref4[1];
      if (breakpoint === 'base') {
        Object.assign(styles, typographyUtils.getTextStyle(value));
      } else {
        styles[theme/* default.mediaQueries */.Ay.mediaQueries[breakpoint]] = typographyUtils.getTextStyle(value);
      }
    });
    return styles;
  }
};

/**
 * Accessibility utilities
 */
var a11yUtils = {
  /**
   * Get focus ring styles
   * @param {string} color - Focus color (optional)
   * @returns {object} Focus ring styles
   */
  focusRing: function focusRing() {
    var color = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : theme/* default.colors */.Ay.colors.primary.main;
    return {
      outline: "".concat(theme/* default.accessibility */.Ay.accessibility.focusRing.width, " ").concat(theme/* default.accessibility */.Ay.accessibility.focusRing.style, " ").concat(color),
      outlineOffset: theme/* default.accessibility */.Ay.accessibility.focusRing.offset
    };
  },
  /**
   * Screen reader only styles
   * @returns {object} SR-only styles
   */
  srOnly: function srOnly() {
    return theme/* default.accessibility */.Ay.accessibility.srOnly;
  },
  /**
   * Ensure minimum touch target size
   * @returns {object} Touch target styles
   */
  touchTarget: function touchTarget() {
    return {
      minWidth: theme/* default.accessibility */.Ay.accessibility.minTouchTarget.width,
      minHeight: theme/* default.accessibility */.Ay.accessibility.minTouchTarget.height
    };
  },
  /**
   * High contrast mode styles
   * @returns {object} High contrast styles
   */
  highContrast: function highContrast() {
    return {
      '@media (prefers-contrast: high)': theme/* default.accessibility */.Ay.accessibility.highContrast
    };
  }
};

/**
 * Animation utilities
 */
var animationUtils = {
  /**
   * Get animation from theme
   * @param {string} name - Animation name
   * @returns {string} Animation value
   */
  getAnimation: function getAnimation(name) {
    return theme/* default.animations */.Ay.animations[name] || name;
  },
  /**
   * Create reduced motion variant
   * @param {string} animation - Animation value
   * @returns {object} Animation with reduced motion support
   */
  withReducedMotion: function withReducedMotion(animation) {
    return (0,defineProperty/* default */.A)({
      animation: animation
    }, theme/* default.mediaQueries */.Ay.mediaQueries.reducedMotion, {
      animation: 'none'
    });
  }
};

/**
 * Component utilities
 */
var componentUtils = {
  /**
   * Get component design tokens
   * @param {string} component - Component name
   * @returns {object} Component tokens
   */
  getComponentTokens: function getComponentTokens(component) {
    return theme/* default.components */.Ay.components[component] || {};
  },
  /**
   * Create component variant styles
   * @param {string} component - Component name
   * @param {string} variant - Variant name
   * @param {string} size - Size variant
   * @returns {object} Component styles
   */
  getVariantStyles: function getVariantStyles(component) {
    var _tokens$height, _tokens$padding, _tokens$fontSize;
    var variant = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'primary';
    var size = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'md';
    var tokens = componentUtils.getComponentTokens(component);

    // Base styles for the component
    var baseStyles = {
      height: (_tokens$height = tokens.height) === null || _tokens$height === void 0 ? void 0 : _tokens$height[size],
      padding: (_tokens$padding = tokens.padding) === null || _tokens$padding === void 0 ? void 0 : _tokens$padding[size],
      fontSize: (_tokens$fontSize = tokens.fontSize) === null || _tokens$fontSize === void 0 ? void 0 : _tokens$fontSize[size],
      borderRadius: theme/* default.borderRadius */.Ay.borderRadius[component] || theme/* default.borderRadius */.Ay.borderRadius.md,
      transition: theme/* default.transitions */.Ay.transitions["default"]
    };

    // Variant-specific styles
    var variantStyles = {
      primary: {
        backgroundColor: theme/* default.colors */.Ay.colors.primary.main,
        color: theme/* default.colors */.Ay.colors.primary.contrastText,
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
        '&:hover': {
          backgroundColor: theme/* default.colors */.Ay.colors.primary.dark
        },
        '&:focus': utils_objectSpread({}, a11yUtils.focusRing())
      },
      secondary: {
        backgroundColor: 'transparent',
        color: theme/* default.colors */.Ay.colors.primary.main,
        border: "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.main),
        '&:hover': {
          backgroundColor: theme/* default.colors */.Ay.colors.primary.light
        },
        '&:focus': utils_objectSpread({}, a11yUtils.focusRing())
      },
      ghost: {
        backgroundColor: 'transparent',
        color: theme/* default.colors */.Ay.colors.text.primary,
        border: '1px solid transparent',
        '&:hover': {
          backgroundColor: theme/* default.colors */.Ay.colors.interactive.hover
        },
        '&:focus': utils_objectSpread({}, a11yUtils.focusRing())
      }
    };
    return utils_objectSpread(utils_objectSpread({}, baseStyles), variantStyles[variant]);
  }
};

/**
 * Responsive design utilities
 */
var responsiveUtils = {
  /**
   * Create responsive styles
   * @param {object} styles - Breakpoint styles
   * @returns {object} Responsive styles object
   */
  responsive: function responsive(styles) {
    var responsiveStyles = {};
    Object.entries(styles).forEach(function (_ref6) {
      var _ref7 = (0,slicedToArray/* default */.A)(_ref6, 2),
        breakpoint = _ref7[0],
        style = _ref7[1];
      if (breakpoint === 'base') {
        Object.assign(responsiveStyles, style);
      } else {
        responsiveStyles[theme/* default.mediaQueries */.Ay.mediaQueries[breakpoint]] = style;
      }
    });
    return responsiveStyles;
  },
  /**
   * Check if current screen matches breakpoint
   * @param {string} breakpoint - Breakpoint name
   * @returns {boolean} Whether breakpoint matches
   */
  matchesBreakpoint: function matchesBreakpoint(breakpoint) {
    if (typeof window === 'undefined') return false;
    var breakpointValue = parseInt(theme/* default.breakpoints */.Ay.breakpoints[breakpoint]);
    return window.innerWidth >= breakpointValue;
  }
};

// Export all utilities
/* harmony default export */ const utils = ({
  color: colorUtils,
  spacing: spacingUtils,
  typography: typographyUtils,
  a11y: a11yUtils,
  animation: animationUtils,
  component: componentUtils,
  responsive: responsiveUtils
});
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/prop-types/index.js
var prop_types = __webpack_require__(5556);
var prop_types_default = /*#__PURE__*/__webpack_require__.n(prop_types);
// EXTERNAL MODULE: ./src/design-system/styled-components.js
var styled_components = __webpack_require__(18563);
;// ./src/design-system/Button.js



var _excluded = ["children", "variant", "size", "disabled", "fullWidth", "startIcon", "endIcon", "onClick"];
var _templateObject;




var StyledButton = styled_components/* styled */.I4.button(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: ", " ", ";\n  border-radius: ", ";\n  font-family: ", ";\n  font-weight: ", ";\n  font-size: ", ";\n  transition: ", ";\n  cursor: ", ";\n  opacity: ", ";\n  width: ", ";\n\n  /* Variant styles */\n  background-color: ", ";\n\n  color: ", ";\n\n  border: ", ";\n\n  &:hover {\n    background-color: ", ";\n\n    color: ", ";\n  }\n\n  &:focus {\n    outline: none;\n    box-shadow: 0 0 0 3px ", ";\n  }\n\n  .button-start-icon {\n    margin-right: ", ";\n  }\n\n  .button-end-icon {\n    margin-left: ", ";\n  }\n"])), function (props) {
  return props.size === 'small' ? theme/* default.spacing */.Ay.spacing[2] : props.size === 'large' ? theme/* default.spacing */.Ay.spacing[4] : theme/* default.spacing */.Ay.spacing[3];
}, function (props) {
  return props.size === 'small' ? theme/* default.spacing */.Ay.spacing[3] : props.size === 'large' ? theme/* default.spacing */.Ay.spacing[6] : theme/* default.spacing */.Ay.spacing[4];
}, theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.typography */.Ay.typography.fontFamily.primary, theme/* default.typography */.Ay.typography.fontWeight.medium, function (props) {
  return props.size === 'small' ? theme/* default.typography */.Ay.typography.fontSize.sm : props.size === 'large' ? theme/* default.typography */.Ay.typography.fontSize.lg : theme/* default.typography */.Ay.typography.fontSize.md;
}, theme/* default.transitions */.Ay.transitions["default"], function (props) {
  return props.disabled ? 'not-allowed' : 'pointer';
}, function (props) {
  return props.disabled ? 0.6 : 1;
}, function (props) {
  return props.fullWidth ? '100%' : 'auto';
}, function (props) {
  if (props.variant === 'primary') return theme/* default.colors */.Ay.colors.primary.main;
  if (props.variant === 'secondary') return theme/* default.colors */.Ay.colors.secondary.main;
  if (props.variant === 'outline') return 'transparent';
  if (props.variant === 'text') return 'transparent';
  return theme/* default.colors */.Ay.colors.primary.main;
}, function (props) {
  if (props.variant === 'outline' || props.variant === 'text') return theme/* default.colors */.Ay.colors.primary.main;
  return theme/* default.colors */.Ay.colors.primary.contrastText;
}, function (props) {
  if (props.variant === 'outline') return "1px solid ".concat(theme/* default.colors */.Ay.colors.primary.main);
  return 'none';
}, function (props) {
  if (props.variant === 'primary') return theme/* default.colors */.Ay.colors.primary.dark;
  if (props.variant === 'secondary') return theme/* default.colors */.Ay.colors.secondary.dark;
  if (props.variant === 'outline') return theme/* default.colors */.Ay.colors.primary.light;
  if (props.variant === 'text') return theme/* default.colors */.Ay.colors.primary.light;
  return theme/* default.colors */.Ay.colors.primary.dark;
}, function (props) {
  if (props.variant === 'outline' || props.variant === 'text') return theme/* default.colors */.Ay.colors.primary.dark;
  return theme/* default.colors */.Ay.colors.primary.contrastText;
}, theme/* default.colors */.Ay.colors.primary.light, theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[2]);
var Button = function Button(_ref) {
  var children = _ref.children,
    _ref$variant = _ref.variant,
    variant = _ref$variant === void 0 ? 'primary' : _ref$variant,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'medium' : _ref$size,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    _ref$fullWidth = _ref.fullWidth,
    fullWidth = _ref$fullWidth === void 0 ? false : _ref$fullWidth,
    startIcon = _ref.startIcon,
    endIcon = _ref.endIcon,
    onClick = _ref.onClick,
    props = (0,objectWithoutProperties/* default */.A)(_ref, _excluded);
  return /*#__PURE__*/react.createElement(StyledButton, (0,esm_extends/* default */.A)({
    variant: variant,
    size: size,
    disabled: disabled,
    fullWidth: fullWidth,
    onClick: disabled ? undefined : onClick
  }, props), startIcon && /*#__PURE__*/react.createElement("span", {
    className: "button-start-icon"
  }, startIcon), children, endIcon && /*#__PURE__*/react.createElement("span", {
    className: "button-end-icon"
  }, endIcon));
};
Button.propTypes = {
  children: (prop_types_default()).node.isRequired,
  variant: prop_types_default().oneOf(['primary', 'secondary', 'outline', 'text']),
  size: prop_types_default().oneOf(['small', 'medium', 'large']),
  disabled: (prop_types_default()).bool,
  fullWidth: (prop_types_default()).bool,
  startIcon: (prop_types_default()).node,
  endIcon: (prop_types_default()).node,
  onClick: (prop_types_default()).func
};
/* harmony default export */ const design_system_Button = (Button);
;// ./src/design-system/Card.js



var Card_excluded = ["children", "elevation", "radius", "fullWidth", "fullHeight"],
  _excluded2 = ["children", "divider"],
  _excluded3 = ["children"],
  _excluded4 = ["children"],
  _excluded5 = ["children", "divider", "align"];
var Card_templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;




var StyledCard = styled_components/* styled */.I4.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['elevation', 'radius', 'fullWidth', 'fullHeight'].includes(prop);
  }
})(Card_templateObject || (Card_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  background-color: white;\n  border-radius: ", ";\n  box-shadow: ", ";\n  overflow: hidden;\n  width: ", ";\n  height: ", ";\n  display: flex;\n  flex-direction: column;\n"])), function (props) {
  return theme/* default.borderRadius */.Ay.borderRadius[props.radius];
}, function (props) {
  return theme/* default.shadows */.Ay.shadows[props.elevation];
}, function (props) {
  return props.fullWidth ? '100%' : 'auto';
}, function (props) {
  return props.fullHeight ? '100%' : 'auto';
});
var CardHeader = styled_components/* styled */.I4.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['divider'].includes(prop);
  }
})(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: ", ";\n  border-bottom: ", ";\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n"])), theme/* default.spacing */.Ay.spacing[4], function (props) {
  return props.divider ? "1px solid ".concat(theme/* default.colors */.Ay.colors.neutral[200]) : 'none';
});
var CardTitle = styled_components/* styled */.I4.h3(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin: 0;\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n"])), theme/* default.typography */.Ay.typography.fontSize.lg, theme/* default.typography */.Ay.typography.fontWeight.semibold, theme/* default.colors */.Ay.colors.neutral[900]);
var CardContent = styled_components/* styled */.I4.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: ", ";\n  flex: 1;\n"])), theme/* default.spacing */.Ay.spacing[4]);
var CardFooter = styled_components/* styled */.I4.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['divider', 'align'].includes(prop);
  }
})(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: ", ";\n  border-top: ", ";\n  display: flex;\n  align-items: center;\n  justify-content: ", ";\n  gap: ", ";\n"])), theme/* default.spacing */.Ay.spacing[4], function (props) {
  return props.divider ? "1px solid ".concat(theme/* default.colors */.Ay.colors.neutral[200]) : 'none';
}, function (props) {
  return props.align === 'right' ? 'flex-end' : props.align === 'center' ? 'center' : 'flex-start';
}, theme/* default.spacing */.Ay.spacing[2]);
var Card = function Card(_ref) {
  var children = _ref.children,
    _ref$elevation = _ref.elevation,
    elevation = _ref$elevation === void 0 ? 'md' : _ref$elevation,
    _ref$radius = _ref.radius,
    radius = _ref$radius === void 0 ? 'md' : _ref$radius,
    _ref$fullWidth = _ref.fullWidth,
    fullWidth = _ref$fullWidth === void 0 ? false : _ref$fullWidth,
    _ref$fullHeight = _ref.fullHeight,
    fullHeight = _ref$fullHeight === void 0 ? false : _ref$fullHeight,
    props = (0,objectWithoutProperties/* default */.A)(_ref, Card_excluded);
  return /*#__PURE__*/react.createElement(StyledCard, (0,esm_extends/* default */.A)({
    elevation: elevation,
    radius: radius,
    fullWidth: fullWidth,
    fullHeight: fullHeight
  }, props), children);
};
Card.Header = function (_ref2) {
  var children = _ref2.children,
    _ref2$divider = _ref2.divider,
    divider = _ref2$divider === void 0 ? false : _ref2$divider,
    props = (0,objectWithoutProperties/* default */.A)(_ref2, _excluded2);
  return /*#__PURE__*/react.createElement(CardHeader, (0,esm_extends/* default */.A)({
    divider: divider
  }, props), children);
};
Card.Title = function (_ref3) {
  var children = _ref3.children,
    props = (0,objectWithoutProperties/* default */.A)(_ref3, _excluded3);
  return /*#__PURE__*/react.createElement(CardTitle, props, children);
};
Card.Content = function (_ref4) {
  var children = _ref4.children,
    props = (0,objectWithoutProperties/* default */.A)(_ref4, _excluded4);
  return /*#__PURE__*/react.createElement(CardContent, props, children);
};
Card.Footer = function (_ref5) {
  var children = _ref5.children,
    _ref5$divider = _ref5.divider,
    divider = _ref5$divider === void 0 ? true : _ref5$divider,
    _ref5$align = _ref5.align,
    align = _ref5$align === void 0 ? 'right' : _ref5$align,
    props = (0,objectWithoutProperties/* default */.A)(_ref5, _excluded5);
  return /*#__PURE__*/react.createElement(CardFooter, (0,esm_extends/* default */.A)({
    divider: divider,
    align: align
  }, props), children);
};
Card.propTypes = {
  children: (prop_types_default()).node.isRequired,
  elevation: prop_types_default().oneOf(['none', 'sm', 'md', 'lg', 'xl', '2xl']),
  radius: prop_types_default().oneOf(['none', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', 'full']),
  fullWidth: (prop_types_default()).bool,
  fullHeight: (prop_types_default()).bool
};
Card.Header.propTypes = {
  children: (prop_types_default()).node.isRequired,
  divider: (prop_types_default()).bool
};
Card.Title.propTypes = {
  children: (prop_types_default()).node.isRequired
};
Card.Content.propTypes = {
  children: (prop_types_default()).node.isRequired
};
Card.Footer.propTypes = {
  children: (prop_types_default()).node.isRequired,
  divider: (prop_types_default()).bool,
  align: prop_types_default().oneOf(['left', 'center', 'right'])
};
/* harmony default export */ const design_system_Card = (Card);
;// ./src/design-system/Input.js




var Input_excluded = ["label", "helperText", "error", "fullWidth", "prefix", "suffix", "disabled"];
var Input_templateObject, Input_templateObject2, Input_templateObject3, _theme$typography, _theme$typography2, _theme$colors, _theme$borderRadius, _theme$spacing, _theme$spacing2, _theme$transitions, _theme$colors6, _theme$colors7, _theme$colors8, Input_templateObject4, _theme$spacing3, Input_templateObject5, _theme$spacing4, _templateObject6, _templateObject7, _theme$typography3, _theme$spacing5;




var InputWrapper = styled_components/* styled */.I4.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['fullWidth'].includes(prop);
  }
})(Input_templateObject || (Input_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  width: ", ";\n"])), function (props) {
  return props.fullWidth ? '100%' : 'auto';
});
var InputLabel = styled_components/* styled */.I4.label(Input_templateObject2 || (Input_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n  margin-bottom: ", ";\n"])), theme/* default.typography */.Ay.typography.fontSize.sm, theme/* default.typography */.Ay.typography.fontWeight.medium, theme/* default.colors */.Ay.colors.neutral[700], theme/* default.spacing */.Ay.spacing[1]);
var StyledInput = styled_components/* styled */.I4.input.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['error', 'focused', 'fullWidth'].includes(prop);
  }
})(Input_templateObject3 || (Input_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-family: ", ";\n  font-size: ", ";\n  color: ", ";\n  background-color: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  padding: ", " ", ";\n  transition: ", ";\n  width: 100%;\n\n  &:focus {\n    outline: none;\n    border-color: ", ";\n    box-shadow: 0 0 0 3px ", ";\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.7;\n  }\n\n  &::placeholder {\n    color: ", ";\n  }\n"])), (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$typography = theme/* default.typography */.Ay.typography) === null || _theme$typography === void 0 || (_theme$typography = _theme$typography.fontFamily) === null || _theme$typography === void 0 ? void 0 : _theme$typography.primary) || 'Inter, sans-serif', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$typography2 = theme/* default.typography */.Ay.typography) === null || _theme$typography2 === void 0 || (_theme$typography2 = _theme$typography2.fontSize) === null || _theme$typography2 === void 0 ? void 0 : _theme$typography2.md) || '16px', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors = theme/* default.colors */.Ay.colors) === null || _theme$colors === void 0 || (_theme$colors = _theme$colors.neutral) === null || _theme$colors === void 0 ? void 0 : _theme$colors[900]) || '#111827', function (props) {
  var _theme$colors2;
  return props.disabled ? (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors2 = theme/* default.colors */.Ay.colors) === null || _theme$colors2 === void 0 || (_theme$colors2 = _theme$colors2.neutral) === null || _theme$colors2 === void 0 ? void 0 : _theme$colors2[100]) || '#F3F4F6' : 'white';
}, function (props) {
  var _theme$colors3, _theme$colors4, _theme$colors5;
  return props.error ? (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors3 = theme/* default.colors */.Ay.colors) === null || _theme$colors3 === void 0 || (_theme$colors3 = _theme$colors3.error) === null || _theme$colors3 === void 0 ? void 0 : _theme$colors3.main) || '#DC2626' : props.focused ? (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors4 = theme/* default.colors */.Ay.colors) === null || _theme$colors4 === void 0 || (_theme$colors4 = _theme$colors4.primary) === null || _theme$colors4 === void 0 ? void 0 : _theme$colors4.main) || '#2563EB' : (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors5 = theme/* default.colors */.Ay.colors) === null || _theme$colors5 === void 0 || (_theme$colors5 = _theme$colors5.neutral) === null || _theme$colors5 === void 0 ? void 0 : _theme$colors5[300]) || '#D1D5DB';
}, (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$borderRadius = theme/* default.borderRadius */.Ay.borderRadius) === null || _theme$borderRadius === void 0 ? void 0 : _theme$borderRadius.md) || '4px', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$spacing = theme/* default.spacing */.Ay.spacing) === null || _theme$spacing === void 0 ? void 0 : _theme$spacing[2]) || '8px', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$spacing2 = theme/* default.spacing */.Ay.spacing) === null || _theme$spacing2 === void 0 ? void 0 : _theme$spacing2[3]) || '12px', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$transitions = theme/* default.transitions */.Ay.transitions) === null || _theme$transitions === void 0 ? void 0 : _theme$transitions["default"]) || 'all 0.2s ease-in-out', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors6 = theme/* default.colors */.Ay.colors) === null || _theme$colors6 === void 0 || (_theme$colors6 = _theme$colors6.primary) === null || _theme$colors6 === void 0 ? void 0 : _theme$colors6.main) || '#2563EB', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors7 = theme/* default.colors */.Ay.colors) === null || _theme$colors7 === void 0 || (_theme$colors7 = _theme$colors7.primary) === null || _theme$colors7 === void 0 ? void 0 : _theme$colors7.light) || 'rgba(37, 99, 235, 0.2)', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors8 = theme/* default.colors */.Ay.colors) === null || _theme$colors8 === void 0 || (_theme$colors8 = _theme$colors8.neutral) === null || _theme$colors8 === void 0 ? void 0 : _theme$colors8[400]) || '#9CA3AF');
var InputPrefix = styled_components/* styled */.I4.div(Input_templateObject4 || (Input_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  margin-right: ", ";\n"])), (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$spacing3 = theme/* default.spacing */.Ay.spacing) === null || _theme$spacing3 === void 0 ? void 0 : _theme$spacing3[2]) || '8px');
var InputSuffix = styled_components/* styled */.I4.div(Input_templateObject5 || (Input_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  margin-left: ", ";\n"])), (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$spacing4 = theme/* default.spacing */.Ay.spacing) === null || _theme$spacing4 === void 0 ? void 0 : _theme$spacing4[2]) || '8px');
var InputContainer = styled_components/* styled */.I4.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n  position: relative;\n  width: 100%;\n"])));
var HelperText = styled_components/* styled */.I4.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['error'].includes(prop);
  }
})(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n  margin-top: ", ";\n  color: ", ";\n"])), (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$typography3 = theme/* default.typography */.Ay.typography) === null || _theme$typography3 === void 0 || (_theme$typography3 = _theme$typography3.fontSize) === null || _theme$typography3 === void 0 ? void 0 : _theme$typography3.xs) || '12px', (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$spacing5 = theme/* default.spacing */.Ay.spacing) === null || _theme$spacing5 === void 0 ? void 0 : _theme$spacing5[1]) || '4px', function (props) {
  var _theme$colors9, _theme$colors0;
  return props.error ? (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors9 = theme/* default.colors */.Ay.colors) === null || _theme$colors9 === void 0 || (_theme$colors9 = _theme$colors9.error) === null || _theme$colors9 === void 0 ? void 0 : _theme$colors9.main) || '#DC2626' : (theme/* default */.Ay === null || theme/* default */.Ay === void 0 || (_theme$colors0 = theme/* default.colors */.Ay.colors) === null || _theme$colors0 === void 0 || (_theme$colors0 = _theme$colors0.neutral) === null || _theme$colors0 === void 0 ? void 0 : _theme$colors0[500]) || '#6B7280';
});
var Input = /*#__PURE__*/(0,react.forwardRef)(function (_ref, ref) {
  var label = _ref.label,
    helperText = _ref.helperText,
    error = _ref.error,
    _ref$fullWidth = _ref.fullWidth,
    fullWidth = _ref$fullWidth === void 0 ? false : _ref$fullWidth,
    prefix = _ref.prefix,
    suffix = _ref.suffix,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    props = (0,objectWithoutProperties/* default */.A)(_ref, Input_excluded);
  var _React$useState = react.useState(false),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    focused = _React$useState2[0],
    setFocused = _React$useState2[1];
  var handleFocus = function handleFocus(e) {
    setFocused(true);
    if (props.onFocus) props.onFocus(e);
  };
  var handleBlur = function handleBlur(e) {
    setFocused(false);
    if (props.onBlur) props.onBlur(e);
  };
  return /*#__PURE__*/react.createElement(InputWrapper, {
    fullWidth: fullWidth
  }, label && /*#__PURE__*/react.createElement(InputLabel, null, label), /*#__PURE__*/react.createElement(InputContainer, null, prefix && /*#__PURE__*/react.createElement(InputPrefix, null, prefix), /*#__PURE__*/react.createElement(StyledInput, (0,esm_extends/* default */.A)({
    ref: ref,
    disabled: disabled,
    error: error,
    focused: focused,
    onFocus: handleFocus,
    onBlur: handleBlur
  }, props)), suffix && /*#__PURE__*/react.createElement(InputSuffix, null, suffix)), helperText && /*#__PURE__*/react.createElement(HelperText, {
    error: error
  }, helperText));
});
Input.displayName = 'Input';
Input.propTypes = {
  label: (prop_types_default()).string,
  helperText: (prop_types_default()).string,
  error: (prop_types_default()).bool,
  fullWidth: (prop_types_default()).bool,
  prefix: (prop_types_default()).node,
  suffix: (prop_types_default()).node,
  disabled: (prop_types_default()).bool,
  onFocus: (prop_types_default()).func,
  onBlur: (prop_types_default()).func
};
/* harmony default export */ const design_system_Input = (Input);
;// ./src/design-system/Select.js




var Select_excluded = ["label", "helperText", "error", "fullWidth", "options", "disabled"];
var Select_templateObject, Select_templateObject2, Select_templateObject3, Select_templateObject4;




var SelectWrapper = styled_components/* styled */.I4.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['fullWidth'].includes(prop);
  }
})(Select_templateObject || (Select_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  width: ", ";\n"])), function (props) {
  return props.fullWidth ? '100%' : 'auto';
});
var SelectLabel = styled_components/* styled */.I4.label(Select_templateObject2 || (Select_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n  margin-bottom: ", ";\n"])), theme/* default.typography */.Ay.typography.fontSize.sm, theme/* default.typography */.Ay.typography.fontWeight.medium, theme/* default.colors */.Ay.colors.neutral[700], theme/* default.spacing */.Ay.spacing[1]);
var StyledSelect = styled_components/* styled */.I4.select(Select_templateObject3 || (Select_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-family: ", ";\n  font-size: ", ";\n  color: ", ";\n  background-color: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  padding: ", " ", ";\n  transition: ", ";\n  width: 100%;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E\");\n  background-position: right ", " center;\n  background-repeat: no-repeat;\n  background-size: 1.5em 1.5em;\n  padding-right: 2.5em;\n\n  &:focus {\n    outline: none;\n    border-color: ", ";\n    box-shadow: 0 0 0 3px ", ";\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.7;\n  }\n"])), theme/* default.typography */.Ay.typography.fontFamily.primary, theme/* default.typography */.Ay.typography.fontSize.md, theme/* default.colors */.Ay.colors.neutral[900], function (props) {
  return props.disabled ? theme/* default.colors */.Ay.colors.neutral[100] : 'white';
}, function (props) {
  return props.error ? theme/* default.colors */.Ay.colors.error.main : props.focused ? theme/* default.colors */.Ay.colors.primary.main : theme/* default.colors */.Ay.colors.neutral[300];
}, theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.spacing */.Ay.spacing[2], theme/* default.spacing */.Ay.spacing[3], theme/* default.transitions */.Ay.transitions["default"], theme/* default.spacing */.Ay.spacing[2], theme/* default.colors */.Ay.colors.primary.main, theme/* default.colors */.Ay.colors.primary.light);
var Select_HelperText = styled_components/* styled */.I4.div(Select_templateObject4 || (Select_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n  margin-top: ", ";\n  color: ", ";\n"])), theme/* default.typography */.Ay.typography.fontSize.xs, theme/* default.spacing */.Ay.spacing[1], function (props) {
  return props.error ? theme/* default.colors */.Ay.colors.error.main : theme/* default.colors */.Ay.colors.neutral[500];
});
var Select = /*#__PURE__*/(0,react.forwardRef)(function (_ref, ref) {
  var label = _ref.label,
    helperText = _ref.helperText,
    error = _ref.error,
    _ref$fullWidth = _ref.fullWidth,
    fullWidth = _ref$fullWidth === void 0 ? false : _ref$fullWidth,
    _ref$options = _ref.options,
    options = _ref$options === void 0 ? [] : _ref$options,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    props = (0,objectWithoutProperties/* default */.A)(_ref, Select_excluded);
  var _React$useState = react.useState(false),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    focused = _React$useState2[0],
    setFocused = _React$useState2[1];
  var handleFocus = function handleFocus(e) {
    setFocused(true);
    if (props.onFocus) props.onFocus(e);
  };
  var handleBlur = function handleBlur(e) {
    setFocused(false);
    if (props.onBlur) props.onBlur(e);
  };
  return /*#__PURE__*/react.createElement(SelectWrapper, {
    fullWidth: fullWidth
  }, label && /*#__PURE__*/react.createElement(SelectLabel, null, label), /*#__PURE__*/react.createElement(StyledSelect, (0,esm_extends/* default */.A)({
    ref: ref,
    disabled: disabled,
    error: error,
    focused: focused,
    onFocus: handleFocus,
    onBlur: handleBlur
  }, props), options.map(function (option) {
    return /*#__PURE__*/react.createElement("option", {
      key: option.value,
      value: option.value
    }, option.label);
  })), helperText && /*#__PURE__*/react.createElement(Select_HelperText, {
    error: error
  }, helperText));
});
Select.displayName = 'Select';
Select.propTypes = {
  label: (prop_types_default()).string,
  helperText: (prop_types_default()).string,
  error: (prop_types_default()).bool,
  fullWidth: (prop_types_default()).bool,
  options: prop_types_default().arrayOf(prop_types_default().shape({
    value: prop_types_default().oneOfType([(prop_types_default()).string, (prop_types_default()).number]).isRequired,
    label: (prop_types_default()).string.isRequired
  })),
  disabled: (prop_types_default()).bool,
  onFocus: (prop_types_default()).func,
  onBlur: (prop_types_default()).func
};
/* harmony default export */ const design_system_Select = (Select);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
;// ./src/design-system/components/Text.js

var Text_templateObject, Text_templateObject2, Text_templateObject3, Text_templateObject4, Text_templateObject5, Text_templateObject6, Text_templateObject7, _templateObject8, _templateObject9;


// Create the base Text component
var Text = styled_components_browser_esm/* default */.Ay.span(Text_templateObject || (Text_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  font-family: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n  line-height: ", ";\n  margin: 0;\n  ", "\n"])), function (props) {
  var _props$theme;
  return ((_props$theme = props.theme) === null || _props$theme === void 0 || (_props$theme = _props$theme.typography) === null || _props$theme === void 0 ? void 0 : _props$theme.fontFamily) || 'Inter, sans-serif';
}, function (props) {
  var _props$theme2;
  return ((_props$theme2 = props.theme) === null || _props$theme2 === void 0 || (_props$theme2 = _props$theme2.typography) === null || _props$theme2 === void 0 || (_props$theme2 = _props$theme2.fontSize) === null || _props$theme2 === void 0 ? void 0 : _props$theme2[props.size || 'md']) || '16px';
}, function (props) {
  var _props$theme3;
  return ((_props$theme3 = props.theme) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.typography) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.fontWeight) === null || _props$theme3 === void 0 ? void 0 : _props$theme3[props.weight || 'normal']) || 400;
}, function (props) {
  var _props$theme4, _props$theme5;
  return props.color ? ((_props$theme4 = props.theme) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.colors) === null || _props$theme4 === void 0 ? void 0 : _props$theme4[props.color]) || props.color : ((_props$theme5 = props.theme) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.colors) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.text) === null || _props$theme5 === void 0 ? void 0 : _props$theme5.primary) || '#111827';
}, function (props) {
  var _props$theme6;
  return ((_props$theme6 = props.theme) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.typography) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.lineHeight) === null || _props$theme6 === void 0 ? void 0 : _props$theme6[props.lineHeight || 'normal']) || 1.5;
}, function (props) {
  return props.truncate && "\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  ";
});

// Create variants using the styled function instead of withComponent
var Paragraph = (0,styled_components_browser_esm/* default */.Ay)(Text)(Text_templateObject2 || (Text_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: block;\n  margin-bottom: ", ";\n"])), function (props) {
  return props.theme.spacing[3];
});
var Heading = (0,styled_components_browser_esm/* default */.Ay)(Text)(Text_templateObject3 || (Text_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-weight: ", ";\n  line-height: ", ";\n  margin-bottom: ", ";\n"])), function (props) {
  return props.theme.typography.fontWeight.bold;
}, function (props) {
  return props.theme.typography.lineHeight.tight;
}, function (props) {
  return props.theme.spacing[3];
});

// Create heading levels
var H1 = (0,styled_components_browser_esm/* default */.Ay)(Heading)(Text_templateObject4 || (Text_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n"])), function (props) {
  return props.theme.typography.fontSize.xxxl;
});
var H2 = (0,styled_components_browser_esm/* default */.Ay)(Heading)(Text_templateObject5 || (Text_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n"])), function (props) {
  return props.theme.typography.fontSize.xxl;
});
var H3 = (0,styled_components_browser_esm/* default */.Ay)(Heading)(Text_templateObject6 || (Text_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n"])), function (props) {
  return props.theme.typography.fontSize.xl;
});
var H4 = (0,styled_components_browser_esm/* default */.Ay)(Heading)(Text_templateObject7 || (Text_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n"])), function (props) {
  return props.theme.typography.fontSize.lg;
});
var H5 = (0,styled_components_browser_esm/* default */.Ay)(Heading)(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n"])), function (props) {
  return props.theme.typography.fontSize.md;
});
var H6 = (0,styled_components_browser_esm/* default */.Ay)(Heading)(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: ", ";\n"])), function (props) {
  return props.theme.typography.fontSize.sm;
});

// Export all components

/* harmony default export */ const components_Text = (Text);
;// ./src/design-system/index.js
/**
 * Enhanced Design System Index
 *
 * This file exports all components, utilities, and theme tokens from the design system.
 * It provides a centralized access point for all design system resources.
 */

// Enhanced Theme



// Visual Hierarchy


// Design System Utilities



// Base Components





// Text Components


// Re-export styled-components for convenience


// Global styles (import this in your app)
var globalStyles = './global.css';

// Design system version
var VERSION = '2.0.0';

// Design system configuration
var config = {
  prefix: 'ds-',
  // CSS class prefix for design system components
  enableRTL: false,
  // Right-to-left language support
  enableDarkMode: true,
  // Dark mode support
  enableHighContrast: true,
  // High contrast mode support
  enableReducedMotion: true // Reduced motion support
};

/***/ }),

/***/ 81945:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sn: () => (/* binding */ API_ENDPOINTS)
/* harmony export */ });
/* unused harmony exports getFirstEndpointUrl, getAllEndpointUrls */
/**
 * API Endpoints Configuration
 *
 * This file centralizes all API endpoint URLs and ensures they're correctly configured
 * with the appropriate base URL from environment variables.
 */

// Get API base URL from environment or use relative URLs
var API_BASE_URL = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_35748_TZCBFLPORZWGBBSQ","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_13760_1592913036":"1","FAST_REFRESH":"true","FPS_BROWSER_APP_PROFILE_STRING":"Internet Explorer","FPS_BROWSER_USER_PROFILE_STRING":"Default","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_API_URL || '';

// For development, use proxy path when running through webpack dev server
var isDevelopment = "production" === 'development';
// In development, use relative URLs to leverage the proxy setup
// In production, use the full API_BASE_URL
var API_PREFIX = isDevelopment ? '/api' : API_BASE_URL;

// Log the API base URL for debugging
console.log('API Base URL:', API_BASE_URL || 'Using relative URLs');
console.log('Development mode:', isDevelopment);
console.log('API Prefix:', API_PREFIX);

// Define all API endpoints with multiple variations to handle different backend configurations
var API_ENDPOINTS = {
  // CSRF token endpoint
  CSRF_TOKEN: "".concat(API_PREFIX, "/csrf-token/"),
  // API status endpoints (try multiple variations)
  STATUS: ["".concat(API_PREFIX, "/status/"), "".concat(API_PREFIX, "/health/"), "".concat(API_PREFIX, "/health-check/")],
  HEALTH: ["".concat(API_PREFIX, "/health/"), "".concat(API_PREFIX, "/health-check/"), "".concat(API_PREFIX, "/status/")],
  // App data endpoints (try multiple variations)
  APP_DATA: ["".concat(API_PREFIX, "/get_app_data/"), "".concat(API_PREFIX, "/apps/"), "".concat(API_PREFIX, "/v1/apps/"), "".concat(API_PREFIX, "/app-data/")],
  SAVE_APP_DATA: ["".concat(API_PREFIX, "/save_app_data/"), "".concat(API_PREFIX, "/apps/"), "".concat(API_PREFIX, "/v1/apps/"), "".concat(API_PREFIX, "/app-data/")],
  EXPORT_APP_DATA: ["".concat(API_PREFIX, "/api/app-data/export/"), "".concat(API_PREFIX, "/export_app_data/")],
  IMPORT_APP_DATA: ["".concat(API_PREFIX, "/api/app-data/import/"), "".concat(API_PREFIX, "/import_app_data/")],
  // AI endpoints
  GENERATE_AI_SUGGESTIONS: ["".concat(API_BASE_URL, "/api/ai/suggestions/"), "".concat(API_BASE_URL, "/generate_ai_suggestions/")],
  GENERATE_IMAGE: ["".concat(API_BASE_URL, "/api/ai/generate-image/"), "".concat(API_BASE_URL, "/generate_image/")],
  // Authentication endpoints
  LOGIN: ["".concat(API_BASE_URL, "/api/auth/login/"), "".concat(API_BASE_URL, "/auth/login/")],
  REGISTER: ["".concat(API_BASE_URL, "/api/auth/register/"), "".concat(API_BASE_URL, "/auth/register/")],
  USER_PROFILE: ["".concat(API_BASE_URL, "/api/auth/profile/"), "".concat(API_BASE_URL, "/auth/profile/")],
  UPDATE_PROFILE: ["".concat(API_BASE_URL, "/api/auth/profile/update/"), "".concat(API_BASE_URL, "/auth/profile/update/")],
  // API keys endpoints
  API_KEYS: ["".concat(API_BASE_URL, "/api/api-keys/"), "".concat(API_BASE_URL, "/api/v1/api-keys/")],
  VALIDATE_API_KEY: ["".concat(API_BASE_URL, "/api/validate-api-key/"), "".concat(API_BASE_URL, "/api/v1/validate-api-key/")],
  // REST API endpoints
  API_V1: "".concat(API_BASE_URL, "/api/v1/"),
  API_V2: "".concat(API_BASE_URL, "/api/v2/"),
  // GraphQL endpoint
  GRAPHQL: "".concat(API_BASE_URL, "/graphql/")
};

/**
 * Get the first URL from an endpoint configuration
 * @param {string|Array} endpoint - Endpoint configuration
 * @returns {string} The first URL
 */
function getFirstEndpointUrl(endpoint) {
  if (Array.isArray(endpoint)) {
    return endpoint[0];
  }
  return endpoint;
}

/**
 * Get all URLs for an endpoint
 * @param {string|Array} endpoint - Endpoint configuration
 * @returns {Array} Array of URLs
 */
function getAllEndpointUrls(endpoint) {
  if (Array.isArray(endpoint)) {
    return endpoint;
  }
  return [endpoint];
}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (API_ENDPOINTS)));

/***/ }),

/***/ 83590:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   As: () => (/* binding */ useAuth),
/* harmony export */   OJ: () => (/* binding */ AuthProvider)
/* harmony export */ });
/* unused harmony export AuthContext */
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(12880);
/* harmony import */ var _components_analytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(48751);







// Create context
var AuthContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: function login() {},
  register: function register() {},
  logout: function logout() {},
  hasRole: function hasRole() {},
  hasPermission: function hasPermission() {}
});

/**
 * AuthProvider component
 * Provides authentication state and functions throughout the application
 */
var AuthProvider = function AuthProvider(_ref) {
  var children = _ref.children;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    user = _useState2[0],
    setUser = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    isLoading = _useState4[0],
    setIsLoading = _useState4[1];
  var _useAnalytics = (0,_components_analytics__WEBPACK_IMPORTED_MODULE_5__/* .useAnalytics */ .st)(),
    trackEvent = _useAnalytics.trackEvent;

  // Initialize authentication state
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    var initAuth = /*#__PURE__*/function () {
      var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {
        var currentUser, token;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              try {
                // Check if user is already authenticated
                if ((0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__/* .isAuthenticated */ .wR)()) {
                  // Get current user
                  currentUser = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__/* .getUser */ .wz)();
                  token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__/* .getToken */ .gf)();
                  if (currentUser) {
                    setUser(currentUser);

                    // Track authentication
                    trackEvent('auth_initialized', {
                      userId: currentUser.id || currentUser.username,
                      username: currentUser.username
                    });
                  }
                }
              } catch (error) {
                console.error('Auth initialization error:', error);
              } finally {
                setIsLoading(false);
              }
            case 1:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function initAuth() {
        return _ref2.apply(this, arguments);
      };
    }();
    initAuth();
  }, [trackEvent]);

  // Login function
  var login = /*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee2(username, password) {
      var result, _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            setIsLoading(true);
            _context2.prev = 1;
            _context2.next = 2;
            return (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__/* .login */ .iD)(username, password);
          case 2:
            result = _context2.sent;
            if (!result.success) {
              _context2.next = 3;
              break;
            }
            setUser(result.user);

            // Track login
            trackEvent('auth_login', {
              userId: result.user.id || result.user.username,
              username: result.user.username
            });
            return _context2.abrupt("return", result);
          case 3:
            // Track login error
            trackEvent('auth_login_error', {
              error: result.error
            });
            throw new Error(result.error);
          case 4:
            _context2.next = 6;
            break;
          case 5:
            _context2.prev = 5;
            _t = _context2["catch"](1);
            // Track login error
            trackEvent('auth_login_error', {
              error: _t.message
            });
            throw _t;
          case 6:
            _context2.prev = 6;
            setIsLoading(false);
            return _context2.finish(6);
          case 7:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 5, 6, 7]]);
    }));
    return function login(_x, _x2) {
      return _ref3.apply(this, arguments);
    };
  }();

  // Register function
  var register = /*#__PURE__*/function () {
    var _ref4 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee3(userData) {
      var result, _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            setIsLoading(true);
            _context3.prev = 1;
            _context3.next = 2;
            return (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__/* .register */ .kz)(userData);
          case 2:
            result = _context3.sent;
            if (!result.success) {
              _context3.next = 3;
              break;
            }
            setUser(result.user);

            // Track registration
            trackEvent('auth_register', {
              userId: result.user.id || result.user.username,
              username: result.user.username
            });
            return _context3.abrupt("return", result);
          case 3:
            // Track registration error
            trackEvent('auth_register_error', {
              error: result.error
            });
            throw new Error(result.error);
          case 4:
            _context3.next = 6;
            break;
          case 5:
            _context3.prev = 5;
            _t2 = _context3["catch"](1);
            // Track registration error
            trackEvent('auth_register_error', {
              error: _t2.message
            });
            throw _t2;
          case 6:
            _context3.prev = 6;
            setIsLoading(false);
            return _context3.finish(6);
          case 7:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 5, 6, 7]]);
    }));
    return function register(_x3) {
      return _ref4.apply(this, arguments);
    };
  }();

  // Logout function
  var logout = /*#__PURE__*/function () {
    var _ref5 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee4() {
      var result, _t3;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_2___default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            setIsLoading(true);
            _context4.prev = 1;
            _context4.next = 2;
            return (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__/* .logout */ .ri)();
          case 2:
            result = _context4.sent;
            setUser(null);

            // Track logout
            trackEvent('auth_logout');
            return _context4.abrupt("return", result.success !== false);
          case 3:
            _context4.prev = 3;
            _t3 = _context4["catch"](1);
            console.error('Logout error:', _t3);
            setUser(null); // Clear user even if logout fails
            return _context4.abrupt("return", false);
          case 4:
            _context4.prev = 4;
            setIsLoading(false);
            return _context4.finish(4);
          case 5:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 3, 4, 5]]);
    }));
    return function logout() {
      return _ref5.apply(this, arguments);
    };
  }();

  // Check if user has a specific role
  var hasRole = function hasRole(role) {
    return user && user.roles && user.roles.includes(role);
  };

  // Check if user has a specific permission
  var hasPermission = function hasPermission(permission) {
    return user && user.permissions && user.permissions.includes(permission);
  };

  // Create context value
  var contextValue = {
    user: user,
    isAuthenticated: !!user,
    isLoading: isLoading,
    login: login,
    register: register,
    logout: logout,
    hasRole: hasRole,
    hasPermission: hasPermission
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(AuthContext.Provider, {
    value: contextValue
  }, children);
};

/**
 * Custom hook for using authentication
 * @returns {Object} Authentication context
 */
var useAuth = function useAuth() {
  return (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(AuthContext);
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (AuthProvider)));

/***/ }),

/***/ 86020:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   Eo: () => (/* binding */ shadows),
/* harmony export */   HP: () => (/* binding */ mediaQueries),
/* harmony export */   Il: () => (/* binding */ typography),
/* harmony export */   Ti: () => (/* binding */ accessibility),
/* harmony export */   Tj: () => (/* binding */ colors),
/* harmony export */   Vq: () => (/* binding */ borderRadius),
/* harmony export */   WT: () => (/* binding */ animations),
/* harmony export */   YK: () => (/* binding */ spacing),
/* harmony export */   bm: () => (/* binding */ transitions),
/* harmony export */   dK: () => (/* binding */ components),
/* harmony export */   fE: () => (/* binding */ zIndex),
/* harmony export */   fi: () => (/* binding */ breakpoints)
/* harmony export */ });
/**
 * App Builder Design System - Enhanced Theme
 *
 * This file defines the comprehensive theme variables used throughout the application.
 * It provides a centralized place to manage colors, typography, spacing, accessibility, etc.
 *
 * Features:
 * - WCAG 2.1 AA compliant color contrasts
 * - Consistent design tokens
 * - Accessibility-first approach
 * - Support for light/dark modes
 * - Enhanced visual hierarchy
 */

// Enhanced Color palette with accessibility considerations
var colors = {
  // Primary colors - WCAG AA compliant
  primary: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    // Main primary - 4.5:1 contrast on white
    600: '#2563EB',
    // Darker primary - 7:1 contrast on white
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A',
    main: '#2563EB',
    light: '#DBEAFE',
    dark: '#1E40AF',
    contrastText: '#FFFFFF'
  },
  // Secondary colors - Enhanced green palette
  secondary: {
    50: '#ECFDF5',
    100: '#D1FAE5',
    200: '#A7F3D0',
    300: '#6EE7B7',
    400: '#34D399',
    500: '#10B981',
    // Main secondary
    600: '#059669',
    700: '#047857',
    800: '#065F46',
    900: '#064E3B',
    main: '#10B981',
    light: '#D1FAE5',
    dark: '#047857',
    contrastText: '#FFFFFF'
  },
  // Accent colors - Purple palette for highlights
  accent: {
    50: '#FAF5FF',
    100: '#F3E8FF',
    200: '#E9D5FF',
    300: '#D8B4FE',
    400: '#C084FC',
    500: '#A855F7',
    600: '#9333EA',
    700: '#7C3AED',
    800: '#6B21A8',
    900: '#581C87',
    main: '#8B5CF6',
    light: '#EDE9FE',
    dark: '#6D28D9',
    contrastText: '#FFFFFF'
  },
  // Enhanced neutral colors with better contrast ratios
  neutral: {
    0: '#FFFFFF',
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    // 7:1 contrast on white
    700: '#374151',
    // 10:1 contrast on white
    800: '#1F2937',
    // 15:1 contrast on white
    900: '#111827',
    // 18:1 contrast on white
    950: '#030712'
  },
  // Semantic colors with enhanced accessibility
  success: {
    50: '#F0FDF4',
    100: '#DCFCE7',
    200: '#BBF7D0',
    300: '#86EFAC',
    400: '#4ADE80',
    500: '#22C55E',
    600: '#16A34A',
    // Main success - 4.5:1 contrast
    700: '#15803D',
    800: '#166534',
    900: '#14532D',
    main: '#16A34A',
    light: '#DCFCE7',
    dark: '#15803D',
    contrastText: '#FFFFFF'
  },
  warning: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    // Main warning - 4.5:1 contrast
    700: '#B45309',
    800: '#92400E',
    900: '#78350F',
    main: '#D97706',
    light: '#FEF3C7',
    dark: '#B45309',
    contrastText: '#FFFFFF'
  },
  error: {
    50: '#FEF2F2',
    100: '#FEE2E2',
    200: '#FECACA',
    300: '#FCA5A5',
    400: '#F87171',
    500: '#EF4444',
    600: '#DC2626',
    // Main error - 4.5:1 contrast
    700: '#B91C1C',
    800: '#991B1B',
    900: '#7F1D1D',
    main: '#DC2626',
    light: '#FEE2E2',
    dark: '#B91C1C',
    contrastText: '#FFFFFF'
  },
  info: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#2563EB',
    // Main info - 7:1 contrast
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A',
    main: '#2563EB',
    light: '#DBEAFE',
    dark: '#1D4ED8',
    contrastText: '#FFFFFF'
  },
  // Background colors
  background: {
    "default": '#FFFFFF',
    paper: '#FFFFFF',
    secondary: '#F9FAFB',
    tertiary: '#F3F4F6',
    overlay: 'rgba(0, 0, 0, 0.5)',
    disabled: '#F3F4F6'
  },
  // Text colors with enhanced contrast ratios for WCAG AA/AAA compliance
  text: {
    primary: '#0F172A',
    // 15.3:1 contrast on white - WCAG AAA
    secondary: '#374151',
    // 7.2:1 contrast on white - WCAG AAA
    tertiary: '#4B5563',
    // 5.7:1 contrast on white - WCAG AA
    disabled: '#6B7280',
    // 4.5:1 contrast on white - WCAG AA
    hint: '#6B7280',
    // 4.5:1 contrast on white - WCAG AA
    inverse: '#FFFFFF',
    link: '#1D4ED8',
    // 5.9:1 contrast on white - WCAG AA
    linkHover: '#1E3A8A' // 8.2:1 contrast on white - WCAG AAA
  },
  // Border colors
  border: {
    "default": '#E5E7EB',
    light: '#F3F4F6',
    medium: '#D1D5DB',
    dark: '#9CA3AF',
    focus: '#2563EB',
    error: '#DC2626',
    success: '#16A34A',
    warning: '#D97706'
  },
  // Interactive states
  interactive: {
    hover: 'rgba(37, 99, 235, 0.04)',
    pressed: 'rgba(37, 99, 235, 0.08)',
    focus: 'rgba(37, 99, 235, 0.12)',
    selected: 'rgba(37, 99, 235, 0.08)',
    disabled: 'rgba(0, 0, 0, 0.04)'
  }
};

// Enhanced Typography with accessibility considerations
var typography = {
  fontFamily: {
    primary: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
    secondary: '"Inter", system-ui, sans-serif',
    code: '"Fira Code", "JetBrains Mono", "Roboto Mono", "Courier New", monospace',
    display: '"Inter", system-ui, sans-serif'
  },
  fontWeight: {
    thin: 100,
    extralight: 200,
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900
  },
  // Enhanced font size scale with better hierarchy
  fontSize: {
    xs: '0.75rem',
    // 12px - Small text, captions
    sm: '0.875rem',
    // 14px - Body text small
    base: '1rem',
    // 16px - Base body text
    md: '1rem',
    // 16px - Alias for base
    lg: '1.125rem',
    // 18px - Large body text
    xl: '1.25rem',
    // 20px - Small headings
    '2xl': '1.5rem',
    // 24px - Medium headings
    '3xl': '1.875rem',
    // 30px - Large headings
    '4xl': '2.25rem',
    // 36px - Extra large headings
    '5xl': '3rem',
    // 48px - Display text
    '6xl': '3.75rem',
    // 60px - Large display
    '7xl': '4.5rem',
    // 72px - Extra large display
    '8xl': '6rem',
    // 96px - Huge display
    '9xl': '8rem' // 128px - Massive display
  },
  // Enhanced line heights for better readability
  lineHeight: {
    none: 1,
    tight: 1.25,
    // For headings
    snug: 1.375,
    // For large text
    normal: 1.5,
    // For body text
    relaxed: 1.625,
    // For comfortable reading
    loose: 2,
    // For very comfortable reading
    3: 0.75,
    4: 1,
    5: 1.25,
    6: 1.5,
    7: 1.75,
    8: 2,
    9: 2.25,
    10: 2.5
  },
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em'
  },
  // Text styles for consistent usage
  textStyles: {
    h1: {
      fontSize: '2.25rem',
      fontWeight: 700,
      lineHeight: 1.25,
      letterSpacing: '-0.025em'
    },
    h2: {
      fontSize: '1.875rem',
      fontWeight: 600,
      lineHeight: 1.25,
      letterSpacing: '-0.025em'
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.375
    },
    h4: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.375
    },
    h5: {
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.375
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: 1.375
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.5
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.375
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 600,
      lineHeight: 1.375,
      letterSpacing: '0.1em',
      textTransform: 'uppercase'
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.25,
      letterSpacing: '0.025em'
    },
    code: {
      fontFamily: '"Fira Code", "JetBrains Mono", "Roboto Mono", monospace',
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.5
    }
  }
};

// Enhanced Spacing system with consistent 4px base unit
var spacing = {
  px: '1px',
  0: '0',
  0.5: '0.125rem',
  // 2px
  1: '0.25rem',
  // 4px
  1.5: '0.375rem',
  // 6px
  2: '0.5rem',
  // 8px
  2.5: '0.625rem',
  // 10px
  3: '0.75rem',
  // 12px
  3.5: '0.875rem',
  // 14px
  4: '1rem',
  // 16px
  5: '1.25rem',
  // 20px
  6: '1.5rem',
  // 24px
  7: '1.75rem',
  // 28px
  8: '2rem',
  // 32px
  9: '2.25rem',
  // 36px
  10: '2.5rem',
  // 40px
  11: '2.75rem',
  // 44px
  12: '3rem',
  // 48px
  14: '3.5rem',
  // 56px
  16: '4rem',
  // 64px
  20: '5rem',
  // 80px
  24: '6rem',
  // 96px
  28: '7rem',
  // 112px
  32: '8rem',
  // 128px
  36: '9rem',
  // 144px
  40: '10rem',
  // 160px
  44: '11rem',
  // 176px
  48: '12rem',
  // 192px
  52: '13rem',
  // 208px
  56: '14rem',
  // 224px
  60: '15rem',
  // 240px
  64: '16rem',
  // 256px
  72: '18rem',
  // 288px
  80: '20rem',
  // 320px
  96: '24rem' // 384px
};

// Enhanced Shadows with better depth perception
var shadows = {
  none: 'none',
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  '3xl': '0 35px 60px -12px rgba(0, 0, 0, 0.35)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  // Colored shadows for interactive elements
  primary: '0 4px 14px 0 rgba(37, 99, 235, 0.15)',
  secondary: '0 4px 14px 0 rgba(16, 185, 129, 0.15)',
  error: '0 4px 14px 0 rgba(220, 38, 38, 0.15)',
  warning: '0 4px 14px 0 rgba(217, 119, 6, 0.15)',
  success: '0 4px 14px 0 rgba(22, 163, 74, 0.15)',
  // Focus shadows for accessibility
  focus: '0 0 0 3px rgba(37, 99, 235, 0.1)',
  focusError: '0 0 0 3px rgba(220, 38, 38, 0.1)',
  focusSuccess: '0 0 0 3px rgba(22, 163, 74, 0.1)',
  focusWarning: '0 0 0 3px rgba(217, 119, 6, 0.1)'
};

// Enhanced Border radius with more options
var borderRadius = {
  none: '0',
  xs: '0.0625rem',
  // 1px
  sm: '0.125rem',
  // 2px
  md: '0.375rem',
  // 6px
  lg: '0.5rem',
  // 8px
  xl: '0.75rem',
  // 12px
  '2xl': '1rem',
  // 16px
  '3xl': '1.5rem',
  // 24px
  '4xl': '2rem',
  // 32px
  full: '9999px',
  // Component-specific radius
  button: '0.375rem',
  card: '0.5rem',
  input: '0.375rem',
  modal: '0.75rem',
  tooltip: '0.25rem'
};

// Enhanced Transitions with better performance
var transitions = {
  none: 'none',
  all: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  "default": 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  fast: 'all 100ms cubic-bezier(0.4, 0, 0.2, 1)',
  slow: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
  // Property-specific transitions
  colors: 'color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  opacity: 'opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  shadow: 'box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  transform: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  // Easing functions
  easing: {
    linear: 'linear',
    "in": 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
};

// Enhanced Z-index with semantic naming
var zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
  // App-specific z-index
  appHeader: 100,
  appSidebar: 200,
  componentPalette: 300,
  propertyEditor: 300,
  previewArea: 100,
  dragOverlay: 1000,
  tutorialOverlay: 1500,
  aiSuggestions: 1200
};

// Enhanced Breakpoints with more granular control
var breakpoints = {
  xs: '0px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
  '3xl': '1920px',
  // Semantic breakpoints
  mobile: '640px',
  tablet: '768px',
  desktop: '1024px',
  wide: '1280px',
  ultrawide: '1536px'
};

// Enhanced Media queries with more options
var mediaQueries = {
  // Min-width queries
  sm: "@media (min-width: ".concat(breakpoints.sm, ")"),
  md: "@media (min-width: ".concat(breakpoints.md, ")"),
  lg: "@media (min-width: ".concat(breakpoints.lg, ")"),
  xl: "@media (min-width: ".concat(breakpoints.xl, ")"),
  '2xl': "@media (min-width: ".concat(breakpoints['2xl'], ")"),
  '3xl': "@media (min-width: ".concat(breakpoints['3xl'], ")"),
  // Max-width queries
  maxSm: "@media (max-width: ".concat(parseInt(breakpoints.sm) - 1, "px)"),
  maxMd: "@media (max-width: ".concat(parseInt(breakpoints.md) - 1, "px)"),
  maxLg: "@media (max-width: ".concat(parseInt(breakpoints.lg) - 1, "px)"),
  maxXl: "@media (max-width: ".concat(parseInt(breakpoints.xl) - 1, "px)"),
  // Range queries
  smToMd: "@media (min-width: ".concat(breakpoints.sm, ") and (max-width: ").concat(parseInt(breakpoints.md) - 1, "px)"),
  mdToLg: "@media (min-width: ".concat(breakpoints.md, ") and (max-width: ").concat(parseInt(breakpoints.lg) - 1, "px)"),
  lgToXl: "@media (min-width: ".concat(breakpoints.lg, ") and (max-width: ").concat(parseInt(breakpoints.xl) - 1, "px)"),
  // Accessibility and preference queries
  reducedMotion: '@media (prefers-reduced-motion: reduce)',
  highContrast: '@media (prefers-contrast: high)',
  darkMode: '@media (prefers-color-scheme: dark)',
  lightMode: '@media (prefers-color-scheme: light)',
  // Device-specific queries
  touch: '@media (hover: none) and (pointer: coarse)',
  mouse: '@media (hover: hover) and (pointer: fine)',
  retina: '@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)'
};

// Accessibility features
var accessibility = {
  // Focus ring styles
  focusRing: {
    width: '2px',
    style: 'solid',
    color: colors.primary.main,
    offset: '2px'
  },
  // Minimum touch target sizes (WCAG 2.1 AA)
  minTouchTarget: {
    width: '44px',
    height: '44px'
  },
  // Screen reader only styles
  srOnly: {
    position: 'absolute',
    width: '1px',
    height: '1px',
    padding: '0',
    margin: '-1px',
    overflow: 'hidden',
    clip: 'rect(0, 0, 0, 0)',
    whiteSpace: 'nowrap',
    border: '0'
  },
  // High contrast mode support
  highContrast: {
    border: '1px solid',
    outline: '1px solid'
  }
};

// Animation presets
var animations = {
  // Fade animations
  fadeIn: 'fadeIn 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  fadeOut: 'fadeOut 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  // Slide animations
  slideInUp: 'slideInUp 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  slideInDown: 'slideInDown 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  slideInLeft: 'slideInLeft 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  slideInRight: 'slideInRight 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  // Scale animations
  scaleIn: 'scaleIn 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  scaleOut: 'scaleOut 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  // Bounce animation for feedback
  bounce: 'bounce 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  // Pulse for loading states
  pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
  // Spin for loading spinners
  spin: 'spin 1s linear infinite'
};

// Component-specific design tokens
var components = {
  button: {
    height: {
      sm: '32px',
      md: '40px',
      lg: '48px'
    },
    padding: {
      sm: '0 12px',
      md: '0 16px',
      lg: '0 24px'
    },
    fontSize: {
      sm: typography.fontSize.sm,
      md: typography.fontSize.base,
      lg: typography.fontSize.lg
    }
  },
  input: {
    height: {
      sm: '32px',
      md: '40px',
      lg: '48px'
    },
    padding: {
      sm: '0 8px',
      md: '0 12px',
      lg: '0 16px'
    }
  },
  card: {
    padding: {
      sm: spacing[4],
      md: spacing[6],
      lg: spacing[8]
    }
  },
  modal: {
    maxWidth: {
      sm: '400px',
      md: '600px',
      lg: '800px',
      xl: '1200px'
    }
  }
};

// Export the enhanced theme object
var theme = {
  colors: colors,
  typography: typography,
  spacing: spacing,
  shadows: shadows,
  borderRadius: borderRadius,
  transitions: transitions,
  zIndex: zIndex,
  breakpoints: breakpoints,
  mediaQueries: mediaQueries,
  accessibility: accessibility,
  animations: animations,
  components: components
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (theme);

/***/ }),

/***/ 86801:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ src_Routes)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(58168);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-router-dom/dist/index.js + 1 modules
var dist = __webpack_require__(11080);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./src/components/theme/ThemeManager.js
var ThemeManager = __webpack_require__(93385);
;// ./src/components/Layout.js

var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;




var LayoutContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: var(--background-color);\n  color: var(--text-color);\n  transition: background-color 0.3s ease, color 0.3s ease;\n"])));
var Header = styled_components_browser_esm/* default */.Ay.header(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n  height: 64px;\n  background-color: var(--background-secondary, #f5f5f5);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n"])));
var Logo = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  font-size: 1.5rem;\n  font-weight: bold;\n\n  a {\n    color: var(--primary-color);\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: none;\n      opacity: 0.9;\n    }\n  }\n"])));
var Nav = styled_components_browser_esm/* default */.Ay.nav(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  ul {\n    display: flex;\n    list-style: none;\n    margin: 0;\n    padding: 0;\n\n    li {\n      margin-left: 20px;\n\n      a {\n        color: var(--text-color);\n        text-decoration: none;\n        padding: 8px 12px;\n        border-radius: 4px;\n        transition: background-color 0.3s ease;\n\n        &:hover {\n          background-color: rgba(0, 0, 0, 0.05);\n        }\n\n        &.active {\n          color: var(--primary-color);\n          font-weight: 500;\n        }\n      }\n    }\n  }\n"])));
var Main = styled_components_browser_esm/* default */.Ay.main(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  flex: 1;\n  padding: 20px;\n"])));
var Footer = styled_components_browser_esm/* default */.Ay.footer(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 20px;\n  text-align: center;\n  background-color: var(--background-secondary, #f5f5f5);\n  border-top: 1px solid var(--border-color, #e8e8e8);\n"])));
var HeaderRight = styled_components_browser_esm/* default */.Ay.div(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  align-items: center;\n"])));

/**
 * Main layout component
 * Enhanced with styled components and theme switcher
 */
var Layout = function Layout(_ref) {
  var children = _ref.children;
  return /*#__PURE__*/react.createElement(LayoutContainer, {
    className: "app-layout"
  }, /*#__PURE__*/react.createElement(Header, {
    className: "app-header"
  }, /*#__PURE__*/react.createElement(Logo, {
    className: "logo"
  }, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/"
  }, "App Builder")), /*#__PURE__*/react.createElement(Nav, {
    className: "main-nav"
  }, /*#__PURE__*/react.createElement("ul", null, /*#__PURE__*/react.createElement("li", null, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/"
  }, "Home")), /*#__PURE__*/react.createElement("li", null, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/app-builder"
  }, "App Builder")), /*#__PURE__*/react.createElement("li", null, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/templates"
  }, "Templates")), /*#__PURE__*/react.createElement("li", null, /*#__PURE__*/react.createElement(dist/* Link */.N_, {
    to: "/websocket"
  }, "WebSocket")))), /*#__PURE__*/react.createElement(HeaderRight, null, /*#__PURE__*/react.createElement(ThemeManager/* ThemeSwitcher */.HP, null))), /*#__PURE__*/react.createElement(Main, {
    className: "app-main"
  }, children), /*#__PURE__*/react.createElement(Footer, {
    className: "app-footer"
  }, /*#__PURE__*/react.createElement("p", null, "\xA9 ", new Date().getFullYear(), " App Builder")));
};
/* harmony default export */ const components_Layout = (Layout);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
;// ./src/components/common/LoadingSpinner.css
// extracted by mini-css-extract-plugin

;// ./src/components/common/LoadingSpinner.js

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





/**
 * Enhanced loading spinner component with customizable options
 *
 * @param {Object} props Component props
 * @param {string} props.tip Text to display below the spinner
 * @param {string} props.size Size of the spinner ('small', 'default', 'large')
 * @param {boolean} props.fullScreen Whether to display the spinner in fullscreen mode
 * @param {string} props.backgroundColor Background color of the container
 * @param {React.ReactNode} props.icon Custom icon to use for the spinner
 * @param {string} props.className Additional CSS class names
 * @param {Object} props.style Additional inline styles
 * @returns {React.ReactElement} Loading spinner component
 */
var LoadingSpinner_LoadingSpinner = function LoadingSpinner(_ref) {
  var _ref$tip = _ref.tip,
    tip = _ref$tip === void 0 ? 'Loading...' : _ref$tip,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'large' : _ref$size,
    _ref$fullScreen = _ref.fullScreen,
    fullScreen = _ref$fullScreen === void 0 ? false : _ref$fullScreen,
    _ref$backgroundColor = _ref.backgroundColor,
    backgroundColor = _ref$backgroundColor === void 0 ? 'rgba(255, 255, 255, 0.8)' : _ref$backgroundColor,
    _ref$icon = _ref.icon,
    icon = _ref$icon === void 0 ? null : _ref$icon,
    _ref$className = _ref.className,
    className = _ref$className === void 0 ? '' : _ref$className,
    _ref$style = _ref.style,
    style = _ref$style === void 0 ? {} : _ref$style;
  // Custom loading icon
  var antIcon = icon || /*#__PURE__*/React.createElement(LoadingOutlined, {
    style: {
      fontSize: size === 'large' ? 40 : 24
    },
    spin: true
  });

  // If fullScreen is true, render the spinner in a fullscreen container
  if (fullScreen) {
    return /*#__PURE__*/React.createElement("div", {
      className: "loading-container ".concat(className),
      style: _objectSpread({
        backgroundColor: backgroundColor
      }, style),
      "aria-live": "polite",
      "aria-busy": "true"
    }, /*#__PURE__*/React.createElement("div", {
      className: "loading-content"
    }, /*#__PURE__*/React.createElement(Spin, {
      className: "loading-spinner",
      indicator: antIcon,
      size: size
    }), tip && /*#__PURE__*/React.createElement("div", {
      className: "loading-text",
      role: "status"
    }, tip)));
  }

  // Otherwise, render the spinner inline
  return /*#__PURE__*/React.createElement("div", {
    style: _objectSpread({
      textAlign: 'center',
      padding: '20px'
    }, style),
    className: className,
    "aria-live": "polite",
    "aria-busy": "true"
  }, /*#__PURE__*/React.createElement(Spin, {
    indicator: antIcon,
    size: size
  }), tip && /*#__PURE__*/React.createElement("div", {
    style: {
      marginTop: '12px'
    },
    role: "status"
  }, tip));
};
/* harmony default export */ const common_LoadingSpinner = ((/* unused pure expression or super */ null && (LoadingSpinner_LoadingSpinner)));
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./src/services/AuthService.js
var AuthService = __webpack_require__(11606);
;// ./src/components/LoadingState.js

var LoadingState_templateObject;




var Text = es/* Typography */.o5.Text;

// Styled components
var FullPageLoading = styled_components_browser_esm/* default */.Ay.div(LoadingState_templateObject || (LoadingState_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  width: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  background: rgba(255, 255, 255, 0.95);\n  z-index: 1000;\n"])));

// Commented out as it's not currently used
// const LoadingContainer = styled.div`
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
//   align-items: center;
//   min-height: ${props => props.minHeight || '200px'};
//   width: 100%;
//   padding: 20px;
//
//   .ant-spin-nested-loading {
//     width: 100%;
//   }
//
//   .ant-spin-container {
//     text-align: center;
//   }
// `;

/**
 * LoadingState component
 * Displays a loading spinner with optional message and description
 */
var LoadingState = function LoadingState(_ref) {
  var _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? true : _ref$loading,
    _ref$message = _ref.message,
    message = _ref$message === void 0 ? 'Loading...' : _ref$message,
    _ref$description = _ref.description,
    description = _ref$description === void 0 ? '' : _ref$description,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'default' : _ref$size,
    _ref$fullPage = _ref.fullPage,
    fullPage = _ref$fullPage === void 0 ? false : _ref$fullPage,
    _ref$children = _ref.children,
    children = _ref$children === void 0 ? null : _ref$children,
    _ref$error = _ref.error,
    error = _ref$error === void 0 ? null : _ref$error,
    _ref$retry = _ref.retry,
    retry = _ref$retry === void 0 ? null : _ref$retry;
  // If not loading and no error, just render children
  if (!loading && !error) {
    return /*#__PURE__*/react.createElement(react.Fragment, null, children);
  }

  // Custom spinner icon
  var antIcon = /*#__PURE__*/react.createElement(icons_es/* LoadingOutlined */.NKq, {
    style: {
      fontSize: size === 'large' ? 40 : 24
    },
    spin: true
  });

  // Content to display
  var content = error ? /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: error.title || 'Error',
    description: /*#__PURE__*/react.createElement(es/* Space */.$x, {
      direction: "vertical"
    }, /*#__PURE__*/react.createElement(Text, null, error.message || 'An error occurred'), retry && /*#__PURE__*/react.createElement("a", {
      onClick: retry
    }, "Try again")),
    type: "error",
    showIcon: true
  }) : /*#__PURE__*/react.createElement(es/* Spin */.tK, {
    indicator: antIcon,
    size: size
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      padding: message ? '30px' : '50px'
    }
  }, message && /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 8
    }
  }, message), description && /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 4,
      color: 'rgba(0, 0, 0, 0.45)'
    }
  }, description)));

  // If fullPage, center in the page
  if (fullPage) {
    return /*#__PURE__*/react.createElement(FullPageLoading, null, content);
  }

  // Otherwise, just return the content
  return content;
};
/* harmony default export */ const components_LoadingState = (LoadingState);
;// ./src/components/auth/ProtectedRoute.js






// Check if we're in development mode
var DEV_MODE = "production" === 'development';

/**
 * ProtectedRoute component
 * Protects routes that require authentication
 */
var ProtectedRoute = function ProtectedRoute(_ref) {
  var children = _ref.children,
    _ref$redirectTo = _ref.redirectTo,
    redirectTo = _ref$redirectTo === void 0 ? '/login' : _ref$redirectTo,
    _ref$roles = _ref.roles,
    roles = _ref$roles === void 0 ? [] : _ref$roles,
    _ref$loadingFallback = _ref.loadingFallback,
    loadingFallback = _ref$loadingFallback === void 0 ? null : _ref$loadingFallback;
  var _useState = (0,react.useState)(true),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    authenticated = _useState4[0],
    setAuthenticated = _useState4[1];
  var _useState5 = (0,react.useState)(true),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    hasRequiredRole = _useState6[0],
    setHasRequiredRole = _useState6[1];
  (0,react.useEffect)(function () {
    // In development mode, always allow access
    if (DEV_MODE) {
      console.log('Development mode: Bypassing authentication for protected route');
      setAuthenticated(true);
      setHasRequiredRole(true);
      setLoading(false);
      return;
    }

    // Check if the user is authenticated
    var isAuthenticated = AuthService/* default */.A.isAuthenticated();
    setAuthenticated(isAuthenticated);
    if (isAuthenticated && roles.length > 0) {
      // Check if the user has the required role
      var user = AuthService/* default */.A.getUser();
      var userRoles = (user === null || user === void 0 ? void 0 : user.roles) || [];

      // Check if the user has at least one of the required roles
      var hasRole = roles.some(function (role) {
        return userRoles.includes(role);
      });
      setHasRequiredRole(hasRole);
    }
    setLoading(false);
  }, [roles]);

  // Add a listener for auth changes
  (0,react.useEffect)(function () {
    // Skip auth listener in development mode
    if (DEV_MODE) {
      return;
    }
    var removeListener = AuthService/* default */.A.addListener(function (event) {
      if (event === 'login' || event === 'register') {
        setAuthenticated(true);

        // Check roles if needed
        if (roles.length > 0) {
          var user = AuthService/* default */.A.getUser();
          var userRoles = (user === null || user === void 0 ? void 0 : user.roles) || [];

          // Check if the user has at least one of the required roles
          var hasRole = roles.some(function (role) {
            return userRoles.includes(role);
          });
          setHasRequiredRole(hasRole);
        }
      } else if (event === 'logout') {
        setAuthenticated(false);
        setHasRequiredRole(false);
      }
    });
    return removeListener;
  }, [roles]);

  // Show loading state
  if (loading) {
    return loadingFallback || /*#__PURE__*/react.createElement(components_LoadingState, {
      loading: true,
      message: "Checking authentication...",
      fullPage: true
    });
  }

  // Redirect if not authenticated
  if (!authenticated) {
    // Add the current URL as a redirect parameter
    var redirectUrl = "".concat(redirectTo, "?redirect=").concat(encodeURIComponent(window.location.pathname));
    return /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: redirectUrl,
      replace: true
    });
  }

  // Redirect if missing required role
  if (!hasRequiredRole) {
    return /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/unauthorized",
      replace: true
    });
  }

  // Render children if authenticated and has required role
  return children;
};
/* harmony default export */ const auth_ProtectedRoute = (ProtectedRoute);
// EXTERNAL MODULE: ./src/contexts/AuthContext.js
var AuthContext = __webpack_require__(83590);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js
var possibleConstructorReturn = __webpack_require__(56822);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js
var getPrototypeOf = __webpack_require__(53954);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/inherits.js
var inherits = __webpack_require__(85501);
;// ./src/components/loading/EnhancedSuspense.js








var EnhancedSuspense_templateObject, EnhancedSuspense_templateObject2, EnhancedSuspense_templateObject3, EnhancedSuspense_templateObject4;
function EnhancedSuspense_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function EnhancedSuspense_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? EnhancedSuspense_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : EnhancedSuspense_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _callSuper(t, o, e) { return o = (0,getPrototypeOf/* default */.A)(o), (0,possibleConstructorReturn/* default */.A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,getPrototypeOf/* default */.A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }




var EnhancedSuspense_Text = es/* Typography */.o5.Text;

/**
 * Enhanced Suspense Component
 *
 * Provides better loading states, error boundaries, and retry functionality
 * for lazy-loaded components with progressive loading indicators.
 * Updated to fix React prop warnings.
 */

var SuspenseContainer = styled_components_browser_esm/* default */.Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['minHeight', 'background', 'padding'].includes(prop);
  }
})(EnhancedSuspense_templateObject || (EnhancedSuspense_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  min-height: ", ";\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ", ";\n  background: ", ";\n"])), function (props) {
  return props.minHeight || '200px';
}, function (props) {
  return props.padding || '20px';
}, function (props) {
  return props.background || 'transparent';
});
var LoadingContent = styled_components_browser_esm/* default */.Ay.div(EnhancedSuspense_templateObject2 || (EnhancedSuspense_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n  max-width: 400px;\n  text-align: center;\n"])));
var ProgressContainer = styled_components_browser_esm/* default */.Ay.div(EnhancedSuspense_templateObject3 || (EnhancedSuspense_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  width: 100%;\n  max-width: 300px;\n"])));
var ErrorContainer = styled_components_browser_esm/* default */.Ay.div(EnhancedSuspense_templateObject4 || (EnhancedSuspense_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n  padding: 24px;\n  border-radius: 8px;\n  background: #fff2f0;\n  border: 1px solid #ffccc7;\n  max-width: 400px;\n"])));

// Enhanced loading component with progress indication
var EnhancedLoadingFallback = function EnhancedLoadingFallback(_ref) {
  var _ref$message = _ref.message,
    message = _ref$message === void 0 ? 'Loading...' : _ref$message,
    _ref$description = _ref.description,
    description = _ref$description === void 0 ? null : _ref$description,
    _ref$showProgress = _ref.showProgress,
    showProgress = _ref$showProgress === void 0 ? false : _ref$showProgress,
    _ref$progress = _ref.progress,
    progress = _ref$progress === void 0 ? 0 : _ref$progress,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'large' : _ref$size,
    _ref$minHeight = _ref.minHeight,
    minHeight = _ref$minHeight === void 0 ? '200px' : _ref$minHeight,
    _ref$background = _ref.background,
    background = _ref$background === void 0 ? 'transparent' : _ref$background,
    _ref$timeout = _ref.timeout,
    timeout = _ref$timeout === void 0 ? 10000 : _ref$timeout;
  var _React$useState = react.useState(false),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    timeoutReached = _React$useState2[0],
    setTimeoutReached = _React$useState2[1];
  react.useEffect(function () {
    if (timeout > 0) {
      var timer = setTimeout(function () {
        setTimeoutReached(true);
      }, timeout);
      return function () {
        return clearTimeout(timer);
      };
    }
  }, [timeout]);
  if (timeoutReached) {
    return /*#__PURE__*/react.createElement(SuspenseContainer, {
      minHeight: minHeight,
      background: background
    }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
      message: "Loading is taking longer than expected",
      description: "The component is still loading. Please wait or try refreshing the page.",
      type: "warning",
      showIcon: true,
      icon: /*#__PURE__*/react.createElement(icons_es/* ExclamationCircleOutlined */.G2i, null),
      action: /*#__PURE__*/react.createElement(es/* Button */.$n, {
        size: "small",
        onClick: function onClick() {
          return window.location.reload();
        }
      }, "Refresh")
    }));
  }
  return /*#__PURE__*/react.createElement(SuspenseContainer, {
    minHeight: minHeight,
    background: background
  }, /*#__PURE__*/react.createElement(LoadingContent, null, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
    size: size
  }), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(EnhancedSuspense_Text, {
    strong: true,
    style: {
      fontSize: 16
    }
  }, message), description && /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react.createElement(EnhancedSuspense_Text, {
    type: "secondary",
    style: {
      fontSize: 14
    }
  }, description))), showProgress && /*#__PURE__*/react.createElement(ProgressContainer, null, /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: progress,
    size: "small",
    showInfo: false,
    strokeColor: {
      '0%': '#108ee9',
      '100%': '#87d068'
    }
  }), /*#__PURE__*/react.createElement(EnhancedSuspense_Text, {
    type: "secondary",
    style: {
      fontSize: 12
    }
  }, progress, "% loaded"))));
};

// Error boundary for Suspense components
var SuspenseErrorBoundary = /*#__PURE__*/function (_Component) {
  function SuspenseErrorBoundary(props) {
    var _this;
    (0,classCallCheck/* default */.A)(this, SuspenseErrorBoundary);
    _this = _callSuper(this, SuspenseErrorBoundary, [props]);
    (0,defineProperty/* default */.A)(_this, "handleRetry", function () {
      _this.setState(function (prevState) {
        return {
          hasError: false,
          error: null,
          retryCount: prevState.retryCount + 1
        };
      });
    });
    _this.state = {
      hasError: false,
      error: null,
      retryCount: 0
    };
    return _this;
  }
  (0,inherits/* default */.A)(SuspenseErrorBoundary, _Component);
  return (0,createClass/* default */.A)(SuspenseErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      console.error('Suspense Error Boundary caught an error:', error, errorInfo);

      // Report to error tracking service
      if (window.reportError) {
        window.reportError(error, EnhancedSuspense_objectSpread({
          context: 'suspense-boundary',
          component: this.props.componentName
        }, errorInfo));
      }
    }
  }, {
    key: "render",
    value: function render() {
      if (this.state.hasError) {
        var _this$state$error;
        var _this$props = this.props,
          _this$props$component = _this$props.componentName,
          componentName = _this$props$component === void 0 ? 'Component' : _this$props$component,
          _this$props$maxRetrie = _this$props.maxRetries,
          maxRetries = _this$props$maxRetrie === void 0 ? 3 : _this$props$maxRetrie;
        var canRetry = this.state.retryCount < maxRetries;
        return /*#__PURE__*/react.createElement(SuspenseContainer, null, /*#__PURE__*/react.createElement(ErrorContainer, null, /*#__PURE__*/react.createElement(icons_es/* ExclamationCircleOutlined */.G2i, {
          style: {
            fontSize: 24,
            color: '#ff4d4f'
          }
        }), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(EnhancedSuspense_Text, {
          strong: true,
          style: {
            color: '#ff4d4f'
          }
        }, "Failed to load ", componentName), /*#__PURE__*/react.createElement("div", {
          style: {
            marginTop: 8
          }
        }, /*#__PURE__*/react.createElement(EnhancedSuspense_Text, {
          type: "secondary",
          style: {
            fontSize: 12
          }
        }, ((_this$state$error = this.state.error) === null || _this$state$error === void 0 ? void 0 : _this$state$error.message) || 'An unexpected error occurred'))), canRetry ? /*#__PURE__*/react.createElement(es/* Button */.$n, {
          type: "primary",
          icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
          onClick: this.handleRetry
        }, "Try Again (", maxRetries - this.state.retryCount, " attempts left)") : /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(EnhancedSuspense_Text, {
          type: "secondary",
          style: {
            fontSize: 12
          }
        }, "Maximum retry attempts reached. Please refresh the page."), /*#__PURE__*/react.createElement(es/* Button */.$n, {
          style: {
            marginTop: 8
          },
          onClick: function onClick() {
            return window.location.reload();
          }
        }, "Refresh Page"))));
      }
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      return {
        hasError: true,
        error: error
      };
    }
  }]);
}(react.Component); // Enhanced Suspense wrapper component
var EnhancedSuspense = function EnhancedSuspense(_ref2) {
  var children = _ref2.children,
    _ref2$fallback = _ref2.fallback,
    fallback = _ref2$fallback === void 0 ? null : _ref2$fallback,
    _ref2$componentName = _ref2.componentName,
    componentName = _ref2$componentName === void 0 ? 'Component' : _ref2$componentName,
    _ref2$loadingMessage = _ref2.loadingMessage,
    loadingMessage = _ref2$loadingMessage === void 0 ? 'Loading...' : _ref2$loadingMessage,
    _ref2$loadingDescript = _ref2.loadingDescription,
    loadingDescription = _ref2$loadingDescript === void 0 ? null : _ref2$loadingDescript,
    _ref2$showProgress = _ref2.showProgress,
    showProgress = _ref2$showProgress === void 0 ? false : _ref2$showProgress,
    _ref2$progress = _ref2.progress,
    progress = _ref2$progress === void 0 ? 0 : _ref2$progress,
    _ref2$minHeight = _ref2.minHeight,
    minHeight = _ref2$minHeight === void 0 ? '200px' : _ref2$minHeight,
    _ref2$background = _ref2.background,
    background = _ref2$background === void 0 ? 'transparent' : _ref2$background,
    _ref2$timeout = _ref2.timeout,
    timeout = _ref2$timeout === void 0 ? 10000 : _ref2$timeout,
    _ref2$maxRetries = _ref2.maxRetries,
    maxRetries = _ref2$maxRetries === void 0 ? 3 : _ref2$maxRetries,
    _ref2$onError = _ref2.onError,
    onError = _ref2$onError === void 0 ? null : _ref2$onError;
  var defaultFallback = fallback || /*#__PURE__*/react.createElement(EnhancedLoadingFallback, {
    message: loadingMessage,
    description: loadingDescription,
    showProgress: showProgress,
    progress: progress,
    minHeight: minHeight,
    background: background,
    timeout: timeout
  });
  return /*#__PURE__*/react.createElement(SuspenseErrorBoundary, {
    componentName: componentName,
    maxRetries: maxRetries,
    onError: onError
  }, /*#__PURE__*/react.createElement(react.Suspense, {
    fallback: defaultFallback
  }, children));
};

// Predefined loading configurations
var LoadingConfigurations = {
  page: {
    minHeight: '100vh',
    background: '#f5f5f5',
    loadingMessage: 'Loading page...',
    timeout: 15000
  },
  component: {
    minHeight: '200px',
    background: 'transparent',
    loadingMessage: 'Loading component...',
    timeout: 10000
  },
  modal: {
    minHeight: '300px',
    background: 'white',
    loadingMessage: 'Loading...',
    timeout: 8000
  },
  inline: {
    minHeight: '100px',
    background: 'transparent',
    loadingMessage: 'Loading...',
    timeout: 5000
  }
};

/* harmony default export */ const loading_EnhancedSuspense = (EnhancedSuspense);
// EXTERNAL MODULE: ./src/utils/lazyLoading.js
var lazyLoading = __webpack_require__(56702);
;// ./src/config/routeConfig.js

function routeConfig_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function routeConfig_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? routeConfig_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : routeConfig_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/**
 * Enhanced Route Configuration with Priority-Based Loading
 * 
 * Routes are categorized by priority and loading strategy to optimize
 * initial bundle size and user experience.
 */

// High Priority Routes (Core functionality - load immediately)
var HighPriorityRoutes = {
  HomePage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(3913)]).then(__webpack_require__.bind(__webpack_require__, 56294));
  }, {
    componentName: 'HomePage',
    fallback: lazyLoading/* LoadingStates */.PF.fullPage,
    retryAttempts: 3,
    preload: true // Preload since it's likely to be accessed
  }),
  HomePageMVP: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 3548).then(__webpack_require__.bind(__webpack_require__, 21167));
  }, {
    componentName: 'HomePageMVP',
    fallback: lazyLoading/* LoadingStates */.PF.fullPage,
    retryAttempts: 3,
    preload: true
  }),
  AppBuilderMVP: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 8832).then(__webpack_require__.bind(__webpack_require__, 18832));
  }, {
    componentName: 'AppBuilderMVP',
    fallback: lazyLoading/* LoadingStates */.PF.withDescription('Loading App Builder...'),
    retryAttempts: 3,
    preload: false // Load on demand
  })
};

// Medium Priority Routes (Important features - load after initial render)
var MediumPriorityRoutes = {
  AppBuilderPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(7749), __webpack_require__.e(6827), __webpack_require__.e(9975), __webpack_require__.e(6254), __webpack_require__.e(6369), __webpack_require__.e(4110)]).then(__webpack_require__.bind(__webpack_require__, 94110));
  }, {
    componentName: 'AppBuilderPage',
    fallback: lazyLoading/* LoadingStates */.PF.withDescription('Loading App Builder...'),
    retryAttempts: 3,
    preload: false
  }),
  AppBuilderWithTheme: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(7749), __webpack_require__.e(6827), __webpack_require__.e(9975), __webpack_require__.e(6254), __webpack_require__.e(6369), __webpack_require__.e(4651)]).then(__webpack_require__.bind(__webpack_require__, 64651));
  }, {
    componentName: 'AppBuilderWithTheme',
    fallback: lazyLoading/* LoadingStates */.PF.withDescription('Loading App Builder...'),
    retryAttempts: 3,
    preload: false
  }),
  DashboardPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 1934).then(__webpack_require__.bind(__webpack_require__, 91934));
  }, {
    componentName: 'DashboardPage',
    fallback: lazyLoading/* LoadingStates */.PF.withDescription('Loading dashboard...'),
    retryAttempts: 2,
    preload: false
  }),
  ProjectsPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 3730).then(__webpack_require__.bind(__webpack_require__, 43730));
  }, {
    componentName: 'ProjectsPage',
    fallback: lazyLoading/* LoadingStates */.PF.withDescription('Loading projects...'),
    retryAttempts: 2,
    preload: false
  })
};

// Low Priority Routes (Secondary features - load on interaction)
var LowPriorityRoutes = {
  WebSocketPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(7749), __webpack_require__.e(1470), __webpack_require__.e(1602)]).then(__webpack_require__.bind(__webpack_require__, 71602));
  }, {
    componentName: 'WebSocketPage',
    fallback: lazyLoading/* LoadingStates */.PF.withDescription('Loading WebSocket tools...'),
    retryAttempts: 2,
    preload: false
  }),
  TemplatesPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 4509).then(__webpack_require__.bind(__webpack_require__, 44509));
  }, {
    componentName: 'TemplatesPage',
    fallback: lazyLoading/* LoadingStates */.PF.withDescription('Loading templates...'),
    retryAttempts: 2,
    preload: false
  }),
  ProfilePage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(7749), __webpack_require__.e(7435)]).then(__webpack_require__.bind(__webpack_require__, 37435));
  }, {
    componentName: 'ProfilePage',
    fallback: lazyLoading/* LoadingStates */.PF.standard,
    retryAttempts: 2,
    preload: false
  }),
  SettingsPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 1375).then(__webpack_require__.bind(__webpack_require__, 91375));
  }, {
    componentName: 'SettingsPage',
    fallback: lazyLoading/* LoadingStates */.PF.standard,
    retryAttempts: 2,
    preload: false
  })
};

// Authentication Routes (Load on demand)
var AuthRoutes = {
  LoginPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 4067).then(__webpack_require__.bind(__webpack_require__, 94067));
  }, {
    componentName: 'LoginPage',
    fallback: lazyLoading/* LoadingStates */.PF.standard,
    retryAttempts: 2,
    preload: false
  }),
  RegisterPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(7749), __webpack_require__.e(5831)]).then(__webpack_require__.bind(__webpack_require__, 65831));
  }, {
    componentName: 'RegisterPage',
    fallback: lazyLoading/* LoadingStates */.PF.standard,
    retryAttempts: 2,
    preload: false
  }),
  ForgotPasswordPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 6548).then(__webpack_require__.bind(__webpack_require__, 36548));
  }, {
    componentName: 'ForgotPasswordPage',
    fallback: lazyLoading/* LoadingStates */.PF.standard,
    retryAttempts: 2,
    preload: false
  }),
  ResetPasswordPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 8552).then(__webpack_require__.bind(__webpack_require__, 38552));
  }, {
    componentName: 'ResetPasswordPage',
    fallback: lazyLoading/* LoadingStates */.PF.standard,
    retryAttempts: 2,
    preload: false
  })
};

// Error and Utility Routes (Very low priority)
var UtilityRoutes = {
  NotFoundPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 253).then(__webpack_require__.bind(__webpack_require__, 20253));
  }, {
    componentName: 'NotFoundPage',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  }),
  UnauthorizedPage: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(368)]).then(__webpack_require__.bind(__webpack_require__, 90368));
  }, {
    componentName: 'UnauthorizedPage',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  })
};

// Test Routes (Development only)
var TestRoutes = {
  ThemeTest: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 4905).then(__webpack_require__.bind(__webpack_require__, 24905));
  }, {
    componentName: 'ThemeTest',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  }),
  DarkModeTest: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(1177)]).then(__webpack_require__.bind(__webpack_require__, 11177));
  }, {
    componentName: 'DarkModeTest',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  }),
  ContrastTest: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(1160)]).then(__webpack_require__.bind(__webpack_require__, 61160));
  }, {
    componentName: 'ContrastTest',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  }),
  HeaderContrastTest: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(6827), __webpack_require__.e(2553)]).then(__webpack_require__.bind(__webpack_require__, 2553));
  }, {
    componentName: 'HeaderContrastTest',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  }),
  ServiceWorkerTest: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 7772).then(__webpack_require__.bind(__webpack_require__, 87772));
  }, {
    componentName: 'ServiceWorkerTest',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  }),
  ResponsiveDemo: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return __webpack_require__.e(/* import() */ 9180).then(__webpack_require__.bind(__webpack_require__, 89180));
  }, {
    componentName: 'ResponsiveDemo',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  }),
  CollaborationTest: (0,lazyLoading/* createLazyComponent */.s_)(function () {
    return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(6903), __webpack_require__.e(631)]).then(__webpack_require__.bind(__webpack_require__, 90631));
  }, {
    componentName: 'CollaborationTest',
    fallback: lazyLoading/* LoadingStates */.PF.minimal,
    retryAttempts: 1,
    preload: false
  })
};

// Combined routes object for easy access
var AllRoutes = routeConfig_objectSpread(routeConfig_objectSpread(routeConfig_objectSpread(routeConfig_objectSpread(routeConfig_objectSpread(routeConfig_objectSpread({}, HighPriorityRoutes), MediumPriorityRoutes), LowPriorityRoutes), AuthRoutes), UtilityRoutes), TestRoutes);

// Route priority configuration for progressive loading
var RoutePriority = {
  high: Object.keys(HighPriorityRoutes),
  medium: Object.keys(MediumPriorityRoutes),
  low: Object.keys(LowPriorityRoutes),
  auth: Object.keys(AuthRoutes),
  utility: Object.keys(UtilityRoutes),
  test: Object.keys(TestRoutes)
};

// Route metadata for analytics and optimization
var RouteMetadata = {
  '/': {
    priority: 'high',
    category: 'landing',
    preload: true
  },
  '/home': {
    priority: 'high',
    category: 'landing',
    preload: true
  },
  '/home-mvp': {
    priority: 'high',
    category: 'landing',
    preload: true
  },
  '/mvp': {
    priority: 'high',
    category: 'app-builder',
    preload: false
  },
  '/app-builder': {
    priority: 'medium',
    category: 'app-builder',
    preload: false
  },
  '/dashboard': {
    priority: 'medium',
    category: 'user',
    preload: false
  },
  '/projects': {
    priority: 'medium',
    category: 'user',
    preload: false
  },
  '/websocket': {
    priority: 'low',
    category: 'tools',
    preload: false
  },
  '/templates': {
    priority: 'low',
    category: 'resources',
    preload: false
  },
  '/profile': {
    priority: 'low',
    category: 'user',
    preload: false
  },
  '/settings': {
    priority: 'low',
    category: 'user',
    preload: false
  },
  '/login': {
    priority: 'auth',
    category: 'auth',
    preload: false
  },
  '/register': {
    priority: 'auth',
    category: 'auth',
    preload: false
  }
};

// Preload strategy based on route priority
var preloadRoutesByPriority = function preloadRoutesByPriority() {
  // Preload high priority routes immediately
  setTimeout(function () {
    RoutePriority.high.forEach(function (routeName) {
      var route = HighPriorityRoutes[routeName];
      if (route && route.preload) {
        route.preload();
      }
    });
  }, 100);

  // Preload medium priority routes after a delay
  setTimeout(function () {
    RoutePriority.medium.forEach(function (routeName) {
      var route = MediumPriorityRoutes[routeName];
      if (route && route.preload) {
        route.preload();
      }
    });
  }, 2000);
};
/* harmony default export */ const routeConfig = ((/* unused pure expression or super */ null && (AllRoutes)));
;// ./src/Routes.js









// Import Quill test page
var QuillTestPage = /*#__PURE__*/react.lazy(function () {
  return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(6903), __webpack_require__.e(6285)]).then(__webpack_require__.bind(__webpack_require__, 86285));
});

// Import Template System Demo page
var TemplateSystemDemo = /*#__PURE__*/react.lazy(function () {
  return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(5385)]).then(__webpack_require__.bind(__webpack_require__, 5385));
});

// Direct import for HomePageMVP to avoid lazy loading issues
var HomePageMVPDirect = /*#__PURE__*/react.lazy(function () {
  return __webpack_require__.e(/* import() */ 3548).then(__webpack_require__.bind(__webpack_require__, 21167));
});

// Import the new integrated App Builder page
var AppBuilderIntegratedPage = /*#__PURE__*/react.lazy(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774), __webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(8292), __webpack_require__.e(8294), __webpack_require__.e(4922), __webpack_require__.e(6474), __webpack_require__.e(7101), __webpack_require__.e(3288), __webpack_require__.e(5505), __webpack_require__.e(1667), __webpack_require__.e(9578)]).then(__webpack_require__.bind(__webpack_require__, 99578));
});

// Import the enhanced IntegratedAppBuilder for the advanced route
var IntegratedAppBuilderPage = /*#__PURE__*/react.lazy(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774), __webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(1470), __webpack_require__.e(3288), __webpack_require__.e(823), __webpack_require__.e(64), __webpack_require__.e(8274), __webpack_require__.e(8930), __webpack_require__.e(3585), __webpack_require__.e(5059)]).then(__webpack_require__.bind(__webpack_require__, 60064)).then(function (module) {
    return {
      "default": function _default() {
        return /*#__PURE__*/react.createElement("div", {
          style: {
            height: '100vh',
            overflow: 'hidden'
          }
        }, /*#__PURE__*/react.createElement(module["default"], {
          projectId: "main-project",
          initialComponents: [],
          enableFeatures: {
            websocket: true,
            tutorial: true,
            aiSuggestions: true,
            templates: true,
            codeExport: true,
            collaboration: true,
            testing: true,
            dataManagement: true,
            performanceMonitoring: true,
            enhancedExport: true,
            tutorialAssistant: true
          },
          onSave: function onSave(projectData) {
            console.log('Project saved:', projectData);
            localStorage.setItem('app-builder-project', JSON.stringify(projectData));
          },
          onLoad: function onLoad(projectId) {
            console.log('Loading project:', projectId);
            var saved = localStorage.getItem('app-builder-project');
            return saved ? JSON.parse(saved) : {
              components: []
            };
          },
          onError: function onError(error) {
            console.error('App Builder error:', error);
          }
        }));
      }
    };
  })["catch"](function (error) {
    console.warn('Failed to load IntegratedAppBuilder, using fallback:', error);
    return Promise.all(/* import() */[__webpack_require__.e(1450), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(5658), __webpack_require__.e(4492), __webpack_require__.e(4704), __webpack_require__.e(8244), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538), __webpack_require__.e(3815), __webpack_require__.e(4666), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(7749), __webpack_require__.e(6827), __webpack_require__.e(9975), __webpack_require__.e(6254), __webpack_require__.e(6369)]).then(__webpack_require__.bind(__webpack_require__, 98302));
  });
});

// Enhanced route configuration with priority-based loading


// Loading component for suspense fallback
var PageLoading = function PageLoading() {
  return /*#__PURE__*/React.createElement(LoadingSpinner, {
    tip: "Loading page...",
    fullScreen: true,
    backgroundColor: "rgba(255, 255, 255, 0.9)"
  });
};

/**
 * Application routes with enhanced lazy loading and priority-based code splitting
 * Updated to include authentication and project management
 */
var Routes = function Routes() {
  var _useAuth = (0,AuthContext/* useAuth */.As)(),
    isAuthenticated = _useAuth.isAuthenticated;

  // Initialize progressive route loading
  (0,react.useEffect)(function () {
    preloadRoutesByPriority();
  }, []);
  return /*#__PURE__*/react.createElement(components_Layout, null, /*#__PURE__*/react.createElement(loading_EnhancedSuspense, (0,esm_extends/* default */.A)({
    componentName: "Routes",
    loadingMessage: "Loading page...",
    loadingDescription: "Please wait while the page loads"
  }, LoadingConfigurations.page, {
    timeout: 15000
  }), /*#__PURE__*/react.createElement(dist/* Routes */.BV, null, /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/",
    element: /*#__PURE__*/react.createElement(HomePageMVPDirect, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/home",
    element: /*#__PURE__*/react.createElement(HighPriorityRoutes.HomePage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/home-mvp",
    element: /*#__PURE__*/react.createElement(HighPriorityRoutes.HomePageMVP, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/mvp",
    element: /*#__PURE__*/react.createElement(HighPriorityRoutes.AppBuilderMVP, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/templates",
    element: /*#__PURE__*/react.createElement(LowPriorityRoutes.TemplatesPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/template-system-demo",
    element: /*#__PURE__*/react.createElement(TemplateSystemDemo, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/login",
    element: isAuthenticated ? /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/dashboard"
    }) : /*#__PURE__*/react.createElement(AuthRoutes.LoginPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/register",
    element: isAuthenticated ? /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/dashboard"
    }) : /*#__PURE__*/react.createElement(AuthRoutes.RegisterPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/forgot-password",
    element: isAuthenticated ? /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/dashboard"
    }) : /*#__PURE__*/react.createElement(AuthRoutes.ForgotPasswordPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/reset-password/:token",
    element: isAuthenticated ? /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/dashboard"
    }) : /*#__PURE__*/react.createElement(AuthRoutes.ResetPasswordPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/dashboard",
    element: /*#__PURE__*/react.createElement(auth_ProtectedRoute, null, /*#__PURE__*/react.createElement(MediumPriorityRoutes.DashboardPage, null))
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/app-builder",
    element: /*#__PURE__*/react.createElement(auth_ProtectedRoute, null, /*#__PURE__*/react.createElement(AppBuilderIntegratedPage, null))
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/app-builder-advanced",
    element: /*#__PURE__*/react.createElement(auth_ProtectedRoute, null, /*#__PURE__*/react.createElement(IntegratedAppBuilderPage, null))
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/app-builder-enhanced",
    element: /*#__PURE__*/react.createElement(auth_ProtectedRoute, null, /*#__PURE__*/react.createElement(MediumPriorityRoutes.AppBuilderWithTheme, null))
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/websocket",
    element: /*#__PURE__*/react.createElement(auth_ProtectedRoute, null, /*#__PURE__*/react.createElement(LowPriorityRoutes.WebSocketPage, null))
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/profile",
    element: /*#__PURE__*/react.createElement(auth_ProtectedRoute, null, /*#__PURE__*/react.createElement(LowPriorityRoutes.ProfilePage, null))
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/projects",
    element: /*#__PURE__*/react.createElement(auth_ProtectedRoute, null, /*#__PURE__*/react.createElement(MediumPriorityRoutes.ProjectsPage, null))
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/settings",
    element: /*#__PURE__*/react.createElement(auth_ProtectedRoute, null, /*#__PURE__*/react.createElement(LowPriorityRoutes.SettingsPage, null))
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/theme-test",
    element: /*#__PURE__*/react.createElement(TestRoutes.ThemeTest, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/dark-mode-test",
    element: /*#__PURE__*/react.createElement(TestRoutes.DarkModeTest, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/contrast-test",
    element: /*#__PURE__*/react.createElement(TestRoutes.ContrastTest, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/header-contrast-test",
    element: /*#__PURE__*/react.createElement(TestRoutes.HeaderContrastTest, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/service-worker-test",
    element: /*#__PURE__*/react.createElement(TestRoutes.ServiceWorkerTest, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/responsive-demo",
    element: /*#__PURE__*/react.createElement(TestRoutes.ResponsiveDemo, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/quill-test",
    element: /*#__PURE__*/react.createElement(QuillTestPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/collaboration-test",
    element: /*#__PURE__*/react.createElement(TestRoutes.CollaborationTest, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/unauthorized",
    element: /*#__PURE__*/react.createElement(UtilityRoutes.UnauthorizedPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "/404",
    element: /*#__PURE__*/react.createElement(UtilityRoutes.NotFoundPage, null)
  }), /*#__PURE__*/react.createElement(dist/* Route */.qh, {
    path: "*",
    element: /*#__PURE__*/react.createElement(dist/* Navigate */.C5, {
      to: "/404"
    })
  }))));
};
/* harmony default export */ const src_Routes = (Routes);

/***/ }),

/***/ 93385:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HP: () => (/* binding */ ThemeSwitcher),
/* harmony export */   NP: () => (/* binding */ ThemeProvider),
/* harmony export */   rV: () => (/* binding */ ThemeCustomizer)
/* harmony export */ });
/* unused harmony exports THEMES, useTheme */
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(71468);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





// Default themes
var THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
  CUSTOM: 'custom'
};

// Default colors
var DEFAULT_COLORS = (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, THEMES.LIGHT, {
  primary: '#1976d2',
  secondary: '#f50057',
  background: '#ffffff',
  surface: '#f5f5f5',
  text: '#212121',
  textSecondary: '#757575',
  border: '#e0e0e0',
  error: '#d32f2f',
  warning: '#f57c00',
  info: '#0288d1',
  success: '#388e3c'
}), THEMES.DARK, {
  primary: '#90caf9',
  secondary: '#f48fb1',
  background: '#121212',
  surface: '#1e1e1e',
  text: '#ffffff',
  textSecondary: '#b0b0b0',
  border: '#333333',
  error: '#f44336',
  warning: '#ff9800',
  info: '#29b6f6',
  success: '#66bb6a'
});

// Create theme context
var ThemeContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({
  theme: THEMES.LIGHT,
  colors: DEFAULT_COLORS[THEMES.LIGHT],
  setTheme: function setTheme() {},
  setCustomColors: function setCustomColors() {}
});

/**
 * Theme provider component
 */
var ThemeProvider = function ThemeProvider(_ref) {
  var children = _ref.children,
    _ref$initialTheme = _ref.initialTheme,
    initialTheme = _ref$initialTheme === void 0 ? THEMES.SYSTEM : _ref$initialTheme;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialTheme),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    theme = _useState2[0],
    setThemeState = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({}),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    customColors = _useState4[0],
    setCustomColors = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? THEMES.DARK : THEMES.LIGHT),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    systemTheme = _useState6[0],
    setSystemTheme = _useState6[1];

  // Get current theme colors
  var getThemeColors = function getThemeColors() {
    if (theme === THEMES.SYSTEM) {
      return DEFAULT_COLORS[systemTheme];
    }
    if (theme === THEMES.CUSTOM) {
      return _objectSpread(_objectSpread({}, DEFAULT_COLORS[THEMES.LIGHT]), customColors);
    }
    return DEFAULT_COLORS[theme];
  };

  // Set theme
  var setTheme = function setTheme(newTheme) {
    setThemeState(newTheme);
    localStorage.setItem('app_theme', newTheme);
  };

  // Apply theme to document
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    // Get colors
    var colors = getThemeColors();

    // Apply colors to document
    Object.entries(colors).forEach(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref2, 2),
        key = _ref3[0],
        value = _ref3[1];
      document.documentElement.style.setProperty("--color-".concat(key), value);
    });

    // Set data-theme attribute
    var effectiveTheme = theme === THEMES.SYSTEM ? systemTheme : theme;
    document.documentElement.setAttribute('data-theme', effectiveTheme);

    // Add/remove dark class
    if (effectiveTheme === THEMES.DARK) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [theme, systemTheme, customColors]);

  // Listen for system theme changes
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    var handleChange = function handleChange(e) {
      setSystemTheme(e.matches ? THEMES.DARK : THEMES.LIGHT);
    };

    // Add listener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
    }

    // Load saved theme
    var savedTheme = localStorage.getItem('app_theme');
    if (savedTheme) {
      setThemeState(savedTheme);
    }

    // Clean up
    return function () {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        // Fallback for older browsers
        mediaQuery.removeListener(handleChange);
      }
    };
  }, []);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeContext.Provider, {
    value: {
      theme: theme,
      colors: getThemeColors(),
      setTheme: setTheme,
      setCustomColors: setCustomColors
    }
  }, children);
};

// Hook to use theme
var useTheme = function useTheme() {
  var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/**
 * ThemeManager Component
 * Manages theme selection, customization, and import/export
 */
var ThemeManager = function ThemeManager(_ref4) {
  var _ref4$placement = _ref4.placement,
    placement = _ref4$placement === void 0 ? 'right' : _ref4$placement,
    _ref4$width = _ref4.width,
    width = _ref4$width === void 0 ? 400 : _ref4$width,
    _ref4$visible = _ref4.visible,
    visible = _ref4$visible === void 0 ? false : _ref4$visible,
    _ref4$onClose = _ref4.onClose,
    onClose = _ref4$onClose === void 0 ? function () {} : _ref4$onClose;
  // Component state
  var _useState7 = useState(false),
    _useState8 = _slicedToArray(_useState7, 2),
    editMode = _useState8[0],
    setEditMode = _useState8[1];
  var _useState9 = useState(null),
    _useState0 = _slicedToArray(_useState9, 2),
    currentTheme = _useState0[0],
    setCurrentTheme = _useState0[1];
  var _useState1 = useState(null),
    _useState10 = _slicedToArray(_useState1, 2),
    previewTheme = _useState10[0],
    setPreviewTheme = _useState10[1];
  var _Form$useForm = Form.useForm(),
    _Form$useForm2 = _slicedToArray(_Form$useForm, 1),
    form = _Form$useForm2[0];

  // Redux
  var dispatch = useDispatch();
  var themes = useSelector(function (state) {
    var _state$theme;
    return ((_state$theme = state.theme) === null || _state$theme === void 0 ? void 0 : _state$theme.themes) || [];
  });
  var userPreferences = useSelector(function (state) {
    return state.preferences || {};
  });

  // Placeholder functions
  var handleThemeChange = function handleThemeChange() {};
  var handleToggleAutoApply = function handleToggleAutoApply() {};
  var handleEditTheme = function handleEditTheme() {};
  var handleCancelEdit = function handleCancelEdit() {};
  var handleSaveTheme = function handleSaveTheme() {};
  var handleCreateTheme = function handleCreateTheme() {};
  var handleDeleteTheme = function handleDeleteTheme() {};
  var handleFormValuesChange = function handleFormValuesChange() {};
  var handleExportTheme = function handleExportTheme() {};
  var handleImportTheme = function handleImportTheme() {};
  return /*#__PURE__*/React.createElement(Drawer, {
    title: "Theme Manager",
    placement: placement,
    width: width,
    onClose: onClose,
    open: visible
  }, /*#__PURE__*/React.createElement(Form, {
    form: form,
    layout: "vertical",
    onValuesChange: handleFormValuesChange
  }, /*#__PURE__*/React.createElement("div", null, "Theme Manager Form Content")));
};

/**
 * ThemeSwitcher Component
 * Simple toggle for switching between light and dark themes
 */
var ThemeSwitcher = function ThemeSwitcher(_ref5) {
  var _ref5$position = _ref5.position,
    position = _ref5$position === void 0 ? 'right' : _ref5$position;
  var _useTheme = useTheme(),
    theme = _useTheme.theme,
    setTheme = _useTheme.setTheme;
  var toggleTheme = function toggleTheme() {
    if (theme === THEMES.DARK) {
      setTheme(THEMES.LIGHT);
    } else {
      setTheme(THEMES.DARK);
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "theme-switcher ".concat(position)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: theme === THEMES.DARK ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .BgColorsOutlined */ .Ebl, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .BgColorsOutlined */ .Ebl, null),
    onClick: toggleTheme,
    "aria-label": "Switch to ".concat(theme === THEMES.DARK ? 'light' : 'dark', " theme")
  }));
};

/**
 * ThemeCustomizer Component
 * Allows customization of theme colors and settings
 */
var ThemeCustomizer = function ThemeCustomizer(_ref6) {
  var onSave = _ref6.onSave;
  var _useTheme2 = useTheme(),
    colors = _useTheme2.colors,
    setCustomColors = _useTheme2.setCustomColors;
  var _Form$useForm3 = antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.useForm(),
    _Form$useForm4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_Form$useForm3, 1),
    form = _Form$useForm4[0];
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    form.setFieldsValue({
      primaryColor: colors.primary,
      secondaryColor: colors.secondary,
      backgroundColor: colors.background,
      textColor: colors.text
    });
  }, [colors, form]);
  var handleSubmit = function handleSubmit(values) {
    setCustomColors(values);
    if (onSave) {
      onSave(values);
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "theme-customizer"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    form: form,
    layout: "vertical",
    onFinish: handleSubmit
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "primaryColor",
    label: "Primary Color"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "color"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "secondaryColor",
    label: "Secondary Color"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "color"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "backgroundColor",
    label: "Background Color"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "color"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "textColor",
    label: "Text Color"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "color"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "primary",
    htmlType: "submit"
  }, "Apply Theme"))));
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (ThemeManager)));

/***/ })

}]);