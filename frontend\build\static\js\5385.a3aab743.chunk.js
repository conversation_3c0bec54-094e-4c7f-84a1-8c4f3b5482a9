"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5385],{

/***/ 5385:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(70572);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(84447);
/* harmony import */ var react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(25672);
/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(62966);






var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }








var Content = antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Content,
  Sider = antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Sider;
var Title = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU.TabPane;
var Option = antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6.Option;
var Panel = antd__WEBPACK_IMPORTED_MODULE_8__/* .Collapse */ .SD.Panel;
var Search = antd__WEBPACK_IMPORTED_MODULE_8__/* .Input */ .pd.Search;
var DirectoryTree = antd__WEBPACK_IMPORTED_MODULE_8__/* .Tree */ .PH.DirectoryTree;

// Styled Components
var DemoContainer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 24px;\n"])));
var DemoHeader = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  text-align: center;\n  margin-bottom: 32px;\n  padding: 40px 20px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 16px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n"])));
var TemplateCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n  border-radius: 12px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);\n  }\n  \n  .ant-card-head {\n    border-bottom: 1px solid #f0f0f0;\n    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);\n  }\n"])));
var MetadataContainer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  background: #f8f9fa;\n  padding: 16px;\n  border-radius: 8px;\n  margin: 16px 0;\n"])));
var JsonViewer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  background: #1e1e1e;\n  border-radius: 8px;\n  padding: 16px;\n  margin: 16px 0;\n  max-height: 400px;\n  overflow-y: auto;\n  \n  pre {\n    margin: 0;\n    color: #d4d4d4;\n    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n    font-size: 12px;\n    line-height: 1.5;\n  }\n"])));
var StatsContainer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin: 24px 0;\n"])));
var FeatureGrid = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 24px;\n  margin: 24px 0;\n"])));
var HierarchyVisualization = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  margin: 24px 0;\n"])));
var InteractiveDemo = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  margin: 24px 0;\n  min-height: 400px;\n"])));
var PreviewContainer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  border: 2px dashed #d9d9d9;\n  border-radius: 8px;\n  padding: 24px;\n  text-align: center;\n  background: #fafafa;\n  min-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n"])));
var ComponentPreview = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 16px;\n  margin: 8px 0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n"])));

/**
 * Template System Demonstration Page
 * 
 * A comprehensive demonstration interface showcasing the hierarchical template system
 * functionality including LayoutTemplate and AppTemplate models, their relationships,
 * and integration with the App Builder Enhanced application.
 */
var TemplateSystemDemo = function TemplateSystemDemo() {
  var _testResults$load, _testResults$search, _testResults$featured, _testResults$categori;
  // State management
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({
      layout: [],
      app: [],
      component: []
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState3, 2),
    templates = _useState4[0],
    setTemplates = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState5, 2),
    selectedTemplate = _useState6[0],
    setSelectedTemplate = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)('overview'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState7, 2),
    activeTab = _useState8[0],
    setActiveTab = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(''),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState9, 2),
    searchTerm = _useState0[0],
    setSearchTerm = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)('all'),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState1, 2),
    filterCategory = _useState10[0],
    setFilterCategory = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)('all'),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState11, 2),
    filterType = _useState12[0],
    setFilterType = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)('visual'),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState13, 2),
    previewMode = _useState14[0],
    setPreviewMode = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState15, 2),
    showHierarchy = _useState16[0],
    setShowHierarchy = _useState16[1];
  var _useState17 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)('browse'),
    _useState18 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState17, 2),
    demoMode = _useState18[0],
    setDemoMode = _useState18[1];
  var _useState19 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState20 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState19, 2),
    importModalVisible = _useState20[0],
    setImportModalVisible = _useState20[1];
  var _useState21 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState22 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState21, 2),
    exportModalVisible = _useState22[0],
    setExportModalVisible = _useState22[1];
  var _useState23 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({}),
    _useState24 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState23, 2),
    testResults = _useState24[0],
    setTestResults = _useState24[1];
  var _useState25 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({}),
    _useState26 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState25, 2),
    systemStats = _useState26[0],
    setSystemStats = _useState26[1];
  var _useState27 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState28 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState27, 2),
    error = _useState28[0],
    setError = _useState28[1];

  // Template categories and types
  var templateCategories = {
    layout: ['grid', 'flex', 'sidebar', 'header', 'footer', 'navigation', 'landing', 'dashboard'],
    app: ['business', 'ecommerce', 'portfolio', 'dashboard', 'landing', 'blog', 'social', 'education', 'healthcare', 'finance', 'other'],
    component: ['button', 'input', 'card', 'modal', 'form', 'chart', 'table', 'navigation', 'media', 'layout']
  };

  // Load templates from API
  var loadTemplates = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee() {
    var _layoutResponse$data$, _appResponse$data$res, _componentResponse$da, _layoutResponse$data$2, _appResponse$data$res2, _componentResponse$da2, _yield$Promise$all, _yield$Promise$all2, layoutResponse, appResponse, componentResponse, totalTemplates, _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setLoading(true);
          setError(null);
          _context.prev = 1;
          _context.next = 2;
          return Promise.all([axios__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Ay.get('/api/layout-templates/'), axios__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Ay.get('/api/app-templates/'), axios__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Ay.get('/api/component-templates/')]);
        case 2:
          _yield$Promise$all = _context.sent;
          _yield$Promise$all2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_yield$Promise$all, 3);
          layoutResponse = _yield$Promise$all2[0];
          appResponse = _yield$Promise$all2[1];
          componentResponse = _yield$Promise$all2[2];
          setTemplates({
            layout: layoutResponse.data.results || layoutResponse.data || [],
            app: appResponse.data.results || appResponse.data || [],
            component: componentResponse.data.results || componentResponse.data || []
          });

          // Calculate system statistics
          totalTemplates = (((_layoutResponse$data$ = layoutResponse.data.results) === null || _layoutResponse$data$ === void 0 ? void 0 : _layoutResponse$data$.length) || 0) + (((_appResponse$data$res = appResponse.data.results) === null || _appResponse$data$res === void 0 ? void 0 : _appResponse$data$res.length) || 0) + (((_componentResponse$da = componentResponse.data.results) === null || _componentResponse$da === void 0 ? void 0 : _componentResponse$da.length) || 0);
          setSystemStats({
            totalTemplates: totalTemplates,
            layoutTemplates: ((_layoutResponse$data$2 = layoutResponse.data.results) === null || _layoutResponse$data$2 === void 0 ? void 0 : _layoutResponse$data$2.length) || 0,
            appTemplates: ((_appResponse$data$res2 = appResponse.data.results) === null || _appResponse$data$res2 === void 0 ? void 0 : _appResponse$data$res2.length) || 0,
            componentTemplates: ((_componentResponse$da2 = componentResponse.data.results) === null || _componentResponse$da2 === void 0 ? void 0 : _componentResponse$da2.length) || 0,
            publicTemplates: [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(layoutResponse.data.results || []), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(appResponse.data.results || []), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(componentResponse.data.results || [])).filter(function (t) {
              return t.is_public;
            }).length
          });
          _context.next = 4;
          break;
        case 3:
          _context.prev = 3;
          _t = _context["catch"](1);
          console.error('Error loading templates:', _t);
          setError('Failed to load templates. Please check your connection and try again.');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error('Failed to load templates');
        case 4:
          _context.prev = 4;
          setLoading(false);
          return _context.finish(4);
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 3, 4, 5]]);
  })), []);

  // Load featured templates
  var loadFeaturedTemplates = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee2() {
    var response, _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 1;
          return axios__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Ay.get('/api/featured-templates/');
        case 1:
          response = _context2.sent;
          return _context2.abrupt("return", response.data);
        case 2:
          _context2.prev = 2;
          _t2 = _context2["catch"](0);
          console.error('Error loading featured templates:', _t2);
          return _context2.abrupt("return", {
            components: [],
            layouts: [],
            apps: []
          });
        case 3:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 2]]);
  })), []);

  // Search templates
  var searchTemplates = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(/*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee3(query) {
      var type,
        category,
        params,
        response,
        _args3 = arguments,
        _t3;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            type = _args3.length > 1 && _args3[1] !== undefined ? _args3[1] : '';
            category = _args3.length > 2 && _args3[2] !== undefined ? _args3[2] : '';
            _context3.prev = 1;
            params = new URLSearchParams();
            if (query) params.append('q', query);
            if (type) params.append('type', type);
            if (category) params.append('category', category);
            _context3.next = 2;
            return axios__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Ay.get("/api/template-search/?".concat(params.toString()));
          case 2:
            response = _context3.sent;
            return _context3.abrupt("return", response.data);
          case 3:
            _context3.prev = 3;
            _t3 = _context3["catch"](1);
            console.error('Error searching templates:', _t3);
            return _context3.abrupt("return", {
              components: [],
              layouts: [],
              apps: []
            });
          case 4:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 3]]);
    }));
    return function (_x) {
      return _ref3.apply(this, arguments);
    };
  }(), []);

  // Test template functionality
  var testTemplateFunction = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(/*#__PURE__*/function () {
    var _ref4 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee4(functionName) {
      var templateId,
        result,
        template,
        response,
        _args4 = arguments,
        _t4,
        _t5;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            templateId = _args4.length > 1 && _args4[1] !== undefined ? _args4[1] : null;
            setTestResults(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, functionName, {
                loading: true
              }));
            });
            _context4.prev = 1;
            _t4 = functionName;
            _context4.next = _t4 === 'load' ? 2 : _t4 === 'search' ? 3 : _t4 === 'featured' ? 5 : _t4 === 'categories' ? 7 : 9;
            break;
          case 2:
            if (templateId) {
              template = [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(templates.layout), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(templates.app), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(templates.component)).find(function (t) {
                return t.id === templateId;
              });
              result = {
                success: true,
                data: template
              };
            }
            return _context4.abrupt("continue", 10);
          case 3:
            _context4.next = 4;
            return searchTemplates(searchTerm, filterType, filterCategory);
          case 4:
            result = _context4.sent;
            return _context4.abrupt("continue", 10);
          case 5:
            _context4.next = 6;
            return loadFeaturedTemplates();
          case 6:
            result = _context4.sent;
            return _context4.abrupt("continue", 10);
          case 7:
            _context4.next = 8;
            return axios__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Ay.get('/api/template-categories/');
          case 8:
            response = _context4.sent;
            result = response.data;
            return _context4.abrupt("continue", 10);
          case 9:
            result = {
              success: false,
              error: 'Unknown function'
            };
          case 10:
            setTestResults(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, functionName, {
                loading: false,
                success: true,
                data: result,
                timestamp: new Date().toISOString()
              }));
            });
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("".concat(functionName, " test completed successfully"));
            _context4.next = 12;
            break;
          case 11:
            _context4.prev = 11;
            _t5 = _context4["catch"](1);
            setTestResults(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, functionName, {
                loading: false,
                success: false,
                error: _t5.message,
                timestamp: new Date().toISOString()
              }));
            });
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error("".concat(functionName, " test failed: ").concat(_t5.message));
          case 12:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 11]]);
    }));
    return function (_x2) {
      return _ref4.apply(this, arguments);
    };
  }(), [templates, searchTerm, filterType, filterCategory, searchTemplates, loadFeaturedTemplates]);

  // Initialize demo
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    loadTemplates();
  }, [loadTemplates]);

  // Filter templates based on search and filters
  var filteredTemplates = react__WEBPACK_IMPORTED_MODULE_7__.useMemo(function () {
    var filterTemplate = function filterTemplate(template) {
      var _template$description;
      var matchesSearch = !searchTerm || template.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_template$description = template.description) === null || _template$description === void 0 ? void 0 : _template$description.toLowerCase().includes(searchTerm.toLowerCase()));
      var matchesCategory = filterCategory === 'all' || template.layout_type === filterCategory || template.app_category === filterCategory || template.component_type === filterCategory;
      return matchesSearch && matchesCategory;
    };
    return {
      layout: templates.layout.filter(filterTemplate),
      app: templates.app.filter(filterTemplate),
      component: templates.component.filter(filterTemplate)
    };
  }, [templates, searchTerm, filterCategory]);

  // Render template preview
  var renderTemplatePreview = function renderTemplatePreview(template, type) {
    if (!template) return null;
    var getTemplateIcon = function getTemplateIcon(type) {
      switch (type) {
        case 'layout':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LayoutOutlined */ .hy2, null);
        case 'app':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ProjectOutlined */ .KGW, null);
        case 'component':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, null);
        default:
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreOutlined */ .rS9, null);
      }
    };
    var getTemplateColor = function getTemplateColor(type) {
      switch (type) {
        case 'layout':
          return '#52c41a';
        case 'app':
          return '#1890ff';
        case 'component':
          return '#722ed1';
        default:
          return '#666';
      }
    };
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TemplateCard, {
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, getTemplateIcon(type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", null, template.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
        color: getTemplateColor(type)
      }, type), template.is_public && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
        color: "green"
      }, "Public")),
      extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
        title: "Preview Template"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .EyeOutlined */ .Om2, null),
        onClick: function onClick() {
          return setSelectedTemplate(_objectSpread(_objectSpread({}, template), {}, {
            type: type
          }));
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
        title: "Test Load"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .PlayCircleOutlined */ .VgC, null),
        onClick: function onClick() {
          return testTemplateFunction('load', template.id);
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
        title: "Export Template"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DownloadOutlined */ .jsW, null),
        onClick: function onClick() {
          var dataStr = JSON.stringify(template, null, 2);
          var dataBlob = new Blob([dataStr], {
            type: 'application/json'
          });
          var url = URL.createObjectURL(dataBlob);
          var link = document.createElement('a');
          link.href = url;
          link.download = "".concat(template.name.replace(/\s+/g, '_'), "_template.json");
          link.click();
          URL.revokeObjectURL(url);
        }
      })))
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH, {
      size: "small",
      column: 1
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
      label: "Description"
    }, template.description || 'No description available'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
      label: "Category"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, null, template.layout_type || template.app_category || template.component_type || 'Unknown')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
      label: "Created"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "secondary"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ClockCircleOutlined */ .L8Y, {
      style: {
        marginRight: 4
      }
    }), new Date(template.created_at).toLocaleDateString())), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
      label: "Components"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Badge */ .Ex, {
      count: template.components ? Array.isArray(template.components) ? template.components.length : (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(template.components) === 'object' ? Object.keys(template.components).length : 0 : 0
    }))), previewMode === 'json' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
      language: "json",
      style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
      customStyle: {
        background: 'transparent',
        padding: 0
      }
    }, JSON.stringify(template.components || {}, null, 2))), previewMode === 'visual' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(PreviewContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, {
      style: {
        fontSize: 48,
        color: '#d9d9d9',
        marginBottom: 16
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "secondary"
    }, "Visual preview would render here"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 12
      }
    }, "Component structure: ", JSON.stringify(template.components || {}).length, " characters")));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DemoContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DemoHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 1,
    style: {
      color: '#1890ff',
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DatabaseOutlined */ .ose, {
    style: {
      marginRight: 16
    }
  }), "Template System Demonstration"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    style: {
      fontSize: 18,
      color: '#666',
      maxWidth: 800,
      margin: '0 auto'
    }
  }, "Comprehensive demonstration of the hierarchical template system featuring LayoutTemplate and AppTemplate models, their relationships, and integration with the App Builder Enhanced application."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    size: "large",
    style: {
      marginTop: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Total Templates",
    value: systemStats.totalTemplates || 0,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreOutlined */ .rS9, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Layout Templates",
    value: systemStats.layoutTemplates || 0,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LayoutOutlined */ .hy2, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "App Templates",
    value: systemStats.appTemplates || 0,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ProjectOutlined */ .KGW, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Component Templates",
    value: systemStats.componentTemplates || 0,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, null)
  }))), error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    message: "Error Loading Templates",
    description: error,
    type: "error",
    showIcon: true,
    closable: true,
    style: {
      marginBottom: 24
    },
    action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      size: "small",
      onClick: loadTemplates
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ReloadOutlined */ .KF4, null), " Retry")
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Navigation & Controls",
    style: {
      position: 'sticky',
      top: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    },
    size: "middle"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Demo Mode:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Group, {
    value: demoMode,
    onChange: function onChange(e) {
      return setDemoMode(e.target.value);
    },
    style: {
      marginTop: 8,
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Button, {
    value: "browse"
  }, "Browse"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Button, {
    value: "test"
  }, "Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Button, {
    value: "hierarchy"
  }, "Hierarchy"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Search Templates:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Search, {
    placeholder: "Search templates...",
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    style: {
      marginTop: 8
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Filter by Category:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6, {
    value: filterCategory,
    onChange: setFilterCategory,
    style: {
      width: '100%',
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
    value: "all"
  }, "All Categories"), Object.entries(templateCategories).map(function (_ref5) {
    var _ref6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_ref5, 2),
      type = _ref6[0],
      categories = _ref6[1];
    return categories.map(function (cat) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
        key: "".concat(type, "-").concat(cat),
        value: cat
      }, cat.charAt(0).toUpperCase() + cat.slice(1), " (", type, ")");
    });
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Filter by Type:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6, {
    value: filterType,
    onChange: setFilterType,
    style: {
      width: '100%',
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
    value: "all"
  }, "All Types"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
    value: "layout"
  }, "Layout Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
    value: "app"
  }, "App Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
    value: "component"
  }, "Component Templates"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Quick Actions:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%',
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ReloadOutlined */ .KF4, null),
    onClick: loadTemplates,
    loading: loading,
    block: true
  }, "Refresh Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .UploadOutlined */ .qvO, null),
    onClick: function onClick() {
      return setImportModalVisible(true);
    },
    block: true
  }, "Import Template"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DownloadOutlined */ .jsW, null),
    onClick: function onClick() {
      return setExportModalVisible(true);
    },
    block: true
  }, "Export Templates"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "View Options:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%',
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, null, "Preview Mode:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Group, {
    value: previewMode,
    onChange: function onChange(e) {
      return setPreviewMode(e.target.value);
    },
    size: "small",
    style: {
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Button, {
    value: "visual"
  }, "Visual"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Button, {
    value: "code"
  }, "Code"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Button, {
    value: "json"
  }, "JSON"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Switch */ .dO, {
    checked: showHierarchy,
    onChange: setShowHierarchy,
    checkedChildren: "Hierarchy",
    unCheckedChildren: "List"
  }))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 18
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    type: "card"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .InfoCircleOutlined */ .rUN, null), "System Overview"),
    key: "overview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Template System Architecture",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      textAlign: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LayoutOutlined */ .hy2, {
    style: {
      fontSize: 48,
      color: '#52c41a',
      marginBottom: 16
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 4
  }, "Layout Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, null, "Define reusable layout structures including grid, flex, sidebar, and navigation layouts. Store component references and configurations in JSONField."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
    color: "green"
  }, "Grid \u2022 Flex \u2022 Sidebar"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      textAlign: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ProjectOutlined */ .KGW, {
    style: {
      fontSize: 48,
      color: '#1890ff',
      marginBottom: 16
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 4
  }, "App Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, null, "Complete application templates with full component structures, categorized by business domain. Include required dependencies and preview images."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
    color: "blue"
  }, "Business \u2022 E-commerce \u2022 Portfolio"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      textAlign: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, {
    style: {
      fontSize: 48,
      color: '#722ed1',
      marginBottom: 16
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 4
  }, "Component Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, null, "Individual component templates with default properties and configurations. Building blocks for layouts and applications."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
    color: "purple"
  }, "Button \u2022 Form \u2022 Chart")))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Key Features",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8, {
    header: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      strong: true
    }, "Template Management"),
    bordered: true,
    dataSource: ['Hierarchical template organization', 'Public and private template visibility', 'Template categorization and tagging', 'Version control and history tracking', 'Template search and filtering', 'Bulk import/export functionality'],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CheckCircleOutlined */ .hWy, {
        style: {
          color: '#52c41a',
          marginRight: 8
        }
      }), item);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8, {
    header: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      strong: true
    }, "Integration Features"),
    bordered: true,
    dataSource: ['Component Builder integration', 'Layout Designer compatibility', 'Theme Manager support', 'Real-time collaboration', 'Code export functionality', 'AI-assisted template suggestions'],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CheckCircleOutlined */ .hWy, {
        style: {
          color: '#1890ff',
          marginRight: 8
        }
      }), item);
    }
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Template JSON Structure",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Collapse */ .SD, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Panel, {
    header: "Layout Template Structure",
    key: "layout"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "json",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, JSON.stringify({
    "id": 1,
    "name": "Grid Layout Template",
    "description": "Responsive grid layout with header and sidebar",
    "layout_type": "grid",
    "components": {
      "structure": "grid",
      "areas": ["header", "sidebar", "main", "footer"],
      "columns": "200px 1fr",
      "rows": "auto 1fr auto"
    },
    "default_props": {
      "gap": "16px",
      "padding": "24px",
      "responsive": true
    },
    "is_public": true,
    "created_at": "2024-01-01T00:00:00Z"
  }, null, 2)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Panel, {
    header: "App Template Structure",
    key: "app"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "json",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, JSON.stringify({
    "id": 1,
    "name": "Hello World Starter",
    "description": "Simple starter template with basic components",
    "app_category": "other",
    "components": {
      "pages": [{
        "name": "home",
        "title": "Home",
        "components": [{
          "id": "header-1",
          "type": "header",
          "props": {
            "title": "Hello World!",
            "backgroundColor": "#1890ff"
          }
        }]
      }]
    },
    "required_components": ["header", "container"],
    "preview_image": "/images/hello-world-preview.png",
    "is_public": true
  }, null, 2))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "System Statistics",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(StatsContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Total Templates",
    value: systemStats.totalTemplates || 0,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DatabaseOutlined */ .ose, null),
    suffix: "templates"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Public Templates",
    value: systemStats.publicTemplates || 0,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .GlobalOutlined */ .clv, null),
    suffix: "public"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Categories",
    value: Object.values(templateCategories).flat().length,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .TagsOutlined */ .owP, null),
    suffix: "categories"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "API Endpoints",
    value: 12,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ApiOutlined */ .bfv, null),
    suffix: "endpoints"
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Hello World Template Showcase",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    message: "Featured Template",
    description: "The Hello World starter template demonstrates the complete template system functionality with a simple, practical example.",
    type: "info",
    showIcon: true,
    style: {
      marginBottom: 16
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    title: "Template Features"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8, {
    size: "small",
    dataSource: ['Responsive header component', 'Container with sample content', 'Feature cards grid layout', 'Call-to-action section', 'Footer with links', 'Mobile-optimized design'],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .StarOutlined */ .L0Y, {
        style: {
          color: '#faad14',
          marginRight: 8
        }
      }), item);
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    title: "Component Structure"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "blue"
  }, "Header with title and subtitle"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "green"
  }, "Main container with content"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "orange"
  }, "Feature cards section"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "purple"
  }, "Call-to-action button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "gray"
  }, "Footer with navigation"))))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreOutlined */ .rS9, null), "Template Gallery"),
    key: "gallery"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Spin */ .tK, {
    spinning: loading
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, filteredTemplates.layout.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 4
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LayoutOutlined */ .hy2, {
    style: {
      marginRight: 8
    }
  }), "Layout Templates (", filteredTemplates.layout.length, ")"), filteredTemplates.layout.map(function (template) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      key: template.id
    }, renderTemplatePreview(template, 'layout'));
  })), filteredTemplates.app.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 4
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ProjectOutlined */ .KGW, {
    style: {
      marginRight: 8
    }
  }), "App Templates (", filteredTemplates.app.length, ")"), filteredTemplates.app.map(function (template) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      key: template.id
    }, renderTemplatePreview(template, 'app'));
  })), filteredTemplates.component.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 4
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, {
    style: {
      marginRight: 8
    }
  }), "Component Templates (", filteredTemplates.component.length, ")"), filteredTemplates.component.map(function (template) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      key: template.id
    }, renderTemplatePreview(template, 'component'));
  })), filteredTemplates.layout.length === 0 && filteredTemplates.app.length === 0 && filteredTemplates.component.length === 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Empty */ .Sv, {
    description: "No templates found matching your criteria",
    image: antd__WEBPACK_IMPORTED_MODULE_8__/* .Empty */ .Sv.PRESENTED_IMAGE_SIMPLE
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    type: "primary",
    onClick: function onClick() {
      setSearchTerm('');
      setFilterCategory('all');
      setFilterType('all');
    }
  }, "Clear Filters")))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ApartmentOutlined */ .T40, null), "Hierarchy View"),
    key: "hierarchy"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(HierarchyVisualization, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 3
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ApartmentOutlined */ .T40, {
    style: {
      marginRight: 8
    }
  }), "Template Hierarchy & Relationships"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, null, "This visualization shows the hierarchical structure and relationships between different template types in the App Builder system. Templates can inherit from each other and share components."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Template Hierarchy Tree",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DirectoryTree, {
    multiple: true,
    defaultExpandAll: true,
    treeData: [{
      title: 'Template System Root',
      key: 'root',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DatabaseOutlined */ .ose, null),
      children: [{
        title: "Layout Templates (".concat(filteredTemplates.layout.length, ")"),
        key: 'layouts',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LayoutOutlined */ .hy2, null),
        children: filteredTemplates.layout.map(function (template) {
          return {
            title: template.name,
            key: "layout-".concat(template.id),
            icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .FolderOutlined */ .zWy, null),
            isLeaf: true
          };
        })
      }, {
        title: "App Templates (".concat(filteredTemplates.app.length, ")"),
        key: 'apps',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ProjectOutlined */ .KGW, null),
        children: filteredTemplates.app.map(function (template) {
          return {
            title: template.name,
            key: "app-".concat(template.id),
            icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .FolderOutlined */ .zWy, null),
            isLeaf: true
          };
        })
      }, {
        title: "Component Templates (".concat(filteredTemplates.component.length, ")"),
        key: 'components',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, null),
        children: filteredTemplates.component.map(function (template) {
          return {
            title: template.name,
            key: "component-".concat(template.id),
            icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .FolderOutlined */ .zWy, null),
            isLeaf: true
          };
        })
      }]
    }],
    onSelect: function onSelect(keys, info) {
      if (info.node.isLeaf) {
        var _info$node$key$split = info.node.key.split('-'),
          _info$node$key$split2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_info$node$key$split, 2),
          type = _info$node$key$split2[0],
          id = _info$node$key$split2[1];
        var template = templates[type === 'layout' ? 'layout' : type === 'app' ? 'app' : 'component'].find(function (t) {
          return t.id === parseInt(id);
        });
        if (template) {
          setSelectedTemplate(_objectSpread(_objectSpread({}, template), {}, {
            type: type
          }));
        }
      }
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Template Relationships",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '20px 0'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginBottom: 20
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, {
    style: {
      fontSize: 24,
      color: '#722ed1'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      margin: '8px 0'
    }
  }, "Component Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: 12
    }
  }, "Building blocks")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      margin: '20px 0'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      borderLeft: '2px solid #d9d9d9',
      height: 30,
      margin: '0 auto',
      width: 0
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "\u2193 Used by")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginBottom: 20
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LayoutOutlined */ .hy2, {
    style: {
      fontSize: 24,
      color: '#52c41a'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      margin: '8px 0'
    }
  }, "Layout Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: 12
    }
  }, "Structure definitions")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      margin: '20px 0'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      borderLeft: '2px solid #d9d9d9',
      height: 30,
      margin: '0 auto',
      width: 0
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "\u2193 Composed into")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ProjectOutlined */ .KGW, {
    style: {
      fontSize: 24,
      color: '#1890ff'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      margin: '8px 0'
    }
  }, "App Templates"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: 12
    }
  }, "Complete applications")))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16],
    style: {
      marginTop: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Template Dependencies",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Table */ .XI, {
    size: "small",
    dataSource: [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(filteredTemplates.layout.map(function (t) {
      return _objectSpread(_objectSpread({}, t), {}, {
        type: 'Layout',
        key: "layout-".concat(t.id)
      });
    })), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(filteredTemplates.app.map(function (t) {
      return _objectSpread(_objectSpread({}, t), {}, {
        type: 'App',
        key: "app-".concat(t.id)
      });
    })), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(filteredTemplates.component.map(function (t) {
      return _objectSpread(_objectSpread({}, t), {}, {
        type: 'Component',
        key: "component-".concat(t.id)
      });
    }))),
    columns: [{
      title: 'Template Name',
      dataIndex: 'name',
      key: 'name',
      render: function render(text, record) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, record.type === 'Layout' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LayoutOutlined */ .hy2, {
          style: {
            color: '#52c41a'
          }
        }), record.type === 'App' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ProjectOutlined */ .KGW, {
          style: {
            color: '#1890ff'
          }
        }), record.type === 'Component' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, {
          style: {
            color: '#722ed1'
          }
        }), text);
      }
    }, {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: function render(type) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
          color: type === 'Layout' ? 'green' : type === 'App' ? 'blue' : 'purple'
        }, type);
      }
    }, {
      title: 'Category',
      key: 'category',
      render: function render(_, record) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, null, record.layout_type || record.app_category || record.component_type || 'Unknown');
      }
    }, {
      title: 'Components',
      key: 'componentCount',
      render: function render(_, record) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Badge */ .Ex, {
          count: record.components ? Array.isArray(record.components) ? record.components.length : (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(record.components) === 'object' ? Object.keys(record.components).length : 0 : 0
        });
      }
    }, {
      title: 'Visibility',
      dataIndex: 'is_public',
      key: 'visibility',
      render: function render(isPublic) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
          color: isPublic ? 'green' : 'orange',
          icon: isPublic ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .UnlockOutlined */ .Rrh, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LockOutlined */ .sXv, null)
        }, isPublic ? 'Public' : 'Private');
      }
    }, {
      title: 'Actions',
      key: 'actions',
      render: function render(_, record) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
          title: "View Details"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
          size: "small",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .EyeOutlined */ .Om2, null),
          onClick: function onClick() {
            return setSelectedTemplate(record);
          }
        })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
          title: "Test Load"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
          size: "small",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .PlayCircleOutlined */ .VgC, null),
          onClick: function onClick() {
            return testTemplateFunction('load', record.id);
          }
        })));
      }
    }],
    pagination: {
      pageSize: 10
    }
  })))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ExperimentOutlined */ .GK9, null), "Interactive Testing"),
    key: "testing"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(InteractiveDemo, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 3
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ExperimentOutlined */ .GK9, {
    style: {
      marginRight: 8
    }
  }), "Interactive Template Testing"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, null, "Test template system functionality including loading, searching, importing, and exporting templates. Monitor API responses and system performance in real-time."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Function Testing",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    },
    size: "middle"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Template Loading Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .PlayCircleOutlined */ .VgC, null),
    onClick: function onClick() {
      var _templates$layout$, _templates$app$, _templates$component$;
      return testTemplateFunction('load', ((_templates$layout$ = templates.layout[0]) === null || _templates$layout$ === void 0 ? void 0 : _templates$layout$.id) || ((_templates$app$ = templates.app[0]) === null || _templates$app$ === void 0 ? void 0 : _templates$app$.id) || ((_templates$component$ = templates.component[0]) === null || _templates$component$ === void 0 ? void 0 : _templates$component$.id));
    },
    loading: (_testResults$load = testResults.load) === null || _testResults$load === void 0 ? void 0 : _testResults$load.loading,
    block: true
  }, "Test Template Loading"), testResults.load && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    style: {
      marginTop: 8
    },
    message: testResults.load.success ? 'Success' : 'Error',
    description: testResults.load.success ? "Template loaded successfully at ".concat(testResults.load.timestamp) : testResults.load.error,
    type: testResults.load.success ? 'success' : 'error',
    showIcon: true,
    closable: true
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Search Functionality Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .SearchOutlined */ .VrN, null),
    onClick: function onClick() {
      return testTemplateFunction('search');
    },
    loading: (_testResults$search = testResults.search) === null || _testResults$search === void 0 ? void 0 : _testResults$search.loading,
    block: true
  }, "Test Template Search"), testResults.search && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    style: {
      marginTop: 8
    },
    message: testResults.search.success ? 'Success' : 'Error',
    description: testResults.search.success ? "Found ".concat(Object.values(testResults.search.data || {}).flat().length, " templates") : testResults.search.error,
    type: testResults.search.success ? 'success' : 'error',
    showIcon: true,
    closable: true
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Featured Templates Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .StarOutlined */ .L0Y, null),
    onClick: function onClick() {
      return testTemplateFunction('featured');
    },
    loading: (_testResults$featured = testResults.featured) === null || _testResults$featured === void 0 ? void 0 : _testResults$featured.loading,
    block: true
  }, "Test Featured Templates"), testResults.featured && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    style: {
      marginTop: 8
    },
    message: testResults.featured.success ? 'Success' : 'Error',
    description: testResults.featured.success ? "Loaded featured templates successfully" : testResults.featured.error,
    type: testResults.featured.success ? 'success' : 'error',
    showIcon: true,
    closable: true
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Categories API Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .TagsOutlined */ .owP, null),
    onClick: function onClick() {
      return testTemplateFunction('categories');
    },
    loading: (_testResults$categori = testResults.categories) === null || _testResults$categori === void 0 ? void 0 : _testResults$categori.loading,
    block: true
  }, "Test Categories API"), testResults.categories && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    style: {
      marginTop: 8
    },
    message: testResults.categories.success ? 'Success' : 'Error',
    description: testResults.categories.success ? "Categories loaded successfully" : testResults.categories.error,
    type: testResults.categories.success ? 'success' : 'error',
    showIcon: true,
    closable: true
  })))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Test Results Monitor",
    size: "small"
  }, Object.keys(testResults).length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Empty */ .Sv, {
    description: "No test results yet",
    image: antd__WEBPACK_IMPORTED_MODULE_8__/* .Empty */ .Sv.PRESENTED_IMAGE_SIMPLE
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "Run tests to see results here")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf, null, Object.entries(testResults).reverse().map(function (_ref7) {
    var _ref8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_ref7, 2),
      testName = _ref8[0],
      result = _ref8[1];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
      key: testName,
      color: result.loading ? 'blue' : result.success ? 'green' : 'red',
      dot: result.loading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .SyncOutlined */ .OmY, {
        spin: true
      }) : result.success ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CheckCircleOutlined */ .hWy, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ExclamationCircleOutlined */ .G2i, null)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      strong: true
    }, testName.toUpperCase(), " Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        fontSize: 12,
        color: '#666'
      }
    }, result.timestamp && new Date(result.timestamp).toLocaleTimeString()), result.loading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "secondary"
    }, "Running..."), result.success && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "success"
    }, "Completed successfully"), result.error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "danger"
    }, "Failed: ", result.error)));
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16],
    style: {
      marginTop: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Import/Export Testing",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    title: "Template Import Test"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Upload */ ._O.Dragger, {
    name: "template",
    accept: ".json",
    beforeUpload: function beforeUpload(file) {
      var reader = new FileReader();
      reader.onload = function (e) {
        try {
          var templateData = JSON.parse(e.target.result);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Template \"".concat(templateData.name || 'Unknown', "\" parsed successfully"));
          console.log('Parsed template:', templateData);
        } catch (error) {
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error('Invalid JSON format');
        }
      };
      reader.readAsText(file);
      return false; // Prevent upload
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", {
    className: "ant-upload-drag-icon"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .UploadOutlined */ .qvO, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", {
    className: "ant-upload-text"
  }, "Click or drag template file to test import"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", {
    className: "ant-upload-hint"
  }, "Supports JSON template files. File will be parsed but not uploaded.")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    title: "Template Export Test"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, null, "Export sample template data:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DownloadOutlined */ .jsW, null),
    onClick: function onClick() {
      var sampleTemplate = {
        name: "Sample Template",
        description: "A sample template for testing",
        type: "layout",
        components: {
          structure: "grid",
          areas: ["header", "main", "footer"]
        },
        created_at: new Date().toISOString()
      };
      var dataStr = JSON.stringify(sampleTemplate, null, 2);
      var dataBlob = new Blob([dataStr], {
        type: 'application/json'
      });
      var url = URL.createObjectURL(dataBlob);
      var link = document.createElement('a');
      link.href = url;
      link.download = 'sample_template.json';
      link.click();
      URL.revokeObjectURL(url);
      antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Sample template exported');
    },
    block: true
  }, "Export Sample Template"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CloudDownloadOutlined */ .Gkj, null),
    onClick: function onClick() {
      if (selectedTemplate) {
        var dataStr = JSON.stringify(selectedTemplate, null, 2);
        var dataBlob = new Blob([dataStr], {
          type: 'application/json'
        });
        var url = URL.createObjectURL(dataBlob);
        var link = document.createElement('a');
        link.href = url;
        link.download = "".concat(selectedTemplate.name.replace(/\s+/g, '_'), "_template.json");
        link.click();
        URL.revokeObjectURL(url);
        antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Selected template exported');
      } else {
        antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.warning('Please select a template first');
      }
    },
    disabled: !selectedTemplate,
    block: true
  }, "Export Selected Template")))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16],
    style: {
      marginTop: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Performance Monitoring",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "API Response Time",
    value: Math.random() * 200 + 50,
    precision: 0,
    suffix: "ms",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ThunderboltOutlined */ .CwG, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Templates Loaded",
    value: systemStats.totalTemplates || 0,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DatabaseOutlined */ .ose, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Success Rate",
    value: 95.8,
    precision: 1,
    suffix: "%",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CheckCircleOutlined */ .hWy, null)
  })))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .BuildOutlined */ .fXc, null), "Integration Demo"),
    key: "integration"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "App Builder Integration Demo",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    message: "Integration Overview",
    description: "This section demonstrates how templates integrate with the Component Builder, Layout Designer, Theme Manager, and other App Builder Enhanced features.",
    type: "info",
    showIcon: true,
    style: {
      marginBottom: 24
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      textAlign: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, {
    style: {
      fontSize: 32,
      color: '#722ed1',
      marginBottom: 12
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 5
  }, "Component Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    style: {
      fontSize: 12
    }
  }, "Templates provide pre-configured components that can be customized in the Component Builder."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    size: "small",
    type: "primary",
    ghost: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .BuildOutlined */ .fXc, null), " Demo Integration"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      textAlign: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LayoutOutlined */ .hy2, {
    style: {
      fontSize: 32,
      color: '#52c41a',
      marginBottom: 12
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 5
  }, "Layout Designer"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    style: {
      fontSize: 12
    }
  }, "Layout templates define responsive grid structures that can be modified in the Layout Designer."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    size: "small",
    type: "primary",
    ghost: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DashboardOutlined */ .zpd, null), " Demo Layout"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      textAlign: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .BulbOutlined */ .o3f, {
    style: {
      fontSize: 32,
      color: '#faad14',
      marginBottom: 12
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 5
  }, "Theme Manager"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    style: {
      fontSize: 12
    }
  }, "Templates support theme customization with consistent styling across all components."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    size: "small",
    type: "primary",
    ghost: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .SettingOutlined */ .JO7, null), " Demo Themes"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      textAlign: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ShareAltOutlined */ .f5H, {
    style: {
      fontSize: 32,
      color: '#1890ff',
      marginBottom: 12
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 5
  }, "Collaboration"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    style: {
      fontSize: 12
    }
  }, "Real-time collaborative editing of templates with live cursor tracking and comments."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    size: "small",
    type: "primary",
    ghost: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .UserOutlined */ .qmv, null), " Demo Collab")))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Template Application Workflow",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf, {
    mode: "alternate"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "blue",
    dot: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .SearchOutlined */ .VrN, null)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "1. Template Selection"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "Browse template gallery and select appropriate template")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "green",
    dot: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DownloadOutlined */ .jsW, null)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "2. Template Loading"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "Load template structure and components into the builder")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "orange",
    dot: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .EditOutlined */ .xjh, null)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "3. Customization"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "Modify components, layouts, and themes using builder tools")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "purple",
    dot: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .EyeOutlined */ .Om2, null)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "4. Preview & Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "Real-time preview with responsive design testing")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Timeline */ .Kf.Item, {
    color: "red",
    dot: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .RocketOutlined */ .PKb, null)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "5. Export & Deploy"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 4
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "Generate production-ready code and deploy application"))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Live Template Preview",
    size: "small"
  }, selectedTemplate ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
    color: "blue"
  }, selectedTemplate.type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, selectedTemplate.name))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(PreviewContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .AppstoreAddOutlined */ .uFx, {
    style: {
      fontSize: 48,
      color: '#d9d9d9',
      marginBottom: 16
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 4,
    style: {
      color: '#666'
    }
  }, "Template Preview"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    type: "secondary"
  }, selectedTemplate.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .BuildOutlined */ .fXc, null),
    size: "small"
  }, "Open in Builder"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .EyeOutlined */ .Om2, null),
    size: "small"
  }, "Full Preview")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Template Properties:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginTop: 8,
      fontSize: 12
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Category: ", selectedTemplate.layout_type || selectedTemplate.app_category || selectedTemplate.component_type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Public: ", selectedTemplate.is_public ? 'Yes' : 'No'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Created: ", new Date(selectedTemplate.created_at).toLocaleDateString())))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Empty */ .Sv, {
    description: "Select a template to see preview",
    image: antd__WEBPACK_IMPORTED_MODULE_8__/* .Empty */ .Sv.PRESENTED_IMAGE_SIMPLE
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    type: "primary",
    onClick: function onClick() {
      return setActiveTab('gallery');
    }
  }, "Browse Templates")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Integration Features",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8, {
    dataSource: [{
      title: 'Real-time Collaboration',
      description: 'Multiple users can edit templates simultaneously with live updates',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ShareAltOutlined */ .f5H, {
        style: {
          color: '#1890ff'
        }
      })
    }, {
      title: 'AI-Assisted Design',
      description: 'Smart suggestions for template improvements and component recommendations',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .BulbOutlined */ .o3f, {
        style: {
          color: '#faad14'
        }
      })
    }, {
      title: 'Code Export',
      description: 'Generate clean, production-ready code in multiple frameworks',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CodeOutlined */ .C$o, {
        style: {
          color: '#52c41a'
        }
      })
    }, {
      title: 'Theme Integration',
      description: 'Seamless theme application across all template components',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .SettingOutlined */ .JO7, {
        style: {
          color: '#722ed1'
        }
      })
    }, {
      title: 'Responsive Design',
      description: 'Automatic responsive behavior with mobile-first approach',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DashboardOutlined */ .zpd, {
        style: {
          color: '#fa8c16'
        }
      })
    }, {
      title: 'Version Control',
      description: 'Track template changes with full version history',
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .BranchesOutlined */ .lhN, {
        style: {
          color: '#13c2c2'
        }
      })
    }],
    renderItem: function renderItem(item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8.Item.Meta, {
        avatar: item.icon,
        title: item.title,
        description: item.description
      }));
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Template System Benefits",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ThunderboltOutlined */ .CwG, {
    style: {
      fontSize: 32,
      color: '#faad14',
      marginBottom: 12
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 5
  }, "Rapid Development"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    style: {
      fontSize: 12
    }
  }, "Start with proven templates to accelerate development and reduce time-to-market.")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CheckCircleOutlined */ .hWy, {
    style: {
      fontSize: 32,
      color: '#52c41a',
      marginBottom: 12
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 5
  }, "Consistency"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    style: {
      fontSize: 12
    }
  }, "Maintain design consistency across projects with standardized templates.")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small",
    style: {
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .RocketOutlined */ .PKb, {
    style: {
      fontSize: 32,
      color: '#1890ff',
      marginBottom: 12
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 5
  }, "Scalability"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, {
    style: {
      fontSize: 12
    }
  }, "Build scalable applications with enterprise-ready template architecture."))))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CodeOutlined */ .C$o, null), "API Documentation"),
    key: "api"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Template System API Documentation",
    style: {
      marginBottom: 24
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    message: "API Overview",
    description: "Complete documentation of the template system REST API endpoints, GraphQL schema, and integration examples.",
    type: "info",
    showIcon: true,
    style: {
      marginBottom: 24
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Collapse */ .SD, {
    defaultActiveKey: ['endpoints']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Panel, {
    header: "REST API Endpoints",
    key: "endpoints"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Table */ .XI, {
    size: "small",
    dataSource: [{
      key: '1',
      method: 'GET',
      endpoint: '/api/layout-templates/',
      description: 'List all layout templates',
      auth: 'Optional'
    }, {
      key: '2',
      method: 'POST',
      endpoint: '/api/layout-templates/',
      description: 'Create new layout template',
      auth: 'Required'
    }, {
      key: '3',
      method: 'GET',
      endpoint: '/api/layout-templates/{id}/',
      description: 'Get specific layout template',
      auth: 'Optional'
    }, {
      key: '4',
      method: 'PUT',
      endpoint: '/api/layout-templates/{id}/',
      description: 'Update layout template',
      auth: 'Required'
    }, {
      key: '5',
      method: 'DELETE',
      endpoint: '/api/layout-templates/{id}/',
      description: 'Delete layout template',
      auth: 'Required'
    }, {
      key: '6',
      method: 'GET',
      endpoint: '/api/app-templates/',
      description: 'List all app templates',
      auth: 'Optional'
    }, {
      key: '7',
      method: 'POST',
      endpoint: '/api/app-templates/',
      description: 'Create new app template',
      auth: 'Required'
    }, {
      key: '8',
      method: 'GET',
      endpoint: '/api/component-templates/',
      description: 'List all component templates',
      auth: 'Optional'
    }, {
      key: '9',
      method: 'GET',
      endpoint: '/api/template-search/',
      description: 'Search templates across all types',
      auth: 'Optional'
    }, {
      key: '10',
      method: 'GET',
      endpoint: '/api/featured-templates/',
      description: 'Get featured templates',
      auth: 'Optional'
    }, {
      key: '11',
      method: 'GET',
      endpoint: '/api/template-categories/',
      description: 'Get template categories',
      auth: 'Optional'
    }, {
      key: '12',
      method: 'POST',
      endpoint: '/api/layout-templates/import_template/',
      description: 'Import layout template',
      auth: 'Required'
    }],
    columns: [{
      title: 'Method',
      dataIndex: 'method',
      key: 'method',
      render: function render(method) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
          color: method === 'GET' ? 'blue' : method === 'POST' ? 'green' : method === 'PUT' ? 'orange' : method === 'DELETE' ? 'red' : 'default'
        }, method);
      }
    }, {
      title: 'Endpoint',
      dataIndex: 'endpoint',
      key: 'endpoint',
      render: function render(endpoint) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
          code: true
        }, endpoint);
      }
    }, {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    }, {
      title: 'Authentication',
      dataIndex: 'auth',
      key: 'auth',
      render: function render(auth) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
          color: auth === 'Required' ? 'red' : 'green'
        }, auth);
      }
    }],
    pagination: false
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Panel, {
    header: "Request/Response Examples",
    key: "examples"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU, {
    type: "card"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "Create Layout Template",
    key: "create-layout"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Request:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "json",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, JSON.stringify({
    "name": "Grid Layout",
    "description": "Responsive grid layout",
    "layout_type": "grid",
    "components": {
      "structure": "grid",
      "areas": ["header", "sidebar", "main", "footer"]
    },
    "default_props": {
      "gap": "16px",
      "responsive": true
    },
    "is_public": true
  }, null, 2)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Response:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "json",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, JSON.stringify({
    "id": 1,
    "name": "Grid Layout",
    "description": "Responsive grid layout",
    "layout_type": "grid",
    "components": {
      "structure": "grid",
      "areas": ["header", "sidebar", "main", "footer"]
    },
    "default_props": {
      "gap": "16px",
      "responsive": true
    },
    "is_public": true,
    "created_at": "2024-01-01T00:00:00Z",
    "user": 1
  }, null, 2)))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "Search Templates",
    key: "search"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Request:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    code: true,
    display: "block",
    style: {
      marginTop: 8
    }
  }, "GET /api/template-search/?q=grid&type=layout&category=dashboard")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    strong: true
  }, "Response:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "json",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, JSON.stringify({
    "layouts": [{
      "id": 1,
      "name": "Dashboard Grid",
      "layout_type": "dashboard"
    }],
    "apps": [],
    "components": []
  }, null, 2)))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Panel, {
    header: "GraphQL Schema",
    key: "graphql"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "graphql",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, "type LayoutTemplate {\n  id: ID!\n  name: String!\n  description: String\n  layoutType: String!\n  componentsJson: JSONString\n  defaultPropsJson: JSONString\n  user: User!\n  isPublic: Boolean!\n  createdAt: DateTime!\n}\n\ntype AppTemplate {\n  id: ID!\n  name: String!\n  description: String\n  appCategory: String!\n  componentsJson: JSONString\n  defaultPropsJson: JSONString\n  requiredComponentsList: JSONString\n  previewImage: String\n  user: User!\n  isPublic: Boolean!\n  createdAt: DateTime!\n}\n\ntype Query {\n  layoutTemplates(\n    name_Icontains: String\n    layoutType: String\n    isPublic: Boolean\n  ): [LayoutTemplate]\n\n  appTemplates(\n    name_Icontains: String\n    appCategory: String\n    isPublic: Boolean\n  ): [AppTemplate]\n}\n\ntype Mutation {\n  createLayoutTemplate(input: LayoutTemplateInput!): LayoutTemplate\n  updateLayoutTemplate(id: ID!, input: LayoutTemplateInput!): LayoutTemplate\n  deleteLayoutTemplate(id: ID!): Boolean\n}"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Panel, {
    header: "Integration Examples",
    key: "integration"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU, {
    type: "card"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "React Hook",
    key: "react-hook"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "javascript",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, "import { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nexport const useTemplates = () => {\n  const [templates, setTemplates] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const loadTemplates = async () => {\n    setLoading(true);\n    try {\n      const [layoutRes, appRes] = await Promise.all([\n        axios.get('/api/layout-templates/'),\n        axios.get('/api/app-templates/')\n      ]);\n\n      setTemplates([\n        ...layoutRes.data.results,\n        ...appRes.data.results\n      ]);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createTemplate = async (templateData) => {\n    const endpoint = templateData.type === 'layout'\n      ? '/api/layout-templates/'\n      : '/api/app-templates/';\n\n    const response = await axios.post(endpoint, templateData);\n    return response.data;\n  };\n\n  useEffect(() => {\n    loadTemplates();\n  }, []);\n\n  return {\n    templates,\n    loading,\n    error,\n    loadTemplates,\n    createTemplate\n  };\n};"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "Python Client",
    key: "python"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "python",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, "import requests\nfrom typing import List, Dict, Optional\n\nclass TemplateClient:\n    def __init__(self, base_url: str, auth_token: Optional[str] = None):\n        self.base_url = base_url\n        self.headers = {}\n        if auth_token:\n            self.headers['Authorization'] = f'Bearer {auth_token}'\n\n    def get_layout_templates(self, **filters) -> List[Dict]:\n        \"\"\"Get layout templates with optional filters\"\"\"\n        response = requests.get(\n            f\"{self.base_url}/api/layout-templates/\",\n            params=filters,\n            headers=self.headers\n        )\n        response.raise_for_status()\n        return response.json()['results']\n\n    def create_layout_template(self, template_data: Dict) -> Dict:\n        \"\"\"Create a new layout template\"\"\"\n        response = requests.post(\n            f\"{self.base_url}/api/layout-templates/\",\n            json=template_data,\n            headers=self.headers\n        )\n        response.raise_for_status()\n        return response.json()\n\n    def search_templates(self, query: str, template_type: str = '') -> Dict:\n        \"\"\"Search templates across all types\"\"\"\n        params = {'q': query}\n        if template_type:\n            params['type'] = template_type\n\n        response = requests.get(\n            f\"{self.base_url}/api/template-search/\",\n            params=params,\n            headers=self.headers\n        )\n        response.raise_for_status()\n        return response.json()\n\n# Usage example\nclient = TemplateClient('http://localhost:8000', 'your-auth-token')\ntemplates = client.get_layout_templates(is_public=True)\nsearch_results = client.search_templates('dashboard', 'layout')"))))))))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: selectedTemplate ? "Template: ".concat(selectedTemplate.name) : 'Template Details',
    open: !!selectedTemplate,
    onCancel: function onCancel() {
      return setSelectedTemplate(null);
    },
    width: 800,
    footer: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      key: "close",
      onClick: function onClick() {
        return setSelectedTemplate(null);
      }
    }, "Close"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      key: "export",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DownloadOutlined */ .jsW, null),
      onClick: function onClick() {
        if (selectedTemplate) {
          var dataStr = JSON.stringify(selectedTemplate, null, 2);
          var dataBlob = new Blob([dataStr], {
            type: 'application/json'
          });
          var url = URL.createObjectURL(dataBlob);
          var link = document.createElement('a');
          link.href = url;
          link.download = "".concat(selectedTemplate.name.replace(/\s+/g, '_'), "_template.json");
          link.click();
          URL.revokeObjectURL(url);
        }
      }
    }, "Export"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      key: "test",
      type: "primary",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .PlayCircleOutlined */ .VgC, null),
      onClick: function onClick() {
        if (selectedTemplate) {
          testTemplateFunction('load', selectedTemplate.id);
        }
      }
    }, "Test Load")]
  }, selectedTemplate && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH, {
    bordered: true,
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
    label: "Name",
    span: 2
  }, selectedTemplate.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
    label: "Type"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
    color: selectedTemplate.type === 'layout' ? 'green' : selectedTemplate.type === 'app' ? 'blue' : 'purple'
  }, selectedTemplate.type)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
    label: "Description",
    span: 3
  }, selectedTemplate.description || 'No description available'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
    label: "Category"
  }, selectedTemplate.layout_type || selectedTemplate.app_category || selectedTemplate.component_type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
    label: "Visibility"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
    color: selectedTemplate.is_public ? 'green' : 'orange'
  }, selectedTemplate.is_public ? 'Public' : 'Private')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Descriptions */ .KH.Item, {
    label: "Created"
  }, new Date(selectedTemplate.created_at).toLocaleDateString())), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU, {
    defaultActiveKey: "components"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "Components",
    key: "components"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "json",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, JSON.stringify(selectedTemplate.components || {}, null, 2)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "Default Props",
    key: "props"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "json",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, JSON.stringify(selectedTemplate.default_props || {}, null, 2)))), selectedTemplate.required_components && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "Required Components",
    key: "required"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(JsonViewer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_12__/* .Prism */ .My, {
    language: "json",
    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_13__/* .vscDarkPlus */ .xJ,
    customStyle: {
      background: 'transparent',
      padding: 0
    }
  }, JSON.stringify(selectedTemplate.required_components || [], null, 2))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Import Template",
    open: importModalVisible,
    onCancel: function onCancel() {
      return setImportModalVisible(false);
    },
    footer: null
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Upload */ ._O.Dragger, {
    name: "template",
    accept: ".json",
    beforeUpload: function beforeUpload(file) {
      var reader = new FileReader();
      reader.onload = function (e) {
        try {
          var templateData = JSON.parse(e.target.result);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Template \"".concat(templateData.name || 'Unknown', "\" imported successfully"));
          setImportModalVisible(false);
          // Here you would typically send the data to your API
          console.log('Imported template:', templateData);
        } catch (error) {
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error('Invalid JSON format');
        }
      };
      reader.readAsText(file);
      return false;
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", {
    className: "ant-upload-drag-icon"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .UploadOutlined */ .qvO, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", {
    className: "ant-upload-text"
  }, "Click or drag template file to import"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", {
    className: "ant-upload-hint"
  }, "Supports JSON template files exported from the system"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Export Templates",
    open: exportModalVisible,
    onCancel: function onCancel() {
      return setExportModalVisible(false);
    },
    footer: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      key: "cancel",
      onClick: function onClick() {
        return setExportModalVisible(false);
      }
    }, "Cancel"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      key: "export",
      type: "primary",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DownloadOutlined */ .jsW, null),
      onClick: function onClick() {
        var allTemplates = {
          layout_templates: templates.layout,
          app_templates: templates.app,
          component_templates: templates.component,
          exported_at: new Date().toISOString(),
          version: '1.0'
        };
        var dataStr = JSON.stringify(allTemplates, null, 2);
        var dataBlob = new Blob([dataStr], {
          type: 'application/json'
        });
        var url = URL.createObjectURL(dataBlob);
        var link = document.createElement('a');
        link.href = url;
        link.download = 'all_templates_export.json';
        link.click();
        URL.revokeObjectURL(url);
        antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('All templates exported successfully');
        setExportModalVisible(false);
      }
    }, "Export All Templates")]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Paragraph, null, "Export all templates in a single JSON file that can be imported into another system."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    message: "Export Information",
    description: "This will export ".concat(systemStats.totalTemplates || 0, " templates including layout, app, and component templates."),
    type: "info",
    showIcon: true
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemplateSystemDemo);

/***/ })

}]);