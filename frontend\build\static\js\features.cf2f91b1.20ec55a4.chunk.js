"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9774],{

/***/ 1163:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ tutorial_TutorialOverlay)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-dom/index.js
var react_dom = __webpack_require__(40961);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 10 modules
var styled_components_browser_esm = __webpack_require__(70572);
// EXTERNAL MODULE: ./src/components/tutorial/TutorialManager.js
var TutorialManager = __webpack_require__(82479);
// EXTERNAL MODULE: ./src/components/tutorial/types.js
var types = __webpack_require__(98835);
;// ./src/components/tutorial/TutorialVisualEnhancements.js


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12, _templateObject13, _templateObject14, _templateObject15, _templateObject16, _templateObject17, _templateObject18, _templateObject19, _templateObject20;
/**
 * Tutorial Visual Enhancements
 * 
 * Enhanced visual feedback, animations, and highlighting for the tutorial system.
 * Provides better user guidance through improved visual cues and interactions.
 */




// Enhanced Animations
var pulseGlow = (0,styled_components_browser_esm/* keyframes */.i7)(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  0% {\n    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);\n    transform: scale(1);\n  }\n  50% {\n    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0.3);\n    transform: scale(1.02);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);\n    transform: scale(1);\n  }\n"])));
var shimmer = (0,styled_components_browser_esm/* keyframes */.i7)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n"])));
var bounceIn = (0,styled_components_browser_esm/* keyframes */.i7)(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  0% {\n    opacity: 0;\n    transform: scale(0.3) translateY(-50px);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.05) translateY(-10px);\n  }\n  70% {\n    transform: scale(0.9) translateY(0);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n"])));
var slideInFromRight = (0,styled_components_browser_esm/* keyframes */.i7)(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  0% {\n    opacity: 0;\n    transform: translateX(100px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n"])));
var fadeInUp = (0,styled_components_browser_esm/* keyframes */.i7)(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  0% {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n"])));

// Enhanced Highlight Overlay
var EnhancedHighlightOverlay = styled_components_browser_esm/* default */.Ay.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  pointer-events: none;\n  border-radius: ", ";\n  z-index: 9999;\n  \n  ", "\n  \n  ", "\n  \n  ", "\n  \n  ", "\n"])), function (props) {
  return props.borderRadius || '8px';
}, function (props) {
  return props.highlightType === 'pulse' && (0,styled_components_browser_esm/* css */.AH)(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n    animation: ", " 2s infinite;\n    border: 3px solid #1890ff;\n    background: rgba(24, 144, 255, 0.1);\n  "])), pulseGlow);
}, function (props) {
  return props.highlightType === 'shimmer' && (0,styled_components_browser_esm/* css */.AH)(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n    border: 2px solid #1890ff;\n    background: linear-gradient(\n      90deg,\n      rgba(24, 144, 255, 0.1) 0%,\n      rgba(24, 144, 255, 0.3) 50%,\n      rgba(24, 144, 255, 0.1) 100%\n    );\n    background-size: 200px 100%;\n    animation: ", " 2s infinite;\n  "])), shimmer);
}, function (props) {
  return props.highlightType === 'glow' && (0,styled_components_browser_esm/* css */.AH)(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n    border: 2px solid #1890ff;\n    background: rgba(24, 144, 255, 0.05);\n    box-shadow: \n      0 0 20px rgba(24, 144, 255, 0.3),\n      inset 0 0 20px rgba(24, 144, 255, 0.1);\n  "])));
}, function (props) {
  return props.highlightType === 'dashed' && (0,styled_components_browser_esm/* css */.AH)(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n    border: 3px dashed #1890ff;\n    background: rgba(24, 144, 255, 0.05);\n    animation: ", " 3s infinite;\n  "])), pulseGlow);
});

// Enhanced Tooltip with Animations
var EnhancedTooltip = styled_components_browser_esm/* default */.Ay.div(_templateObject1 || (_templateObject1 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  background: white;\n  border-radius: 12px;\n  box-shadow: \n    0 10px 40px rgba(0, 0, 0, 0.15),\n    0 4px 12px rgba(0, 0, 0, 0.1);\n  padding: 24px;\n  max-width: 350px;\n  z-index: 10000;\n  \n  animation: ", ";\n  \n  &::before {\n    content: '';\n    position: absolute;\n    width: 0;\n    height: 0;\n    border-style: solid;\n    \n    ", "\n    \n    ", "\n    \n    ", "\n    \n    ", "\n  }\n"])), function (props) {
  switch (props.animationType) {
    case 'bounceIn':
      return (0,styled_components_browser_esm/* css */.AH)(_templateObject10 || (_templateObject10 = (0,taggedTemplateLiteral/* default */.A)(["", " 0.6s ease-out"])), bounceIn);
    case 'slideIn':
      return (0,styled_components_browser_esm/* css */.AH)(_templateObject11 || (_templateObject11 = (0,taggedTemplateLiteral/* default */.A)(["", " 0.4s ease-out"])), slideInFromRight);
    case 'fadeIn':
    default:
      return (0,styled_components_browser_esm/* css */.AH)(_templateObject12 || (_templateObject12 = (0,taggedTemplateLiteral/* default */.A)(["", " 0.3s ease-out"])), fadeInUp);
  }
}, function (props) {
  return props.position === 'top' && (0,styled_components_browser_esm/* css */.AH)(_templateObject13 || (_templateObject13 = (0,taggedTemplateLiteral/* default */.A)(["\n      bottom: -10px;\n      left: 50%;\n      transform: translateX(-50%);\n      border-width: 10px 10px 0 10px;\n      border-color: white transparent transparent transparent;\n    "])));
}, function (props) {
  return props.position === 'bottom' && (0,styled_components_browser_esm/* css */.AH)(_templateObject14 || (_templateObject14 = (0,taggedTemplateLiteral/* default */.A)(["\n      top: -10px;\n      left: 50%;\n      transform: translateX(-50%);\n      border-width: 0 10px 10px 10px;\n      border-color: transparent transparent white transparent;\n    "])));
}, function (props) {
  return props.position === 'left' && (0,styled_components_browser_esm/* css */.AH)(_templateObject15 || (_templateObject15 = (0,taggedTemplateLiteral/* default */.A)(["\n      right: -10px;\n      top: 50%;\n      transform: translateY(-50%);\n      border-width: 10px 0 10px 10px;\n      border-color: transparent transparent transparent white;\n    "])));
}, function (props) {
  return props.position === 'right' && (0,styled_components_browser_esm/* css */.AH)(_templateObject16 || (_templateObject16 = (0,taggedTemplateLiteral/* default */.A)(["\n      left: -10px;\n      top: 50%;\n      transform: translateY(-50%);\n      border-width: 10px 10px 10px 0;\n      border-color: transparent white transparent transparent;\n    "])));
});

// Progress Indicator with Enhanced Visuals
var ProgressRing = styled_components_browser_esm/* default */.Ay.div(_templateObject17 || (_templateObject17 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: relative;\n  width: 60px;\n  height: 60px;\n  \n  svg {\n    transform: rotate(-90deg);\n    width: 100%;\n    height: 100%;\n  }\n  \n  .progress-ring-background {\n    fill: none;\n    stroke: #f0f0f0;\n    stroke-width: 4;\n  }\n  \n  .progress-ring-progress {\n    fill: none;\n    stroke: #1890ff;\n    stroke-width: 4;\n    stroke-linecap: round;\n    transition: stroke-dashoffset 0.5s ease-in-out;\n  }\n  \n  .progress-text {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    font-size: 12px;\n    font-weight: bold;\n    color: #1890ff;\n  }\n"])));

// Interactive Element Highlighter
var InteractiveHighlighter = styled_components_browser_esm/* default */.Ay.div(_templateObject18 || (_templateObject18 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  pointer-events: none;\n  z-index: 9998;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -20px;\n    left: -20px;\n    right: -20px;\n    bottom: -20px;\n    border: 2px solid #52c41a;\n    border-radius: 12px;\n    background: rgba(82, 196, 26, 0.1);\n    animation: ", " 1.5s infinite;\n  }\n  \n  &::after {\n    content: '\uD83D\uDC46 Click here';\n    position: absolute;\n    top: -40px;\n    left: 50%;\n    transform: translateX(-50%);\n    background: #52c41a;\n    color: white;\n    padding: 4px 8px;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: bold;\n    white-space: nowrap;\n    animation: ", " 0.6s ease-out;\n  }\n"])), pulseGlow, bounceIn);

// Spotlight Effect
var SpotlightOverlay = styled_components_browser_esm/* default */.Ay.div(_templateObject19 || (_templateObject19 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.7);\n  z-index: 9997;\n  pointer-events: none;\n  \n  ", "\n"])), function (props) {
  return props.spotlightArea && (0,styled_components_browser_esm/* css */.AH)(_templateObject20 || (_templateObject20 = (0,taggedTemplateLiteral/* default */.A)(["\n    mask: radial-gradient(\n      circle at ", "px ", "px,\n      transparent ", "px,\n      black ", "px\n    );\n    -webkit-mask: radial-gradient(\n      circle at ", "px ", "px,\n      transparent ", "px,\n      black ", "px\n    );\n  "])), props.spotlightArea.x, props.spotlightArea.y, props.spotlightArea.radius, props.spotlightArea.radius + 20, props.spotlightArea.x, props.spotlightArea.y, props.spotlightArea.radius, props.spotlightArea.radius + 20);
});

// Component for Enhanced Visual Feedback
var TutorialVisualFeedback = function TutorialVisualFeedback(_ref) {
  var targetElement = _ref.targetElement,
    _ref$highlightType = _ref.highlightType,
    highlightType = _ref$highlightType === void 0 ? 'pulse' : _ref$highlightType,
    _ref$showSpotlight = _ref.showSpotlight,
    showSpotlight = _ref$showSpotlight === void 0 ? false : _ref$showSpotlight,
    _ref$showInteractiveH = _ref.showInteractiveHint,
    showInteractiveHint = _ref$showInteractiveH === void 0 ? false : _ref$showInteractiveH,
    _ref$animationType = _ref.animationType,
    animationType = _ref$animationType === void 0 ? 'fadeIn' : _ref$animationType;
  var highlightRef = (0,react.useRef)(null);
  var _React$useState = react.useState(null),
    _React$useState2 = (0,slicedToArray/* default */.A)(_React$useState, 2),
    elementRect = _React$useState2[0],
    setElementRect = _React$useState2[1];
  (0,react.useEffect)(function () {
    if (targetElement) {
      var updatePosition = function updatePosition() {
        var rect = targetElement.getBoundingClientRect();
        setElementRect(rect);
      };
      updatePosition();
      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition);
      return function () {
        window.removeEventListener('resize', updatePosition);
        window.removeEventListener('scroll', updatePosition);
      };
    }
  }, [targetElement]);
  if (!elementRect) return null;
  var spotlightArea = showSpotlight ? {
    x: elementRect.left + elementRect.width / 2,
    y: elementRect.top + elementRect.height / 2,
    radius: Math.max(elementRect.width, elementRect.height) / 2 + 20
  } : null;
  return /*#__PURE__*/react.createElement(react.Fragment, null, showSpotlight && /*#__PURE__*/react.createElement(SpotlightOverlay, {
    spotlightArea: spotlightArea
  }), /*#__PURE__*/react.createElement(EnhancedHighlightOverlay, {
    ref: highlightRef,
    highlightType: highlightType,
    style: {
      top: elementRect.top - 4,
      left: elementRect.left - 4,
      width: elementRect.width + 8,
      height: elementRect.height + 8
    }
  }), showInteractiveHint && /*#__PURE__*/react.createElement(InteractiveHighlighter, {
    style: {
      top: elementRect.top,
      left: elementRect.left,
      width: elementRect.width,
      height: elementRect.height
    }
  }));
};

// Enhanced Progress Ring Component
var TutorialProgressRing = function TutorialProgressRing(_ref2) {
  var progress = _ref2.progress,
    total = _ref2.total,
    _ref2$size = _ref2.size,
    size = _ref2$size === void 0 ? 60 : _ref2$size;
  var radius = (size - 8) / 2;
  var circumference = radius * 2 * Math.PI;
  var strokeDashoffset = circumference - progress / total * circumference;
  return /*#__PURE__*/react.createElement(ProgressRing, {
    style: {
      width: size,
      height: size
    }
  }, /*#__PURE__*/react.createElement("svg", null, /*#__PURE__*/react.createElement("circle", {
    className: "progress-ring-background",
    cx: size / 2,
    cy: size / 2,
    r: radius
  }), /*#__PURE__*/react.createElement("circle", {
    className: "progress-ring-progress",
    cx: size / 2,
    cy: size / 2,
    r: radius,
    strokeDasharray: circumference,
    strokeDashoffset: strokeDashoffset
  })), /*#__PURE__*/react.createElement("div", {
    className: "progress-text"
  }, progress, "/", total));
};

// CSS Injection for Global Tutorial Styles
var injectTutorialStyles = function injectTutorialStyles() {
  var styleId = 'tutorial-visual-enhancements';
  if (document.getElementById(styleId)) {
    return;
  }
  var style = document.createElement('style');
  style.id = styleId;
  style.textContent = "\n    .tutorial-highlight-pulse {\n      animation: tutorial-pulse 2s infinite !important;\n    }\n    \n    .tutorial-highlight-glow {\n      box-shadow: 0 0 20px rgba(24, 144, 255, 0.5) !important;\n      border: 2px solid #1890ff !important;\n    }\n    \n    .tutorial-interactive-element {\n      position: relative !important;\n      z-index: 10001 !important;\n    }\n    \n    .tutorial-disabled-overlay {\n      position: fixed !important;\n      top: 0 !important;\n      left: 0 !important;\n      width: 100vw !important;\n      height: 100vh !important;\n      background: rgba(0, 0, 0, 0.3) !important;\n      z-index: 9996 !important;\n      pointer-events: auto !important;\n    }\n    \n    @keyframes tutorial-pulse {\n      0% {\n        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);\n      }\n      70% {\n        box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);\n      }\n      100% {\n        box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);\n      }\n    }\n  ";
  document.head.appendChild(style);
};
/* harmony default export */ const TutorialVisualEnhancements = ({
  TutorialVisualFeedback: TutorialVisualFeedback,
  TutorialProgressRing: TutorialProgressRing,
  EnhancedHighlightOverlay: EnhancedHighlightOverlay,
  EnhancedTooltip: EnhancedTooltip,
  injectTutorialStyles: injectTutorialStyles
});
;// ./src/components/tutorial/TutorialOverlay.js


var TutorialOverlay_templateObject, TutorialOverlay_templateObject2, TutorialOverlay_templateObject3, TutorialOverlay_templateObject4, TutorialOverlay_templateObject5;
/**
 * Tutorial Overlay Component
 * 
 * Provides the main overlay system for tutorials with element highlighting,
 * step navigation, and interactive guidance.
 */









var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text;

// Styled Components
var OverlayContainer = styled_components_browser_esm/* default */.Ay.div(TutorialOverlay_templateObject || (TutorialOverlay_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: ", ";\n  pointer-events: ", ";\n  transition: opacity ", "ms ease-in-out;\n  opacity: ", ";\n"])), types/* Z_INDEX */.Mu.TUTORIAL_OVERLAY, function (props) {
  return props.allowInteraction ? 'none' : 'auto';
}, types/* ANIMATION_DURATIONS */.BH.OVERLAY_FADE, function (props) {
  return props.visible ? 1 : 0;
});
var HighlightArea = styled_components_browser_esm/* default */.Ay.div(TutorialOverlay_templateObject2 || (TutorialOverlay_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  background: transparent;\n  border: 2px solid #1890ff;\n  border-radius: ", "px;\n  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);\n  z-index: ", ";\n  pointer-events: none;\n  transition: all ", "ms ease-in-out;\n  animation: pulse 2s infinite;\n\n  @keyframes pulse {\n    0% { box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2); }\n    50% { box-shadow: 0 0 0 8px rgba(24, 144, 255, 0.1); }\n    100% { box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2); }\n  }\n"])), function (props) {
  return props.borderRadius || 4;
}, types/* Z_INDEX */.Mu.TUTORIAL_HIGHLIGHT, types/* ANIMATION_DURATIONS */.BH.HIGHLIGHT_FADE_IN);
var StepTooltip = styled_components_browser_esm/* default */.Ay.div(TutorialOverlay_templateObject3 || (TutorialOverlay_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  position: absolute;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  padding: 20px;\n  max-width: 350px;\n  min-width: 280px;\n  z-index: ", ";\n  pointer-events: auto;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    width: 0;\n    height: 0;\n    border-style: solid;\n    \n    ", "\n  }\n"])), types/* Z_INDEX */.Mu.TUTORIAL_TOOLTIP, function (props) {
  switch (props.position) {
    case 'top':
      return "\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            border-width: 8px 8px 0 8px;\n            border-color: white transparent transparent transparent;\n          ";
    case 'bottom':
      return "\n            top: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            border-width: 0 8px 8px 8px;\n            border-color: transparent transparent white transparent;\n          ";
    case 'left':
      return "\n            right: -8px;\n            top: 50%;\n            transform: translateY(-50%);\n            border-width: 8px 0 8px 8px;\n            border-color: transparent transparent transparent white;\n          ";
    case 'right':
      return "\n            left: -8px;\n            top: 50%;\n            transform: translateY(-50%);\n            border-width: 8px 8px 8px 0;\n            border-color: transparent white transparent transparent;\n          ";
    default:
      return '';
  }
});
var ControlsContainer = styled_components_browser_esm/* default */.Ay.div(TutorialOverlay_templateObject4 || (TutorialOverlay_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 16px;\n  gap: 8px;\n"])));
var ProgressContainer = styled_components_browser_esm/* default */.Ay.div(TutorialOverlay_templateObject5 || (TutorialOverlay_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin: 12px 0;\n"])));
var TutorialOverlay = function TutorialOverlay() {
  var _useTutorial = (0,TutorialManager/* useTutorial */.s8)(),
    isActive = _useTutorial.isActive,
    isPaused = _useTutorial.isPaused,
    activeTutorial = _useTutorial.activeTutorial,
    currentStep = _useTutorial.currentStep,
    currentStepIndex = _useTutorial.currentStepIndex,
    nextStep = _useTutorial.nextStep,
    previousStep = _useTutorial.previousStep,
    pauseTutorial = _useTutorial.pauseTutorial,
    resumeTutorial = _useTutorial.resumeTutorial,
    skipTutorial = _useTutorial.skipTutorial,
    completeTutorial = _useTutorial.completeTutorial,
    preferences = _useTutorial.preferences;
  var _useState = (0,react.useState)(null),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    highlightRect = _useState2[0],
    setHighlightRect = _useState2[1];
  var _useState3 = (0,react.useState)({
      x: 0,
      y: 0
    }),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    tooltipPosition = _useState4[0],
    setTooltipPosition = _useState4[1];
  var _useState5 = (0,react.useState)('bottom'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    tooltipPlacement = _useState6[0],
    setTooltipPlacement = _useState6[1];
  var overlayRef = (0,react.useRef)(null);
  var resizeObserverRef = (0,react.useRef)(null);
  var _useState7 = (0,react.useState)(null),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    targetElement = _useState8[0],
    setTargetElement = _useState8[1];

  // Inject enhanced tutorial styles
  (0,react.useEffect)(function () {
    injectTutorialStyles();
  }, []);

  // Calculate element position and highlight
  var updateHighlight = (0,react.useCallback)(function () {
    if (!currentStep || !currentStep.targetSelector) {
      setHighlightRect(null);
      return;
    }
    var element = document.querySelector(currentStep.targetSelector);
    if (!element) {
      console.warn("Target element not found: ".concat(currentStep.targetSelector));
      setHighlightRect(null);
      setTargetElement(null);
      return;
    }
    setTargetElement(element);
    var rect = element.getBoundingClientRect();
    var padding = currentStep.highlightPadding || 8;
    var highlightRect = {
      top: rect.top - padding,
      left: rect.left - padding,
      width: rect.width + padding * 2,
      height: rect.height + padding * 2
    };
    setHighlightRect(highlightRect);

    // Calculate tooltip position
    var tooltipWidth = 350;
    var tooltipHeight = 200; // Estimated
    var viewportWidth = window.innerWidth;
    var viewportHeight = window.innerHeight;
    var x, y, placement;

    // Determine best placement
    var spaceBelow = viewportHeight - rect.bottom;
    var spaceAbove = rect.top;
    var spaceRight = viewportWidth - rect.right;
    var spaceLeft = rect.left;
    if (currentStep.position === 'auto') {
      if (spaceBelow >= tooltipHeight) {
        placement = 'bottom';
        x = Math.max(10, Math.min(rect.left, viewportWidth - tooltipWidth - 10));
        y = rect.bottom + 16;
      } else if (spaceAbove >= tooltipHeight) {
        placement = 'top';
        x = Math.max(10, Math.min(rect.left, viewportWidth - tooltipWidth - 10));
        y = rect.top - tooltipHeight - 16;
      } else if (spaceRight >= tooltipWidth) {
        placement = 'right';
        x = rect.right + 16;
        y = Math.max(10, Math.min(rect.top, viewportHeight - tooltipHeight - 10));
      } else if (spaceLeft >= tooltipWidth) {
        placement = 'left';
        x = rect.left - tooltipWidth - 16;
        y = Math.max(10, Math.min(rect.top, viewportHeight - tooltipHeight - 10));
      } else {
        // Center on screen if no good position
        placement = 'center';
        x = (viewportWidth - tooltipWidth) / 2;
        y = (viewportHeight - tooltipHeight) / 2;
      }
    } else {
      placement = currentStep.position;
      // Use specified position logic
      switch (placement) {
        case 'bottom':
          x = Math.max(10, Math.min(rect.left, viewportWidth - tooltipWidth - 10));
          y = rect.bottom + 16;
          break;
        case 'top':
          x = Math.max(10, Math.min(rect.left, viewportWidth - tooltipWidth - 10));
          y = rect.top - tooltipHeight - 16;
          break;
        case 'right':
          x = rect.right + 16;
          y = Math.max(10, Math.min(rect.top, viewportHeight - tooltipHeight - 10));
          break;
        case 'left':
          x = rect.left - tooltipWidth - 16;
          y = Math.max(10, Math.min(rect.top, viewportHeight - tooltipHeight - 10));
          break;
        default:
          x = (viewportWidth - tooltipWidth) / 2;
          y = (viewportHeight - tooltipHeight) / 2;
      }
    }
    setTooltipPosition({
      x: x,
      y: y
    });
    setTooltipPlacement(placement);
  }, [currentStep]);

  // Update highlight when step changes or window resizes
  (0,react.useEffect)(function () {
    if (!isActive || !currentStep) return;
    updateHighlight();

    // Set up resize observer for target element
    if (currentStep.targetSelector) {
      var _targetElement = document.querySelector(currentStep.targetSelector);
      if (_targetElement && window.ResizeObserver) {
        resizeObserverRef.current = new ResizeObserver(updateHighlight);
        resizeObserverRef.current.observe(_targetElement);
      }
    }

    // Listen for window resize
    window.addEventListener('resize', updateHighlight);
    window.addEventListener('scroll', updateHighlight);
    return function () {
      window.removeEventListener('resize', updateHighlight);
      window.removeEventListener('scroll', updateHighlight);
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [isActive, currentStep, updateHighlight]);

  // Keyboard shortcuts
  (0,react.useEffect)(function () {
    if (!isActive || !preferences.enableKeyboardShortcuts) return;
    var handleKeyDown = function handleKeyDown(e) {
      switch (e.key) {
        case types/* TUTORIAL_SHORTCUTS */.Vp.NEXT_STEP:
          e.preventDefault();
          nextStep();
          break;
        case types/* TUTORIAL_SHORTCUTS */.Vp.PREVIOUS_STEP:
          e.preventDefault();
          previousStep();
          break;
        case types/* TUTORIAL_SHORTCUTS */.Vp.SKIP_TUTORIAL:
          e.preventDefault();
          skipTutorial();
          break;
        case types/* TUTORIAL_SHORTCUTS */.Vp.PAUSE_TUTORIAL:
          e.preventDefault();
          if (isPaused) {
            resumeTutorial();
          } else {
            pauseTutorial();
          }
          break;
        default:
          break;
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return function () {
      return document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isActive, isPaused, nextStep, previousStep, skipTutorial, pauseTutorial, resumeTutorial, preferences.enableKeyboardShortcuts]);

  // Auto-advance step
  (0,react.useEffect)(function () {
    if (!currentStep || !currentStep.autoAdvance || isPaused) return;
    var timer = setTimeout(function () {
      nextStep();
    }, currentStep.autoAdvanceDelay || 3000);
    return function () {
      return clearTimeout(timer);
    };
  }, [currentStep, isPaused, nextStep]);

  // Call step lifecycle methods
  (0,react.useEffect)(function () {
    if (!currentStep) return;
    if (currentStep.onEnter) {
      currentStep.onEnter(currentStep, activeTutorial);
    }
    return function () {
      if (currentStep.onExit) {
        currentStep.onExit(currentStep, activeTutorial);
      }
    };
  }, [currentStep, activeTutorial]);
  if (!isActive || !activeTutorial || !currentStep) {
    return null;
  }
  var progress = (currentStepIndex + 1) / activeTutorial.steps.length * 100;
  var isFirstStep = currentStepIndex === 0;
  var isLastStep = currentStepIndex === activeTutorial.steps.length - 1;
  var renderStepContent = function renderStepContent() {
    if (currentStep.customComponent) {
      return currentStep.customComponent;
    }
    return /*#__PURE__*/react.createElement(StepTooltip, {
      position: tooltipPlacement,
      style: {
        left: tooltipPosition.x,
        top: tooltipPosition.y
      }
    }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Title, {
      level: 4,
      style: {
        margin: '0 0 8px 0'
      }
    }, currentStep.title), /*#__PURE__*/react.createElement(Text, null, currentStep.content), preferences.showProgressIndicator && /*#__PURE__*/react.createElement(ProgressContainer, null, /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }
    }, /*#__PURE__*/react.createElement(TutorialProgressRing, {
      progress: currentStepIndex + 1,
      total: activeTutorial.steps.length,
      size: 50
    }), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(es/* Progress */.ke, {
      percent: progress,
      size: "small",
      showInfo: false,
      strokeColor: "#1890ff"
    }), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, "Step ", currentStepIndex + 1, " of ", activeTutorial.steps.length)))), /*#__PURE__*/react.createElement(ControlsContainer, null, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "Previous Step"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      icon: /*#__PURE__*/react.createElement(icons_es/* ArrowLeftOutlined */.Lrl, null),
      onClick: previousStep,
      disabled: isFirstStep,
      size: "small"
    })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: isPaused ? "Resume" : "Pause"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      icon: isPaused ? /*#__PURE__*/react.createElement(icons_es/* PlayCircleOutlined */.VgC, null) : /*#__PURE__*/react.createElement(icons_es/* PauseOutlined */.ZHH, null),
      onClick: isPaused ? resumeTutorial : pauseTutorial,
      size: "small"
    }))), /*#__PURE__*/react.createElement(es/* Space */.$x, null, currentStep.showSkip && /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "Skip Tutorial"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      icon: /*#__PURE__*/react.createElement(icons_es/* StepForwardOutlined */.QES, null),
      onClick: skipTutorial,
      size: "small",
      type: "text"
    }, "Skip")), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      type: "primary",
      icon: /*#__PURE__*/react.createElement(icons_es/* ArrowRightOutlined */.uxM, null),
      onClick: isLastStep ? completeTutorial : nextStep,
      size: "small"
    }, isLastStep ? 'Complete' : 'Next')))));
  };
  return /*#__PURE__*/(0,react_dom.createPortal)(/*#__PURE__*/react.createElement(OverlayContainer, {
    ref: overlayRef,
    visible: isActive && !isPaused,
    allowInteraction: currentStep.type === types/* TUTORIAL_STEP_TYPES */.Cy.INTERACTIVE
  }, targetElement && /*#__PURE__*/react.createElement(TutorialVisualFeedback, {
    targetElement: targetElement,
    highlightType: currentStep.type === types/* TUTORIAL_STEP_TYPES */.Cy.INTERACTIVE ? 'pulse' : 'glow',
    showSpotlight: currentStep.showSpotlight || false,
    showInteractiveHint: currentStep.type === types/* TUTORIAL_STEP_TYPES */.Cy.INTERACTIVE,
    animationType: "fadeIn"
  }), highlightRect && /*#__PURE__*/react.createElement(HighlightArea, {
    style: {
      top: highlightRect.top,
      left: highlightRect.left,
      width: highlightRect.width,
      height: highlightRect.height
    },
    borderRadius: currentStep.highlightBorderRadius
  }), renderStepContent(), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* CloseOutlined */.r$3, null),
    onClick: skipTutorial,
    style: {
      position: 'absolute',
      top: 20,
      right: 20,
      zIndex: types/* Z_INDEX */.Mu.TUTORIAL_CONTROLS
    },
    type: "text",
    size: "large"
  })), document.body);
};
/* harmony default export */ const tutorial_TutorialOverlay = (TutorialOverlay);

/***/ }),

/***/ 1549:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _TutorialManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82479);
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(98835);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
/**
 * Tutorial Progress Component
 * 
 * Displays tutorial progress, completion badges, and statistics
 * for the user's tutorial journey.
 */







var Title = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;

// Styled Components
var ProgressCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n  \n  .ant-card-body {\n    padding: 20px;\n  }\n"])));
var BadgeContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n  margin-top: 16px;\n"])));
var BadgeItem = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 12px;\n  border: 1px solid #f0f0f0;\n  border-radius: 8px;\n  background: ", ";\n  border-color: ", ";\n  min-width: 80px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n"])), function (props) {
  return props.earned ? '#f6ffed' : '#fafafa';
}, function (props) {
  return props.earned ? '#b7eb8f' : '#d9d9d9';
});
var CategoryIcon = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  color: white;\n  margin-bottom: 8px;\n  background: ", ";\n"])), function (props) {
  switch (props.category) {
    case _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.BEGINNER:
      return '#52c41a';
    case _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.INTERMEDIATE:
      return '#1890ff';
    case _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.ADVANCED:
      return '#f5222d';
    case _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.FEATURE_SPECIFIC:
      return '#722ed1';
    default:
      return '#8c8c8c';
  }
});
var StatsContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  background: #fafafa;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 16px;\n"])));
var TutorialProgress = function TutorialProgress(_ref) {
  var _ref$userId = _ref.userId,
    userId = _ref$userId === void 0 ? 'anonymous' : _ref$userId;
  var _useTutorial = (0,_TutorialManager__WEBPACK_IMPORTED_MODULE_6__/* .useTutorial */ .s8)(),
    getAllTutorials = _useTutorial.getAllTutorials,
    getTutorialProgress = _useTutorial.getTutorialProgress,
    getStatistics = _useTutorial.getStatistics,
    getBadges = _useTutorial.getBadges,
    startTutorial = _useTutorial.startTutorial;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(getAllTutorials()),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 1),
    tutorials = _useState2[0];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    statistics = _useState4[0],
    setStatistics = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    badges = _useState6[0],
    setBadges = _useState6[1];
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    setStatistics(getStatistics());
    setBadges(getBadges());
  }, [getStatistics, getBadges]);
  var getTutorialsByCategory = function getTutorialsByCategory() {
    var categorized = {};
    tutorials.forEach(function (tutorial) {
      if (!categorized[tutorial.category]) {
        categorized[tutorial.category] = [];
      }
      categorized[tutorial.category].push(tutorial);
    });
    return categorized;
  };
  var getCategoryProgress = function getCategoryProgress(category) {
    var categoryTutorials = tutorials.filter(function (t) {
      return t.category === category;
    });
    var completed = categoryTutorials.filter(function (t) {
      var progress = getTutorialProgress(t.id);
      return (progress === null || progress === void 0 ? void 0 : progress.status) === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_STATUS */ .y1.COMPLETED;
    }).length;
    return {
      completed: completed,
      total: categoryTutorials.length,
      percentage: categoryTutorials.length > 0 ? completed / categoryTutorials.length * 100 : 0
    };
  };
  var getOverallProgress = function getOverallProgress() {
    var completed = tutorials.filter(function (t) {
      var progress = getTutorialProgress(t.id);
      return (progress === null || progress === void 0 ? void 0 : progress.status) === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_STATUS */ .y1.COMPLETED;
    }).length;
    return {
      completed: completed,
      total: tutorials.length,
      percentage: tutorials.length > 0 ? completed / tutorials.length * 100 : 0
    };
  };
  var formatDuration = function formatDuration(minutes) {
    if (minutes < 60) return "".concat(minutes, "m");
    var hours = Math.floor(minutes / 60);
    var mins = minutes % 60;
    return "".concat(hours, "h ").concat(mins, "m");
  };
  var getCategoryIcon = function getCategoryIcon(category) {
    switch (category) {
      case _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.BEGINNER:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BookOutlined */ .E6Q, null);
      case _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.INTERMEDIATE:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .AimOutlined */ .SWB, null);
      case _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.ADVANCED:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .FireOutlined */ .Pso, null);
      case _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.FEATURE_SPECIFIC:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .StarOutlined */ .L0Y, null);
      default:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .BookOutlined */ .E6Q, null);
    }
  };
  var getDifficultyStars = function getDifficultyStars(difficulty) {
    return Array.from({
      length: 5
    }, function (_, i) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .StarOutlined */ .L0Y, {
        key: i,
        style: {
          color: i < difficulty ? '#faad14' : '#d9d9d9',
          fontSize: '12px'
        }
      });
    });
  };
  var overallProgress = getOverallProgress();
  var categorizedTutorials = getTutorialsByCategory();
  if (tutorials.length === 0) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Empty */ .Sv, {
      description: "No tutorials available",
      image: antd__WEBPACK_IMPORTED_MODULE_3__/* .Empty */ .Sv.PRESENTED_IMAGE_SIMPLE
    }));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ProgressCard, {
    title: "Your Learning Progress"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StatsContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Row */ .fI, {
    gutter: 16
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    span: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Overall Progress",
    value: overallProgress.percentage,
    precision: 1,
    suffix: "%",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .TrophyOutlined */ .tMQ, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    span: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Completed",
    value: overallProgress.completed,
    suffix: "/ ".concat(overallProgress.total),
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckCircleOutlined */ .hWy, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    span: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Time Spent",
    value: (statistics === null || statistics === void 0 ? void 0 : statistics.totalTimeSpent) || 0,
    formatter: function formatter(value) {
      return formatDuration(value);
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .ClockCircleOutlined */ .L8Y, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
    span: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Statistic */ .jL, {
    title: "Badges Earned",
    value: badges.length,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .StarOutlined */ .L0Y, null)
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Progress */ .ke, {
    percent: overallProgress.percentage,
    strokeColor: {
      '0%': '#108ee9',
      '100%': '#87d068'
    },
    showInfo: false
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ProgressCard, {
    title: "Progress by Category"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Row */ .fI, {
    gutter: 16
  }, Object.entries(categorizedTutorials).map(function (_ref2) {
    var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref2, 2),
      category = _ref3[0],
      categoryTutorials = _ref3[1];
    var progress = getCategoryProgress(category);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Col */ .fv, {
      span: 12,
      key: category
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
      size: "small",
      style: {
        marginBottom: 16
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
      align: "center"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(CategoryIcon, {
      category: category
    }, getCategoryIcon(category)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        flex: 1
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      strong: true,
      style: {
        textTransform: 'capitalize'
      }
    }, category.replace('_', ' ')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "secondary"
    }, progress.completed, " / ", progress.total, " completed"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Progress */ .ke, {
      percent: progress.percentage,
      size: "small",
      showInfo: false,
      strokeColor: category === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.BEGINNER ? '#52c41a' : category === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.INTERMEDIATE ? '#1890ff' : category === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.ADVANCED ? '#f5222d' : '#722ed1'
    })))));
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ProgressCard, {
    title: "Tutorial Journey"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Timeline */ .Kf, null, tutorials.map(function (tutorial) {
    var progress = getTutorialProgress(tutorial.id);
    var isCompleted = (progress === null || progress === void 0 ? void 0 : progress.status) === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_STATUS */ .y1.COMPLETED;
    var isInProgress = (progress === null || progress === void 0 ? void 0 : progress.status) === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_STATUS */ .y1.IN_PROGRESS;
    var isSkipped = (progress === null || progress === void 0 ? void 0 : progress.status) === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_STATUS */ .y1.SKIPPED;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Timeline */ .Kf.Item, {
      key: tutorial.id,
      dot: isCompleted ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckCircleOutlined */ .hWy, {
        style: {
          color: '#52c41a'
        }
      }) : isInProgress ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .PlayCircleOutlined */ .VgC, {
        style: {
          color: '#1890ff'
        }
      }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Avatar */ .eu, {
        size: "small",
        icon: getCategoryIcon(tutorial.category)
      }),
      color: isCompleted ? 'green' : isInProgress ? 'blue' : 'gray'
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      strong: true
    }, tutorial.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "secondary"
    }, tutorial.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
      color: tutorial.category === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.BEGINNER ? 'green' : tutorial.category === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.INTERMEDIATE ? 'blue' : tutorial.category === _types__WEBPACK_IMPORTED_MODULE_7__/* .TUTORIAL_CATEGORIES */ .Ej.ADVANCED ? 'red' : 'purple',
      text: tutorial.category.replace('_', ' ')
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "secondary"
    }, getDifficultyStars(tutorial.difficulty)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "secondary"
    }, "~", tutorial.estimatedDuration, "min")), isSkipped && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "warning"
    }, "Skipped"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, !isCompleted && !isSkipped && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      type: isInProgress ? "default" : "primary",
      size: "small",
      onClick: function onClick() {
        return startTutorial(tutorial.id);
      }
    }, isInProgress ? 'Continue' : 'Start'))));
  }))), badges.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ProgressCard, {
    title: "Achievement Badges"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(BadgeContainer, null, badges.map(function (badge, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      key: index,
      title: "Earned: ".concat(new Date(badge.earnedAt).toLocaleDateString())
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(BadgeItem, {
      earned: true
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .TrophyOutlined */ .tMQ, {
      style: {
        fontSize: 24,
        color: '#faad14'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      style: {
        fontSize: 12,
        textAlign: 'center'
      }
    }, badge.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 10
      }
    }, badge.category)));
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TutorialProgress);

/***/ }),

/***/ 52461:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(92901);
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(98835);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Tutorial Storage Service
 * 
 * Handles persistence of tutorial progress, preferences, and state
 * using localStorage with fallback mechanisms and data validation.
 */


var TutorialStorage = /*#__PURE__*/function () {
  function TutorialStorage() {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(this, TutorialStorage);
    this.isLocalStorageAvailable = this.checkLocalStorageAvailability();
    this.memoryFallback = new Map();
  }

  /**
   * Check if localStorage is available
   */
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(TutorialStorage, [{
    key: "checkLocalStorageAvailability",
    value: function checkLocalStorageAvailability() {
      try {
        var test = '__localStorage_test__';
        localStorage.setItem(test, test);
        localStorage.removeItem(test);
        return true;
      } catch (e) {
        console.warn('localStorage not available, using memory fallback');
        return false;
      }
    }

    /**
     * Generic storage methods with fallback
     */
  }, {
    key: "setItem",
    value: function setItem(key, value) {
      try {
        var serializedValue = JSON.stringify(value);
        if (this.isLocalStorageAvailable) {
          localStorage.setItem(key, serializedValue);
        } else {
          this.memoryFallback.set(key, serializedValue);
        }
      } catch (error) {
        console.error('Error storing data:', error);
        this.memoryFallback.set(key, JSON.stringify(value));
      }
    }
  }, {
    key: "getItem",
    value: function getItem(key) {
      var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      try {
        var value;
        if (this.isLocalStorageAvailable) {
          value = localStorage.getItem(key);
        } else {
          value = this.memoryFallback.get(key);
        }
        return value ? JSON.parse(value) : defaultValue;
      } catch (error) {
        console.error('Error retrieving data:', error);
        return defaultValue;
      }
    }
  }, {
    key: "removeItem",
    value: function removeItem(key) {
      try {
        if (this.isLocalStorageAvailable) {
          localStorage.removeItem(key);
        } else {
          this.memoryFallback["delete"](key);
        }
      } catch (error) {
        console.error('Error removing data:', error);
      }
    }

    /**
     * Tutorial Progress Management
     */
  }, {
    key: "getTutorialProgress",
    value: function getTutorialProgress(tutorialId) {
      var userId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'anonymous';
      var allProgress = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, {});
      var userProgress = allProgress[userId] || {};
      if (userProgress[tutorialId]) {
        return userProgress[tutorialId];
      }

      // Return default progress if none exists
      return (0,_types__WEBPACK_IMPORTED_MODULE_3__/* .createTutorialProgress */ .e2)({
        tutorialId: tutorialId,
        userId: userId
      });
    }
  }, {
    key: "saveTutorialProgress",
    value: function saveTutorialProgress(progress) {
      var allProgress = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, {});
      if (!allProgress[progress.userId]) {
        allProgress[progress.userId] = {};
      }
      allProgress[progress.userId][progress.tutorialId] = _objectSpread(_objectSpread({}, progress), {}, {
        lastUpdated: new Date().toISOString()
      });
      this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, allProgress);
    }
  }, {
    key: "getAllTutorialProgress",
    value: function getAllTutorialProgress() {
      var userId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'anonymous';
      var allProgress = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, {});
      return allProgress[userId] || {};
    }
  }, {
    key: "clearTutorialProgress",
    value: function clearTutorialProgress(tutorialId) {
      var userId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'anonymous';
      var allProgress = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, {});
      if (allProgress[userId] && allProgress[userId][tutorialId]) {
        delete allProgress[userId][tutorialId];
        this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, allProgress);
      }
    }

    /**
     * Tutorial Preferences Management
     */
  }, {
    key: "getTutorialPreferences",
    value: function getTutorialPreferences() {
      return this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PREFERENCES, _types__WEBPACK_IMPORTED_MODULE_3__/* .DEFAULT_TUTORIAL_PREFERENCES */ .I$);
    }
  }, {
    key: "saveTutorialPreferences",
    value: function saveTutorialPreferences(preferences) {
      var currentPreferences = this.getTutorialPreferences();
      var updatedPreferences = _objectSpread(_objectSpread(_objectSpread({}, currentPreferences), preferences), {}, {
        lastUpdated: new Date().toISOString()
      });
      this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PREFERENCES, updatedPreferences);
    }
  }, {
    key: "resetTutorialPreferences",
    value: function resetTutorialPreferences() {
      this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PREFERENCES, _types__WEBPACK_IMPORTED_MODULE_3__/* .DEFAULT_TUTORIAL_PREFERENCES */ .I$);
    }

    /**
     * Help Context Tracking
     */
  }, {
    key: "getShownHelpContexts",
    value: function getShownHelpContexts() {
      var userId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'anonymous';
      var allShown = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.HELP_CONTEXT_SHOWN, {});
      return allShown[userId] || [];
    }
  }, {
    key: "markHelpContextShown",
    value: function markHelpContextShown(contextId) {
      var userId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'anonymous';
      var allShown = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.HELP_CONTEXT_SHOWN, {});
      if (!allShown[userId]) {
        allShown[userId] = [];
      }
      if (!allShown[userId].includes(contextId)) {
        allShown[userId].push(contextId);
        this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.HELP_CONTEXT_SHOWN, allShown);
      }
    }

    /**
     * Tutorial Completion Badges
     */
  }, {
    key: "getTutorialBadges",
    value: function getTutorialBadges() {
      var userId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'anonymous';
      var allBadges = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_COMPLETION_BADGES, {});
      return allBadges[userId] || [];
    }
  }, {
    key: "addTutorialBadge",
    value: function addTutorialBadge(badge) {
      var userId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'anonymous';
      var allBadges = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_COMPLETION_BADGES, {});
      if (!allBadges[userId]) {
        allBadges[userId] = [];
      }
      var badgeWithTimestamp = _objectSpread(_objectSpread({}, badge), {}, {
        earnedAt: new Date().toISOString()
      });
      allBadges[userId].push(badgeWithTimestamp);
      this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_COMPLETION_BADGES, allBadges);
    }

    /**
     * Analytics and Statistics
     */
  }, {
    key: "getTutorialStatistics",
    value: function getTutorialStatistics() {
      var userId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'anonymous';
      var progress = this.getAllTutorialProgress(userId);
      var badges = this.getTutorialBadges(userId);
      var stats = {
        totalTutorialsStarted: 0,
        totalTutorialsCompleted: 0,
        totalTutorialsSkipped: 0,
        totalTimeSpent: 0,
        averageCompletionTime: 0,
        badgesEarned: badges.length,
        completionRate: 0
      };
      Object.values(progress).forEach(function (tutorialProgress) {
        if (tutorialProgress.status !== _types__WEBPACK_IMPORTED_MODULE_3__/* .TUTORIAL_STATUS */ .y1.NOT_STARTED) {
          stats.totalTutorialsStarted++;
        }
        if (tutorialProgress.status === _types__WEBPACK_IMPORTED_MODULE_3__/* .TUTORIAL_STATUS */ .y1.COMPLETED) {
          stats.totalTutorialsCompleted++;
        }
        if (tutorialProgress.status === _types__WEBPACK_IMPORTED_MODULE_3__/* .TUTORIAL_STATUS */ .y1.SKIPPED) {
          stats.totalTutorialsSkipped++;
        }
        stats.totalTimeSpent += tutorialProgress.timeSpent || 0;
      });
      if (stats.totalTutorialsCompleted > 0) {
        stats.averageCompletionTime = stats.totalTimeSpent / stats.totalTutorialsCompleted;
      }
      if (stats.totalTutorialsStarted > 0) {
        stats.completionRate = stats.totalTutorialsCompleted / stats.totalTutorialsStarted * 100;
      }
      return stats;
    }

    /**
     * Data Export/Import for backup and migration
     */
  }, {
    key: "exportTutorialData",
    value: function exportTutorialData() {
      var userId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'anonymous';
      return {
        progress: this.getAllTutorialProgress(userId),
        preferences: this.getTutorialPreferences(),
        badges: this.getTutorialBadges(userId),
        shownContexts: this.getShownHelpContexts(userId),
        exportedAt: new Date().toISOString(),
        version: '1.0'
      };
    }
  }, {
    key: "importTutorialData",
    value: function importTutorialData(data) {
      var userId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'anonymous';
      try {
        if (data.progress) {
          var allProgress = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, {});
          allProgress[userId] = data.progress;
          this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, allProgress);
        }
        if (data.preferences) {
          this.saveTutorialPreferences(data.preferences);
        }
        if (data.badges) {
          var allBadges = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_COMPLETION_BADGES, {});
          allBadges[userId] = data.badges;
          this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_COMPLETION_BADGES, allBadges);
        }
        if (data.shownContexts) {
          var allShown = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.HELP_CONTEXT_SHOWN, {});
          allShown[userId] = data.shownContexts;
          this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.HELP_CONTEXT_SHOWN, allShown);
        }
        return true;
      } catch (error) {
        console.error('Error importing tutorial data:', error);
        return false;
      }
    }

    /**
     * Clear all tutorial data
     */
  }, {
    key: "clearAllTutorialData",
    value: function clearAllTutorialData() {
      var userId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'anonymous';
      var allProgress = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, {});
      var allBadges = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_COMPLETION_BADGES, {});
      var allShown = this.getItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.HELP_CONTEXT_SHOWN, {});
      delete allProgress[userId];
      delete allBadges[userId];
      delete allShown[userId];
      this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_PROGRESS, allProgress);
      this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.TUTORIAL_COMPLETION_BADGES, allBadges);
      this.setItem(_types__WEBPACK_IMPORTED_MODULE_3__/* .STORAGE_KEYS */ .d5.HELP_CONTEXT_SHOWN, allShown);

      // Reset preferences to default
      this.resetTutorialPreferences();
    }
  }]);
}(); // Create singleton instance
var tutorialStorage = new TutorialStorage();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tutorialStorage);

/***/ }),

/***/ 82479:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   s8: () => (/* binding */ useTutorial)
/* harmony export */ });
/* unused harmony export TutorialProvider */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(64467);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _TutorialStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(52461);
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(98835);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Tutorial Manager
 * 
 * Central manager for the tutorial system that handles tutorial registration,
 * execution, progress tracking, and coordination between components.
 */






// Tutorial Manager Context
var TutorialContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)();

// Action Types
var TUTORIAL_ACTIONS = {
  REGISTER_TUTORIAL: 'REGISTER_TUTORIAL',
  UNREGISTER_TUTORIAL: 'UNREGISTER_TUTORIAL',
  START_TUTORIAL: 'START_TUTORIAL',
  PAUSE_TUTORIAL: 'PAUSE_TUTORIAL',
  RESUME_TUTORIAL: 'RESUME_TUTORIAL',
  COMPLETE_TUTORIAL: 'COMPLETE_TUTORIAL',
  SKIP_TUTORIAL: 'SKIP_TUTORIAL',
  NEXT_STEP: 'NEXT_STEP',
  PREVIOUS_STEP: 'PREVIOUS_STEP',
  SKIP_STEP: 'SKIP_STEP',
  SET_CURRENT_STEP: 'SET_CURRENT_STEP',
  UPDATE_PROGRESS: 'UPDATE_PROGRESS',
  LOAD_PROGRESS: 'LOAD_PROGRESS',
  SET_PREFERENCES: 'SET_PREFERENCES',
  SHOW_CONTEXT_HELP: 'SHOW_CONTEXT_HELP',
  HIDE_CONTEXT_HELP: 'HIDE_CONTEXT_HELP',
  SET_ACTIVE_TUTORIAL: 'SET_ACTIVE_TUTORIAL'
};

// Initial State
var initialState = {
  tutorials: new Map(),
  activeTutorial: null,
  currentStep: null,
  currentStepIndex: 0,
  isActive: false,
  isPaused: false,
  progress: new Map(),
  preferences: _TutorialStorage__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A.getTutorialPreferences(),
  contextHelp: {
    visible: false,
    content: null,
    position: null
  },
  statistics: {
    totalCompleted: 0,
    totalStarted: 0,
    averageTime: 0
  }
};

// Reducer
function tutorialReducer(state, action) {
  var _action$payload;
  switch (action.type) {
    case TUTORIAL_ACTIONS.REGISTER_TUTORIAL:
      var newTutorials = new Map(state.tutorials);
      newTutorials.set(action.payload.id, action.payload);
      return _objectSpread(_objectSpread({}, state), {}, {
        tutorials: newTutorials
      });
    case TUTORIAL_ACTIONS.UNREGISTER_TUTORIAL:
      var updatedTutorials = new Map(state.tutorials);
      updatedTutorials["delete"](action.payload);
      return _objectSpread(_objectSpread({}, state), {}, {
        tutorials: updatedTutorials
      });
    case TUTORIAL_ACTIONS.SET_ACTIVE_TUTORIAL:
      return _objectSpread(_objectSpread({}, state), {}, {
        activeTutorial: action.payload,
        isActive: !!action.payload,
        currentStepIndex: 0,
        currentStep: ((_action$payload = action.payload) === null || _action$payload === void 0 ? void 0 : _action$payload.steps[0]) || null,
        isPaused: false
      });
    case TUTORIAL_ACTIONS.START_TUTORIAL:
      return _objectSpread(_objectSpread({}, state), {}, {
        activeTutorial: action.payload.tutorial,
        isActive: true,
        currentStepIndex: action.payload.stepIndex || 0,
        currentStep: action.payload.tutorial.steps[action.payload.stepIndex || 0],
        isPaused: false
      });
    case TUTORIAL_ACTIONS.PAUSE_TUTORIAL:
      return _objectSpread(_objectSpread({}, state), {}, {
        isPaused: true
      });
    case TUTORIAL_ACTIONS.RESUME_TUTORIAL:
      return _objectSpread(_objectSpread({}, state), {}, {
        isPaused: false
      });
    case TUTORIAL_ACTIONS.COMPLETE_TUTORIAL:
    case TUTORIAL_ACTIONS.SKIP_TUTORIAL:
      return _objectSpread(_objectSpread({}, state), {}, {
        activeTutorial: null,
        currentStep: null,
        currentStepIndex: 0,
        isActive: false,
        isPaused: false
      });
    case TUTORIAL_ACTIONS.NEXT_STEP:
      if (!state.activeTutorial) return state;
      var nextIndex = state.currentStepIndex + 1;
      var nextStep = state.activeTutorial.steps[nextIndex];
      return _objectSpread(_objectSpread({}, state), {}, {
        currentStepIndex: nextIndex,
        currentStep: nextStep || null,
        isActive: !!nextStep
      });
    case TUTORIAL_ACTIONS.PREVIOUS_STEP:
      if (!state.activeTutorial) return state;
      var prevIndex = Math.max(0, state.currentStepIndex - 1);
      var prevStep = state.activeTutorial.steps[prevIndex];
      return _objectSpread(_objectSpread({}, state), {}, {
        currentStepIndex: prevIndex,
        currentStep: prevStep
      });
    case TUTORIAL_ACTIONS.SET_CURRENT_STEP:
      if (!state.activeTutorial) return state;
      var stepIndex = action.payload;
      var step = state.activeTutorial.steps[stepIndex];
      return _objectSpread(_objectSpread({}, state), {}, {
        currentStepIndex: stepIndex,
        currentStep: step || null
      });
    case TUTORIAL_ACTIONS.UPDATE_PROGRESS:
      var newProgress = new Map(state.progress);
      newProgress.set(action.payload.tutorialId, action.payload);
      return _objectSpread(_objectSpread({}, state), {}, {
        progress: newProgress
      });
    case TUTORIAL_ACTIONS.LOAD_PROGRESS:
      return _objectSpread(_objectSpread({}, state), {}, {
        progress: new Map(Object.entries(action.payload))
      });
    case TUTORIAL_ACTIONS.SET_PREFERENCES:
      return _objectSpread(_objectSpread({}, state), {}, {
        preferences: _objectSpread(_objectSpread({}, state.preferences), action.payload)
      });
    case TUTORIAL_ACTIONS.SHOW_CONTEXT_HELP:
      return _objectSpread(_objectSpread({}, state), {}, {
        contextHelp: {
          visible: true,
          content: action.payload.content,
          position: action.payload.position
        }
      });
    case TUTORIAL_ACTIONS.HIDE_CONTEXT_HELP:
      return _objectSpread(_objectSpread({}, state), {}, {
        contextHelp: {
          visible: false,
          content: null,
          position: null
        }
      });
    default:
      return state;
  }
}

// Tutorial Manager Provider Component
var TutorialProvider = function TutorialProvider(_ref) {
  var children = _ref.children,
    _ref$userId = _ref.userId,
    userId = _ref$userId === void 0 ? 'anonymous' : _ref$userId;
  var _useReducer = useReducer(tutorialReducer, initialState),
    _useReducer2 = _slicedToArray(_useReducer, 2),
    state = _useReducer2[0],
    dispatch = _useReducer2[1];

  // Load initial data
  useEffect(function () {
    var progress = tutorialStorage.getAllTutorialProgress(userId);
    dispatch({
      type: TUTORIAL_ACTIONS.LOAD_PROGRESS,
      payload: progress
    });
  }, [userId]);

  // Tutorial Management Functions
  var registerTutorial = useCallback(function (tutorial) {
    dispatch({
      type: TUTORIAL_ACTIONS.REGISTER_TUTORIAL,
      payload: tutorial
    });
  }, []);
  var unregisterTutorial = useCallback(function (tutorialId) {
    dispatch({
      type: TUTORIAL_ACTIONS.UNREGISTER_TUTORIAL,
      payload: tutorialId
    });
  }, []);
  var startTutorial = useCallback(function (tutorialId) {
    var stepIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    var tutorial = state.tutorials.get(tutorialId);
    if (!tutorial) {
      message.error('Tutorial not found');
      return false;
    }

    // Check prerequisites
    if (tutorial.prerequisites.length > 0) {
      var hasPrerequisites = tutorial.prerequisites.every(function (prereqId) {
        var prereqProgress = state.progress.get(prereqId);
        return (prereqProgress === null || prereqProgress === void 0 ? void 0 : prereqProgress.status) === TUTORIAL_STATUS.COMPLETED;
      });
      if (!hasPrerequisites) {
        message.warning('Please complete prerequisite tutorials first');
        return false;
      }
    }

    // Create or update progress
    var progress = createTutorialProgress({
      tutorialId: tutorialId,
      userId: userId,
      status: TUTORIAL_STATUS.IN_PROGRESS,
      currentStepIndex: stepIndex,
      startedAt: new Date().toISOString()
    });
    tutorialStorage.saveTutorialProgress(progress);
    dispatch({
      type: TUTORIAL_ACTIONS.UPDATE_PROGRESS,
      payload: progress
    });
    dispatch({
      type: TUTORIAL_ACTIONS.START_TUTORIAL,
      payload: {
        tutorial: tutorial,
        stepIndex: stepIndex
      }
    });

    // Call tutorial's onStart callback
    if (tutorial.onStart) {
      tutorial.onStart(tutorial, progress);
    }

    // Emit event
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_STARTED, {
      tutorialId: tutorialId,
      stepIndex: stepIndex
    });
    return true;
  }, [state.tutorials, state.progress, userId]);
  var pauseTutorial = useCallback(function () {
    if (!state.activeTutorial) return;
    var progress = state.progress.get(state.activeTutorial.id);
    if (progress) {
      var updatedProgress = _objectSpread(_objectSpread({}, progress), {}, {
        status: TUTORIAL_STATUS.PAUSED,
        pausedAt: new Date().toISOString()
      });
      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({
        type: TUTORIAL_ACTIONS.UPDATE_PROGRESS,
        payload: updatedProgress
      });
    }
    dispatch({
      type: TUTORIAL_ACTIONS.PAUSE_TUTORIAL
    });
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_PAUSED, {
      tutorialId: state.activeTutorial.id
    });
  }, [state.activeTutorial, state.progress]);
  var resumeTutorial = useCallback(function () {
    if (!state.activeTutorial) return;
    var progress = state.progress.get(state.activeTutorial.id);
    if (progress) {
      var updatedProgress = _objectSpread(_objectSpread({}, progress), {}, {
        status: TUTORIAL_STATUS.IN_PROGRESS,
        pausedAt: null
      });
      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({
        type: TUTORIAL_ACTIONS.UPDATE_PROGRESS,
        payload: updatedProgress
      });
    }
    dispatch({
      type: TUTORIAL_ACTIONS.RESUME_TUTORIAL
    });
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_RESUMED, {
      tutorialId: state.activeTutorial.id
    });
  }, [state.activeTutorial, state.progress]);
  var completeTutorial = useCallback(function () {
    if (!state.activeTutorial) return;
    var tutorial = state.activeTutorial;
    var progress = state.progress.get(tutorial.id);
    if (progress) {
      var updatedProgress = _objectSpread(_objectSpread({}, progress), {}, {
        status: TUTORIAL_STATUS.COMPLETED,
        completedAt: new Date().toISOString(),
        completedSteps: tutorial.steps.map(function (_, index) {
          return index;
        })
      });
      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({
        type: TUTORIAL_ACTIONS.UPDATE_PROGRESS,
        payload: updatedProgress
      });

      // Add completion badge
      tutorialStorage.addTutorialBadge({
        tutorialId: tutorial.id,
        title: tutorial.title,
        category: tutorial.category,
        difficulty: tutorial.difficulty
      }, userId);
    }

    // Call tutorial's onComplete callback
    if (tutorial.onComplete) {
      tutorial.onComplete(tutorial, progress);
    }
    dispatch({
      type: TUTORIAL_ACTIONS.COMPLETE_TUTORIAL
    });
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_COMPLETED, {
      tutorialId: tutorial.id
    });
    message.success("Tutorial \"".concat(tutorial.title, "\" completed!"));
  }, [state.activeTutorial, state.progress, userId]);
  var skipTutorial = useCallback(function () {
    if (!state.activeTutorial) return;
    var tutorial = state.activeTutorial;
    var progress = state.progress.get(tutorial.id);
    if (progress) {
      var updatedProgress = _objectSpread(_objectSpread({}, progress), {}, {
        status: TUTORIAL_STATUS.SKIPPED,
        completedAt: new Date().toISOString()
      });
      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({
        type: TUTORIAL_ACTIONS.UPDATE_PROGRESS,
        payload: updatedProgress
      });
    }

    // Call tutorial's onSkip callback
    if (tutorial.onSkip) {
      tutorial.onSkip(tutorial, progress);
    }
    dispatch({
      type: TUTORIAL_ACTIONS.SKIP_TUTORIAL
    });
    emitTutorialEvent(TUTORIAL_EVENTS.TUTORIAL_SKIPPED, {
      tutorialId: tutorial.id
    });
  }, [state.activeTutorial, state.progress]);

  // Step Navigation
  var nextStep = useCallback(function () {
    if (!state.activeTutorial || !state.currentStep) return;
    var currentProgress = state.progress.get(state.activeTutorial.id);
    if (currentProgress) {
      var updatedProgress = _objectSpread(_objectSpread({}, currentProgress), {}, {
        currentStepIndex: state.currentStepIndex + 1,
        completedSteps: _toConsumableArray(new Set([].concat(_toConsumableArray(currentProgress.completedSteps), [state.currentStepIndex])))
      });
      tutorialStorage.saveTutorialProgress(updatedProgress);
      dispatch({
        type: TUTORIAL_ACTIONS.UPDATE_PROGRESS,
        payload: updatedProgress
      });
    }

    // Check if tutorial is complete
    if (state.currentStepIndex >= state.activeTutorial.steps.length - 1) {
      completeTutorial();
      return;
    }
    dispatch({
      type: TUTORIAL_ACTIONS.NEXT_STEP
    });
    emitTutorialEvent(TUTORIAL_EVENTS.STEP_COMPLETED, {
      tutorialId: state.activeTutorial.id,
      stepIndex: state.currentStepIndex
    });
  }, [state.activeTutorial, state.currentStep, state.currentStepIndex, state.progress, completeTutorial]);
  var previousStep = useCallback(function () {
    if (!state.activeTutorial || state.currentStepIndex <= 0) return;
    dispatch({
      type: TUTORIAL_ACTIONS.PREVIOUS_STEP
    });
  }, [state.activeTutorial, state.currentStepIndex]);
  var goToStep = useCallback(function (stepIndex) {
    if (!state.activeTutorial || stepIndex < 0 || stepIndex >= state.activeTutorial.steps.length) return;
    dispatch({
      type: TUTORIAL_ACTIONS.SET_CURRENT_STEP,
      payload: stepIndex
    });
  }, [state.activeTutorial]);

  // Helper function to emit tutorial events
  var emitTutorialEvent = useCallback(function (eventType, data) {
    // This could be extended to integrate with analytics services
    console.log('Tutorial Event:', eventType, data);

    // Dispatch custom event for other components to listen to
    window.dispatchEvent(new CustomEvent('tutorialEvent', {
      detail: {
        type: eventType,
        data: data
      }
    }));
  }, []);

  // Context value
  var value = _objectSpread(_objectSpread({}, state), {}, {
    // Tutorial Management
    registerTutorial: registerTutorial,
    unregisterTutorial: unregisterTutorial,
    startTutorial: startTutorial,
    pauseTutorial: pauseTutorial,
    resumeTutorial: resumeTutorial,
    completeTutorial: completeTutorial,
    skipTutorial: skipTutorial,
    // Step Navigation
    nextStep: nextStep,
    previousStep: previousStep,
    goToStep: goToStep,
    // Utilities
    getTutorial: function getTutorial(id) {
      return state.tutorials.get(id);
    },
    getTutorialProgress: function getTutorialProgress(id) {
      return state.progress.get(id);
    },
    getAllTutorials: function getAllTutorials() {
      return Array.from(state.tutorials.values());
    },
    getAvailableTutorials: function getAvailableTutorials() {
      return Array.from(state.tutorials.values()).filter(function (tutorial) {
        var progress = state.progress.get(tutorial.id);
        return !progress || progress.status !== TUTORIAL_STATUS.COMPLETED || !state.preferences.skipCompletedTutorials;
      });
    },
    // Statistics
    getStatistics: function getStatistics() {
      return tutorialStorage.getTutorialStatistics(userId);
    },
    getBadges: function getBadges() {
      return tutorialStorage.getTutorialBadges(userId);
    }
  });
  return /*#__PURE__*/React.createElement(TutorialContext.Provider, {
    value: value
  }, children);
};

// Hook to use tutorial context
var useTutorial = function useTutorial() {
  var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(TutorialContext);
  if (!context) {
    throw new Error('useTutorial must be used within a TutorialProvider');
  }
  return context;
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (TutorialProvider)));

/***/ }),

/***/ 90720:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _TutorialManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82479);
/* harmony import */ var _TutorialOverlay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1163);
/* harmony import */ var _TutorialProgress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1549);
/* harmony import */ var _ContextualHelp__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(89292);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;








var Title = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;
var Step = antd__WEBPACK_IMPORTED_MODULE_3__/* .Steps */ .gj.Step;

/**
 * Tutorial Assistant
 * 
 * Main tutorial interface component that provides guided tutorials
 * with step-by-step instructions, progress tracking, and contextual help.
 */

var TutorialContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  width: 350px;\n  max-height: 80vh;\n  z-index: 1000;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  \n  @media (max-width: 768px) {\n    top: 10px;\n    right: 10px;\n    left: 10px;\n    width: auto;\n    max-height: 70vh;\n  }\n"])));
var TutorialHeader = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  padding: 16px;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  color: white;\n  \n  .tutorial-title {\n    color: white;\n    margin: 0;\n    font-size: 16px;\n    font-weight: 600;\n  }\n  \n  .tutorial-subtitle {\n    color: rgba(255, 255, 255, 0.8);\n    margin: 4px 0 0 0;\n    font-size: 12px;\n  }\n"])));
var TutorialContent = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  padding: 16px;\n  max-height: 400px;\n  overflow-y: auto;\n"])));
var TutorialControls = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  padding: 12px 16px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"])));
var StepContent = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  margin: 16px 0;\n  \n  .step-title {\n    font-weight: 600;\n    margin-bottom: 8px;\n    color: #262626;\n  }\n  \n  .step-description {\n    color: #595959;\n    line-height: 1.6;\n    margin-bottom: 12px;\n  }\n  \n  .step-actions {\n    margin-top: 12px;\n  }\n"])));
var TutorialAssistant = function TutorialAssistant(_ref) {
  var currentStep = _ref.currentStep,
    totalSteps = _ref.totalSteps,
    onNext = _ref.onNext,
    onPrevious = _ref.onPrevious,
    onSkip = _ref.onSkip,
    onComplete = _ref.onComplete,
    onPause = _ref.onPause,
    onResume = _ref.onResume,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact;
  var _useTutorial = (0,_TutorialManager__WEBPACK_IMPORTED_MODULE_6__/* .useTutorial */ .s8)(),
    activeTutorial = _useTutorial.activeTutorial,
    isActive = _useTutorial.isActive,
    isPaused = _useTutorial.isPaused,
    currentStepIndex = _useTutorial.currentStepIndex,
    nextStep = _useTutorial.nextStep,
    previousStep = _useTutorial.previousStep,
    pauseTutorial = _useTutorial.pauseTutorial,
    resumeTutorial = _useTutorial.resumeTutorial,
    skipTutorial = _useTutorial.skipTutorial,
    completeTutorial = _useTutorial.completeTutorial;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    showOverlay = _useState2[0],
    setShowOverlay = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    showHelp = _useState4[0],
    setShowHelp = _useState4[1];

  // Use tutorial context if available, otherwise use props
  var tutorial = activeTutorial;
  var step = (activeTutorial === null || activeTutorial === void 0 ? void 0 : activeTutorial.steps[currentStepIndex]) || currentStep;
  var stepIndex = currentStepIndex;
  var total = (activeTutorial === null || activeTutorial === void 0 ? void 0 : activeTutorial.steps.length) || totalSteps;
  var active = isActive;
  var paused = isPaused;

  // Handle step navigation
  var handleNext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (onNext) {
      onNext();
    } else {
      nextStep();
    }
  }, [onNext, nextStep]);
  var handlePrevious = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (onPrevious) {
      onPrevious();
    } else {
      previousStep();
    }
  }, [onPrevious, previousStep]);
  var handlePause = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (onPause) {
      onPause();
    } else {
      pauseTutorial();
    }
  }, [onPause, pauseTutorial]);
  var handleResume = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (onResume) {
      onResume();
    } else {
      resumeTutorial();
    }
  }, [onResume, resumeTutorial]);
  var handleSkip = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (onSkip) {
      onSkip();
    } else {
      skipTutorial();
    }
  }, [onSkip, skipTutorial]);
  var handleComplete = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (onComplete) {
      onComplete();
    } else {
      completeTutorial();
    }
  }, [onComplete, completeTutorial]);

  // Show overlay for current step if it has overlay content
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (step && step.overlay) {
      setShowOverlay(true);
    } else {
      setShowOverlay(false);
    }
  }, [step]);

  // Don't render if no active tutorial
  if (!active || !step) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .FloatButton */ .ff, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .QuestionCircleOutlined */ .faO, null),
      tooltip: "Tutorial Help",
      onClick: function onClick() {
        return setShowHelp(!showHelp);
      },
      style: {
        right: 24,
        bottom: 24
      }
    }), showHelp && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ContextualHelp__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, null));
  }
  var progress = total > 0 ? (stepIndex + 1) / total * 100 : 0;
  var isLastStep = stepIndex >= total - 1;
  var isFirstStep = stepIndex <= 0;
  if (compact) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
      size: "small",
      style: {
        width: 280,
        position: 'fixed',
        top: 20,
        right: 20,
        zIndex: 1000
      },
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        strong: true
      }, (tutorial === null || tutorial === void 0 ? void 0 : tutorial.title) || 'Tutorial'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: "secondary"
      }, "(", stepIndex + 1, "/", total, ")")),
      extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
        type: "text",
        size: "small",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CloseOutlined */ .r$3, null),
        onClick: handleSkip
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Progress */ .ke, {
      percent: progress,
      size: "small"
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, null, step.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      size: "small",
      disabled: isFirstStep,
      onClick: handlePrevious
    }, "Previous"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      type: "primary",
      size: "small",
      onClick: isLastStep ? handleComplete : handleNext
    }, isLastStep ? 'Complete' : 'Next')))), showOverlay && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TutorialOverlay__WEBPACK_IMPORTED_MODULE_7__["default"], {
      step: step,
      onNext: handleNext,
      onPrevious: handlePrevious,
      onSkip: handleSkip
    }));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TutorialContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TutorialHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Title, {
    level: 4,
    className: "tutorial-title"
  }, (tutorial === null || tutorial === void 0 ? void 0 : tutorial.title) || 'Tutorial'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    className: "tutorial-subtitle"
  }, "Step ", stepIndex + 1, " of ", total, " \u2022 ", (tutorial === null || tutorial === void 0 ? void 0 : tutorial.category) || 'General')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TutorialContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TutorialProgress__WEBPACK_IMPORTED_MODULE_8__["default"], {
    current: stepIndex,
    total: total,
    percent: progress
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(StepContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "step-title"
  }, step.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    className: "step-description"
  }, step.description), step.tips && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: 12
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: '12px'
    }
  }, "\uD83D\uDCA1 Tip: ", step.tips))), (tutorial === null || tutorial === void 0 ? void 0 : tutorial.steps) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Steps */ .gj, {
    current: stepIndex,
    direction: "vertical",
    size: "small",
    style: {
      marginTop: 16
    }
  }, tutorial.steps.map(function (tutorialStep, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Step, {
      key: index,
      title: tutorialStep.title,
      description: index === stepIndex ? tutorialStep.description : null,
      icon: index < stepIndex ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckCircleOutlined */ .hWy, null) : undefined
    });
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TutorialControls, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: paused ? "Resume" : "Pause"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "text",
    icon: paused ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .PlayCircleOutlined */ .VgC, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .PauseCircleOutlined */ .Xcy, null),
    onClick: paused ? handleResume : handlePause
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: "Skip Tutorial"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CloseOutlined */ .r$3, null),
    onClick: handleSkip
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .StepBackwardOutlined */ .Y53, null),
    disabled: isFirstStep,
    onClick: handlePrevious
  }, "Previous"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "primary",
    icon: isLastStep ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckCircleOutlined */ .hWy, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .StepForwardOutlined */ .QES, null),
    onClick: isLastStep ? handleComplete : handleNext
  }, isLastStep ? 'Complete' : 'Next')))), showOverlay && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TutorialOverlay__WEBPACK_IMPORTED_MODULE_7__["default"], {
    step: step,
    onNext: handleNext,
    onPrevious: handlePrevious,
    onSkip: handleSkip,
    onComplete: isLastStep ? handleComplete : undefined
  }), showHelp && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ContextualHelp__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, null));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TutorialAssistant);

/***/ }),

/***/ 98835:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BH: () => (/* binding */ ANIMATION_DURATIONS),
/* harmony export */   Cy: () => (/* binding */ TUTORIAL_STEP_TYPES),
/* harmony export */   Ej: () => (/* binding */ TUTORIAL_CATEGORIES),
/* harmony export */   I$: () => (/* binding */ DEFAULT_TUTORIAL_PREFERENCES),
/* harmony export */   Mu: () => (/* binding */ Z_INDEX),
/* harmony export */   Vp: () => (/* binding */ TUTORIAL_SHORTCUTS),
/* harmony export */   d5: () => (/* binding */ STORAGE_KEYS),
/* harmony export */   e2: () => (/* binding */ createTutorialProgress),
/* harmony export */   ek: () => (/* binding */ HELP_CONTEXT_TYPES),
/* harmony export */   pS: () => (/* binding */ createHelpContext),
/* harmony export */   y1: () => (/* binding */ TUTORIAL_STATUS)
/* harmony export */ });
/* unused harmony exports createTutorialStep, createTutorial, TUTORIAL_EVENTS */
/**
 * Tutorial System Type Definitions and Constants
 * 
 * This file defines the data structures and constants used throughout
 * the tutorial system for type safety and consistency.
 */

// Tutorial Step Types
var TUTORIAL_STEP_TYPES = {
  HIGHLIGHT: 'highlight',
  // Highlight a specific element
  MODAL: 'modal',
  // Show modal with information
  TOOLTIP: 'tooltip',
  // Show tooltip near element
  OVERLAY: 'overlay',
  // Full screen overlay with content
  INTERACTIVE: 'interactive',
  // Require user interaction
  WAIT: 'wait' // Wait for user action or condition
};

// Tutorial Categories
var TUTORIAL_CATEGORIES = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced',
  FEATURE_SPECIFIC: 'feature-specific'
};

// Tutorial Status
var TUTORIAL_STATUS = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  SKIPPED: 'skipped'
};

// Help Context Types
var HELP_CONTEXT_TYPES = {
  COMPONENT_PALETTE: 'component_palette',
  PREVIEW_AREA: 'preview_area',
  PROPERTY_EDITOR: 'property_editor',
  DRAG_DROP: 'drag_drop',
  THEME_MANAGER: 'theme_manager',
  LAYOUT_DESIGNER: 'layout_designer',
  CODE_EXPORT: 'code_export',
  WEBSOCKET: 'websocket'
};

// Tutorial Step Schema
var createTutorialStep = function createTutorialStep(_ref) {
  var id = _ref.id,
    _ref$type = _ref.type,
    type = _ref$type === void 0 ? TUTORIAL_STEP_TYPES.HIGHLIGHT : _ref$type,
    title = _ref.title,
    content = _ref.content,
    _ref$targetSelector = _ref.targetSelector,
    targetSelector = _ref$targetSelector === void 0 ? null : _ref$targetSelector,
    _ref$position = _ref.position,
    position = _ref$position === void 0 ? 'bottom' : _ref$position,
    _ref$showSkip = _ref.showSkip,
    showSkip = _ref$showSkip === void 0 ? true : _ref$showSkip,
    _ref$showPrevious = _ref.showPrevious,
    showPrevious = _ref$showPrevious === void 0 ? true : _ref$showPrevious,
    _ref$showNext = _ref.showNext,
    showNext = _ref$showNext === void 0 ? true : _ref$showNext,
    _ref$autoAdvance = _ref.autoAdvance,
    autoAdvance = _ref$autoAdvance === void 0 ? false : _ref$autoAdvance,
    _ref$autoAdvanceDelay = _ref.autoAdvanceDelay,
    autoAdvanceDelay = _ref$autoAdvanceDelay === void 0 ? 0 : _ref$autoAdvanceDelay,
    _ref$requiredAction = _ref.requiredAction,
    requiredAction = _ref$requiredAction === void 0 ? null : _ref$requiredAction,
    _ref$validationFn = _ref.validationFn,
    validationFn = _ref$validationFn === void 0 ? null : _ref$validationFn,
    _ref$onEnter = _ref.onEnter,
    onEnter = _ref$onEnter === void 0 ? null : _ref$onEnter,
    _ref$onExit = _ref.onExit,
    onExit = _ref$onExit === void 0 ? null : _ref$onExit,
    _ref$customComponent = _ref.customComponent,
    customComponent = _ref$customComponent === void 0 ? null : _ref$customComponent,
    _ref$highlightPadding = _ref.highlightPadding,
    highlightPadding = _ref$highlightPadding === void 0 ? 8 : _ref$highlightPadding,
    _ref$highlightBorderR = _ref.highlightBorderRadius,
    highlightBorderRadius = _ref$highlightBorderR === void 0 ? 4 : _ref$highlightBorderR,
    _ref$zIndex = _ref.zIndex,
    zIndex = _ref$zIndex === void 0 ? 10000 : _ref$zIndex;
  return {
    id: id,
    type: type,
    title: title,
    content: content,
    targetSelector: targetSelector,
    position: position,
    showSkip: showSkip,
    showPrevious: showPrevious,
    showNext: showNext,
    autoAdvance: autoAdvance,
    autoAdvanceDelay: autoAdvanceDelay,
    requiredAction: requiredAction,
    validationFn: validationFn,
    onEnter: onEnter,
    onExit: onExit,
    customComponent: customComponent,
    highlightPadding: highlightPadding,
    highlightBorderRadius: highlightBorderRadius,
    zIndex: zIndex
  };
};

// Tutorial Schema
var createTutorial = function createTutorial(_ref2) {
  var id = _ref2.id,
    title = _ref2.title,
    description = _ref2.description,
    _ref2$category = _ref2.category,
    category = _ref2$category === void 0 ? TUTORIAL_CATEGORIES.BEGINNER : _ref2$category,
    _ref2$estimatedDurati = _ref2.estimatedDuration,
    estimatedDuration = _ref2$estimatedDurati === void 0 ? 5 : _ref2$estimatedDurati,
    _ref2$prerequisites = _ref2.prerequisites,
    prerequisites = _ref2$prerequisites === void 0 ? [] : _ref2$prerequisites,
    _ref2$tags = _ref2.tags,
    tags = _ref2$tags === void 0 ? [] : _ref2$tags,
    _ref2$steps = _ref2.steps,
    steps = _ref2$steps === void 0 ? [] : _ref2$steps,
    _ref2$onStart = _ref2.onStart,
    onStart = _ref2$onStart === void 0 ? null : _ref2$onStart,
    _ref2$onComplete = _ref2.onComplete,
    onComplete = _ref2$onComplete === void 0 ? null : _ref2$onComplete,
    _ref2$onSkip = _ref2.onSkip,
    onSkip = _ref2$onSkip === void 0 ? null : _ref2$onSkip,
    _ref2$icon = _ref2.icon,
    icon = _ref2$icon === void 0 ? null : _ref2$icon,
    _ref2$difficulty = _ref2.difficulty,
    difficulty = _ref2$difficulty === void 0 ? 1 : _ref2$difficulty,
    _ref2$isRequired = _ref2.isRequired,
    isRequired = _ref2$isRequired === void 0 ? false : _ref2$isRequired;
  return {
    id: id,
    title: title,
    description: description,
    category: category,
    estimatedDuration: estimatedDuration,
    prerequisites: prerequisites,
    tags: tags,
    steps: steps,
    onStart: onStart,
    onComplete: onComplete,
    onSkip: onSkip,
    icon: icon,
    difficulty: difficulty,
    isRequired: isRequired,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
};

// Tutorial Progress Schema
var createTutorialProgress = function createTutorialProgress(_ref3) {
  var tutorialId = _ref3.tutorialId,
    _ref3$userId = _ref3.userId,
    userId = _ref3$userId === void 0 ? 'anonymous' : _ref3$userId,
    _ref3$status = _ref3.status,
    status = _ref3$status === void 0 ? TUTORIAL_STATUS.NOT_STARTED : _ref3$status,
    _ref3$currentStepInde = _ref3.currentStepIndex,
    currentStepIndex = _ref3$currentStepInde === void 0 ? 0 : _ref3$currentStepInde,
    _ref3$completedSteps = _ref3.completedSteps,
    completedSteps = _ref3$completedSteps === void 0 ? [] : _ref3$completedSteps,
    _ref3$startedAt = _ref3.startedAt,
    startedAt = _ref3$startedAt === void 0 ? null : _ref3$startedAt,
    _ref3$completedAt = _ref3.completedAt,
    completedAt = _ref3$completedAt === void 0 ? null : _ref3$completedAt,
    _ref3$pausedAt = _ref3.pausedAt,
    pausedAt = _ref3$pausedAt === void 0 ? null : _ref3$pausedAt,
    _ref3$timeSpent = _ref3.timeSpent,
    timeSpent = _ref3$timeSpent === void 0 ? 0 : _ref3$timeSpent,
    _ref3$skippedSteps = _ref3.skippedSteps,
    skippedSteps = _ref3$skippedSteps === void 0 ? [] : _ref3$skippedSteps,
    _ref3$metadata = _ref3.metadata,
    metadata = _ref3$metadata === void 0 ? {} : _ref3$metadata;
  return {
    tutorialId: tutorialId,
    userId: userId,
    status: status,
    currentStepIndex: currentStepIndex,
    completedSteps: completedSteps,
    startedAt: startedAt,
    completedAt: completedAt,
    pausedAt: pausedAt,
    timeSpent: timeSpent,
    skippedSteps: skippedSteps,
    metadata: metadata,
    lastUpdated: new Date().toISOString()
  };
};

// Help Context Schema
var createHelpContext = function createHelpContext(_ref4) {
  var id = _ref4.id,
    type = _ref4.type,
    title = _ref4.title,
    content = _ref4.content,
    _ref4$triggers = _ref4.triggers,
    triggers = _ref4$triggers === void 0 ? [] : _ref4$triggers,
    _ref4$conditions = _ref4.conditions,
    conditions = _ref4$conditions === void 0 ? [] : _ref4$conditions,
    _ref4$priority = _ref4.priority,
    priority = _ref4$priority === void 0 ? 1 : _ref4$priority,
    _ref4$showOnce = _ref4.showOnce,
    showOnce = _ref4$showOnce === void 0 ? false : _ref4$showOnce,
    _ref4$relatedTutorial = _ref4.relatedTutorials,
    relatedTutorials = _ref4$relatedTutorial === void 0 ? [] : _ref4$relatedTutorial,
    _ref4$customComponent = _ref4.customComponent,
    customComponent = _ref4$customComponent === void 0 ? null : _ref4$customComponent;
  return {
    id: id,
    type: type,
    title: title,
    content: content,
    triggers: triggers,
    conditions: conditions,
    priority: priority,
    showOnce: showOnce,
    relatedTutorials: relatedTutorials,
    customComponent: customComponent,
    createdAt: new Date().toISOString()
  };
};

// Tutorial Event Types for analytics and tracking
var TUTORIAL_EVENTS = {
  TUTORIAL_STARTED: 'tutorial_started',
  TUTORIAL_COMPLETED: 'tutorial_completed',
  TUTORIAL_SKIPPED: 'tutorial_skipped',
  TUTORIAL_PAUSED: 'tutorial_paused',
  TUTORIAL_RESUMED: 'tutorial_resumed',
  STEP_STARTED: 'step_started',
  STEP_COMPLETED: 'step_completed',
  STEP_SKIPPED: 'step_skipped',
  HELP_REQUESTED: 'help_requested',
  HELP_DISMISSED: 'help_dismissed',
  CONTEXT_HELP_SHOWN: 'context_help_shown'
};

// Storage Keys
var STORAGE_KEYS = {
  TUTORIAL_PROGRESS: 'app_builder_tutorial_progress',
  TUTORIAL_PREFERENCES: 'app_builder_tutorial_preferences',
  HELP_CONTEXT_SHOWN: 'app_builder_help_context_shown',
  TUTORIAL_COMPLETION_BADGES: 'app_builder_tutorial_badges'
};

// Default Tutorial Preferences
var DEFAULT_TUTORIAL_PREFERENCES = {
  autoStartTutorials: false,
  showContextualHelp: true,
  showTooltips: true,
  enableSoundEffects: false,
  animationSpeed: 'normal',
  // 'slow', 'normal', 'fast'
  skipCompletedTutorials: true,
  showProgressIndicator: true,
  enableKeyboardShortcuts: true
};

// Keyboard Shortcuts
var TUTORIAL_SHORTCUTS = {
  NEXT_STEP: 'ArrowRight',
  PREVIOUS_STEP: 'ArrowLeft',
  SKIP_TUTORIAL: 'Escape',
  PAUSE_TUTORIAL: 'Space',
  SHOW_HELP: 'F1'
};

// Animation Durations (in milliseconds)
var ANIMATION_DURATIONS = {
  HIGHLIGHT_FADE_IN: 300,
  HIGHLIGHT_FADE_OUT: 200,
  TOOLTIP_SHOW: 200,
  TOOLTIP_HIDE: 150,
  MODAL_SHOW: 300,
  MODAL_HIDE: 200,
  OVERLAY_FADE: 400
};

// Z-Index Layers
var Z_INDEX = {
  TUTORIAL_OVERLAY: 10000,
  TUTORIAL_HIGHLIGHT: 10001,
  TUTORIAL_TOOLTIP: 10002,
  TUTORIAL_MODAL: 10003,
  TUTORIAL_CONTROLS: 10004
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  TUTORIAL_STEP_TYPES: TUTORIAL_STEP_TYPES,
  TUTORIAL_CATEGORIES: TUTORIAL_CATEGORIES,
  TUTORIAL_STATUS: TUTORIAL_STATUS,
  HELP_CONTEXT_TYPES: HELP_CONTEXT_TYPES,
  TUTORIAL_EVENTS: TUTORIAL_EVENTS,
  STORAGE_KEYS: STORAGE_KEYS,
  DEFAULT_TUTORIAL_PREFERENCES: DEFAULT_TUTORIAL_PREFERENCES,
  TUTORIAL_SHORTCUTS: TUTORIAL_SHORTCUTS,
  ANIMATION_DURATIONS: ANIMATION_DURATIONS,
  Z_INDEX: Z_INDEX,
  createTutorialStep: createTutorialStep,
  createTutorial: createTutorial,
  createTutorialProgress: createTutorialProgress,
  createHelpContext: createHelpContext
});

/***/ })

}]);