"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1177],{

/***/ 11177:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(82569);
/* harmony import */ var _ui_DarkModeToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57683);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);

var _templateObject, _templateObject2, _templateObject3, _templateObject4;





var Title = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Title,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Paragraph,
  Text = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Text;
var TestContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  padding: var(--spacing-lg);\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"])));
var TestCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-md);\n    transform: translateY(-2px);\n  }\n"])));
var ColorSwatch = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  display: inline-block;\n  width: 40px;\n  height: 40px;\n  border-radius: var(--border-radius-md);\n  margin-right: var(--spacing-sm);\n  border: 1px solid var(--color-border);\n  vertical-align: middle;\n"])));
var ThemeInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(["\n  background-color: var(--color-background-secondary);\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border-light);\n  margin: var(--spacing-md) 0;\n"])));
var DarkModeTest = function DarkModeTest() {
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_3__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    themeMode = _useEnhancedTheme.themeMode,
    colors = _useEnhancedTheme.colors,
    systemPrefersDark = _useEnhancedTheme.systemPrefersDark;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(TestContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(TestCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 2
  }, "Dark Mode Test"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Paragraph, null, "This component tests the dark mode functionality and theme switching."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    size: "large",
    wrap: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ui_DarkModeToggle__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    showDropdown: true
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ui_DarkModeToggle__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {
    showDropdown: false
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ThemeInfo, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 4
  }, "Current Theme Information"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("strong", null, "Theme Mode:"), " ", themeMode), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("strong", null, "Is Dark Mode:"), " ", isDarkMode ? 'Yes' : 'No'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("strong", null, "System Prefers Dark:"), " ", systemPrefersDark ? 'Yes' : 'No'))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 4
  }, "Color Palette"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ColorSwatch, {
    style: {
      backgroundColor: colors.primary
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, "Primary: ", colors.primary)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ColorSwatch, {
    style: {
      backgroundColor: colors.secondary
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, "Secondary: ", colors.secondary)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ColorSwatch, {
    style: {
      backgroundColor: colors.background
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, "Background: ", colors.background)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ColorSwatch, {
    style: {
      backgroundColor: colors.surface
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, "Surface: ", colors.surface)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(ColorSwatch, {
    style: {
      backgroundColor: colors.text
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, "Text: ", colors.text))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 4
  }, "Interactive Elements"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    wrap: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary"
  }, "Primary Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "default"
  }, "Default Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "dashed"
  }, "Dashed Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "text"
  }, "Text Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "link"
  }, "Link Button")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 4
  }, "Text Variations"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, {
    direction: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, null, "Default text color"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "secondary"
  }, "Secondary text color"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "success"
  }, "Success text color"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "warning"
  }, "Warning text color"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    type: "danger"
  }, "Danger text color"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Text, {
    disabled: true
  }, "Disabled text color"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(TestCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 3
  }, "Nested Card Example"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Paragraph, null, "This card demonstrates how nested components look in the current theme."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    type: "inner",
    title: "Inner Card"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Paragraph, null, "This is an inner card that should adapt to the theme as well."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Button */ .$n, {
    type: "primary",
    size: "small"
  }, "Inner Button"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DarkModeTest);

/***/ }),

/***/ 57683:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(16918);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(82569);


var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8;

// Optimized Ant Design imports for better tree-shaking





// Animations
var rotate = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* .keyframes */ .i7)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"])));
var fadeIn = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* .keyframes */ .i7)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n"])));

// Styled components
var ToggleContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var ToggleButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay)(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 1px solid var(--color-border);\n  background-color: var(--color-surface);\n  color: var(--color-text);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  /* Ensure WCAG AA contrast compliance */\n  &:hover, &:focus {\n    border-color: var(--color-primary);\n    color: var(--color-primary);\n    background-color: var(--color-background-secondary);\n    transform: scale(1.05);\n    box-shadow: var(--shadow-md);\n  }\n\n  &:focus-visible {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n\n  .anticon {\n    font-size: 18px;\n    animation: ", " 0.3s ease;\n\n    /* Ensure icon has sufficient contrast */\n    filter: contrast(1.1);\n  }\n\n  &.rotating .anticon {\n    animation: ", " 0.5s ease;\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n\n    &:hover, &:focus {\n      border-width: 3px;\n    }\n\n    .anticon {\n      filter: contrast(1.3);\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n\n    &:active {\n      transform: none;\n    }\n\n    .anticon {\n      animation: none;\n    }\n\n    &.rotating .anticon {\n      animation: none;\n    }\n  }\n"])), fadeIn, rotate);
var DropdownContent = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  min-width: 180px;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: 8px;\n  box-shadow: var(--shadow-lg);\n  padding: 4px;\n"])));
var ThemeOption = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-radius: 6px;\n  margin: 2px 0;\n  color: var(--color-text);\n\n  &:hover {\n    background-color: var(--color-background-secondary);\n    transform: translateX(2px);\n  }\n\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 1px;\n  }\n\n  &.active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-sm);\n  }\n\n  .option-content {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n  }\n\n  .anticon {\n    font-size: 16px;\n\n    /* Ensure icon contrast in active state */\n    filter: ", ";\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 1px solid var(--color-border);\n\n    &:hover {\n      border-color: var(--color-primary);\n    }\n\n    &.active {\n      border: 2px solid white;\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: background-color 0.2s ease;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"])), function (props) {
  var _props$className;
  return (_props$className = props.className) !== null && _props$className !== void 0 && _props$className.includes('active') ? 'none' : 'contrast(1.1)';
});
var ThemeLabel = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.span(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.2;\n"])));
var ThemeDescription = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  font-size: 12px;\n  color: ", ";\n  margin-top: 2px;\n  line-height: 1.2;\n\n  /* Ensure description text meets contrast requirements */\n  opacity: ", ";\n"])), function (props) {
  return props.active ? 'rgba(255, 255, 255, 0.8)' : 'var(--color-text-secondary)';
}, function (props) {
  return props.active ? 0.9 : 0.8;
});
var DarkModeToggle = function DarkModeToggle(_ref) {
  var _ref$showDropdown = _ref.showDropdown,
    showDropdown = _ref$showDropdown === void 0 ? true : _ref$showDropdown,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'default' : _ref$size;
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_6__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    themeMode = _useEnhancedTheme.themeMode,
    toggleDarkMode = _useEnhancedTheme.toggleDarkMode,
    setThemeMode = _useEnhancedTheme.setThemeMode,
    systemPrefersDark = _useEnhancedTheme.systemPrefersDark;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    isRotating = _useState2[0],
    setIsRotating = _useState2[1];
  var handleToggle = function handleToggle() {
    setIsRotating(true);
    toggleDarkMode();
    setTimeout(function () {
      return setIsRotating(false);
    }, 500);
  };
  var handleThemeChange = function handleThemeChange(mode) {
    setIsRotating(true);
    setThemeMode(mode);
    setTimeout(function () {
      return setIsRotating(false);
    }, 500);
  };
  var getIcon = function getIcon() {
    if (themeMode === 'system') {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw, null);
    }
    return isDarkMode ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MoonOutlined */ .IO1, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SunOutlined */ .qVE, null);
  };
  var getTooltipTitle = function getTooltipTitle() {
    switch (themeMode) {
      case 'light':
        return 'Light mode';
      case 'dark':
        return 'Dark mode';
      case 'system':
        return "System mode (".concat(systemPrefersDark ? 'dark' : 'light', ")");
      default:
        return 'Toggle theme';
    }
  };
  var themeOptions = [{
    key: 'light',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SunOutlined */ .qVE, null),
    label: 'Light',
    description: 'Light theme'
  }, {
    key: 'dark',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MoonOutlined */ .IO1, null),
    label: 'Dark',
    description: 'Dark theme'
  }, {
    key: 'system',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DesktopOutlined */ .zlw, null),
    label: 'System',
    description: 'Follow system preference'
  }];
  var dropdownMenu = {
    items: themeOptions.map(function (option) {
      return {
        key: option.key,
        label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeOption, {
          className: themeMode === option.key ? 'active' : '',
          onClick: function onClick() {
            return handleThemeChange(option.key);
          },
          role: "menuitem",
          tabIndex: 0,
          "aria-selected": themeMode === option.key,
          onKeyDown: function onKeyDown(e) {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleThemeChange(option.key);
            }
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
          className: "option-content"
        }, option.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeLabel, null, option.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ThemeDescription, {
          active: themeMode === option.key
        }, option.description))), themeMode === option.key && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .CheckOutlined */ .JIb, {
          "aria-label": "Selected"
        }))
      };
    })
  };
  if (!showDropdown) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      title: getTooltipTitle(),
      placement: "bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleButton, {
      type: "text",
      size: size,
      className: isRotating ? 'rotating' : '',
      onClick: handleToggle,
      "aria-label": "Switch to ".concat(isDarkMode ? 'light' : 'dark', " mode. Current theme: ").concat(getTooltipTitle()),
      "aria-pressed": isDarkMode,
      role: "switch"
    }, getIcon())));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Dropdown */ .ms, {
    menu: dropdownMenu,
    trigger: ['click'],
    placement: "bottomRight",
    arrow: true,
    dropdownRender: function dropdownRender(menu) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(DropdownContent, {
        role: "menu",
        "aria-label": "Theme selection menu"
      }, menu);
    },
    onOpenChange: function onOpenChange(open) {
      // Announce to screen readers when menu opens/closes
      if (open) {
        // Focus management for accessibility
        setTimeout(function () {
          var firstMenuItem = document.querySelector('[role="menuitem"]');
          if (firstMenuItem) {
            firstMenuItem.focus();
          }
        }, 100);
      }
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
    title: getTooltipTitle(),
    placement: "bottom"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(ToggleButton, {
    type: "text",
    size: size,
    className: isRotating ? 'rotating' : '',
    "aria-label": "Theme options menu. Current theme: ".concat(getTooltipTitle()),
    "aria-haspopup": "menu",
    "aria-expanded": "false",
    role: "button"
  }, getIcon()))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DarkModeToggle);

/***/ })

}]);