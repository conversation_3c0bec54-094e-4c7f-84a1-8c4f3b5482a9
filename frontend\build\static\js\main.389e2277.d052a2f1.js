"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6143],{

/***/ 12880:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Be: () => (/* binding */ refreshToken),
/* harmony export */   VM: () => (/* binding */ getUserProfile),
/* harmony export */   ec: () => (/* binding */ changePassword),
/* harmony export */   eg: () => (/* binding */ updateUserProfile),
/* harmony export */   gf: () => (/* binding */ getToken),
/* harmony export */   iD: () => (/* binding */ login),
/* harmony export */   kz: () => (/* binding */ register),
/* harmony export */   ri: () => (/* binding */ logout),
/* harmony export */   wR: () => (/* binding */ isAuthenticated),
/* harmony export */   wz: () => (/* binding */ getUser)
/* harmony export */ });
/* unused harmony exports setToken, getRefreshToken, setRefreshToken, setUser, clearAuth, requestPasswordReset, resetPassword */
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(28908);


/**
 * Authentication Utilities
 * 
 * This module provides utilities for user authentication.
 */



// Local storage keys
var TOKEN_KEY = 'app_auth_token';
var REFRESH_TOKEN_KEY = 'app_refresh_token';
var USER_KEY = 'app_user';

/**
 * Get authentication token
 * @returns {string|null} Authentication token
 */
var getToken = function getToken() {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * Set authentication token
 * @param {string} token - Authentication token
 */
var setToken = function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * Get refresh token
 * @returns {string|null} Refresh token
 */
var getRefreshToken = function getRefreshToken() {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * Set refresh token
 * @param {string} token - Refresh token
 */
var setRefreshToken = function setRefreshToken(token) {
  localStorage.setItem(REFRESH_TOKEN_KEY, token);
};

/**
 * Get user data
 * @returns {Object|null} User data
 */
var getUser = function getUser() {
  var userJson = localStorage.getItem(USER_KEY);
  if (userJson) {
    try {
      return JSON.parse(userJson);
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }
  return null;
};

/**
 * Set user data
 * @param {Object} user - User data
 */
var setUser = function setUser(user) {
  localStorage.setItem(USER_KEY, JSON.stringify(user));
};

/**
 * Clear authentication data
 */
var clearAuth = function clearAuth() {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem(USER_KEY);
};

/**
 * Check if user is authenticated
 * @returns {boolean} Whether the user is authenticated
 */
var isAuthenticated = function isAuthenticated() {
  return !!getToken();
};

/**
 * Login user
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Promise<Object>} Login result
 */
var login = /*#__PURE__*/function () {
  var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee(username, password) {
    var response, userResponse, _error$response, _t, _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 1;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.post('/auth/login/', {
            username: username,
            password: password
          });
        case 1:
          response = _context.sent;
          if (!response.access) {
            _context.next = 6;
            break;
          }
          // JWT response format
          setToken(response.access);
          if (response.refresh) {
            setRefreshToken(response.refresh);
          }

          // Get user profile after successful login
          _context.prev = 2;
          _context.next = 3;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.get('/auth/profile/');
        case 3:
          userResponse = _context.sent;
          if (userResponse) {
            setUser(userResponse);
          }
          _context.next = 5;
          break;
        case 4:
          _context.prev = 4;
          _t = _context["catch"](2);
          console.warn('Failed to fetch user profile:', _t);
        case 5:
          return _context.abrupt("return", {
            success: true,
            user: response.user || {
              username: username
            }
          });
        case 6:
          if (!response.token) {
            _context.next = 7;
            break;
          }
          // Token auth response format
          setToken(response.token);
          if (response.user) {
            setUser(response.user);
          }
          return _context.abrupt("return", {
            success: true,
            user: response.user
          });
        case 7:
          return _context.abrupt("return", {
            success: false,
            error: 'Invalid response from server'
          });
        case 8:
          _context.prev = 8;
          _t2 = _context["catch"](0);
          console.error('Login error:', _t2);
          return _context.abrupt("return", {
            success: false,
            error: ((_error$response = _t2.response) === null || _error$response === void 0 || (_error$response = _error$response.data) === null || _error$response === void 0 ? void 0 : _error$response.detail) || _t2.message || 'Login failed'
          });
        case 9:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 8], [2, 4]]);
  }));
  return function login(_x, _x2) {
    return _ref.apply(this, arguments);
  };
}();

/**
 * Register user
 * @param {Object} userData - User data
 * @returns {Promise<Object>} Registration result
 */
var register = /*#__PURE__*/function () {
  var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2(userData) {
    var response, userResponse, _error$response2, _error$response3, _t3, _t4;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 1;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.post('/auth/register/', userData);
        case 1:
          response = _context2.sent;
          if (!response.access) {
            _context2.next = 6;
            break;
          }
          // JWT response format
          setToken(response.access);
          if (response.refresh) {
            setRefreshToken(response.refresh);
          }

          // Get user profile after successful registration
          _context2.prev = 2;
          _context2.next = 3;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.get('/auth/profile/');
        case 3:
          userResponse = _context2.sent;
          if (userResponse) {
            setUser(userResponse);
          }
          _context2.next = 5;
          break;
        case 4:
          _context2.prev = 4;
          _t3 = _context2["catch"](2);
          console.warn('Failed to fetch user profile:', _t3);
        case 5:
          return _context2.abrupt("return", {
            success: true,
            user: response.user || {
              username: userData.username
            }
          });
        case 6:
          if (!response.token) {
            _context2.next = 7;
            break;
          }
          // Token auth response format
          setToken(response.token);
          if (response.user) {
            setUser(response.user);
          }
          return _context2.abrupt("return", {
            success: true,
            user: response.user
          });
        case 7:
          if (!response.success) {
            _context2.next = 8;
            break;
          }
          return _context2.abrupt("return", {
            success: true,
            user: response.user
          });
        case 8:
          return _context2.abrupt("return", {
            success: false,
            error: response.error || 'Registration failed'
          });
        case 9:
          _context2.prev = 9;
          _t4 = _context2["catch"](0);
          console.error('Registration error:', _t4);
          return _context2.abrupt("return", {
            success: false,
            error: ((_error$response2 = _t4.response) === null || _error$response2 === void 0 || (_error$response2 = _error$response2.data) === null || _error$response2 === void 0 ? void 0 : _error$response2.detail) || ((_error$response3 = _t4.response) === null || _error$response3 === void 0 || (_error$response3 = _error$response3.data) === null || _error$response3 === void 0 ? void 0 : _error$response3.message) || _t4.message || 'Registration failed'
          });
        case 10:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 9], [2, 4]]);
  }));
  return function register(_x3) {
    return _ref2.apply(this, arguments);
  };
}();

/**
 * Logout user
 * @returns {Promise<Object>} Logout result
 */
var logout = /*#__PURE__*/function () {
  var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee3() {
    var _t5;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 1;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.post('/auth/logout');
        case 1:
          _context3.next = 3;
          break;
        case 2:
          _context3.prev = 2;
          _t5 = _context3["catch"](0);
          console.warn('Logout notification failed:', _t5);
        case 3:
          _context3.prev = 3;
          // Clear authentication data
          clearAuth();
          return _context3.finish(3);
        case 4:
          return _context3.abrupt("return", {
            success: true
          });
        case 5:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 2, 3, 4]]);
  }));
  return function logout() {
    return _ref3.apply(this, arguments);
  };
}();

/**
 * Refresh authentication token
 * @returns {Promise<string|null>} New authentication token
 */
var refreshToken = /*#__PURE__*/function () {
  var _ref4 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee4() {
    var refreshTokenValue, response, _t6;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.prev = 0;
          refreshTokenValue = getRefreshToken();
          if (refreshTokenValue) {
            _context4.next = 1;
            break;
          }
          throw new Error('No refresh token available');
        case 1:
          _context4.next = 2;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.post('/auth/token/refresh/', {
            refresh: refreshTokenValue
          });
        case 2:
          response = _context4.sent;
          if (!response.access) {
            _context4.next = 3;
            break;
          }
          setToken(response.access);
          if (response.refresh) {
            setRefreshToken(response.refresh);
          }
          return _context4.abrupt("return", response.access);
        case 3:
          if (!response.token) {
            _context4.next = 4;
            break;
          }
          // Fallback to token auth format
          setToken(response.token);
          if (response.refreshToken) {
            setRefreshToken(response.refreshToken);
          }
          return _context4.abrupt("return", response.token);
        case 4:
          return _context4.abrupt("return", null);
        case 5:
          _context4.prev = 5;
          _t6 = _context4["catch"](0);
          console.error('Token refresh error:', _t6);

          // Clear authentication data on refresh failure
          clearAuth();
          return _context4.abrupt("return", null);
        case 6:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[0, 5]]);
  }));
  return function refreshToken() {
    return _ref4.apply(this, arguments);
  };
}();

/**
 * Get user profile
 * @returns {Promise<Object>} User profile
 */
var getUserProfile = /*#__PURE__*/function () {
  var _ref5 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee5() {
    var _t7;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.prev = 0;
          _context5.next = 1;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.get('/auth/profile');
        case 1:
          return _context5.abrupt("return", _context5.sent);
        case 2:
          _context5.prev = 2;
          _t7 = _context5["catch"](0);
          console.error('Get user profile error:', _t7);
          return _context5.abrupt("return", null);
        case 3:
        case "end":
          return _context5.stop();
      }
    }, _callee5, null, [[0, 2]]);
  }));
  return function getUserProfile() {
    return _ref5.apply(this, arguments);
  };
}();

/**
 * Update user profile
 * @param {Object} profileData - Profile data
 * @returns {Promise<Object>} Update result
 */
var updateUserProfile = /*#__PURE__*/function () {
  var _ref6 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee6(profileData) {
    var response, _t8;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.prev = 0;
          _context6.next = 1;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.put('/auth/profile', profileData);
        case 1:
          response = _context6.sent;
          if (!response.user) {
            _context6.next = 2;
            break;
          }
          setUser(response.user);
          return _context6.abrupt("return", {
            success: true,
            user: response.user
          });
        case 2:
          return _context6.abrupt("return", {
            success: false,
            error: 'Invalid response from server'
          });
        case 3:
          _context6.prev = 3;
          _t8 = _context6["catch"](0);
          console.error('Update profile error:', _t8);
          return _context6.abrupt("return", {
            success: false,
            error: _t8.message || 'Update profile failed'
          });
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6, null, [[0, 3]]);
  }));
  return function updateUserProfile(_x4) {
    return _ref6.apply(this, arguments);
  };
}();

/**
 * Change password
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Change result
 */
var changePassword = /*#__PURE__*/function () {
  var _ref7 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee7(currentPassword, newPassword) {
    var response, _t9;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 1;
          return _services_ApiClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.post('/auth/change-password', {
            currentPassword: currentPassword,
            newPassword: newPassword
          });
        case 1:
          response = _context7.sent;
          return _context7.abrupt("return", {
            success: response.success,
            error: response.error
          });
        case 2:
          _context7.prev = 2;
          _t9 = _context7["catch"](0);
          console.error('Change password error:', _t9);
          return _context7.abrupt("return", {
            success: false,
            error: _t9.message || 'Change password failed'
          });
        case 3:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 2]]);
  }));
  return function changePassword(_x5, _x6) {
    return _ref7.apply(this, arguments);
  };
}();

/**
 * Request password reset
 * @param {string} email - User email
 * @returns {Promise<Object>} Request result
 */
var requestPasswordReset = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref8 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee8(email) {
    var response, _t0;
    return _regeneratorRuntime.wrap(function (_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 1;
          return apiClient.post('/auth/reset-password', {
            email: email
          });
        case 1:
          response = _context8.sent;
          return _context8.abrupt("return", {
            success: response.success,
            error: response.error
          });
        case 2:
          _context8.prev = 2;
          _t0 = _context8["catch"](0);
          console.error('Password reset request error:', _t0);
          return _context8.abrupt("return", {
            success: false,
            error: _t0.message || 'Password reset request failed'
          });
        case 3:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 2]]);
  }));
  return function requestPasswordReset(_x7) {
    return _ref8.apply(this, arguments);
  };
}()));

/**
 * Reset password
 * @param {string} token - Reset token
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Reset result
 */
var resetPassword = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref9 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee9(token, newPassword) {
    var response, _t1;
    return _regeneratorRuntime.wrap(function (_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 1;
          return apiClient.post('/auth/reset-password/confirm', {
            token: token,
            newPassword: newPassword
          });
        case 1:
          response = _context9.sent;
          return _context9.abrupt("return", {
            success: response.success,
            error: response.error
          });
        case 2:
          _context9.prev = 2;
          _t1 = _context9["catch"](0);
          console.error('Password reset error:', _t1);
          return _context9.abrupt("return", {
            success: false,
            error: _t1.message || 'Password reset failed'
          });
        case 3:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[0, 2]]);
  }));
  return function resetPassword(_x8, _x9) {
    return _ref9.apply(this, arguments);
  };
}()));

/***/ }),

/***/ 17648:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export initErrorTracking */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/**
 * Error Tracking System
 * 
 * This utility provides comprehensive error tracking and reporting functionality.
 * It captures unhandled errors, console errors, network errors, and more.
 */

// Configuration
var config = {
  // Whether error tracking is enabled
  enabled: true,
  // Sampling rate (0.0 to 1.0) - what percentage of errors to track
  samplingRate: 1.0,
  // Maximum number of errors to store
  errorLimit: 100,
  // Maximum number of breadcrumbs to store
  breadcrumbLimit: 50,
  // Errors to ignore (regexes)
  ignoredErrors: [/ResizeObserver loop limit exceeded/, /Loading chunk \d+ failed/, /Network request failed/, /Script error/, /Extension context invalidated/, /Failed to report error/, /Error reporting failed/, /TypeError: Failed to fetch/, /TypeError: NetworkError when attempting to fetch resource/, /AbortError/, /Request aborted/, /Request timed out/, /Load failed/, /__REACT_DEVTOOLS_GLOBAL_HOOK__/, /Cannot set property __REACT_DEVTOOLS_GLOBAL_HOOK__/, /which has only a getter/],
  // Endpoint to report errors to
  reportingEndpoint: '/api/errors',
  // Whether to log errors to console
  logToConsole: true,
  // Whether to capture console errors
  captureConsoleErrors: true,
  // Whether to capture network errors
  captureNetworkErrors: true,
  // Whether to capture unhandled rejections
  captureUnhandledRejections: true,
  // Whether to capture breadcrumbs
  captureBreadcrumbs: true
};

// Circuit breaker for error reporting
var circuitBreaker = {
  state: 'CLOSED',
  // CLOSED, OPEN, HALF_OPEN
  failureCount: 0,
  lastFailureTime: null,
  failureThreshold: 5,
  // Open circuit after 5 consecutive failures
  timeout: 60000,
  // 1 minute timeout before trying again
  successThreshold: 2 // Number of successes needed to close circuit
};

// Error storage
var errorStore = {
  errors: [],
  breadcrumbs: [],
  sessionId: generateSessionId(),
  startTime: new Date().toISOString(),
  reportingQueue: [],
  // Queue for failed reports
  lastReportAttempt: null,
  reportingInProgress: false
};

/**
 * Generate a unique session ID
 * 
 * @returns {string} - A unique session ID
 */
function generateSessionId() {
  return "".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
}

/**
 * Initialize the error tracking system
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} - The error tracking API
 */
function initErrorTracking() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  // Merge options with default config
  Object.assign(config, options);
  if (!config.enabled) {
    console.log('Error tracking is disabled');
    return createPublicApi();
  }

  // Set up global error handlers
  setupErrorHandlers();

  // Log initialization
  console.log('Error tracking initialized');

  // Return the public API
  return createPublicApi();
}

/**
 * Set up global error handlers
 */
function setupErrorHandlers() {
  // Handle unhandled errors
  window.addEventListener('error', handleGlobalError);

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', handleUnhandledRejection);

  // Capture console errors
  if (config.captureConsoleErrors) {
    setupConsoleCapture();
  }

  // Capture network errors
  if (config.captureNetworkErrors) {
    setupNetworkCapture();
  }

  // Capture breadcrumbs
  if (config.captureBreadcrumbs) {
    setupBreadcrumbCapture();
  }
}

/**
 * Handle global errors
 * 
 * @param {ErrorEvent} event - The error event
 */
function handleGlobalError(event) {
  // Prevent default browser error handling
  event.preventDefault();

  // Track the error
  _trackError({
    type: 'uncaught_error',
    message: event.message || 'Unknown error',
    stack: event.error ? event.error.stack : null,
    source: event.filename,
    line: event.lineno,
    column: event.colno,
    timestamp: new Date().toISOString()
  });

  // Log to console if enabled
  if (config.logToConsole) {
    console.error('Uncaught error:', event.message);
  }
}

/**
 * Handle unhandled promise rejections
 * 
 * @param {PromiseRejectionEvent} event - The rejection event
 */
function handleUnhandledRejection(event) {
  // Prevent default browser error handling
  event.preventDefault();

  // Get error details
  var error = event.reason;
  var message = error instanceof Error ? error.message : String(error);
  var stack = error instanceof Error ? error.stack : null;

  // Track the error
  _trackError({
    type: 'unhandled_rejection',
    message: message || 'Unhandled promise rejection',
    stack: stack,
    timestamp: new Date().toISOString()
  });

  // Log to console if enabled
  if (config.logToConsole) {
    console.error('Unhandled rejection:', message);
  }
}

/**
 * Set up console error capture
 */
function setupConsoleCapture() {
  // Save original console methods
  var originalConsoleError = console.error;
  var originalConsoleWarn = console.warn;

  // Override console.error
  console.error = function () {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    // Call original method
    originalConsoleError.apply(console, args);

    // Track the error
    var message = args.map(function (arg) {
      return typeof arg === 'string' ? arg : JSON.stringify(arg);
    }).join(' ');
    _trackError({
      type: 'console_error',
      message: message,
      timestamp: new Date().toISOString()
    });
  };

  // Override console.warn
  console.warn = function () {
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    // Call original method
    originalConsoleWarn.apply(console, args);

    // Add breadcrumb
    if (config.captureBreadcrumbs) {
      _addBreadcrumb({
        type: 'console_warn',
        message: args.map(function (arg) {
          return typeof arg === 'string' ? arg : JSON.stringify(arg);
        }).join(' '),
        timestamp: new Date().toISOString()
      });
    }
  };
}

/**
 * Set up network error capture
 */
function setupNetworkCapture() {
  // Store original fetch for error reporting
  if (!window._originalFetch) {
    window._originalFetch = window.fetch;
  }
  var originalFetch = window._originalFetch;

  // Override fetch
  window.fetch = /*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
    var _len3,
      args,
      _key3,
      response,
      _args$,
      _args$2,
      _args$3,
      _args = arguments,
      _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          for (_len3 = _args.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
            args[_key3] = _args[_key3];
          }
          _context.prev = 1;
          _context.next = 2;
          return originalFetch.apply(window, args);
        case 2:
          response = _context.sent;
          // Add breadcrumb for successful requests
          if (config.captureBreadcrumbs) {
            _addBreadcrumb({
              type: 'network',
              category: 'fetch',
              data: {
                url: typeof args[0] === 'string' ? args[0] : args[0].url,
                method: ((_args$ = args[1]) === null || _args$ === void 0 ? void 0 : _args$.method) || 'GET',
                status: response.status
              },
              timestamp: new Date().toISOString()
            });
          }

          // Track error responses
          if (!response.ok) {
            _trackError({
              type: 'network_error',
              message: "Fetch error: ".concat(response.status, " ").concat(response.statusText),
              data: {
                url: typeof args[0] === 'string' ? args[0] : args[0].url,
                method: ((_args$2 = args[1]) === null || _args$2 === void 0 ? void 0 : _args$2.method) || 'GET',
                status: response.status,
                statusText: response.statusText
              },
              timestamp: new Date().toISOString()
            });
          }
          return _context.abrupt("return", response);
        case 3:
          _context.prev = 3;
          _t = _context["catch"](1);
          // Track network errors
          _trackError({
            type: 'network_error',
            message: "Fetch failed: ".concat(_t.message),
            stack: _t.stack,
            data: {
              url: typeof args[0] === 'string' ? args[0] : (_args$3 = args[0]) === null || _args$3 === void 0 ? void 0 : _args$3.url
            },
            timestamp: new Date().toISOString()
          });
          throw _t;
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 3]]);
  }));

  // Override XMLHttpRequest
  var originalXhrOpen = XMLHttpRequest.prototype.open;
  var originalXhrSend = XMLHttpRequest.prototype.send;
  XMLHttpRequest.prototype.open = function (method, url) {
    this._errorTracking = {
      method: method,
      url: url
    };
    return originalXhrOpen.apply(this, arguments);
  };
  XMLHttpRequest.prototype.send = function () {
    // Add event listeners
    this.addEventListener('load', function () {
      // Add breadcrumb for successful requests
      if (config.captureBreadcrumbs) {
        var _this$_errorTracking, _this$_errorTracking2;
        _addBreadcrumb({
          type: 'network',
          category: 'xhr',
          data: {
            url: (_this$_errorTracking = this._errorTracking) === null || _this$_errorTracking === void 0 ? void 0 : _this$_errorTracking.url,
            method: (_this$_errorTracking2 = this._errorTracking) === null || _this$_errorTracking2 === void 0 ? void 0 : _this$_errorTracking2.method,
            status: this.status
          },
          timestamp: new Date().toISOString()
        });
      }

      // Track error responses
      if (this.status >= 400) {
        var _this$_errorTracking3, _this$_errorTracking4;
        _trackError({
          type: 'network_error',
          message: "XHR error: ".concat(this.status, " ").concat(this.statusText),
          data: {
            url: (_this$_errorTracking3 = this._errorTracking) === null || _this$_errorTracking3 === void 0 ? void 0 : _this$_errorTracking3.url,
            method: (_this$_errorTracking4 = this._errorTracking) === null || _this$_errorTracking4 === void 0 ? void 0 : _this$_errorTracking4.method,
            status: this.status,
            statusText: this.statusText
          },
          timestamp: new Date().toISOString()
        });
      }
    });
    this.addEventListener('error', function () {
      var _this$_errorTracking5, _this$_errorTracking6;
      // Track network errors
      _trackError({
        type: 'network_error',
        message: 'XHR failed',
        data: {
          url: (_this$_errorTracking5 = this._errorTracking) === null || _this$_errorTracking5 === void 0 ? void 0 : _this$_errorTracking5.url,
          method: (_this$_errorTracking6 = this._errorTracking) === null || _this$_errorTracking6 === void 0 ? void 0 : _this$_errorTracking6.method
        },
        timestamp: new Date().toISOString()
      });
    });
    return originalXhrSend.apply(this, arguments);
  };
}

/**
 * Set up breadcrumb capture
 */
function setupBreadcrumbCapture() {
  // Capture clicks
  document.addEventListener('click', function (event) {
    var _element$innerText;
    // Get the clicked element
    var element = event.target;

    // Get element details
    var tagName = element.tagName.toLowerCase();
    var id = element.id ? "#".concat(element.id) : '';
    var classes = Array.from(element.classList).map(function (c) {
      return ".".concat(c);
    }).join('');
    var text = (_element$innerText = element.innerText) === null || _element$innerText === void 0 ? void 0 : _element$innerText.substring(0, 50);

    // Add breadcrumb
    _addBreadcrumb({
      type: 'user',
      category: 'click',
      data: {
        element: "".concat(tagName).concat(id).concat(classes),
        text: text
      },
      timestamp: new Date().toISOString()
    });
  });

  // Capture navigation
  window.addEventListener('popstate', function () {
    _addBreadcrumb({
      type: 'navigation',
      data: {
        from: document.referrer,
        to: window.location.href
      },
      timestamp: new Date().toISOString()
    });
  });
}

/**
 * Track an error
 * 
 * @param {Object} error - The error to track
 */
function _trackError(error) {
  // Check if error tracking is enabled
  if (!config.enabled) {
    return;
  }

  // Apply sampling rate
  if (Math.random() > config.samplingRate) {
    return;
  }

  // Check if error should be ignored
  if (shouldIgnoreError(error)) {
    return;
  }

  // Add session information
  error.sessionId = errorStore.sessionId;

  // Add user agent
  error.userAgent = navigator.userAgent;

  // Add URL
  error.url = window.location.href;

  // Add breadcrumbs
  error.breadcrumbs = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(errorStore.breadcrumbs);

  // Add to error store
  errorStore.errors.push(error);

  // Limit the number of stored errors
  if (errorStore.errors.length > config.errorLimit) {
    errorStore.errors.shift();
  }

  // Report the error
  reportError(error);
}

/**
 * Check if an error should be ignored
 * 
 * @param {Object} error - The error to check
 * @returns {boolean} - Whether the error should be ignored
 */
function shouldIgnoreError(error) {
  // Check against ignored errors
  return config.ignoredErrors.some(function (pattern) {
    if (pattern instanceof RegExp) {
      return pattern.test(error.message);
    }
    return error.message.includes(pattern);
  });
}

/**
 * Add a breadcrumb
 * 
 * @param {Object} breadcrumb - The breadcrumb to add
 */
function _addBreadcrumb(breadcrumb) {
  // Check if breadcrumb tracking is enabled
  if (!config.captureBreadcrumbs) {
    return;
  }

  // Add to breadcrumb store
  errorStore.breadcrumbs.push(breadcrumb);

  // Limit the number of stored breadcrumbs
  if (errorStore.breadcrumbs.length > config.breadcrumbLimit) {
    errorStore.breadcrumbs.shift();
  }
}

/**
 * Check if circuit breaker allows error reporting
 * @returns {boolean} - Whether reporting is allowed
 */
function canReportError() {
  var now = Date.now();
  switch (circuitBreaker.state) {
    case 'OPEN':
      // Check if timeout has passed
      if (now - circuitBreaker.lastFailureTime >= circuitBreaker.timeout) {
        circuitBreaker.state = 'HALF_OPEN';
        return true;
      }
      return false;
    case 'HALF_OPEN':
    case 'CLOSED':
      return true;
    default:
      return true;
  }
}

/**
 * Handle successful error reporting
 */
function onReportSuccess() {
  if (circuitBreaker.state === 'HALF_OPEN') {
    circuitBreaker.successThreshold--;
    if (circuitBreaker.successThreshold <= 0) {
      circuitBreaker.state = 'CLOSED';
      circuitBreaker.failureCount = 0;
      circuitBreaker.successThreshold = 2; // Reset
    }
  } else if (circuitBreaker.state === 'CLOSED') {
    circuitBreaker.failureCount = 0;
  }
}

/**
 * Handle failed error reporting
 */
function onReportFailure() {
  circuitBreaker.failureCount++;
  circuitBreaker.lastFailureTime = Date.now();
  if (circuitBreaker.failureCount >= circuitBreaker.failureThreshold) {
    circuitBreaker.state = 'OPEN';
  }
}

/**
 * Store error locally as fallback when reporting fails
 * @param {Object} error - The error to store
 */
function storeErrorLocally(error) {
  try {
    var key = 'errorTracker_failedReports';
    var stored = localStorage.getItem(key);
    var failedReports = stored ? JSON.parse(stored) : [];

    // Add timestamp and limit storage
    failedReports.push(_objectSpread(_objectSpread({}, error), {}, {
      storedAt: new Date().toISOString()
    }));

    // Keep only last 50 failed reports
    if (failedReports.length > 50) {
      failedReports.splice(0, failedReports.length - 50);
    }
    localStorage.setItem(key, JSON.stringify(failedReports));
  } catch (storageError) {
    // Ignore storage errors
    if (config.logToConsole && "production" === 'development') {}
  }
}

/**
 * Report an error to the server with circuit breaker and retry logic
 *
 * @param {Object} error - The error to report
 * @param {number} retryCount - Current retry attempt (default: 0)
 * @param {number} maxRetries - Maximum number of retries (default: 3)
 */
function reportError(error) {
  var _error$message, _error$url;
  var retryCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
  var maxRetries = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 3;
  // Check if reporting is enabled
  if (!config.reportingEndpoint) {
    return;
  }

  // Check circuit breaker
  if (!canReportError()) {
    // Add to queue for later retry
    errorStore.reportingQueue.push(error);
    return;
  }

  // Prevent reporting errors about error reporting to avoid infinite loops
  if (error.type === 'error_reporting_failure' || (_error$message = error.message) !== null && _error$message !== void 0 && _error$message.includes('Failed to report error') || (_error$url = error.url) !== null && _error$url !== void 0 && _error$url.includes(config.reportingEndpoint)) {
    return;
  }

  // Prevent concurrent reporting
  if (errorStore.reportingInProgress) {
    errorStore.reportingQueue.push(error);
    return;
  }
  errorStore.reportingInProgress = true;
  errorStore.lastReportAttempt = Date.now();

  // Calculate delay for exponential backoff
  var delay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Max 30 seconds

  var attemptReport = function attemptReport() {
    // Use original fetch to avoid infinite loops with network error tracking
    var originalFetch = window._originalFetch || window.fetch;
    originalFetch(config.reportingEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(error),
      keepalive: true
    }).then(function (response) {
      if (response.ok) {
        onReportSuccess();

        // Process queued errors if circuit is now closed
        if (circuitBreaker.state === 'CLOSED' && errorStore.reportingQueue.length > 0) {
          var queuedError = errorStore.reportingQueue.shift();
          setTimeout(function () {
            return reportError(queuedError);
          }, 100);
        }
      } else {
        throw new Error("HTTP ".concat(response.status, ": ").concat(response.statusText));
      }
    })["catch"](function (err) {
      onReportFailure();

      // Retry with exponential backoff if we haven't exceeded max retries
      if (retryCount < maxRetries) {
        setTimeout(function () {
          reportError(error, retryCount + 1, maxRetries);
        }, delay);
      } else {
        // Store in local storage as fallback
        storeErrorLocally(error);

        // Log final failure only in development
        if (config.logToConsole && "production" === 'development') {}
      }
    })["finally"](function () {
      errorStore.reportingInProgress = false;
    });
  };

  // Apply initial delay for retries
  if (retryCount > 0) {
    setTimeout(attemptReport, delay);
  } else {
    attemptReport();
  }
}

/**
 * Create the public API
 * 
 * @returns {Object} - The public API
 */
function createPublicApi() {
  return {
    /**
     * Track an error manually
     * 
     * @param {Error|string} error - The error to track
     * @param {Object} additionalData - Additional data to include
     */
    trackError: function trackError(error) {
      var additionalData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      // Convert error to the right format
      var errorObj = error instanceof Error ? _objectSpread(_objectSpread({
        type: 'manual',
        message: error.message,
        stack: error.stack
      }, additionalData), {}, {
        timestamp: new Date().toISOString()
      }) : _objectSpread(_objectSpread({
        type: 'manual',
        message: String(error)
      }, additionalData), {}, {
        timestamp: new Date().toISOString()
      });

      // Track the error
      _trackError(errorObj);
    },
    /**
     * Add a breadcrumb manually
     * 
     * @param {string} message - The breadcrumb message
     * @param {string} category - The breadcrumb category
     * @param {Object} data - Additional data to include
     */
    addBreadcrumb: function addBreadcrumb(message) {
      var category = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'manual';
      var data = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      _addBreadcrumb({
        type: 'manual',
        category: category,
        message: message,
        data: data,
        timestamp: new Date().toISOString()
      });
    },
    /**
     * Get all tracked errors
     * 
     * @returns {Array} - The tracked errors
     */
    getErrors: function getErrors() {
      return (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(errorStore.errors);
    },
    /**
     * Get all breadcrumbs
     * 
     * @returns {Array} - The breadcrumbs
     */
    getBreadcrumbs: function getBreadcrumbs() {
      return (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(errorStore.breadcrumbs);
    },
    /**
     * Clear all tracked errors
     */
    clearErrors: function clearErrors() {
      errorStore.errors = [];
    },
    /**
     * Clear all breadcrumbs
     */
    clearBreadcrumbs: function clearBreadcrumbs() {
      errorStore.breadcrumbs = [];
    },
    /**
     * Get the current configuration
     * 
     * @returns {Object} - The current configuration
     */
    getConfig: function getConfig() {
      return _objectSpread({}, config);
    },
    /**
     * Update the configuration
     *
     * @param {Object} options - The new configuration options
     */
    updateConfig: function updateConfig(options) {
      Object.assign(config, options);
    },
    /**
     * Get circuit breaker status
     *
     * @returns {Object} - Circuit breaker status
     */
    getCircuitBreakerStatus: function getCircuitBreakerStatus() {
      return {
        state: circuitBreaker.state,
        failureCount: circuitBreaker.failureCount,
        lastFailureTime: circuitBreaker.lastFailureTime,
        queueLength: errorStore.reportingQueue.length
      };
    },
    /**
     * Manually retry queued errors
     *
     * @returns {Promise<void>} - Promise that resolves when retry is complete
     */
    retryQueuedErrors: function retryQueuedErrors() {
      return new Promise(function (resolve) {
        if (errorStore.reportingQueue.length === 0) {
          resolve();
          return;
        }

        // Reset circuit breaker to allow retries
        if (circuitBreaker.state === 'OPEN') {
          circuitBreaker.state = 'HALF_OPEN';
        }

        // Process one queued error
        var queuedError = errorStore.reportingQueue.shift();
        reportError(queuedError);

        // Wait a bit and resolve
        setTimeout(resolve, 1000);
      });
    },
    /**
     * Get locally stored failed reports
     *
     * @returns {Array} - Array of failed reports from localStorage
     */
    getLocallyStoredErrors: function getLocallyStoredErrors() {
      try {
        var key = 'errorTracker_failedReports';
        var stored = localStorage.getItem(key);
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        return [];
      }
    },
    /**
     * Clear locally stored failed reports
     */
    clearLocallyStoredErrors: function clearLocallyStoredErrors() {
      try {
        localStorage.removeItem('errorTracker_failedReports');
      } catch (error) {
        // Ignore storage errors
      }
    }
  };
}

// Create and export the default instance
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initErrorTracking());

/***/ }),

/***/ 56666:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   lf: () => (/* binding */ disableMockWebSocketServer)
/* harmony export */ });
/* unused harmony export initMockWebSocketServer */
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(56822);
/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(53954);
/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85501);
/* harmony import */ var _babel_runtime_helpers_wrapNativeSuper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(73437);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(64467);







function _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
/**
 * Mock WebSocket Server
 * 
 * This module provides a mock WebSocket server for development and testing.
 * It simulates WebSocket connections and messages when the backend is not available.
 */
// Mock WebSocket class
var MockWebSocket = /*#__PURE__*/function (_EventTarget) {
  function MockWebSocket(url) {
    var _this;
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this, MockWebSocket);
    _this = _callSuper(this, MockWebSocket);
    _this.url = url;
    _this.readyState = MockWebSocket.CONNECTING;
    _this.protocol = '';
    _this.extensions = '';
    _this.bufferedAmount = 0;
    _this.binaryType = 'blob';

    // Simulate connection delay
    setTimeout(function () {
      _this.readyState = MockWebSocket.OPEN;

      // Dispatch open event
      var openEvent = new Event('open');
      _this.dispatchEvent(openEvent);
      if (typeof _this.onopen === 'function') {
        _this.onopen(openEvent);
      }
      console.log("Mock WebSocket connected to ".concat(url));
    }, 500);
    return _this;
  }

  // Send method
  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(MockWebSocket, _EventTarget);
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(MockWebSocket, [{
    key: "send",
    value: function send(data) {
      var _this2 = this;
      if (this.readyState !== MockWebSocket.OPEN) {
        throw new Error('WebSocket is not open');
      }
      console.log("Mock WebSocket sending data:", data);

      // Parse the data
      var parsedData;
      try {
        parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      } catch (error) {
        parsedData = data;
      }

      // Handle different message types
      setTimeout(function () {
        if (parsedData.type === 'ping') {
          _this2._handlePing(parsedData);
        } else if (parsedData.type === 'request_app_data') {
          _this2._handleAppDataRequest(parsedData);
        } else {
          _this2._handleGenericMessage(parsedData);
        }
      }, 200);
    }

    // Close method
  }, {
    key: "close",
    value: function close() {
      var _this3 = this;
      var code = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1000;
      var reason = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      if (this.readyState === MockWebSocket.CLOSED) {
        return;
      }
      this.readyState = MockWebSocket.CLOSING;

      // Simulate closing delay
      setTimeout(function () {
        _this3.readyState = MockWebSocket.CLOSED;

        // Dispatch close event
        var closeEvent = new CloseEvent('close', {
          code: code,
          reason: reason,
          wasClean: code === 1000
        });
        _this3.dispatchEvent(closeEvent);
        if (typeof _this3.onclose === 'function') {
          _this3.onclose(closeEvent);
        }
        console.log("Mock WebSocket closed: ".concat(code, " ").concat(reason));
      }, 100);
    }

    // Handle ping message
  }, {
    key: "_handlePing",
    value: function _handlePing(data) {
      // Respond with pong
      var response = {
        type: 'pong',
        timestamp: new Date().toISOString(),
        originalTimestamp: data.timestamp,
        server: 'MockWebSocketServer'
      };
      this._sendMessage(response);
    }

    // Handle app data request
  }, {
    key: "_handleAppDataRequest",
    value: function _handleAppDataRequest(data) {
      // Respond with mock app data
      var response = {
        type: 'app_data',
        data: {
          app: {
            name: 'App Builder',
            version: '1.0.0',
            components: [{
              id: 1,
              type: 'Button',
              props: {
                text: 'Click Me',
                variant: 'primary'
              }
            }, {
              id: 2,
              type: 'Input',
              props: {
                placeholder: 'Enter text',
                label: 'Name'
              }
            }, {
              id: 3,
              type: 'Text',
              props: {
                content: 'Hello World',
                style: {
                  fontWeight: 'bold'
                }
              }
            }],
            layouts: [{
              id: 1,
              type: 'Grid',
              components: [1, 2],
              styles: {
                gap: '10px'
              }
            }, {
              id: 2,
              type: 'Flex',
              components: [3],
              styles: {
                justifyContent: 'center'
              }
            }],
            styles: {
              '.container': {
                display: 'flex',
                flexDirection: 'column',
                gap: '20px'
              },
              '.header': {
                fontSize: '24px',
                fontWeight: 'bold',
                marginBottom: '16px'
              }
            },
            status: 'online'
          },
          _meta: {
            source: 'mock-websocket',
            timestamp: new Date().toISOString(),
            requestId: data.id || data.timestamp
          }
        },
        timestamp: new Date().toISOString()
      };
      this._sendMessage(response);
    }

    // Handle generic message
  }, {
    key: "_handleGenericMessage",
    value: function _handleGenericMessage(data) {
      // Echo the message back
      var response = {
        type: 'echo',
        originalMessage: data,
        timestamp: new Date().toISOString(),
        server: 'MockWebSocketServer'
      };
      this._sendMessage(response);
    }

    // Send a message to the client
  }, {
    key: "_sendMessage",
    value: function _sendMessage(data) {
      var messageData = JSON.stringify(data);

      // Create message event
      var messageEvent = new MessageEvent('message', {
        data: messageData,
        origin: this.url,
        lastEventId: '',
        source: null,
        ports: []
      });

      // Dispatch message event
      this.dispatchEvent(messageEvent);
      if (typeof this.onmessage === 'function') {
        this.onmessage(messageEvent);
      }
      console.log("Mock WebSocket received data:", data);
    }
  }]);
}(/*#__PURE__*/(0,_babel_runtime_helpers_wrapNativeSuper__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(EventTarget));
/**
 * Initialize the mock WebSocket server
 * @param {Object} options - Configuration options
 * @param {boolean} options.enabled - Whether the mock server is enabled
 * @param {boolean} options.logMessages - Whether to log messages
 */
(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(MockWebSocket, "CONNECTING", 0);
(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(MockWebSocket, "OPEN", 1);
(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(MockWebSocket, "CLOSING", 2);
(0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(MockWebSocket, "CLOSED", 3);
function initMockWebSocketServer() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$enabled = options.enabled,
    enabled = _options$enabled === void 0 ? "production" === 'development' : _options$enabled,
    _options$logMessages = options.logMessages,
    logMessages = _options$logMessages === void 0 ? true : _options$logMessages;
  if (!enabled) {
    console.log('Mock WebSocket server is disabled');
    return;
  }
  console.log('Initializing mock WebSocket server...');

  // Store the original WebSocket class
  window._originalWebSocket = window.WebSocket;

  // Override the WebSocket class
  window.WebSocket = MockWebSocket;
  console.log('Mock WebSocket server initialized');
}

/**
 * Disable the mock WebSocket server
 */
function disableMockWebSocketServer() {
  // Restore the original WebSocket class if it exists
  if (window._originalWebSocket) {
    window.WebSocket = window._originalWebSocket;
    console.log('Mock WebSocket server disabled');
  }
}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  initMockWebSocketServer: initMockWebSocketServer,
  disableMockWebSocketServer: disableMockWebSocketServer,
  MockWebSocket: MockWebSocket
});

/***/ }),

/***/ 56702:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PF: () => (/* binding */ LoadingStates),
/* harmony export */   s_: () => (/* binding */ createLazyComponent)
/* harmony export */ });
/* unused harmony exports useProgressiveLoading, preloadOnInteraction, preloadOnVisible, DefaultLoadingComponent, LazyLoadErrorBoundary */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(56822);
/* harmony import */ var _babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(53954);
/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(85501);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(35346);











function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _callSuper(t, o, e) { return o = (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A)(o), (0,_babel_runtime_helpers_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0,_babel_runtime_helpers_getPrototypeOf__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }




/**
 * Enhanced Lazy Loading Utilities
 * Provides robust lazy loading infrastructure with error handling and loading states
 */

// Default loading component
var DefaultLoadingComponent = function DefaultLoadingComponent(_ref) {
  var _ref$size = _ref.size,
    size = _ref$size === void 0 ? 'large' : _ref$size,
    _ref$tip = _ref.tip,
    tip = _ref$tip === void 0 ? 'Loading...' : _ref$tip,
    _ref$fullPage = _ref.fullPage,
    fullPage = _ref$fullPage === void 0 ? false : _ref$fullPage,
    _ref$description = _ref.description,
    description = _ref$description === void 0 ? null : _ref$description;
  var spinIcon = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__/* .LoadingOutlined */ .NKq, {
    style: {
      fontSize: 24
    },
    spin: true
  });
  var spinComponent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("div", {
    style: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '12px',
      padding: fullPage ? '60px 20px' : '20px',
      minHeight: fullPage ? '200px' : 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(antd__WEBPACK_IMPORTED_MODULE_12__/* .Spin */ .tK, {
    indicator: spinIcon,
    size: size,
    tip: tip
  }), description && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("div", {
    style: {
      color: '#666',
      fontSize: '14px',
      textAlign: 'center',
      maxWidth: '300px'
    }
  }, description));
  return fullPage ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("div", {
    style: {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  }, spinComponent) : spinComponent;
};

// Error boundary for lazy loaded components
var LazyLoadErrorBoundary = /*#__PURE__*/function (_React$Component) {
  function LazyLoadErrorBoundary(props) {
    var _this;
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(this, LazyLoadErrorBoundary);
    _this = _callSuper(this, LazyLoadErrorBoundary, [props]);
    (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A)(_this, "handleRetry", function () {
      _this.setState({
        hasError: false,
        error: null
      });
      // Force a re-render by updating the key
      if (_this.props.onRetry) {
        _this.props.onRetry();
      }
    });
    _this.state = {
      hasError: false,
      error: null
    };
    return _this;
  }
  (0,_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)(LazyLoadErrorBoundary, _React$Component);
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(LazyLoadErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      console.error('Lazy loading error:', error, errorInfo);

      // Report to error tracking service if available
      if (window.reportError) {
        window.reportError(error, _objectSpread({
          context: 'lazy-loading'
        }, errorInfo));
      }
    }
  }, {
    key: "render",
    value: function render() {
      if (this.state.hasError) {
        var _this$props = this.props,
          fallback = _this$props.fallback,
          _this$props$component = _this$props.componentName,
          componentName = _this$props$component === void 0 ? 'Component' : _this$props$component;
        if (fallback) {
          return fallback;
        }
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(antd__WEBPACK_IMPORTED_MODULE_12__/* .Alert */ .Fc, {
          message: "Failed to load ".concat(componentName),
          description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement("p", null, "There was an error loading this component. This might be due to a network issue or a temporary problem."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(antd__WEBPACK_IMPORTED_MODULE_12__/* .Button */ .$n, {
            type: "primary",
            icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__/* .ReloadOutlined */ .KF4, null),
            onClick: this.handleRetry,
            style: {
              marginTop: '8px'
            }
          }, "Try Again")),
          type: "error",
          showIcon: true,
          style: {
            margin: '20px'
          }
        });
      }
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      return {
        hasError: true,
        error: error
      };
    }
  }]);
}(react__WEBPACK_IMPORTED_MODULE_11__.Component);
/**
 * Enhanced lazy loading wrapper with retry logic and error handling
 */
var createLazyComponent = function createLazyComponent(importFunction) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$fallback = options.fallback,
    fallback = _options$fallback === void 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, null) : _options$fallback,
    _options$errorFallbac = options.errorFallback,
    errorFallback = _options$errorFallbac === void 0 ? null : _options$errorFallbac,
    _options$componentNam = options.componentName,
    componentName = _options$componentNam === void 0 ? 'Component' : _options$componentNam,
    _options$retryAttempt = options.retryAttempts,
    retryAttempts = _options$retryAttempt === void 0 ? 3 : _options$retryAttempt,
    _options$retryDelay = options.retryDelay,
    retryDelay = _options$retryDelay === void 0 ? 1000 : _options$retryDelay,
    _options$preload = options.preload,
    preload = _options$preload === void 0 ? false : _options$preload;

  // Create the lazy component with retry logic
  var LazyComponent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.lazy(function () {
    var attempts = 0;
    var _loadWithRetry = /*#__PURE__*/function () {
      var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10___default().mark(function _callee() {
        var module, _t;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_10___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 1;
              return importFunction();
            case 1:
              module = _context.sent;
              return _context.abrupt("return", module);
            case 2:
              _context.prev = 2;
              _t = _context["catch"](0);
              attempts++;
              if (!(attempts < retryAttempts)) {
                _context.next = 4;
                break;
              }
              console.warn("Failed to load ".concat(componentName, ", retrying... (").concat(attempts, "/").concat(retryAttempts, ")"));
              _context.next = 3;
              return new Promise(function (resolve) {
                return setTimeout(resolve, retryDelay * attempts);
              });
            case 3:
              return _context.abrupt("return", _loadWithRetry());
            case 4:
              console.error("Failed to load ".concat(componentName, " after ").concat(retryAttempts, " attempts:"), _t);
              throw _t;
            case 5:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 2]]);
      }));
      return function loadWithRetry() {
        return _ref2.apply(this, arguments);
      };
    }();
    return _loadWithRetry();
  });

  // Preload the component if requested
  if (preload) {
    // Preload after a short delay to not block initial render
    setTimeout(function () {
      importFunction()["catch"](function (err) {
        console.warn("Preload failed for ".concat(componentName, ":"), err);
      });
    }, 100);
  }

  // Return wrapped component
  var WrappedComponent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.forwardRef(function (props, ref) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(LazyLoadErrorBoundary, {
      componentName: componentName,
      fallback: errorFallback,
      onRetry: function onRetry() {
        // Force component re-mount by changing key
        if (props.onRetry) props.onRetry();
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(react__WEBPACK_IMPORTED_MODULE_11__.Suspense, {
      fallback: fallback
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(LazyComponent, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)({}, props, {
      ref: ref
    }))));
  });
  WrappedComponent.displayName = "Lazy(".concat(componentName, ")");

  // Add preload method to component
  WrappedComponent.preload = function () {
    return importFunction();
  };
  return WrappedComponent;
};

/**
 * Hook for progressive loading of components
 */
var useProgressiveLoading = function useProgressiveLoading() {
  var components = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
  var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100;
  var _React$useState = React.useState(new Set()),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    loadedComponents = _React$useState2[0],
    setLoadedComponents = _React$useState2[1];
  React.useEffect(function () {
    var timeouts = [];
    components.forEach(function (component, index) {
      var timeout = setTimeout(function () {
        if (component.preload) {
          component.preload().then(function () {
            setLoadedComponents(function (prev) {
              return new Set([].concat(_toConsumableArray(prev), [component.displayName]));
            });
          })["catch"](function (err) {
            console.warn("Progressive loading failed for ".concat(component.displayName, ":"), err);
          });
        }
      }, delay * (index + 1));
      timeouts.push(timeout);
    });
    return function () {
      timeouts.forEach(clearTimeout);
    };
  }, [components, delay]);
  return loadedComponents;
};

/**
 * Preload components based on user interaction
 */
var preloadOnInteraction = function preloadOnInteraction(component) {
  var events = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ['mouseenter', 'focus'];
  return function (targetElement) {
    if (!targetElement || !component.preload) return;
    var _handleInteraction = function handleInteraction() {
      component.preload();
      // Remove listeners after first interaction
      events.forEach(function (event) {
        targetElement.removeEventListener(event, _handleInteraction);
      });
    };
    events.forEach(function (event) {
      targetElement.addEventListener(event, _handleInteraction, {
        passive: true
      });
    });
    return function () {
      events.forEach(function (event) {
        targetElement.removeEventListener(event, _handleInteraction);
      });
    };
  };
};

/**
 * Intersection Observer based preloading
 */
var preloadOnVisible = function preloadOnVisible(component) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$threshold = options.threshold,
    threshold = _options$threshold === void 0 ? 0.1 : _options$threshold,
    _options$rootMargin = options.rootMargin,
    rootMargin = _options$rootMargin === void 0 ? '50px' : _options$rootMargin;
  return function (targetElement) {
    if (!targetElement || !component.preload || !window.IntersectionObserver) return;
    var observer = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          component.preload();
          observer.unobserve(targetElement);
        }
      });
    }, {
      threshold: threshold,
      rootMargin: rootMargin
    });
    observer.observe(targetElement);
    return function () {
      return observer.disconnect();
    };
  };
};

// Export loading components for reuse


// Predefined loading states for common scenarios
var LoadingStates = {
  minimal: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, {
    size: "small",
    tip: "Loading..."
  }),
  standard: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, {
    size: "large",
    tip: "Loading component..."
  }),
  fullPage: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, {
    size: "large",
    tip: "Loading...",
    fullPage: true
  }),
  withDescription: function withDescription(description) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(DefaultLoadingComponent, {
      size: "large",
      tip: "Loading...",
      description: description
    });
  }
};

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, [7727,5108,9845,107,8980,6434,5101,1025,3976,9669,276,720,5435,1114,7571,2518,9060,4772,7449,8771,9843,9832,2698,5124,1629,8287,1486,2675,9907,7192,9372,4802,1807,747,7088,8278,6059,8346,687,2036,4488,7496,4447,1889,2773,6110,6037,3357,2272,895,1115,3205,7056,3385,6261,8020,2665,8104,9225,1390,8589,3230,9919,2378,939,6082,551,1735,38,5588,1387,5955,7419,2024,3879,9682], () => (__webpack_exec__(89140)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);