"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3323],{

/***/ 562:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var merge = __webpack_require__(29303)
var xlink = __webpack_require__(26451)
var xml = __webpack_require__(97134)
var xmlns = __webpack_require__(96337)
var aria = __webpack_require__(22250)
var html = __webpack_require__(98450)

module.exports = merge([xml, xlink, xmlns, aria, html])


/***/ }),

/***/ 4360:
/***/ ((module) => {



/* eslint-env browser */

var el

var semicolon = 59 //  ';'

module.exports = decodeEntity

function decodeEntity(characters) {
  var entity = '&' + characters + ';'
  var char

  el = el || document.createElement('i')
  el.innerHTML = entity
  char = el.textContent

  // Some entities do not require the closing semicolon (`&not` - for instance),
  // which leads to situations where parsing the assumed entity of &notit; will
  // result in the string `¬it;`.  When we encounter a trailing semicolon after
  // parsing and the entity to decode was not a semicolon (`&semi;`), we can
  // assume that the matching was incomplete
  if (char.charCodeAt(char.length - 1) === semicolon && characters !== 'semi') {
    return false
  }

  // If the decoded string is equal to the input, the entity was not valid
  return char === entity ? false : char
}


/***/ }),

/***/ 4812:
/***/ ((module) => {



module.exports = normalize

function normalize(value) {
  return value.toLowerCase()
}


/***/ }),

/***/ 19632:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var legacy = __webpack_require__(82719)
var invalid = __webpack_require__(21257)
var decimal = __webpack_require__(82899)
var hexadecimal = __webpack_require__(71711)
var alphanumerical = __webpack_require__(94934)
var decodeEntity = __webpack_require__(4360)

module.exports = parseEntities

var own = {}.hasOwnProperty
var fromCharCode = String.fromCharCode
var noop = Function.prototype

// Default settings.
var defaults = {
  warning: null,
  reference: null,
  text: null,
  warningContext: null,
  referenceContext: null,
  textContext: null,
  position: {},
  additional: null,
  attribute: false,
  nonTerminated: true
}

// Characters.
var tab = 9 // '\t'
var lineFeed = 10 // '\n'
var formFeed = 12 // '\f'
var space = 32 // ' '
var ampersand = 38 // '&'
var semicolon = 59 // ';'
var lessThan = 60 // '<'
var equalsTo = 61 // '='
var numberSign = 35 // '#'
var uppercaseX = 88 // 'X'
var lowercaseX = 120 // 'x'
var replacementCharacter = 65533 // '�'

// Reference types.
var name = 'named'
var hexa = 'hexadecimal'
var deci = 'decimal'

// Map of bases.
var bases = {}

bases[hexa] = 16
bases[deci] = 10

// Map of types to tests.
// Each type of character reference accepts different characters.
// This test is used to detect whether a reference has ended (as the semicolon
// is not strictly needed).
var tests = {}

tests[name] = alphanumerical
tests[deci] = decimal
tests[hexa] = hexadecimal

// Warning types.
var namedNotTerminated = 1
var numericNotTerminated = 2
var namedEmpty = 3
var numericEmpty = 4
var namedUnknown = 5
var numericDisallowed = 6
var numericProhibited = 7

// Warning messages.
var messages = {}

messages[namedNotTerminated] =
  'Named character references must be terminated by a semicolon'
messages[numericNotTerminated] =
  'Numeric character references must be terminated by a semicolon'
messages[namedEmpty] = 'Named character references cannot be empty'
messages[numericEmpty] = 'Numeric character references cannot be empty'
messages[namedUnknown] = 'Named character references must be known'
messages[numericDisallowed] =
  'Numeric character references cannot be disallowed'
messages[numericProhibited] =
  'Numeric character references cannot be outside the permissible Unicode range'

// Wrap to ensure clean parameters are given to `parse`.
function parseEntities(value, options) {
  var settings = {}
  var option
  var key

  if (!options) {
    options = {}
  }

  for (key in defaults) {
    option = options[key]
    settings[key] =
      option === null || option === undefined ? defaults[key] : option
  }

  if (settings.position.indent || settings.position.start) {
    settings.indent = settings.position.indent || []
    settings.position = settings.position.start
  }

  return parse(value, settings)
}

// Parse entities.
// eslint-disable-next-line complexity
function parse(value, settings) {
  var additional = settings.additional
  var nonTerminated = settings.nonTerminated
  var handleText = settings.text
  var handleReference = settings.reference
  var handleWarning = settings.warning
  var textContext = settings.textContext
  var referenceContext = settings.referenceContext
  var warningContext = settings.warningContext
  var pos = settings.position
  var indent = settings.indent || []
  var length = value.length
  var index = 0
  var lines = -1
  var column = pos.column || 1
  var line = pos.line || 1
  var queue = ''
  var result = []
  var entityCharacters
  var namedEntity
  var terminated
  var characters
  var character
  var reference
  var following
  var warning
  var reason
  var output
  var entity
  var begin
  var start
  var type
  var test
  var prev
  var next
  var diff
  var end

  if (typeof additional === 'string') {
    additional = additional.charCodeAt(0)
  }

  // Cache the current point.
  prev = now()

  // Wrap `handleWarning`.
  warning = handleWarning ? parseError : noop

  // Ensure the algorithm walks over the first character and the end
  // (inclusive).
  index--
  length++

  while (++index < length) {
    // If the previous character was a newline.
    if (character === lineFeed) {
      column = indent[lines] || 1
    }

    character = value.charCodeAt(index)

    if (character === ampersand) {
      following = value.charCodeAt(index + 1)

      // The behaviour depends on the identity of the next character.
      if (
        following === tab ||
        following === lineFeed ||
        following === formFeed ||
        following === space ||
        following === ampersand ||
        following === lessThan ||
        following !== following ||
        (additional && following === additional)
      ) {
        // Not a character reference.
        // No characters are consumed, and nothing is returned.
        // This is not an error, either.
        queue += fromCharCode(character)
        column++

        continue
      }

      start = index + 1
      begin = start
      end = start

      if (following === numberSign) {
        // Numerical entity.
        end = ++begin

        // The behaviour further depends on the next character.
        following = value.charCodeAt(end)

        if (following === uppercaseX || following === lowercaseX) {
          // ASCII hex digits.
          type = hexa
          end = ++begin
        } else {
          // ASCII digits.
          type = deci
        }
      } else {
        // Named entity.
        type = name
      }

      entityCharacters = ''
      entity = ''
      characters = ''
      test = tests[type]
      end--

      while (++end < length) {
        following = value.charCodeAt(end)

        if (!test(following)) {
          break
        }

        characters += fromCharCode(following)

        // Check if we can match a legacy named reference.
        // If so, we cache that as the last viable named reference.
        // This ensures we do not need to walk backwards later.
        if (type === name && own.call(legacy, characters)) {
          entityCharacters = characters
          entity = legacy[characters]
        }
      }

      terminated = value.charCodeAt(end) === semicolon

      if (terminated) {
        end++

        namedEntity = type === name ? decodeEntity(characters) : false

        if (namedEntity) {
          entityCharacters = characters
          entity = namedEntity
        }
      }

      diff = 1 + end - start

      if (!terminated && !nonTerminated) {
        // Empty.
      } else if (!characters) {
        // An empty (possible) entity is valid, unless it’s numeric (thus an
        // ampersand followed by an octothorp).
        if (type !== name) {
          warning(numericEmpty, diff)
        }
      } else if (type === name) {
        // An ampersand followed by anything unknown, and not terminated, is
        // invalid.
        if (terminated && !entity) {
          warning(namedUnknown, 1)
        } else {
          // If theres something after an entity name which is not known, cap
          // the reference.
          if (entityCharacters !== characters) {
            end = begin + entityCharacters.length
            diff = 1 + end - begin
            terminated = false
          }

          // If the reference is not terminated, warn.
          if (!terminated) {
            reason = entityCharacters ? namedNotTerminated : namedEmpty

            if (settings.attribute) {
              following = value.charCodeAt(end)

              if (following === equalsTo) {
                warning(reason, diff)
                entity = null
              } else if (alphanumerical(following)) {
                entity = null
              } else {
                warning(reason, diff)
              }
            } else {
              warning(reason, diff)
            }
          }
        }

        reference = entity
      } else {
        if (!terminated) {
          // All non-terminated numeric entities are not rendered, and trigger a
          // warning.
          warning(numericNotTerminated, diff)
        }

        // When terminated and number, parse as either hexadecimal or decimal.
        reference = parseInt(characters, bases[type])

        // Trigger a warning when the parsed number is prohibited, and replace
        // with replacement character.
        if (prohibited(reference)) {
          warning(numericProhibited, diff)
          reference = fromCharCode(replacementCharacter)
        } else if (reference in invalid) {
          // Trigger a warning when the parsed number is disallowed, and replace
          // by an alternative.
          warning(numericDisallowed, diff)
          reference = invalid[reference]
        } else {
          // Parse the number.
          output = ''

          // Trigger a warning when the parsed number should not be used.
          if (disallowed(reference)) {
            warning(numericDisallowed, diff)
          }

          // Stringify the number.
          if (reference > 0xffff) {
            reference -= 0x10000
            output += fromCharCode((reference >>> (10 & 0x3ff)) | 0xd800)
            reference = 0xdc00 | (reference & 0x3ff)
          }

          reference = output + fromCharCode(reference)
        }
      }

      // Found it!
      // First eat the queued characters as normal text, then eat an entity.
      if (reference) {
        flush()

        prev = now()
        index = end - 1
        column += end - start + 1
        result.push(reference)
        next = now()
        next.offset++

        if (handleReference) {
          handleReference.call(
            referenceContext,
            reference,
            {start: prev, end: next},
            value.slice(start - 1, end)
          )
        }

        prev = next
      } else {
        // If we could not find a reference, queue the checked characters (as
        // normal characters), and move the pointer to their end.
        // This is possible because we can be certain neither newlines nor
        // ampersands are included.
        characters = value.slice(start - 1, end)
        queue += characters
        column += characters.length
        index = end - 1
      }
    } else {
      // Handle anything other than an ampersand, including newlines and EOF.
      if (
        character === 10 // Line feed
      ) {
        line++
        lines++
        column = 0
      }

      if (character === character) {
        queue += fromCharCode(character)
        column++
      } else {
        flush()
      }
    }
  }

  // Return the reduced nodes.
  return result.join('')

  // Get current position.
  function now() {
    return {
      line: line,
      column: column,
      offset: index + (pos.offset || 0)
    }
  }

  // “Throw” a parse-error: a warning.
  function parseError(code, offset) {
    var position = now()

    position.column += offset
    position.offset += offset

    handleWarning.call(warningContext, messages[code], position, code)
  }

  // Flush `queue` (normal text).
  // Macro invoked before each entity and at the end of `value`.
  // Does nothing when `queue` is empty.
  function flush() {
    if (queue) {
      result.push(queue)

      if (handleText) {
        handleText.call(textContext, queue, {start: prev, end: now()})
      }

      queue = ''
    }
  }
}

// Check if `character` is outside the permissible unicode range.
function prohibited(code) {
  return (code >= 0xd800 && code <= 0xdfff) || code > 0x10ffff
}

// Check if `character` is disallowed.
function disallowed(code) {
  return (
    (code >= 0x0001 && code <= 0x0008) ||
    code === 0x000b ||
    (code >= 0x000d && code <= 0x001f) ||
    (code >= 0x007f && code <= 0x009f) ||
    (code >= 0xfdd0 && code <= 0xfdef) ||
    (code & 0xffff) === 0xffff ||
    (code & 0xffff) === 0xfffe
  )
}


/***/ }),

/***/ 22250:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var types = __webpack_require__(64845)
var create = __webpack_require__(91454)

var booleanish = types.booleanish
var number = types.number
var spaceSeparated = types.spaceSeparated

module.exports = create({
  transform: ariaTransform,
  properties: {
    ariaActiveDescendant: null,
    ariaAtomic: booleanish,
    ariaAutoComplete: null,
    ariaBusy: booleanish,
    ariaChecked: booleanish,
    ariaColCount: number,
    ariaColIndex: number,
    ariaColSpan: number,
    ariaControls: spaceSeparated,
    ariaCurrent: null,
    ariaDescribedBy: spaceSeparated,
    ariaDetails: null,
    ariaDisabled: booleanish,
    ariaDropEffect: spaceSeparated,
    ariaErrorMessage: null,
    ariaExpanded: booleanish,
    ariaFlowTo: spaceSeparated,
    ariaGrabbed: booleanish,
    ariaHasPopup: null,
    ariaHidden: booleanish,
    ariaInvalid: null,
    ariaKeyShortcuts: null,
    ariaLabel: null,
    ariaLabelledBy: spaceSeparated,
    ariaLevel: number,
    ariaLive: null,
    ariaModal: booleanish,
    ariaMultiLine: booleanish,
    ariaMultiSelectable: booleanish,
    ariaOrientation: null,
    ariaOwns: spaceSeparated,
    ariaPlaceholder: null,
    ariaPosInSet: number,
    ariaPressed: booleanish,
    ariaReadOnly: booleanish,
    ariaRelevant: null,
    ariaRequired: booleanish,
    ariaRoleDescription: spaceSeparated,
    ariaRowCount: number,
    ariaRowIndex: number,
    ariaRowSpan: number,
    ariaSelected: booleanish,
    ariaSetSize: number,
    ariaSort: null,
    ariaValueMax: number,
    ariaValueMin: number,
    ariaValueNow: number,
    ariaValueText: null,
    role: null
  }
})

function ariaTransform(_, prop) {
  return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()
}


/***/ }),

/***/ 24758:
/***/ ((module) => {



module.exports = Info

var proto = Info.prototype

proto.space = null
proto.attribute = null
proto.property = null
proto.boolean = false
proto.booleanish = false
proto.overloadedBoolean = false
proto.number = false
proto.commaSeparated = false
proto.spaceSeparated = false
proto.commaOrSpaceSeparated = false
proto.mustUseProperty = false
proto.defined = false

function Info(property, attribute) {
  this.property = property
  this.attribute = attribute
}


/***/ }),

/***/ 26451:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var create = __webpack_require__(91454)

module.exports = create({
  space: 'xlink',
  transform: xlinkTransform,
  properties: {
    xLinkActuate: null,
    xLinkArcRole: null,
    xLinkHref: null,
    xLinkRole: null,
    xLinkShow: null,
    xLinkTitle: null,
    xLinkType: null
  }
})

function xlinkTransform(_, prop) {
  return 'xlink:' + prop.slice(5).toLowerCase()
}


/***/ }),

/***/ 29303:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var xtend = __webpack_require__(57510)
var Schema = __webpack_require__(86585)

module.exports = merge

function merge(definitions) {
  var length = definitions.length
  var property = []
  var normal = []
  var index = -1
  var info
  var space

  while (++index < length) {
    info = definitions[index]
    property.push(info.property)
    normal.push(info.normal)
    space = info.space
  }

  return new Schema(
    xtend.apply(null, property),
    xtend.apply(null, normal),
    space
  )
}


/***/ }),

/***/ 43768:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {



var high = __webpack_require__(45981)
var fault = __webpack_require__(85587)

exports.highlight = highlight
exports.highlightAuto = highlightAuto
exports.registerLanguage = registerLanguage
exports.listLanguages = listLanguages
exports.registerAlias = registerAlias

Emitter.prototype.addText = text
Emitter.prototype.addKeyword = addKeyword
Emitter.prototype.addSublanguage = addSublanguage
Emitter.prototype.openNode = open
Emitter.prototype.closeNode = close
Emitter.prototype.closeAllNodes = noop
Emitter.prototype.finalize = noop
Emitter.prototype.toHTML = toHtmlNoop

var defaultPrefix = 'hljs-'

// Highlighting `value` in the language `name`.
function highlight(name, value, options) {
  var before = high.configure({})
  var settings = options || {}
  var prefix = settings.prefix
  var result

  if (typeof name !== 'string') {
    throw fault('Expected `string` for name, got `%s`', name)
  }

  if (!high.getLanguage(name)) {
    throw fault('Unknown language: `%s` is not registered', name)
  }

  if (typeof value !== 'string') {
    throw fault('Expected `string` for value, got `%s`', value)
  }

  if (prefix === null || prefix === undefined) {
    prefix = defaultPrefix
  }

  high.configure({__emitter: Emitter, classPrefix: prefix})

  result = high.highlight(value, {language: name, ignoreIllegals: true})

  high.configure(before || {})

  /* istanbul ignore if - Highlight.js seems to use this (currently) for broken
   * grammars, so let’s keep it in there just to be sure. */
  if (result.errorRaised) {
    throw result.errorRaised
  }

  return {
    relevance: result.relevance,
    language: result.language,
    value: result.emitter.rootNode.children
  }
}

function highlightAuto(value, options) {
  var settings = options || {}
  var subset = settings.subset || high.listLanguages()
  var prefix = settings.prefix
  var length = subset.length
  var index = -1
  var result
  var secondBest
  var current
  var name

  if (prefix === null || prefix === undefined) {
    prefix = defaultPrefix
  }

  if (typeof value !== 'string') {
    throw fault('Expected `string` for value, got `%s`', value)
  }

  secondBest = {relevance: 0, language: null, value: []}
  result = {relevance: 0, language: null, value: []}

  while (++index < length) {
    name = subset[index]

    if (!high.getLanguage(name)) {
      continue
    }

    current = highlight(name, value, options)
    current.language = name

    if (current.relevance > secondBest.relevance) {
      secondBest = current
    }

    if (current.relevance > result.relevance) {
      secondBest = result
      result = current
    }
  }

  if (secondBest.language) {
    result.secondBest = secondBest
  }

  return result
}

// Register a language.
function registerLanguage(name, syntax) {
  high.registerLanguage(name, syntax)
}

// Get a list of all registered languages.
function listLanguages() {
  return high.listLanguages()
}

// Register more aliases for an already registered language.
function registerAlias(name, alias) {
  var map = name
  var key

  if (alias) {
    map = {}
    map[name] = alias
  }

  for (key in map) {
    high.registerAliases(map[key], {languageName: key})
  }
}

function Emitter(options) {
  this.options = options
  this.rootNode = {children: []}
  this.stack = [this.rootNode]
}

function addKeyword(value, name) {
  this.openNode(name)
  this.addText(value)
  this.closeNode()
}

function addSublanguage(other, name) {
  var stack = this.stack
  var current = stack[stack.length - 1]
  var results = other.rootNode.children
  var node = name
    ? {
        type: 'element',
        tagName: 'span',
        properties: {className: [name]},
        children: results
      }
    : results

  current.children = current.children.concat(node)
}

function text(value) {
  var stack = this.stack
  var current
  var tail

  if (value === '') return

  current = stack[stack.length - 1]
  tail = current.children[current.children.length - 1]

  if (tail && tail.type === 'text') {
    tail.value += value
  } else {
    current.children.push({type: 'text', value: value})
  }
}

function open(name) {
  var stack = this.stack
  var className = this.options.classPrefix + name
  var current = stack[stack.length - 1]
  var child = {
    type: 'element',
    tagName: 'span',
    properties: {className: [className]},
    children: []
  }

  current.children.push(child)
  stack.push(child)
}

function close() {
  this.stack.pop()
}

function toHtmlNoop() {
  return ''
}

function noop() {}


/***/ }),

/***/ 48326:
/***/ ((module) => {



module.exports = caseSensitiveTransform

function caseSensitiveTransform(attributes, attribute) {
  return attribute in attributes ? attributes[attribute] : attribute
}


/***/ }),

/***/ 64845:
/***/ ((__unused_webpack_module, exports) => {



var powers = 0

exports.boolean = increment()
exports.booleanish = increment()
exports.overloadedBoolean = increment()
exports.number = increment()
exports.spaceSeparated = increment()
exports.commaSeparated = increment()
exports.commaOrSpaceSeparated = increment()

function increment() {
  return Math.pow(2, ++powers)
}


/***/ }),

/***/ 68271:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var caseSensitiveTransform = __webpack_require__(48326)

module.exports = caseInsensitiveTransform

function caseInsensitiveTransform(attributes, property) {
  return caseSensitiveTransform(attributes, property.toLowerCase())
}


/***/ }),

/***/ 68796:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var normalize = __webpack_require__(4812)
var DefinedInfo = __webpack_require__(86374)
var Info = __webpack_require__(24758)

var data = 'data'

module.exports = find

var valid = /^data[-\w.:]+$/i
var dash = /-[a-z]/g
var cap = /[A-Z]/g

function find(schema, value) {
  var normal = normalize(value)
  var prop = value
  var Type = Info

  if (normal in schema.normal) {
    return schema.property[schema.normal[normal]]
  }

  if (normal.length > 4 && normal.slice(0, 4) === data && valid.test(value)) {
    // Attribute or property.
    if (value.charAt(4) === '-') {
      prop = datasetToProperty(value)
    } else {
      value = datasetToAttribute(value)
    }

    Type = DefinedInfo
  }

  return new Type(prop, value)
}

function datasetToProperty(attribute) {
  var value = attribute.slice(5).replace(dash, camelcase)
  return data + value.charAt(0).toUpperCase() + value.slice(1)
}

function datasetToAttribute(property) {
  var value = property.slice(4)

  if (dash.test(value)) {
    return property
  }

  value = value.replace(cap, kebab)

  if (value.charAt(0) !== '-') {
    value = '-' + value
  }

  return data + value
}

function kebab($0) {
  return '-' + $0.toLowerCase()
}

function camelcase($0) {
  return $0.charAt(1).toUpperCase()
}


/***/ }),

/***/ 86374:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var Info = __webpack_require__(24758)
var types = __webpack_require__(64845)

module.exports = DefinedInfo

DefinedInfo.prototype = new Info()
DefinedInfo.prototype.defined = true

var checks = [
  'boolean',
  'booleanish',
  'overloadedBoolean',
  'number',
  'commaSeparated',
  'spaceSeparated',
  'commaOrSpaceSeparated'
]
var checksLength = checks.length

function DefinedInfo(property, attribute, mask, space) {
  var index = -1
  var check

  mark(this, 'space', space)

  Info.call(this, property, attribute)

  while (++index < checksLength) {
    check = checks[index]
    mark(this, check, (mask & types[check]) === types[check])
  }
}

function mark(values, key, value) {
  if (value) {
    values[key] = value
  }
}


/***/ }),

/***/ 86585:
/***/ ((module) => {



module.exports = Schema

var proto = Schema.prototype

proto.space = null
proto.normal = {}
proto.property = {}

function Schema(property, normal, space) {
  this.property = property
  this.normal = normal

  if (space) {
    this.space = space
  }
}


/***/ }),

/***/ 88732:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var low = __webpack_require__(43768)

module.exports = low

low.registerLanguage('1c', __webpack_require__(32402))
low.registerLanguage('abnf', __webpack_require__(52701))
low.registerLanguage(
  'accesslog',
  __webpack_require__(35076)
)
low.registerLanguage(
  'actionscript',
  __webpack_require__(49115)
)
low.registerLanguage('ada', __webpack_require__(99428))
low.registerLanguage(
  'angelscript',
  __webpack_require__(57246)
)
low.registerLanguage('apache', __webpack_require__(29980))
low.registerLanguage(
  'applescript',
  __webpack_require__(42731)
)
low.registerLanguage('arcade', __webpack_require__(18206))
low.registerLanguage('arduino', __webpack_require__(27220))
low.registerLanguage('armasm', __webpack_require__(79139))
low.registerLanguage('xml', __webpack_require__(17285))
low.registerLanguage('asciidoc', __webpack_require__(49405))
low.registerLanguage('aspectj', __webpack_require__(36478))
low.registerLanguage(
  'autohotkey',
  __webpack_require__(62923)
)
low.registerLanguage('autoit', __webpack_require__(77556))
low.registerLanguage('avrasm', __webpack_require__(13732))
low.registerLanguage('awk', __webpack_require__(19277))
low.registerLanguage('axapta', __webpack_require__(77909))
low.registerLanguage('bash', __webpack_require__(35344))
low.registerLanguage('basic', __webpack_require__(6722))
low.registerLanguage('bnf', __webpack_require__(75610))
low.registerLanguage(
  'brainfuck',
  __webpack_require__(25439)
)
low.registerLanguage('c-like', __webpack_require__(97053))
low.registerLanguage('c', __webpack_require__(39497))
low.registerLanguage('cal', __webpack_require__(67040))
low.registerLanguage(
  'capnproto',
  __webpack_require__(38055)
)
low.registerLanguage('ceylon', __webpack_require__(3838))
low.registerLanguage('clean', __webpack_require__(78333))
low.registerLanguage('clojure', __webpack_require__(80364))
low.registerLanguage(
  'clojure-repl',
  __webpack_require__(5892)
)
low.registerLanguage('cmake', __webpack_require__(56427))
low.registerLanguage(
  'coffeescript',
  __webpack_require__(64125)
)
low.registerLanguage('coq', __webpack_require__(39081))
low.registerLanguage('cos', __webpack_require__(30279))
low.registerLanguage('cpp', __webpack_require__(29721))
low.registerLanguage('crmsh', __webpack_require__(67015))
low.registerLanguage('crystal', __webpack_require__(33270))
low.registerLanguage('csharp', __webpack_require__(88425))
low.registerLanguage('csp', __webpack_require__(82086))
low.registerLanguage('css', __webpack_require__(53315))
low.registerLanguage('d', __webpack_require__(91036))
low.registerLanguage('markdown', __webpack_require__(96503))
low.registerLanguage('dart', __webpack_require__(58811))
low.registerLanguage('delphi', __webpack_require__(61870))
low.registerLanguage('diff', __webpack_require__(6033))
low.registerLanguage('django', __webpack_require__(35133))
low.registerLanguage('dns', __webpack_require__(49927))
low.registerLanguage(
  'dockerfile',
  __webpack_require__(26756)
)
low.registerLanguage('dos', __webpack_require__(55754))
low.registerLanguage('dsconfig', __webpack_require__(91533))
low.registerLanguage('dts', __webpack_require__(39129))
low.registerLanguage('dust', __webpack_require__(14058))
low.registerLanguage('ebnf', __webpack_require__(44177))
low.registerLanguage('elixir', __webpack_require__(81297))
low.registerLanguage('elm', __webpack_require__(42292))
low.registerLanguage('ruby', __webpack_require__(82978))
low.registerLanguage('erb', __webpack_require__(84663))
low.registerLanguage(
  'erlang-repl',
  __webpack_require__(81983)
)
low.registerLanguage('erlang', __webpack_require__(945))
low.registerLanguage('excel', __webpack_require__(58451))
low.registerLanguage('fix', __webpack_require__(7003))
low.registerLanguage('flix', __webpack_require__(24701))
low.registerLanguage('fortran', __webpack_require__(30258))
low.registerLanguage('fsharp', __webpack_require__(94108))
low.registerLanguage('gams', __webpack_require__(48126))
low.registerLanguage('gauss', __webpack_require__(25117))
low.registerLanguage('gcode', __webpack_require__(19156))
low.registerLanguage('gherkin', __webpack_require__(13666))
low.registerLanguage('glsl', __webpack_require__(19614))
low.registerLanguage('gml', __webpack_require__(2904))
low.registerLanguage('go', __webpack_require__(44564))
low.registerLanguage('golo', __webpack_require__(14205))
low.registerLanguage('gradle', __webpack_require__(29789))
low.registerLanguage('groovy', __webpack_require__(72896))
low.registerLanguage('haml', __webpack_require__(57292))
low.registerLanguage(
  'handlebars',
  __webpack_require__(18514)
)
low.registerLanguage('haskell', __webpack_require__(34904))
low.registerLanguage('haxe', __webpack_require__(58670))
low.registerLanguage('hsp', __webpack_require__(74060))
low.registerLanguage('htmlbars', __webpack_require__(22329))
low.registerLanguage('http', __webpack_require__(73402))
low.registerLanguage('hy', __webpack_require__(45617))
low.registerLanguage('inform7', __webpack_require__(12962))
low.registerLanguage('ini', __webpack_require__(40634))
low.registerLanguage('irpf90', __webpack_require__(76414))
low.registerLanguage('isbl', __webpack_require__(1012))
low.registerLanguage('java', __webpack_require__(81786))
low.registerLanguage(
  'javascript',
  __webpack_require__(95089)
)
low.registerLanguage(
  'jboss-cli',
  __webpack_require__(39040)
)
low.registerLanguage('json', __webpack_require__(65772))
low.registerLanguage('julia', __webpack_require__(27601))
low.registerLanguage(
  'julia-repl',
  __webpack_require__(3231)
)
low.registerLanguage('kotlin', __webpack_require__(99559))
low.registerLanguage('lasso', __webpack_require__(31942))
low.registerLanguage('latex', __webpack_require__(8100))
low.registerLanguage('ldif', __webpack_require__(86685))
low.registerLanguage('leaf', __webpack_require__(98186))
low.registerLanguage('less', __webpack_require__(52539))
low.registerLanguage('lisp', __webpack_require__(73458))
low.registerLanguage(
  'livecodeserver',
  __webpack_require__(75814)
)
low.registerLanguage(
  'livescript',
  __webpack_require__(3035)
)
low.registerLanguage('llvm', __webpack_require__(33543))
low.registerLanguage('lsl', __webpack_require__(3399))
low.registerLanguage('lua', __webpack_require__(32670))
low.registerLanguage('makefile', __webpack_require__(59934))
low.registerLanguage(
  'mathematica',
  __webpack_require__(55672)
)
low.registerLanguage('matlab', __webpack_require__(56023))
low.registerLanguage('maxima', __webpack_require__(29433))
low.registerLanguage('mel', __webpack_require__(68254))
low.registerLanguage('mercury', __webpack_require__(48557))
low.registerLanguage('mipsasm', __webpack_require__(24714))
low.registerLanguage('mizar', __webpack_require__(91788))
low.registerLanguage('perl', __webpack_require__(59272))
low.registerLanguage(
  'mojolicious',
  __webpack_require__(90593)
)
low.registerLanguage('monkey', __webpack_require__(29387))
low.registerLanguage(
  'moonscript',
  __webpack_require__(75468)
)
low.registerLanguage('n1ql', __webpack_require__(52962))
low.registerLanguage('nginx', __webpack_require__(4896))
low.registerLanguage('nim', __webpack_require__(63712))
low.registerLanguage('nix', __webpack_require__(34659))
low.registerLanguage(
  'node-repl',
  __webpack_require__(26122)
)
low.registerLanguage('nsis', __webpack_require__(64561))
low.registerLanguage(
  'objectivec',
  __webpack_require__(96494)
)
low.registerLanguage('ocaml', __webpack_require__(60108))
low.registerLanguage('openscad', __webpack_require__(89689))
low.registerLanguage('oxygene', __webpack_require__(78327))
low.registerLanguage('parser3', __webpack_require__(52052))
low.registerLanguage('pf', __webpack_require__(9412))
low.registerLanguage('pgsql', __webpack_require__(80431))
low.registerLanguage('php', __webpack_require__(34608))
low.registerLanguage(
  'php-template',
  __webpack_require__(85579)
)
low.registerLanguage(
  'plaintext',
  __webpack_require__(7939)
)
low.registerLanguage('pony', __webpack_require__(39350))
low.registerLanguage(
  'powershell',
  __webpack_require__(26571)
)
low.registerLanguage(
  'processing',
  __webpack_require__(50181)
)
low.registerLanguage('profile', __webpack_require__(46269))
low.registerLanguage('prolog', __webpack_require__(3105))
low.registerLanguage(
  'properties',
  __webpack_require__(79975)
)
low.registerLanguage('protobuf', __webpack_require__(25093))
low.registerLanguage('puppet', __webpack_require__(69104))
low.registerLanguage(
  'purebasic',
  __webpack_require__(94412)
)
low.registerLanguage('python', __webpack_require__(87192))
low.registerLanguage(
  'python-repl',
  __webpack_require__(90736)
)
low.registerLanguage('q', __webpack_require__(23255))
low.registerLanguage('qml', __webpack_require__(55506))
low.registerLanguage('r', __webpack_require__(22698))
low.registerLanguage('reasonml', __webpack_require__(15331))
low.registerLanguage('rib', __webpack_require__(24745))
low.registerLanguage('roboconf', __webpack_require__(75812))
low.registerLanguage('routeros', __webpack_require__(53557))
low.registerLanguage('rsl', __webpack_require__(53409))
low.registerLanguage(
  'ruleslanguage',
  __webpack_require__(78385)
)
low.registerLanguage('rust', __webpack_require__(39688))
low.registerLanguage('sas', __webpack_require__(7005))
low.registerLanguage('scala', __webpack_require__(43618))
low.registerLanguage('scheme', __webpack_require__(36617))
low.registerLanguage('scilab', __webpack_require__(92970))
low.registerLanguage('scss', __webpack_require__(3206))
low.registerLanguage('shell', __webpack_require__(77918))
low.registerLanguage('smali', __webpack_require__(91180))
low.registerLanguage(
  'smalltalk',
  __webpack_require__(72241)
)
low.registerLanguage('sml', __webpack_require__(4460))
low.registerLanguage('sqf', __webpack_require__(7186))
low.registerLanguage('sql_more', __webpack_require__(30414))
low.registerLanguage('sql', __webpack_require__(76384))
low.registerLanguage('stan', __webpack_require__(24490))
low.registerLanguage('stata', __webpack_require__(81497))
low.registerLanguage('step21', __webpack_require__(36809))
low.registerLanguage('stylus', __webpack_require__(62224))
low.registerLanguage('subunit', __webpack_require__(95976))
low.registerLanguage('swift', __webpack_require__(94635))
low.registerLanguage(
  'taggerscript',
  __webpack_require__(98199)
)
low.registerLanguage('yaml', __webpack_require__(17533))
low.registerLanguage('tap', __webpack_require__(58713))
low.registerLanguage('tcl', __webpack_require__(41519))
low.registerLanguage('thrift', __webpack_require__(45449))
low.registerLanguage('tp', __webpack_require__(88454))
low.registerLanguage('twig', __webpack_require__(23195))
low.registerLanguage(
  'typescript',
  __webpack_require__(62825)
)
low.registerLanguage('vala', __webpack_require__(55320))
low.registerLanguage('vbnet', __webpack_require__(50959))
low.registerLanguage('vbscript', __webpack_require__(4893))
low.registerLanguage(
  'vbscript-html',
  __webpack_require__(88103)
)
low.registerLanguage('verilog', __webpack_require__(57522))
low.registerLanguage('vhdl', __webpack_require__(37492))
low.registerLanguage('vim', __webpack_require__(1192))
low.registerLanguage('x86asm', __webpack_require__(53447))
low.registerLanguage('xl', __webpack_require__(15790))
low.registerLanguage('xquery', __webpack_require__(78130))
low.registerLanguage('zephir', __webpack_require__(57204))


/***/ }),

/***/ 91454:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var normalize = __webpack_require__(4812)
var Schema = __webpack_require__(86585)
var DefinedInfo = __webpack_require__(86374)

module.exports = create

function create(definition) {
  var space = definition.space
  var mustUseProperty = definition.mustUseProperty || []
  var attributes = definition.attributes || {}
  var props = definition.properties
  var transform = definition.transform
  var property = {}
  var normal = {}
  var prop
  var info

  for (prop in props) {
    info = new DefinedInfo(
      prop,
      transform(attributes, prop),
      props[prop],
      space
    )

    if (mustUseProperty.indexOf(prop) !== -1) {
      info.mustUseProperty = true
    }

    property[prop] = info

    normal[normalize(prop)] = prop
    normal[normalize(info.attribute)] = prop
  }

  return new Schema(property, normal, space)
}


/***/ }),

/***/ 96337:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var create = __webpack_require__(91454)
var caseInsensitiveTransform = __webpack_require__(68271)

module.exports = create({
  space: 'xmlns',
  attributes: {
    xmlnsxlink: 'xmlns:xlink'
  },
  transform: caseInsensitiveTransform,
  properties: {
    xmlns: null,
    xmlnsXLink: null
  }
})


/***/ }),

/***/ 97134:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var create = __webpack_require__(91454)

module.exports = create({
  space: 'xml',
  transform: xmlTransform,
  properties: {
    xmlLang: null,
    xmlBase: null,
    xmlSpace: null
  }
})

function xmlTransform(_, prop) {
  return 'xml:' + prop.slice(3).toLowerCase()
}


/***/ }),

/***/ 98450:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



var types = __webpack_require__(64845)
var create = __webpack_require__(91454)
var caseInsensitiveTransform = __webpack_require__(68271)

var boolean = types.boolean
var overloadedBoolean = types.overloadedBoolean
var booleanish = types.booleanish
var number = types.number
var spaceSeparated = types.spaceSeparated
var commaSeparated = types.commaSeparated

module.exports = create({
  space: 'html',
  attributes: {
    acceptcharset: 'accept-charset',
    classname: 'class',
    htmlfor: 'for',
    httpequiv: 'http-equiv'
  },
  transform: caseInsensitiveTransform,
  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],
  properties: {
    // Standard Properties.
    abbr: null,
    accept: commaSeparated,
    acceptCharset: spaceSeparated,
    accessKey: spaceSeparated,
    action: null,
    allow: null,
    allowFullScreen: boolean,
    allowPaymentRequest: boolean,
    allowUserMedia: boolean,
    alt: null,
    as: null,
    async: boolean,
    autoCapitalize: null,
    autoComplete: spaceSeparated,
    autoFocus: boolean,
    autoPlay: boolean,
    capture: boolean,
    charSet: null,
    checked: boolean,
    cite: null,
    className: spaceSeparated,
    cols: number,
    colSpan: null,
    content: null,
    contentEditable: booleanish,
    controls: boolean,
    controlsList: spaceSeparated,
    coords: number | commaSeparated,
    crossOrigin: null,
    data: null,
    dateTime: null,
    decoding: null,
    default: boolean,
    defer: boolean,
    dir: null,
    dirName: null,
    disabled: boolean,
    download: overloadedBoolean,
    draggable: booleanish,
    encType: null,
    enterKeyHint: null,
    form: null,
    formAction: null,
    formEncType: null,
    formMethod: null,
    formNoValidate: boolean,
    formTarget: null,
    headers: spaceSeparated,
    height: number,
    hidden: boolean,
    high: number,
    href: null,
    hrefLang: null,
    htmlFor: spaceSeparated,
    httpEquiv: spaceSeparated,
    id: null,
    imageSizes: null,
    imageSrcSet: commaSeparated,
    inputMode: null,
    integrity: null,
    is: null,
    isMap: boolean,
    itemId: null,
    itemProp: spaceSeparated,
    itemRef: spaceSeparated,
    itemScope: boolean,
    itemType: spaceSeparated,
    kind: null,
    label: null,
    lang: null,
    language: null,
    list: null,
    loading: null,
    loop: boolean,
    low: number,
    manifest: null,
    max: null,
    maxLength: number,
    media: null,
    method: null,
    min: null,
    minLength: number,
    multiple: boolean,
    muted: boolean,
    name: null,
    nonce: null,
    noModule: boolean,
    noValidate: boolean,
    onAbort: null,
    onAfterPrint: null,
    onAuxClick: null,
    onBeforePrint: null,
    onBeforeUnload: null,
    onBlur: null,
    onCancel: null,
    onCanPlay: null,
    onCanPlayThrough: null,
    onChange: null,
    onClick: null,
    onClose: null,
    onContextMenu: null,
    onCopy: null,
    onCueChange: null,
    onCut: null,
    onDblClick: null,
    onDrag: null,
    onDragEnd: null,
    onDragEnter: null,
    onDragExit: null,
    onDragLeave: null,
    onDragOver: null,
    onDragStart: null,
    onDrop: null,
    onDurationChange: null,
    onEmptied: null,
    onEnded: null,
    onError: null,
    onFocus: null,
    onFormData: null,
    onHashChange: null,
    onInput: null,
    onInvalid: null,
    onKeyDown: null,
    onKeyPress: null,
    onKeyUp: null,
    onLanguageChange: null,
    onLoad: null,
    onLoadedData: null,
    onLoadedMetadata: null,
    onLoadEnd: null,
    onLoadStart: null,
    onMessage: null,
    onMessageError: null,
    onMouseDown: null,
    onMouseEnter: null,
    onMouseLeave: null,
    onMouseMove: null,
    onMouseOut: null,
    onMouseOver: null,
    onMouseUp: null,
    onOffline: null,
    onOnline: null,
    onPageHide: null,
    onPageShow: null,
    onPaste: null,
    onPause: null,
    onPlay: null,
    onPlaying: null,
    onPopState: null,
    onProgress: null,
    onRateChange: null,
    onRejectionHandled: null,
    onReset: null,
    onResize: null,
    onScroll: null,
    onSecurityPolicyViolation: null,
    onSeeked: null,
    onSeeking: null,
    onSelect: null,
    onSlotChange: null,
    onStalled: null,
    onStorage: null,
    onSubmit: null,
    onSuspend: null,
    onTimeUpdate: null,
    onToggle: null,
    onUnhandledRejection: null,
    onUnload: null,
    onVolumeChange: null,
    onWaiting: null,
    onWheel: null,
    open: boolean,
    optimum: number,
    pattern: null,
    ping: spaceSeparated,
    placeholder: null,
    playsInline: boolean,
    poster: null,
    preload: null,
    readOnly: boolean,
    referrerPolicy: null,
    rel: spaceSeparated,
    required: boolean,
    reversed: boolean,
    rows: number,
    rowSpan: number,
    sandbox: spaceSeparated,
    scope: null,
    scoped: boolean,
    seamless: boolean,
    selected: boolean,
    shape: null,
    size: number,
    sizes: null,
    slot: null,
    span: number,
    spellCheck: booleanish,
    src: null,
    srcDoc: null,
    srcLang: null,
    srcSet: commaSeparated,
    start: number,
    step: null,
    style: null,
    tabIndex: number,
    target: null,
    title: null,
    translate: null,
    type: null,
    typeMustMatch: boolean,
    useMap: null,
    value: booleanish,
    width: number,
    wrap: null,

    // Legacy.
    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis
    align: null, // Several. Use CSS `text-align` instead,
    aLink: null, // `<body>`. Use CSS `a:active {color}` instead
    archive: spaceSeparated, // `<object>`. List of URIs to archives
    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`
    background: null, // `<body>`. Use CSS `background-image` instead
    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead
    border: number, // `<table>`. Use CSS `border-width` instead,
    borderColor: null, // `<table>`. Use CSS `border-color` instead,
    bottomMargin: number, // `<body>`
    cellPadding: null, // `<table>`
    cellSpacing: null, // `<table>`
    char: null, // Several table elements. When `align=char`, sets the character to align on
    charOff: null, // Several table elements. When `char`, offsets the alignment
    classId: null, // `<object>`
    clear: null, // `<br>`. Use CSS `clear` instead
    code: null, // `<object>`
    codeBase: null, // `<object>`
    codeType: null, // `<object>`
    color: null, // `<font>` and `<hr>`. Use CSS instead
    compact: boolean, // Lists. Use CSS to reduce space between items instead
    declare: boolean, // `<object>`
    event: null, // `<script>`
    face: null, // `<font>`. Use CSS instead
    frame: null, // `<table>`
    frameBorder: null, // `<iframe>`. Use CSS `border` instead
    hSpace: number, // `<img>` and `<object>`
    leftMargin: number, // `<body>`
    link: null, // `<body>`. Use CSS `a:link {color: *}` instead
    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`
    lowSrc: null, // `<img>`. Use a `<picture>`
    marginHeight: number, // `<body>`
    marginWidth: number, // `<body>`
    noResize: boolean, // `<frame>`
    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`
    noShade: boolean, // `<hr>`. Use background-color and height instead of borders
    noWrap: boolean, // `<td>` and `<th>`
    object: null, // `<applet>`
    profile: null, // `<head>`
    prompt: null, // `<isindex>`
    rev: null, // `<link>`
    rightMargin: number, // `<body>`
    rules: null, // `<table>`
    scheme: null, // `<meta>`
    scrolling: booleanish, // `<frame>`. Use overflow in the child context
    standby: null, // `<object>`
    summary: null, // `<table>`
    text: null, // `<body>`. Use CSS `color` instead
    topMargin: number, // `<body>`
    valueType: null, // `<param>`
    version: null, // `<html>`. Use a doctype.
    vAlign: null, // Several. Use CSS `vertical-align` instead
    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead
    vSpace: number, // `<img>` and `<object>`

    // Non-standard Properties.
    allowTransparency: null,
    autoCorrect: null,
    autoSave: null,
    disablePictureInPicture: boolean,
    disableRemotePlayback: boolean,
    prefix: null,
    property: null,
    results: number,
    security: null,
    unselectable: null
  }
})


/***/ })

}]);