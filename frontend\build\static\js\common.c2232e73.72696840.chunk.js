"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8293],{

/***/ 94588:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cd: () => (/* binding */ useSelection),
/* harmony export */   EF: () => (/* binding */ useContextMenu),
/* harmony export */   KW: () => (/* binding */ useKeyboardShortcuts),
/* harmony export */   R2: () => (/* binding */ useLoadingState),
/* harmony export */   aD: () => (/* binding */ useUndoRedo),
/* harmony export */   iD: () => (/* binding */ useClipboard)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/**
 * Hook for managing undo/redo functionality
 * @param {*} initialState - Initial state value
 * @param {number} maxHistorySize - Maximum number of history entries to keep
 * @returns {Object} State and undo/redo functions
 */
var useUndoRedo = function useUndoRedo(initialState) {
  var maxHistorySize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 50;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([initialState]),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    history = _useState2[0],
    setHistory = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    currentIndex = _useState4[0],
    setCurrentIndex = _useState4[1];
  var isUndoRedoAction = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);

  // Get current state
  var currentState = history[currentIndex];

  // Push new state to history
  var pushState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (newState) {
    if (isUndoRedoAction.current) {
      isUndoRedoAction.current = false;
      return;
    }
    setHistory(function (prev) {
      var newHistory = prev.slice(0, currentIndex + 1);
      newHistory.push(newState);

      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
        return newHistory;
      }
      return newHistory;
    });
    setCurrentIndex(function (prev) {
      var newIndex = Math.min(prev + 1, maxHistorySize - 1);
      return newIndex;
    });
  }, [currentIndex, maxHistorySize]);

  // Undo function
  var undo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (currentIndex > 0) {
      isUndoRedoAction.current = true;
      setCurrentIndex(function (prev) {
        return prev - 1;
      });
      return history[currentIndex - 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Redo function
  var redo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (currentIndex < history.length - 1) {
      isUndoRedoAction.current = true;
      setCurrentIndex(function (prev) {
        return prev + 1;
      });
      return history[currentIndex + 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Check if undo is available
  var canUndo = currentIndex > 0;

  // Check if redo is available
  var canRedo = currentIndex < history.length - 1;

  // Clear history
  var clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setHistory([currentState]);
    setCurrentIndex(0);
  }, [currentState]);

  // Get history info
  var getHistoryInfo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return {
      totalStates: history.length,
      currentIndex: currentIndex,
      canUndo: canUndo,
      canRedo: canRedo
    };
  }, [history.length, currentIndex, canUndo, canRedo]);
  return {
    state: currentState,
    pushState: pushState,
    undo: undo,
    redo: redo,
    canUndo: canUndo,
    canRedo: canRedo,
    clearHistory: clearHistory,
    getHistoryInfo: getHistoryInfo
  };
};

/**
 * Hook for keyboard shortcuts
 * @param {Object} shortcuts - Object mapping key combinations to functions
 * @param {Array} dependencies - Dependencies for the effect
 */
var useKeyboardShortcuts = function useKeyboardShortcuts(shortcuts) {
  var dependencies = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleKeyDown = function handleKeyDown(event) {
      var ctrlKey = event.ctrlKey,
        metaKey = event.metaKey,
        shiftKey = event.shiftKey,
        altKey = event.altKey,
        key = event.key;

      // Create key combination string
      var modifiers = [];
      if (ctrlKey || metaKey) modifiers.push('ctrl');
      if (shiftKey) modifiers.push('shift');
      if (altKey) modifiers.push('alt');
      var combination = [].concat(modifiers, [key.toLowerCase()]).join('+');

      // Check if combination exists in shortcuts
      if (shortcuts[combination]) {
        event.preventDefault();
        shortcuts[combination](event);
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return function () {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, dependencies);
};

/**
 * Hook for managing contextual menus
 * @returns {Object} Context menu state and functions
 */
var useContextMenu = function useContextMenu() {
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      visible: false,
      x: 0,
      y: 0,
      items: []
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    contextMenu = _useState6[0],
    setContextMenu = _useState6[1];
  var showContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (event, items) {
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      items: items || []
    });
  }, []);
  var hideContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setContextMenu(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        visible: false
      });
    });
  }, []);

  // Hide context menu when clicking outside
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleClick = function handleClick() {
      if (contextMenu.visible) {
        hideContextMenu();
      }
    };
    document.addEventListener('click', handleClick);
    return function () {
      document.removeEventListener('click', handleClick);
    };
  }, [contextMenu.visible, hideContextMenu]);
  return {
    contextMenu: contextMenu,
    showContextMenu: showContextMenu,
    hideContextMenu: hideContextMenu
  };
};

/**
 * Hook for managing loading states with debouncing
 * @param {number} delay - Delay before showing loading state
 * @returns {Object} Loading state and functions
 */
var useLoadingState = function useLoadingState() {
  var delay = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 200;
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    isLoading = _useState8[0],
    setIsLoading = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(''),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    loadingMessage = _useState0[0],
    setLoadingMessage = _useState0[1];
  var timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
  var startLoading = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    var message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Loading...';
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(function () {
      setIsLoading(true);
      setLoadingMessage(message);
    }, delay);
  }, [delay]);
  var stopLoading = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsLoading(false);
    setLoadingMessage('');
  }, []);

  // Cleanup timeout on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    return function () {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  return {
    isLoading: isLoading,
    loadingMessage: loadingMessage,
    startLoading: startLoading,
    stopLoading: stopLoading
  };
};

/**
 * Hook for managing component selection with multi-select support
 * @param {Array} items - Array of selectable items
 * @returns {Object} Selection state and functions
 */
var useSelection = function useSelection() {
  var items = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set()),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    selectedItems = _useState10[0],
    setSelectedItems = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState11, 2),
    lastSelectedIndex = _useState12[0],
    setLastSelectedIndex = _useState12[1];
  var selectItem = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (item) {
    var multiSelect = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var itemIndex = items.findIndex(function (i) {
      return i.id === item.id;
    });
    setSelectedItems(function (prev) {
      var newSelection = new Set(multiSelect ? prev : []);
      if (newSelection.has(item.id)) {
        newSelection["delete"](item.id);
      } else {
        newSelection.add(item.id);
      }
      return newSelection;
    });
    setLastSelectedIndex(itemIndex);
  }, [items]);
  var selectRange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (item) {
    var itemIndex = items.findIndex(function (i) {
      return i.id === item.id;
    });
    if (lastSelectedIndex !== -1) {
      var start = Math.min(lastSelectedIndex, itemIndex);
      var end = Math.max(lastSelectedIndex, itemIndex);
      setSelectedItems(function (prev) {
        var newSelection = new Set(prev);
        for (var i = start; i <= end; i++) {
          if (items[i]) {
            newSelection.add(items[i].id);
          }
        }
        return newSelection;
      });
    } else {
      selectItem(item);
    }
  }, [items, lastSelectedIndex, selectItem]);
  var selectAll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setSelectedItems(new Set(items.map(function (item) {
      return item.id;
    })));
  }, [items]);
  var clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setSelectedItems(new Set());
    setLastSelectedIndex(-1);
  }, []);
  var isSelected = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (itemId) {
    return selectedItems.has(itemId);
  }, [selectedItems]);
  var getSelectedItems = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return items.filter(function (item) {
      return selectedItems.has(item.id);
    });
  }, [items, selectedItems]);
  return {
    selectedItems: Array.from(selectedItems),
    selectItem: selectItem,
    selectRange: selectRange,
    selectAll: selectAll,
    clearSelection: clearSelection,
    isSelected: isSelected,
    getSelectedItems: getSelectedItems,
    selectedCount: selectedItems.size
  };
};

/**
 * Hook for managing clipboard operations
 * @returns {Object} Clipboard functions
 */
var useClipboard = function useClipboard() {
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState13, 2),
    clipboardData = _useState14[0],
    setClipboardData = _useState14[1];
  var copy = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (data) {
    setClipboardData(data);

    // Also copy to system clipboard if possible
    if (navigator.clipboard && typeof data === 'string') {
      navigator.clipboard.writeText(data)["catch"](console.error);
    }
  }, []);
  var paste = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return clipboardData;
  }, [clipboardData]);
  var clear = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setClipboardData(null);
  }, []);
  var hasData = clipboardData !== null;
  return {
    copy: copy,
    paste: paste,
    clear: clear,
    hasData: hasData,
    data: clipboardData
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useUndoRedo)));

/***/ }),

/***/ 97787:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(70823);

/**
 * useWebSocket Hook
 *
 * A React hook for using WebSockets in components.
 */



/**
 * Hook for using WebSockets in React components
 * @param {string|Object} options - WebSocket URL or options object
 * @param {string} options.url - WebSocket URL
 * @param {string} options.endpoint - WebSocket endpoint (alternative to URL)
 * @param {boolean} options.autoConnect - Automatically connect on mount (default: true)
 * @param {boolean} options.reconnect - Automatically reconnect on close (default: true)
 * @param {number} options.reconnectInterval - Initial reconnect interval in ms (default: 1000)
 * @param {number} options.maxReconnectAttempts - Maximum reconnect attempts (default: 10)
 * @param {boolean} options.debug - Enable debug logging (default: false)
 * @param {Function} options.onOpen - Callback when connection opens
 * @param {Function} options.onMessage - Callback when message is received
 * @param {Function} options.onClose - Callback when connection closes
 * @param {Function} options.onError - Callback when error occurs
 * @returns {Object} WebSocket hook API
 */
var useWebSocket = function useWebSocket(options) {
  // Handle string URL as options
  var config = typeof options === 'string' ? {
    url: options
  } : options;

  // State for connection status and last message
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.CLOSED),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    connectionState = _useState2[0],
    setConnectionState = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    lastMessage = _useState4[0],
    setLastMessage = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    lastError = _useState6[0],
    setLastError = _useState6[1];

  // Refs for WebSocketClient and callbacks
  var clientRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
  var callbacksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({
    onOpen: config.onOpen,
    onMessage: config.onMessage,
    onClose: config.onClose,
    onError: config.onError
  });

  // Update callbacks ref when props change
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    callbacksRef.current = {
      onOpen: config.onOpen,
      onMessage: config.onMessage,
      onClose: config.onClose,
      onError: config.onError
    };
  }, [config.onOpen, config.onMessage, config.onClose, config.onError]);

  // Initialize WebSocketClient
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    // Create client options
    var clientOptions = {
      url: config.url,
      reconnectInterval: config.reconnectInterval,
      maxReconnectAttempts: config.maxReconnectAttempts,
      debug: config.debug,
      automaticOpen: false // We'll handle connection manually
    };

    // Create client
    var client;
    if (config.endpoint) {
      client = _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.forEndpoint(config.endpoint, clientOptions);
    } else {
      client = new _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A(clientOptions);
    }
    clientRef.current = client;

    // Set up event listeners
    var stateChangeListener = function stateChangeListener(_ref) {
      var currentState = _ref.currentState;
      setConnectionState(currentState);
    };
    var openListener = function openListener(event) {
      if (callbacksRef.current.onOpen) {
        callbacksRef.current.onOpen(event);
      }
    };
    var messageListener = function messageListener(_ref2) {
      var data = _ref2.data;
      setLastMessage(data);
      if (callbacksRef.current.onMessage) {
        callbacksRef.current.onMessage(data);
      }
    };
    var closeListener = function closeListener(event) {
      if (callbacksRef.current.onClose) {
        callbacksRef.current.onClose(event);
      }
    };
    var errorListener = function errorListener(event) {
      // Create a more detailed error object
      var errorDetails = {
        timestamp: new Date().toISOString(),
        type: event.type || 'unknown',
        message: event.message || 'WebSocket connection error',
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        originalEvent: event
      };

      // Log detailed error information
      if (config.debug) {
        console.error('WebSocket Error:', errorDetails);
      }

      // Update error state
      setLastError(errorDetails);

      // Call error callback with enhanced error information
      if (callbacksRef.current.onError) {
        callbacksRef.current.onError(errorDetails);
      }
    };

    // Add event listeners
    client.addEventListener('state_change', stateChangeListener);
    client.addEventListener('open', openListener);
    client.addEventListener('message', messageListener);
    client.addEventListener('close', closeListener);
    client.addEventListener('error', errorListener);

    // Connect if autoConnect is true
    if (config.autoConnect !== false) {
      client.open();
    }

    // Cleanup function
    return function () {
      // Remove event listeners
      client.removeEventListener('state_change', stateChangeListener);
      client.removeEventListener('open', openListener);
      client.removeEventListener('message', messageListener);
      client.removeEventListener('close', closeListener);
      client.removeEventListener('error', errorListener);

      // Close connection
      client.close();
    };
  }, [config.url, config.endpoint]); // Only recreate client if URL or endpoint changes

  // Connect function with retry logic
  var connect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {
    if (clientRef.current) {
      try {
        // Reset error state when attempting to connect
        setLastError(null);

        // Attempt to open the connection
        clientRef.current.open();

        // Log connection attempt if debug is enabled
        if (config.debug) {
          console.log("WebSocket: Attempting to connect to ".concat(config.url || config.endpoint));
        }
      } catch (error) {
        // Handle any synchronous errors during connection attempt
        var errorDetails = {
          timestamp: new Date().toISOString(),
          type: 'connection_error',
          message: error.message || 'Failed to initiate WebSocket connection',
          originalError: error
        };
        setLastError(errorDetails);
        if (callbacksRef.current.onError) {
          callbacksRef.current.onError(errorDetails);
        }
      }
    }
  }, [config.debug, config.url, config.endpoint]);

  // Disconnect function
  var disconnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (code, reason) {
    if (clientRef.current) {
      clientRef.current.close(code, reason);
    }
  }, []);

  // Send function
  var send = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (data) {
    if (clientRef.current) {
      return clientRef.current.send(data);
    }
    return false;
  }, []);

  // Add event listener function
  var addEventListener = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (type, listener, options) {
    if (clientRef.current) {
      return clientRef.current.addEventListener(type, listener, options);
    }
    return function () {};
  }, []);

  // Remove event listener function
  var removeEventListener = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (type, listener) {
    if (clientRef.current) {
      return clientRef.current.removeEventListener(type, listener);
    }
    return false;
  }, []);

  // Return hook API
  return {
    // State
    connectionState: connectionState,
    lastMessage: lastMessage,
    lastError: lastError,
    // Connection status helpers
    isConnecting: connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.CONNECTING || connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.RECONNECTING,
    isOpen: connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.OPEN,
    isClosed: connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.CLOSED || connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.CLOSING,
    hasError: lastError !== null,
    // Error helpers
    errorMessage: lastError ? lastError.message : null,
    errorType: lastError ? lastError.type : null,
    errorTimestamp: lastError ? lastError.timestamp : null,
    // Methods
    connect: connect,
    disconnect: disconnect,
    send: send,
    addEventListener: addEventListener,
    removeEventListener: removeEventListener,
    // Reset error state
    clearError: function clearError() {
      return setLastError(null);
    },
    // Raw client (for advanced usage)
    client: clientRef.current
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useWebSocket);

/***/ })

}]);