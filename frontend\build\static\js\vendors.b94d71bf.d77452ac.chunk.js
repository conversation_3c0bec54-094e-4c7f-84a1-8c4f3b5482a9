(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5249],{

/***/ 24714:
/***/ ((module) => {

/*
Language: MIPS Assembly
Author: <PERSON><PERSON><PERSON><PERSON> Fumika <<EMAIL>>
Description: MIPS Assembly (up to MIPS32R2)
Website: https://en.wikipedia.org/wiki/MIPS_architecture
Category: assembler
*/

function mipsasm(hljs) {
  // local labels: %?[FB]?[AT]?\d{1,2}\w+
  return {
    name: 'MIPS Assembly',
    case_insensitive: true,
    aliases: [ 'mips' ],
    keywords: {
      $pattern: '\\.?' + hljs.IDENT_RE,
      meta:
        // GNU preprocs
        '.2byte .4byte .align .ascii .asciz .balign .byte .code .data .else .end .endif .endm .endr .equ .err .exitm .extern .global .hword .if .ifdef .ifndef .include .irp .long .macro .rept .req .section .set .skip .space .text .word .ltorg ',
      built_in:
        '$0 $1 $2 $3 $4 $5 $6 $7 $8 $9 $10 $11 $12 $13 $14 $15 ' + // integer registers
        '$16 $17 $18 $19 $20 $21 $22 $23 $24 $25 $26 $27 $28 $29 $30 $31 ' + // integer registers
        'zero at v0 v1 a0 a1 a2 a3 a4 a5 a6 a7 ' + // integer register aliases
        't0 t1 t2 t3 t4 t5 t6 t7 t8 t9 s0 s1 s2 s3 s4 s5 s6 s7 s8 ' + // integer register aliases
        'k0 k1 gp sp fp ra ' + // integer register aliases
        '$f0 $f1 $f2 $f2 $f4 $f5 $f6 $f7 $f8 $f9 $f10 $f11 $f12 $f13 $f14 $f15 ' + // floating-point registers
        '$f16 $f17 $f18 $f19 $f20 $f21 $f22 $f23 $f24 $f25 $f26 $f27 $f28 $f29 $f30 $f31 ' + // floating-point registers
        'Context Random EntryLo0 EntryLo1 Context PageMask Wired EntryHi ' + // Coprocessor 0 registers
        'HWREna BadVAddr Count Compare SR IntCtl SRSCtl SRSMap Cause EPC PRId ' + // Coprocessor 0 registers
        'EBase Config Config1 Config2 Config3 LLAddr Debug DEPC DESAVE CacheErr ' + // Coprocessor 0 registers
        'ECC ErrorEPC TagLo DataLo TagHi DataHi WatchLo WatchHi PerfCtl PerfCnt ' // Coprocessor 0 registers
    },
    contains: [
      {
        className: 'keyword',
        begin: '\\b(' + // mnemonics
            // 32-bit integer instructions
            'addi?u?|andi?|b(al)?|beql?|bgez(al)?l?|bgtzl?|blezl?|bltz(al)?l?|' +
            'bnel?|cl[oz]|divu?|ext|ins|j(al)?|jalr(\\.hb)?|jr(\\.hb)?|lbu?|lhu?|' +
            'll|lui|lw[lr]?|maddu?|mfhi|mflo|movn|movz|move|msubu?|mthi|mtlo|mul|' +
            'multu?|nop|nor|ori?|rotrv?|sb|sc|se[bh]|sh|sllv?|slti?u?|srav?|' +
            'srlv?|subu?|sw[lr]?|xori?|wsbh|' +
            // floating-point instructions
            'abs\\.[sd]|add\\.[sd]|alnv.ps|bc1[ft]l?|' +
            'c\\.(s?f|un|u?eq|[ou]lt|[ou]le|ngle?|seq|l[et]|ng[et])\\.[sd]|' +
            '(ceil|floor|round|trunc)\\.[lw]\\.[sd]|cfc1|cvt\\.d\\.[lsw]|' +
            'cvt\\.l\\.[dsw]|cvt\\.ps\\.s|cvt\\.s\\.[dlw]|cvt\\.s\\.p[lu]|cvt\\.w\\.[dls]|' +
            'div\\.[ds]|ldx?c1|luxc1|lwx?c1|madd\\.[sd]|mfc1|mov[fntz]?\\.[ds]|' +
            'msub\\.[sd]|mth?c1|mul\\.[ds]|neg\\.[ds]|nmadd\\.[ds]|nmsub\\.[ds]|' +
            'p[lu][lu]\\.ps|recip\\.fmt|r?sqrt\\.[ds]|sdx?c1|sub\\.[ds]|suxc1|' +
            'swx?c1|' +
            // system control instructions
            'break|cache|d?eret|[de]i|ehb|mfc0|mtc0|pause|prefx?|rdhwr|' +
            'rdpgpr|sdbbp|ssnop|synci?|syscall|teqi?|tgei?u?|tlb(p|r|w[ir])|' +
            'tlti?u?|tnei?|wait|wrpgpr' +
        ')',
        end: '\\s'
      },
      // lines ending with ; or # aren't really comments, probably auto-detect fail
      hljs.COMMENT('[;#](?!\\s*$)', '$'),
      hljs.C_BLOCK_COMMENT_MODE,
      hljs.QUOTE_STRING_MODE,
      {
        className: 'string',
        begin: '\'',
        end: '[^\\\\]\'',
        relevance: 0
      },
      {
        className: 'title',
        begin: '\\|',
        end: '\\|',
        illegal: '\\n',
        relevance: 0
      },
      {
        className: 'number',
        variants: [
          { // hex
            begin: '0x[0-9a-f]+'
          },
          { // bare number
            begin: '\\b-?\\d+'
          }
        ],
        relevance: 0
      },
      {
        className: 'symbol',
        variants: [
          { // GNU MIPS syntax
            begin: '^\\s*[a-z_\\.\\$][a-z0-9_\\.\\$]+:'
          },
          { // numbered local labels
            begin: '^\\s*[0-9]+:'
          },
          { // number local label reference (backwards, forwards)
            begin: '[0-9]+[bf]'
          }
        ],
        relevance: 0
      }
    ],
    // forward slashes are not allowed
    illegal: /\//
  };
}

module.exports = mipsasm;


/***/ }),

/***/ 29433:
/***/ ((module) => {

/*
Language: Maxima
Author: Robert Dodier <<EMAIL>>
Website: http://maxima.sourceforge.net
Category: scientific
*/

function maxima(hljs) {
  const KEYWORDS =
    'if then else elseif for thru do while unless step in and or not';
  const LITERALS =
    'true false unknown inf minf ind und %e %i %pi %phi %gamma';
  const BUILTIN_FUNCTIONS =
    ' abasep abs absint absolute_real_time acos acosh acot acoth acsc acsch activate' +
    ' addcol add_edge add_edges addmatrices addrow add_vertex add_vertices adjacency_matrix' +
    ' adjoin adjoint af agd airy airy_ai airy_bi airy_dai airy_dbi algsys alg_type' +
    ' alias allroots alphacharp alphanumericp amortization %and annuity_fv' +
    ' annuity_pv antid antidiff AntiDifference append appendfile apply apply1 apply2' +
    ' applyb1 apropos args arit_amortization arithmetic arithsum array arrayapply' +
    ' arrayinfo arraymake arraysetapply ascii asec asech asin asinh askinteger' +
    ' asksign assoc assoc_legendre_p assoc_legendre_q assume assume_external_byte_order' +
    ' asympa at atan atan2 atanh atensimp atom atvalue augcoefmatrix augmented_lagrangian_method' +
    ' av average_degree backtrace bars barsplot barsplot_description base64 base64_decode' +
    ' bashindices batch batchload bc2 bdvac belln benefit_cost bern bernpoly bernstein_approx' +
    ' bernstein_expand bernstein_poly bessel bessel_i bessel_j bessel_k bessel_simplify' +
    ' bessel_y beta beta_incomplete beta_incomplete_generalized beta_incomplete_regularized' +
    ' bezout bfallroots bffac bf_find_root bf_fmin_cobyla bfhzeta bfloat bfloatp' +
    ' bfpsi bfpsi0 bfzeta biconnected_components bimetric binomial bipartition' +
    ' block blockmatrixp bode_gain bode_phase bothcoef box boxplot boxplot_description' +
    ' break bug_report build_info|10 buildq build_sample burn cabs canform canten' +
    ' cardinality carg cartan cartesian_product catch cauchy_matrix cbffac cdf_bernoulli' +
    ' cdf_beta cdf_binomial cdf_cauchy cdf_chi2 cdf_continuous_uniform cdf_discrete_uniform' +
    ' cdf_exp cdf_f cdf_gamma cdf_general_finite_discrete cdf_geometric cdf_gumbel' +
    ' cdf_hypergeometric cdf_laplace cdf_logistic cdf_lognormal cdf_negative_binomial' +
    ' cdf_noncentral_chi2 cdf_noncentral_student_t cdf_normal cdf_pareto cdf_poisson' +
    ' cdf_rank_sum cdf_rayleigh cdf_signed_rank cdf_student_t cdf_weibull cdisplay' +
    ' ceiling central_moment cequal cequalignore cf cfdisrep cfexpand cgeodesic' +
    ' cgreaterp cgreaterpignore changename changevar chaosgame charat charfun charfun2' +
    ' charlist charp charpoly chdir chebyshev_t chebyshev_u checkdiv check_overlaps' +
    ' chinese cholesky christof chromatic_index chromatic_number cint circulant_graph' +
    ' clear_edge_weight clear_rules clear_vertex_label clebsch_gordan clebsch_graph' +
    ' clessp clesspignore close closefile cmetric coeff coefmatrix cograd col collapse' +
    ' collectterms columnop columnspace columnswap columnvector combination combine' +
    ' comp2pui compare compfile compile compile_file complement_graph complete_bipartite_graph' +
    ' complete_graph complex_number_p components compose_functions concan concat' +
    ' conjugate conmetderiv connected_components connect_vertices cons constant' +
    ' constantp constituent constvalue cont2part content continuous_freq contortion' +
    ' contour_plot contract contract_edge contragrad contrib_ode convert coord' +
    ' copy copy_file copy_graph copylist copymatrix cor cos cosh cot coth cov cov1' +
    ' covdiff covect covers crc24sum create_graph create_list csc csch csetup cspline' +
    ' ctaylor ct_coordsys ctransform ctranspose cube_graph cuboctahedron_graph' +
    ' cunlisp cv cycle_digraph cycle_graph cylindrical days360 dblint deactivate' +
    ' declare declare_constvalue declare_dimensions declare_fundamental_dimensions' +
    ' declare_fundamental_units declare_qty declare_translated declare_unit_conversion' +
    ' declare_units declare_weights decsym defcon define define_alt_display define_variable' +
    ' defint defmatch defrule defstruct deftaylor degree_sequence del delete deleten' +
    ' delta demo demoivre denom depends derivdegree derivlist describe desolve' +
    ' determinant dfloat dgauss_a dgauss_b dgeev dgemm dgeqrf dgesv dgesvd diag' +
    ' diagmatrix diag_matrix diagmatrixp diameter diff digitcharp dimacs_export' +
    ' dimacs_import dimension dimensionless dimensions dimensions_as_list direct' +
    ' directory discrete_freq disjoin disjointp disolate disp dispcon dispform' +
    ' dispfun dispJordan display disprule dispterms distrib divide divisors divsum' +
    ' dkummer_m dkummer_u dlange dodecahedron_graph dotproduct dotsimp dpart' +
    ' draw draw2d draw3d drawdf draw_file draw_graph dscalar echelon edge_coloring' +
    ' edge_connectivity edges eigens_by_jacobi eigenvalues eigenvectors eighth' +
    ' einstein eivals eivects elapsed_real_time elapsed_run_time ele2comp ele2polynome' +
    ' ele2pui elem elementp elevation_grid elim elim_allbut eliminate eliminate_using' +
    ' ellipse elliptic_e elliptic_ec elliptic_eu elliptic_f elliptic_kc elliptic_pi' +
    ' ematrix empty_graph emptyp endcons entermatrix entertensor entier equal equalp' +
    ' equiv_classes erf erfc erf_generalized erfi errcatch error errormsg errors' +
    ' euler ev eval_string evenp every evolution evolution2d evundiff example exp' +
    ' expand expandwrt expandwrt_factored expint expintegral_chi expintegral_ci' +
    ' expintegral_e expintegral_e1 expintegral_ei expintegral_e_simplify expintegral_li' +
    ' expintegral_shi expintegral_si explicit explose exponentialize express expt' +
    ' exsec extdiff extract_linear_equations extremal_subset ezgcd %f f90 facsum' +
    ' factcomb factor factorfacsum factorial factorout factorsum facts fast_central_elements' +
    ' fast_linsolve fasttimes featurep fernfale fft fib fibtophi fifth filename_merge' +
    ' file_search file_type fillarray findde find_root find_root_abs find_root_error' +
    ' find_root_rel first fix flatten flength float floatnump floor flower_snark' +
    ' flush flush1deriv flushd flushnd flush_output fmin_cobyla forget fortran' +
    ' fourcos fourexpand fourier fourier_elim fourint fourintcos fourintsin foursimp' +
    ' foursin fourth fposition frame_bracket freeof freshline fresnel_c fresnel_s' +
    ' from_adjacency_matrix frucht_graph full_listify fullmap fullmapl fullratsimp' +
    ' fullratsubst fullsetify funcsolve fundamental_dimensions fundamental_units' +
    ' fundef funmake funp fv g0 g1 gamma gamma_greek gamma_incomplete gamma_incomplete_generalized' +
    ' gamma_incomplete_regularized gauss gauss_a gauss_b gaussprob gcd gcdex gcdivide' +
    ' gcfac gcfactor gd generalized_lambert_w genfact gen_laguerre genmatrix gensym' +
    ' geo_amortization geo_annuity_fv geo_annuity_pv geomap geometric geometric_mean' +
    ' geosum get getcurrentdirectory get_edge_weight getenv get_lu_factors get_output_stream_string' +
    ' get_pixel get_plot_option get_tex_environment get_tex_environment_default' +
    ' get_vertex_label gfactor gfactorsum ggf girth global_variances gn gnuplot_close' +
    ' gnuplot_replot gnuplot_reset gnuplot_restart gnuplot_start go Gosper GosperSum' +
    ' gr2d gr3d gradef gramschmidt graph6_decode graph6_encode graph6_export graph6_import' +
    ' graph_center graph_charpoly graph_eigenvalues graph_flow graph_order graph_periphery' +
    ' graph_product graph_size graph_union great_rhombicosidodecahedron_graph great_rhombicuboctahedron_graph' +
    ' grid_graph grind grobner_basis grotzch_graph hamilton_cycle hamilton_path' +
    ' hankel hankel_1 hankel_2 harmonic harmonic_mean hav heawood_graph hermite' +
    ' hessian hgfred hilbertmap hilbert_matrix hipow histogram histogram_description' +
    ' hodge horner hypergeometric i0 i1 %ibes ic1 ic2 ic_convert ichr1 ichr2 icosahedron_graph' +
    ' icosidodecahedron_graph icurvature ident identfor identity idiff idim idummy' +
    ' ieqn %if ifactors iframes ifs igcdex igeodesic_coords ilt image imagpart' +
    ' imetric implicit implicit_derivative implicit_plot indexed_tensor indices' +
    ' induced_subgraph inferencep inference_result infix info_display init_atensor' +
    ' init_ctensor in_neighbors innerproduct inpart inprod inrt integerp integer_partitions' +
    ' integrate intersect intersection intervalp intopois intosum invariant1 invariant2' +
    ' inverse_fft inverse_jacobi_cd inverse_jacobi_cn inverse_jacobi_cs inverse_jacobi_dc' +
    ' inverse_jacobi_dn inverse_jacobi_ds inverse_jacobi_nc inverse_jacobi_nd inverse_jacobi_ns' +
    ' inverse_jacobi_sc inverse_jacobi_sd inverse_jacobi_sn invert invert_by_adjoint' +
    ' invert_by_lu inv_mod irr is is_biconnected is_bipartite is_connected is_digraph' +
    ' is_edge_in_graph is_graph is_graph_or_digraph ishow is_isomorphic isolate' +
    ' isomorphism is_planar isqrt isreal_p is_sconnected is_tree is_vertex_in_graph' +
    ' items_inference %j j0 j1 jacobi jacobian jacobi_cd jacobi_cn jacobi_cs jacobi_dc' +
    ' jacobi_dn jacobi_ds jacobi_nc jacobi_nd jacobi_ns jacobi_p jacobi_sc jacobi_sd' +
    ' jacobi_sn JF jn join jordan julia julia_set julia_sin %k kdels kdelta kill' +
    ' killcontext kostka kron_delta kronecker_product kummer_m kummer_u kurtosis' +
    ' kurtosis_bernoulli kurtosis_beta kurtosis_binomial kurtosis_chi2 kurtosis_continuous_uniform' +
    ' kurtosis_discrete_uniform kurtosis_exp kurtosis_f kurtosis_gamma kurtosis_general_finite_discrete' +
    ' kurtosis_geometric kurtosis_gumbel kurtosis_hypergeometric kurtosis_laplace' +
    ' kurtosis_logistic kurtosis_lognormal kurtosis_negative_binomial kurtosis_noncentral_chi2' +
    ' kurtosis_noncentral_student_t kurtosis_normal kurtosis_pareto kurtosis_poisson' +
    ' kurtosis_rayleigh kurtosis_student_t kurtosis_weibull label labels lagrange' +
    ' laguerre lambda lambert_w laplace laplacian_matrix last lbfgs lc2kdt lcharp' +
    ' lc_l lcm lc_u ldefint ldisp ldisplay legendre_p legendre_q leinstein length' +
    ' let letrules letsimp levi_civita lfreeof lgtreillis lhs li liediff limit' +
    ' Lindstedt linear linearinterpol linear_program linear_regression line_graph' +
    ' linsolve listarray list_correlations listify list_matrix_entries list_nc_monomials' +
    ' listoftens listofvars listp lmax lmin load loadfile local locate_matrix_entry' +
    ' log logcontract log_gamma lopow lorentz_gauge lowercasep lpart lratsubst' +
    ' lreduce lriemann lsquares_estimates lsquares_estimates_approximate lsquares_estimates_exact' +
    ' lsquares_mse lsquares_residual_mse lsquares_residuals lsum ltreillis lu_backsub' +
    ' lucas lu_factor %m macroexpand macroexpand1 make_array makebox makefact makegamma' +
    ' make_graph make_level_picture makelist makeOrders make_poly_continent make_poly_country' +
    ' make_polygon make_random_state make_rgb_picture makeset make_string_input_stream' +
    ' make_string_output_stream make_transform mandelbrot mandelbrot_set map mapatom' +
    ' maplist matchdeclare matchfix mat_cond mat_fullunblocker mat_function mathml_display' +
    ' mat_norm matrix matrixmap matrixp matrix_size mattrace mat_trace mat_unblocker' +
    ' max max_clique max_degree max_flow maximize_lp max_independent_set max_matching' +
    ' maybe md5sum mean mean_bernoulli mean_beta mean_binomial mean_chi2 mean_continuous_uniform' +
    ' mean_deviation mean_discrete_uniform mean_exp mean_f mean_gamma mean_general_finite_discrete' +
    ' mean_geometric mean_gumbel mean_hypergeometric mean_laplace mean_logistic' +
    ' mean_lognormal mean_negative_binomial mean_noncentral_chi2 mean_noncentral_student_t' +
    ' mean_normal mean_pareto mean_poisson mean_rayleigh mean_student_t mean_weibull' +
    ' median median_deviation member mesh metricexpandall mgf1_sha1 min min_degree' +
    ' min_edge_cut minfactorial minimalPoly minimize_lp minimum_spanning_tree minor' +
    ' minpack_lsquares minpack_solve min_vertex_cover min_vertex_cut mkdir mnewton' +
    ' mod mode_declare mode_identity ModeMatrix moebius mon2schur mono monomial_dimensions' +
    ' multibernstein_poly multi_display_for_texinfo multi_elem multinomial multinomial_coeff' +
    ' multi_orbit multiplot_mode multi_pui multsym multthru mycielski_graph nary' +
    ' natural_unit nc_degree ncexpt ncharpoly negative_picture neighbors new newcontext' +
    ' newdet new_graph newline newton new_variable next_prime nicedummies niceindices' +
    ' ninth nofix nonarray noncentral_moment nonmetricity nonnegintegerp nonscalarp' +
    ' nonzeroandfreeof notequal nounify nptetrad npv nroots nterms ntermst' +
    ' nthroot nullity nullspace num numbered_boundaries numberp number_to_octets' +
    ' num_distinct_partitions numerval numfactor num_partitions nusum nzeta nzetai' +
    ' nzetar octets_to_number octets_to_oid odd_girth oddp ode2 ode_check odelin' +
    ' oid_to_octets op opena opena_binary openr openr_binary openw openw_binary' +
    ' operatorp opsubst optimize %or orbit orbits ordergreat ordergreatp orderless' +
    ' orderlessp orthogonal_complement orthopoly_recur orthopoly_weight outermap' +
    ' out_neighbors outofpois pade parabolic_cylinder_d parametric parametric_surface' +
    ' parg parGosper parse_string parse_timedate part part2cont partfrac partition' +
    ' partition_set partpol path_digraph path_graph pathname_directory pathname_name' +
    ' pathname_type pdf_bernoulli pdf_beta pdf_binomial pdf_cauchy pdf_chi2 pdf_continuous_uniform' +
    ' pdf_discrete_uniform pdf_exp pdf_f pdf_gamma pdf_general_finite_discrete' +
    ' pdf_geometric pdf_gumbel pdf_hypergeometric pdf_laplace pdf_logistic pdf_lognormal' +
    ' pdf_negative_binomial pdf_noncentral_chi2 pdf_noncentral_student_t pdf_normal' +
    ' pdf_pareto pdf_poisson pdf_rank_sum pdf_rayleigh pdf_signed_rank pdf_student_t' +
    ' pdf_weibull pearson_skewness permanent permut permutation permutations petersen_graph' +
    ' petrov pickapart picture_equalp picturep piechart piechart_description planar_embedding' +
    ' playback plog plot2d plot3d plotdf ploteq plsquares pochhammer points poisdiff' +
    ' poisexpt poisint poismap poisplus poissimp poissubst poistimes poistrim polar' +
    ' polarform polartorect polar_to_xy poly_add poly_buchberger poly_buchberger_criterion' +
    ' poly_colon_ideal poly_content polydecomp poly_depends_p poly_elimination_ideal' +
    ' poly_exact_divide poly_expand poly_expt poly_gcd polygon poly_grobner poly_grobner_equal' +
    ' poly_grobner_member poly_grobner_subsetp poly_ideal_intersection poly_ideal_polysaturation' +
    ' poly_ideal_polysaturation1 poly_ideal_saturation poly_ideal_saturation1 poly_lcm' +
    ' poly_minimization polymod poly_multiply polynome2ele polynomialp poly_normal_form' +
    ' poly_normalize poly_normalize_list poly_polysaturation_extension poly_primitive_part' +
    ' poly_pseudo_divide poly_reduced_grobner poly_reduction poly_saturation_extension' +
    ' poly_s_polynomial poly_subtract polytocompanion pop postfix potential power_mod' +
    ' powerseries powerset prefix prev_prime primep primes principal_components' +
    ' print printf printfile print_graph printpois printprops prodrac product properties' +
    ' propvars psi psubst ptriangularize pui pui2comp pui2ele pui2polynome pui_direct' +
    ' puireduc push put pv qput qrange qty quad_control quad_qag quad_qagi quad_qagp' +
    ' quad_qags quad_qawc quad_qawf quad_qawo quad_qaws quadrilateral quantile' +
    ' quantile_bernoulli quantile_beta quantile_binomial quantile_cauchy quantile_chi2' +
    ' quantile_continuous_uniform quantile_discrete_uniform quantile_exp quantile_f' +
    ' quantile_gamma quantile_general_finite_discrete quantile_geometric quantile_gumbel' +
    ' quantile_hypergeometric quantile_laplace quantile_logistic quantile_lognormal' +
    ' quantile_negative_binomial quantile_noncentral_chi2 quantile_noncentral_student_t' +
    ' quantile_normal quantile_pareto quantile_poisson quantile_rayleigh quantile_student_t' +
    ' quantile_weibull quartile_skewness quit qunit quotient racah_v racah_w radcan' +
    ' radius random random_bernoulli random_beta random_binomial random_bipartite_graph' +
    ' random_cauchy random_chi2 random_continuous_uniform random_digraph random_discrete_uniform' +
    ' random_exp random_f random_gamma random_general_finite_discrete random_geometric' +
    ' random_graph random_graph1 random_gumbel random_hypergeometric random_laplace' +
    ' random_logistic random_lognormal random_negative_binomial random_network' +
    ' random_noncentral_chi2 random_noncentral_student_t random_normal random_pareto' +
    ' random_permutation random_poisson random_rayleigh random_regular_graph random_student_t' +
    ' random_tournament random_tree random_weibull range rank rat ratcoef ratdenom' +
    ' ratdiff ratdisrep ratexpand ratinterpol rational rationalize ratnumer ratnump' +
    ' ratp ratsimp ratsubst ratvars ratweight read read_array read_binary_array' +
    ' read_binary_list read_binary_matrix readbyte readchar read_hashed_array readline' +
    ' read_list read_matrix read_nested_list readonly read_xpm real_imagpart_to_conjugate' +
    ' realpart realroots rearray rectangle rectform rectform_log_if_constant recttopolar' +
    ' rediff reduce_consts reduce_order region region_boundaries region_boundaries_plus' +
    ' rem remainder remarray rembox remcomps remcon remcoord remfun remfunction' +
    ' remlet remove remove_constvalue remove_dimensions remove_edge remove_fundamental_dimensions' +
    ' remove_fundamental_units remove_plot_option remove_vertex rempart remrule' +
    ' remsym remvalue rename rename_file reset reset_displays residue resolvante' +
    ' resolvante_alternee1 resolvante_bipartite resolvante_diedrale resolvante_klein' +
    ' resolvante_klein3 resolvante_produit_sym resolvante_unitaire resolvante_vierer' +
    ' rest resultant return reveal reverse revert revert2 rgb2level rhs ricci riemann' +
    ' rinvariant risch rk rmdir rncombine romberg room rootscontract round row' +
    ' rowop rowswap rreduce run_testsuite %s save saving scalarp scaled_bessel_i' +
    ' scaled_bessel_i0 scaled_bessel_i1 scalefactors scanmap scatterplot scatterplot_description' +
    ' scene schur2comp sconcat scopy scsimp scurvature sdowncase sec sech second' +
    ' sequal sequalignore set_alt_display setdifference set_draw_defaults set_edge_weight' +
    ' setelmx setequalp setify setp set_partitions set_plot_option set_prompt set_random_state' +
    ' set_tex_environment set_tex_environment_default setunits setup_autoload set_up_dot_simplifications' +
    ' set_vertex_label seventh sexplode sf sha1sum sha256sum shortest_path shortest_weighted_path' +
    ' show showcomps showratvars sierpinskiale sierpinskimap sign signum similaritytransform' +
    ' simp_inequality simplify_sum simplode simpmetderiv simtran sin sinh sinsert' +
    ' sinvertcase sixth skewness skewness_bernoulli skewness_beta skewness_binomial' +
    ' skewness_chi2 skewness_continuous_uniform skewness_discrete_uniform skewness_exp' +
    ' skewness_f skewness_gamma skewness_general_finite_discrete skewness_geometric' +
    ' skewness_gumbel skewness_hypergeometric skewness_laplace skewness_logistic' +
    ' skewness_lognormal skewness_negative_binomial skewness_noncentral_chi2 skewness_noncentral_student_t' +
    ' skewness_normal skewness_pareto skewness_poisson skewness_rayleigh skewness_student_t' +
    ' skewness_weibull slength smake small_rhombicosidodecahedron_graph small_rhombicuboctahedron_graph' +
    ' smax smin smismatch snowmap snub_cube_graph snub_dodecahedron_graph solve' +
    ' solve_rec solve_rec_rat some somrac sort sparse6_decode sparse6_encode sparse6_export' +
    ' sparse6_import specint spherical spherical_bessel_j spherical_bessel_y spherical_hankel1' +
    ' spherical_hankel2 spherical_harmonic spherical_to_xyz splice split sposition' +
    ' sprint sqfr sqrt sqrtdenest sremove sremovefirst sreverse ssearch ssort sstatus' +
    ' ssubst ssubstfirst staircase standardize standardize_inverse_trig starplot' +
    ' starplot_description status std std1 std_bernoulli std_beta std_binomial' +
    ' std_chi2 std_continuous_uniform std_discrete_uniform std_exp std_f std_gamma' +
    ' std_general_finite_discrete std_geometric std_gumbel std_hypergeometric std_laplace' +
    ' std_logistic std_lognormal std_negative_binomial std_noncentral_chi2 std_noncentral_student_t' +
    ' std_normal std_pareto std_poisson std_rayleigh std_student_t std_weibull' +
    ' stemplot stirling stirling1 stirling2 strim striml strimr string stringout' +
    ' stringp strong_components struve_h struve_l sublis sublist sublist_indices' +
    ' submatrix subsample subset subsetp subst substinpart subst_parallel substpart' +
    ' substring subvar subvarp sum sumcontract summand_to_rec supcase supcontext' +
    ' symbolp symmdifference symmetricp system take_channel take_inference tan' +
    ' tanh taylor taylorinfo taylorp taylor_simplifier taytorat tcl_output tcontract' +
    ' tellrat tellsimp tellsimpafter tentex tenth test_mean test_means_difference' +
    ' test_normality test_proportion test_proportions_difference test_rank_sum' +
    ' test_sign test_signed_rank test_variance test_variance_ratio tex tex1 tex_display' +
    ' texput %th third throw time timedate timer timer_info tldefint tlimit todd_coxeter' +
    ' toeplitz tokens to_lisp topological_sort to_poly to_poly_solve totaldisrep' +
    ' totalfourier totient tpartpol trace tracematrix trace_options transform_sample' +
    ' translate translate_file transpose treefale tree_reduce treillis treinat' +
    ' triangle triangularize trigexpand trigrat trigreduce trigsimp trunc truncate' +
    ' truncated_cube_graph truncated_dodecahedron_graph truncated_icosahedron_graph' +
    ' truncated_tetrahedron_graph tr_warnings_get tube tutte_graph ueivects uforget' +
    ' ultraspherical underlying_graph undiff union unique uniteigenvectors unitp' +
    ' units unit_step unitvector unorder unsum untellrat untimer' +
    ' untrace uppercasep uricci uriemann uvect vandermonde_matrix var var1 var_bernoulli' +
    ' var_beta var_binomial var_chi2 var_continuous_uniform var_discrete_uniform' +
    ' var_exp var_f var_gamma var_general_finite_discrete var_geometric var_gumbel' +
    ' var_hypergeometric var_laplace var_logistic var_lognormal var_negative_binomial' +
    ' var_noncentral_chi2 var_noncentral_student_t var_normal var_pareto var_poisson' +
    ' var_rayleigh var_student_t var_weibull vector vectorpotential vectorsimp' +
    ' verbify vers vertex_coloring vertex_connectivity vertex_degree vertex_distance' +
    ' vertex_eccentricity vertex_in_degree vertex_out_degree vertices vertices_to_cycle' +
    ' vertices_to_path %w weyl wheel_graph wiener_index wigner_3j wigner_6j' +
    ' wigner_9j with_stdout write_binary_data writebyte write_data writefile wronskian' +
    ' xreduce xthru %y Zeilberger zeroequiv zerofor zeromatrix zeromatrixp zeta' +
    ' zgeev zheev zlange zn_add_table zn_carmichael_lambda zn_characteristic_factors' +
    ' zn_determinant zn_factor_generators zn_invert_by_lu zn_log zn_mult_table' +
    ' absboxchar activecontexts adapt_depth additive adim aform algebraic' +
    ' algepsilon algexact aliases allbut all_dotsimp_denoms allocation allsym alphabetic' +
    ' animation antisymmetric arrays askexp assume_pos assume_pos_pred assumescalar' +
    ' asymbol atomgrad atrig1 axes axis_3d axis_bottom axis_left axis_right axis_top' +
    ' azimuth background background_color backsubst berlefact bernstein_explicit' +
    ' besselexpand beta_args_sum_to_integer beta_expand bftorat bftrunc bindtest' +
    ' border boundaries_array box boxchar breakup %c capping cauchysum cbrange' +
    ' cbtics center cflength cframe_flag cnonmet_flag color color_bar color_bar_tics' +
    ' colorbox columns commutative complex cone context contexts contour contour_levels' +
    ' cosnpiflag ctaypov ctaypt ctayswitch ctayvar ct_coords ctorsion_flag ctrgsimp' +
    ' cube current_let_rule_package cylinder data_file_name debugmode decreasing' +
    ' default_let_rule_package delay dependencies derivabbrev derivsubst detout' +
    ' diagmetric diff dim dimensions dispflag display2d|10 display_format_internal' +
    ' distribute_over doallmxops domain domxexpt domxmxops domxnctimes dontfactor' +
    ' doscmxops doscmxplus dot0nscsimp dot0simp dot1simp dotassoc dotconstrules' +
    ' dotdistrib dotexptsimp dotident dotscrules draw_graph_program draw_realpart' +
    ' edge_color edge_coloring edge_partition edge_type edge_width %edispflag' +
    ' elevation %emode endphi endtheta engineering_format_floats enhanced3d %enumer' +
    ' epsilon_lp erfflag erf_representation errormsg error_size error_syms error_type' +
    ' %e_to_numlog eval even evenfun evflag evfun ev_point expandwrt_denom expintexpand' +
    ' expintrep expon expop exptdispflag exptisolate exptsubst facexpand facsum_combine' +
    ' factlim factorflag factorial_expand factors_only fb feature features' +
    ' file_name file_output_append file_search_demo file_search_lisp file_search_maxima|10' +
    ' file_search_tests file_search_usage file_type_lisp file_type_maxima|10 fill_color' +
    ' fill_density filled_func fixed_vertices flipflag float2bf font font_size' +
    ' fortindent fortspaces fpprec fpprintprec functions gamma_expand gammalim' +
    ' gdet genindex gensumnum GGFCFMAX GGFINFINITY globalsolve gnuplot_command' +
    ' gnuplot_curve_styles gnuplot_curve_titles gnuplot_default_term_command gnuplot_dumb_term_command' +
    ' gnuplot_file_args gnuplot_file_name gnuplot_out_file gnuplot_pdf_term_command' +
    ' gnuplot_pm3d gnuplot_png_term_command gnuplot_postamble gnuplot_preamble' +
    ' gnuplot_ps_term_command gnuplot_svg_term_command gnuplot_term gnuplot_view_args' +
    ' Gosper_in_Zeilberger gradefs grid grid2d grind halfangles head_angle head_both' +
    ' head_length head_type height hypergeometric_representation %iargs ibase' +
    ' icc1 icc2 icounter idummyx ieqnprint ifb ifc1 ifc2 ifg ifgi ifr iframe_bracket_form' +
    ' ifri igeowedge_flag ikt1 ikt2 imaginary inchar increasing infeval' +
    ' infinity inflag infolists inm inmc1 inmc2 intanalysis integer integervalued' +
    ' integrate_use_rootsof integration_constant integration_constant_counter interpolate_color' +
    ' intfaclim ip_grid ip_grid_in irrational isolate_wrt_times iterations itr' +
    ' julia_parameter %k1 %k2 keepfloat key key_pos kinvariant kt label label_alignment' +
    ' label_orientation labels lassociative lbfgs_ncorrections lbfgs_nfeval_max' +
    ' leftjust legend letrat let_rule_packages lfg lg lhospitallim limsubst linear' +
    ' linear_solver linechar linel|10 linenum line_type linewidth line_width linsolve_params' +
    ' linsolvewarn lispdisp listarith listconstvars listdummyvars lmxchar load_pathname' +
    ' loadprint logabs logarc logcb logconcoeffp logexpand lognegint logsimp logx' +
    ' logx_secondary logy logy_secondary logz lriem m1pbranch macroexpansion macros' +
    ' mainvar manual_demo maperror mapprint matrix_element_add matrix_element_mult' +
    ' matrix_element_transpose maxapplydepth maxapplyheight maxima_tempdir|10 maxima_userdir|10' +
    ' maxnegex MAX_ORD maxposex maxpsifracdenom maxpsifracnum maxpsinegint maxpsiposint' +
    ' maxtayorder mesh_lines_color method mod_big_prime mode_check_errorp' +
    ' mode_checkp mode_check_warnp mod_test mod_threshold modular_linear_solver' +
    ' modulus multiplicative multiplicities myoptions nary negdistrib negsumdispflag' +
    ' newline newtonepsilon newtonmaxiter nextlayerfactor niceindicespref nm nmc' +
    ' noeval nolabels nonegative_lp noninteger nonscalar noun noundisp nouns np' +
    ' npi nticks ntrig numer numer_pbranch obase odd oddfun opacity opproperties' +
    ' opsubst optimprefix optionset orientation origin orthopoly_returns_intervals' +
    ' outative outchar packagefile palette partswitch pdf_file pfeformat phiresolution' +
    ' %piargs piece pivot_count_sx pivot_max_sx plot_format plot_options plot_realpart' +
    ' png_file pochhammer_max_index points pointsize point_size points_joined point_type' +
    ' poislim poisson poly_coefficient_ring poly_elimination_order polyfactor poly_grobner_algorithm' +
    ' poly_grobner_debug poly_monomial_order poly_primary_elimination_order poly_return_term_list' +
    ' poly_secondary_elimination_order poly_top_reduction_only posfun position' +
    ' powerdisp pred prederror primep_number_of_tests product_use_gamma program' +
    ' programmode promote_float_to_bigfloat prompt proportional_axes props psexpand' +
    ' ps_file radexpand radius radsubstflag rassociative ratalgdenom ratchristof' +
    ' ratdenomdivide rateinstein ratepsilon ratfac rational ratmx ratprint ratriemann' +
    ' ratsimpexpons ratvarswitch ratweights ratweyl ratwtlvl real realonly redraw' +
    ' refcheck resolution restart resultant ric riem rmxchar %rnum_list rombergabs' +
    ' rombergit rombergmin rombergtol rootsconmode rootsepsilon run_viewer same_xy' +
    ' same_xyz savedef savefactors scalar scalarmatrixp scale scale_lp setcheck' +
    ' setcheckbreak setval show_edge_color show_edges show_edge_type show_edge_width' +
    ' show_id show_label showtime show_vertex_color show_vertex_size show_vertex_type' +
    ' show_vertices show_weight simp simplified_output simplify_products simpproduct' +
    ' simpsum sinnpiflag solvedecomposes solveexplicit solvefactors solvenullwarn' +
    ' solveradcan solvetrigwarn space sparse sphere spring_embedding_depth sqrtdispflag' +
    ' stardisp startphi starttheta stats_numer stringdisp structures style sublis_apply_lambda' +
    ' subnumsimp sumexpand sumsplitfact surface surface_hide svg_file symmetric' +
    ' tab taylordepth taylor_logexpand taylor_order_coefficients taylor_truncate_polynomials' +
    ' tensorkill terminal testsuite_files thetaresolution timer_devalue title tlimswitch' +
    ' tr track transcompile transform transform_xy translate_fast_arrays transparent' +
    ' transrun tr_array_as_ref tr_bound_function_applyp tr_file_tty_messagesp tr_float_can_branch_complex' +
    ' tr_function_call_default trigexpandplus trigexpandtimes triginverses trigsign' +
    ' trivial_solutions tr_numer tr_optimize_max_loop tr_semicompile tr_state_vars' +
    ' tr_warn_bad_function_calls tr_warn_fexpr tr_warn_meval tr_warn_mode' +
    ' tr_warn_undeclared tr_warn_undefined_variable tstep ttyoff tube_extremes' +
    ' ufg ug %unitexpand unit_vectors uric uriem use_fast_arrays user_preamble' +
    ' usersetunits values vect_cross verbose vertex_color vertex_coloring vertex_partition' +
    ' vertex_size vertex_type view warnings weyl width windowname windowtitle wired_surface' +
    ' wireframe xaxis xaxis_color xaxis_secondary xaxis_type xaxis_width xlabel' +
    ' xlabel_secondary xlength xrange xrange_secondary xtics xtics_axis xtics_rotate' +
    ' xtics_rotate_secondary xtics_secondary xtics_secondary_axis xu_grid x_voxel' +
    ' xy_file xyplane xy_scale yaxis yaxis_color yaxis_secondary yaxis_type yaxis_width' +
    ' ylabel ylabel_secondary ylength yrange yrange_secondary ytics ytics_axis' +
    ' ytics_rotate ytics_rotate_secondary ytics_secondary ytics_secondary_axis' +
    ' yv_grid y_voxel yx_ratio zaxis zaxis_color zaxis_type zaxis_width zeroa zerob' +
    ' zerobern zeta%pi zlabel zlabel_rotate zlength zmin zn_primroot_limit zn_primroot_pretest';
  const SYMBOLS = '_ __ %|0 %%|0';

  return {
    name: 'Maxima',
    keywords: {
      $pattern: '[A-Za-z_%][0-9A-Za-z_%]*',
      keyword: KEYWORDS,
      literal: LITERALS,
      built_in: BUILTIN_FUNCTIONS,
      symbol: SYMBOLS
    },
    contains: [
      {
        className: 'comment',
        begin: '/\\*',
        end: '\\*/',
        contains: [ 'self' ]
      },
      hljs.QUOTE_STRING_MODE,
      {
        className: 'number',
        relevance: 0,
        variants: [
          {
            // float number w/ exponent
            // hmm, I wonder if we ought to include other exponent markers?
            begin: '\\b(\\d+|\\d+\\.|\\.\\d+|\\d+\\.\\d+)[Ee][-+]?\\d+\\b'
          },
          {
            // bigfloat number
            begin: '\\b(\\d+|\\d+\\.|\\.\\d+|\\d+\\.\\d+)[Bb][-+]?\\d+\\b',
            relevance: 10
          },
          {
            // float number w/out exponent
            // Doesn't seem to recognize floats which start with '.'
            begin: '\\b(\\.\\d+|\\d+\\.\\d+)\\b'
          },
          {
            // integer in base up to 36
            // Doesn't seem to recognize integers which end with '.'
            begin: '\\b(\\d+|0[0-9A-Za-z]+)\\.?\\b'
          }
        ]
      }
    ],
    illegal: /@/
  };
}

module.exports = maxima;


/***/ }),

/***/ 48557:
/***/ ((module) => {

/*
Language: Mercury
Author: mucaho <<EMAIL>>
Description: Mercury is a logic/functional programming language which combines the clarity and expressiveness of declarative programming with advanced static analysis and error detection features.
Website: https://www.mercurylang.org
*/

function mercury(hljs) {
  const KEYWORDS = {
    keyword:
      'module use_module import_module include_module end_module initialise ' +
      'mutable initialize finalize finalise interface implementation pred ' +
      'mode func type inst solver any_pred any_func is semidet det nondet ' +
      'multi erroneous failure cc_nondet cc_multi typeclass instance where ' +
      'pragma promise external trace atomic or_else require_complete_switch ' +
      'require_det require_semidet require_multi require_nondet ' +
      'require_cc_multi require_cc_nondet require_erroneous require_failure',
    meta:
      // pragma
      'inline no_inline type_spec source_file fact_table obsolete memo ' +
      'loop_check minimal_model terminates does_not_terminate ' +
      'check_termination promise_equivalent_clauses ' +
      // preprocessor
      'foreign_proc foreign_decl foreign_code foreign_type ' +
      'foreign_import_module foreign_export_enum foreign_export ' +
      'foreign_enum may_call_mercury will_not_call_mercury thread_safe ' +
      'not_thread_safe maybe_thread_safe promise_pure promise_semipure ' +
      'tabled_for_io local untrailed trailed attach_to_io_state ' +
      'can_pass_as_mercury_type stable will_not_throw_exception ' +
      'may_modify_trail will_not_modify_trail may_duplicate ' +
      'may_not_duplicate affects_liveness does_not_affect_liveness ' +
      'doesnt_affect_liveness no_sharing unknown_sharing sharing',
    built_in:
      'some all not if then else true fail false try catch catch_any ' +
      'semidet_true semidet_false semidet_fail impure_true impure semipure'
  };

  const COMMENT = hljs.COMMENT('%', '$');

  const NUMCODE = {
    className: 'number',
    begin: "0'.\\|0[box][0-9a-fA-F]*"
  };

  const ATOM = hljs.inherit(hljs.APOS_STRING_MODE, {
    relevance: 0
  });
  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {
    relevance: 0
  });
  const STRING_FMT = {
    className: 'subst',
    begin: '\\\\[abfnrtv]\\|\\\\x[0-9a-fA-F]*\\\\\\|%[-+# *.0-9]*[dioxXucsfeEgGp]',
    relevance: 0
  };
  STRING.contains = STRING.contains.slice(); // we need our own copy of contains
  STRING.contains.push(STRING_FMT);

  const IMPLICATION = {
    className: 'built_in',
    variants: [
      {
        begin: '<=>'
      },
      {
        begin: '<=',
        relevance: 0
      },
      {
        begin: '=>',
        relevance: 0
      },
      {
        begin: '/\\\\'
      },
      {
        begin: '\\\\/'
      }
    ]
  };

  const HEAD_BODY_CONJUNCTION = {
    className: 'built_in',
    variants: [
      {
        begin: ':-\\|-->'
      },
      {
        begin: '=',
        relevance: 0
      }
    ]
  };

  return {
    name: 'Mercury',
    aliases: [
      'm',
      'moo'
    ],
    keywords: KEYWORDS,
    contains: [
      IMPLICATION,
      HEAD_BODY_CONJUNCTION,
      COMMENT,
      hljs.C_BLOCK_COMMENT_MODE,
      NUMCODE,
      hljs.NUMBER_MODE,
      ATOM,
      STRING,
      { // relevance booster
        begin: /:-/
      },
      { // relevance booster
        begin: /\.$/
      }
    ]
  };
}

module.exports = mercury;


/***/ }),

/***/ 56023:
/***/ ((module) => {

/*
Language: Matlab
Author: Denis Bardadym <<EMAIL>>
Contributors: Eugene Nizhibitsky <<EMAIL>>, Egor Rogov <<EMAIL>>
Website: https://www.mathworks.com/products/matlab.html
Category: scientific
*/

/*
  Formal syntax is not published, helpful link:
  https://github.com/kornilova-l/matlab-IntelliJ-plugin/blob/master/src/main/grammar/Matlab.bnf
*/
function matlab(hljs) {

  var TRANSPOSE_RE = '(\'|\\.\')+';
  var TRANSPOSE = {
    relevance: 0,
    contains: [
      { begin: TRANSPOSE_RE }
    ]
  };

  return {
    name: 'Matlab',
    keywords: {
      keyword:
        'arguments break case catch classdef continue else elseif end enumeration events for function ' +
        'global if methods otherwise parfor persistent properties return spmd switch try while',
      built_in:
        'sin sind sinh asin asind asinh cos cosd cosh acos acosd acosh tan tand tanh atan ' +
        'atand atan2 atanh sec secd sech asec asecd asech csc cscd csch acsc acscd acsch cot ' +
        'cotd coth acot acotd acoth hypot exp expm1 log log1p log10 log2 pow2 realpow reallog ' +
        'realsqrt sqrt nthroot nextpow2 abs angle complex conj imag real unwrap isreal ' +
        'cplxpair fix floor ceil round mod rem sign airy besselj bessely besselh besseli ' +
        'besselk beta betainc betaln ellipj ellipke erf erfc erfcx erfinv expint gamma ' +
        'gammainc gammaln psi legendre cross dot factor isprime primes gcd lcm rat rats perms ' +
        'nchoosek factorial cart2sph cart2pol pol2cart sph2cart hsv2rgb rgb2hsv zeros ones ' +
        'eye repmat rand randn linspace logspace freqspace meshgrid accumarray size length ' +
        'ndims numel disp isempty isequal isequalwithequalnans cat reshape diag blkdiag tril ' +
        'triu fliplr flipud flipdim rot90 find sub2ind ind2sub bsxfun ndgrid permute ipermute ' +
        'shiftdim circshift squeeze isscalar isvector ans eps realmax realmin pi i|0 inf nan ' +
        'isnan isinf isfinite j|0 why compan gallery hadamard hankel hilb invhilb magic pascal ' +
        'rosser toeplitz vander wilkinson max min nanmax nanmin mean nanmean type table ' +
        'readtable writetable sortrows sort figure plot plot3 scatter scatter3 cellfun ' +
        'legend intersect ismember procrustes hold num2cell '
    },
    illegal: '(//|"|#|/\\*|\\s+/\\w+)',
    contains: [
      {
        className: 'function',
        beginKeywords: 'function', end: '$',
        contains: [
          hljs.UNDERSCORE_TITLE_MODE,
          {
            className: 'params',
            variants: [
              {begin: '\\(', end: '\\)'},
              {begin: '\\[', end: '\\]'}
            ]
          }
        ]
      },
      {
        className: 'built_in',
        begin: /true|false/,
        relevance: 0,
        starts: TRANSPOSE
      },
      {
        begin: '[a-zA-Z][a-zA-Z_0-9]*' + TRANSPOSE_RE,
        relevance: 0
      },
      {
        className: 'number',
        begin: hljs.C_NUMBER_RE,
        relevance: 0,
        starts: TRANSPOSE
      },
      {
        className: 'string',
        begin: '\'', end: '\'',
        contains: [
          hljs.BACKSLASH_ESCAPE,
          {begin: '\'\''}]
      },
      {
        begin: /\]|\}|\)/,
        relevance: 0,
        starts: TRANSPOSE
      },
      {
        className: 'string',
        begin: '"', end: '"',
        contains: [
          hljs.BACKSLASH_ESCAPE,
          {begin: '""'}
        ],
        starts: TRANSPOSE
      },
      hljs.COMMENT('^\\s*%\\{\\s*$', '^\\s*%\\}\\s*$'),
      hljs.COMMENT('%', '$')
    ]
  };
}

module.exports = matlab;


/***/ }),

/***/ 59934:
/***/ ((module) => {

/*
Language: Makefile
Author: Ivan Sagalaev <<EMAIL>>
Contributors: Joël Porquet <<EMAIL>>
Website: https://www.gnu.org/software/make/manual/html_node/Introduction.html
Category: common
*/

function makefile(hljs) {
  /* Variables: simple (eg $(var)) and special (eg $@) */
  const VARIABLE = {
    className: 'variable',
    variants: [
      {
        begin: '\\$\\(' + hljs.UNDERSCORE_IDENT_RE + '\\)',
        contains: [ hljs.BACKSLASH_ESCAPE ]
      },
      {
        begin: /\$[@%<?\^\+\*]/
      }
    ]
  };
  /* Quoted string with variables inside */
  const QUOTE_STRING = {
    className: 'string',
    begin: /"/,
    end: /"/,
    contains: [
      hljs.BACKSLASH_ESCAPE,
      VARIABLE
    ]
  };
  /* Function: $(func arg,...) */
  const FUNC = {
    className: 'variable',
    begin: /\$\([\w-]+\s/,
    end: /\)/,
    keywords: {
      built_in:
        'subst patsubst strip findstring filter filter-out sort ' +
        'word wordlist firstword lastword dir notdir suffix basename ' +
        'addsuffix addprefix join wildcard realpath abspath error warning ' +
        'shell origin flavor foreach if or and call eval file value'
    },
    contains: [ VARIABLE ]
  };
  /* Variable assignment */
  const ASSIGNMENT = {
    begin: '^' + hljs.UNDERSCORE_IDENT_RE + '\\s*(?=[:+?]?=)'
  };
  /* Meta targets (.PHONY) */
  const META = {
    className: 'meta',
    begin: /^\.PHONY:/,
    end: /$/,
    keywords: {
      $pattern: /[\.\w]+/,
      'meta-keyword': '.PHONY'
    }
  };
  /* Targets */
  const TARGET = {
    className: 'section',
    begin: /^[^\s]+:/,
    end: /$/,
    contains: [ VARIABLE ]
  };
  return {
    name: 'Makefile',
    aliases: [
      'mk',
      'mak',
      'make',
    ],
    keywords: {
      $pattern: /[\w-]+/,
      keyword: 'define endef undefine ifdef ifndef ifeq ifneq else endif ' +
      'include -include sinclude override export unexport private vpath'
    },
    contains: [
      hljs.HASH_COMMENT_MODE,
      VARIABLE,
      QUOTE_STRING,
      FUNC,
      ASSIGNMENT,
      META,
      TARGET
    ]
  };
}

module.exports = makefile;


/***/ }),

/***/ 68254:
/***/ ((module) => {

/*
Language: MEL
Description: Maya Embedded Language
Author: Shuen-Huei Guan <<EMAIL>>
Website: http://www.autodesk.com/products/autodesk-maya/overview
Category: graphics
*/

function mel(hljs) {
  return {
    name: 'MEL',
    keywords:
      'int float string vector matrix if else switch case default while do for in break ' +
      'continue global proc return about abs addAttr addAttributeEditorNodeHelp addDynamic ' +
      'addNewShelfTab addPP addPanelCategory addPrefixToName advanceToNextDrivenKey ' +
      'affectedNet affects aimConstraint air alias aliasAttr align alignCtx alignCurve ' +
      'alignSurface allViewFit ambientLight angle angleBetween animCone animCurveEditor ' +
      'animDisplay animView annotate appendStringArray applicationName applyAttrPreset ' +
      'applyTake arcLenDimContext arcLengthDimension arclen arrayMapper art3dPaintCtx ' +
      'artAttrCtx artAttrPaintVertexCtx artAttrSkinPaintCtx artAttrTool artBuildPaintMenu ' +
      'artFluidAttrCtx artPuttyCtx artSelectCtx artSetPaintCtx artUserPaintCtx assignCommand ' +
      'assignInputDevice assignViewportFactories attachCurve attachDeviceAttr attachSurface ' +
      'attrColorSliderGrp attrCompatibility attrControlGrp attrEnumOptionMenu ' +
      'attrEnumOptionMenuGrp attrFieldGrp attrFieldSliderGrp attrNavigationControlGrp ' +
      'attrPresetEditWin attributeExists attributeInfo attributeMenu attributeQuery ' +
      'autoKeyframe autoPlace bakeClip bakeFluidShading bakePartialHistory bakeResults ' +
      'bakeSimulation basename basenameEx batchRender bessel bevel bevelPlus binMembership ' +
      'bindSkin blend2 blendShape blendShapeEditor blendShapePanel blendTwoAttr blindDataType ' +
      'boneLattice boundary boxDollyCtx boxZoomCtx bufferCurve buildBookmarkMenu ' +
      'buildKeyframeMenu button buttonManip CBG cacheFile cacheFileCombine cacheFileMerge ' +
      'cacheFileTrack camera cameraView canCreateManip canvas capitalizeString catch ' +
      'catchQuiet ceil changeSubdivComponentDisplayLevel changeSubdivRegion channelBox ' +
      'character characterMap characterOutlineEditor characterize chdir checkBox checkBoxGrp ' +
      'checkDefaultRenderGlobals choice circle circularFillet clamp clear clearCache clip ' +
      'clipEditor clipEditorCurrentTimeCtx clipSchedule clipSchedulerOutliner clipTrimBefore ' +
      'closeCurve closeSurface cluster cmdFileOutput cmdScrollFieldExecuter ' +
      'cmdScrollFieldReporter cmdShell coarsenSubdivSelectionList collision color ' +
      'colorAtPoint colorEditor colorIndex colorIndexSliderGrp colorSliderButtonGrp ' +
      'colorSliderGrp columnLayout commandEcho commandLine commandPort compactHairSystem ' +
      'componentEditor compositingInterop computePolysetVolume condition cone confirmDialog ' +
      'connectAttr connectControl connectDynamic connectJoint connectionInfo constrain ' +
      'constrainValue constructionHistory container containsMultibyte contextInfo control ' +
      'convertFromOldLayers convertIffToPsd convertLightmap convertSolidTx convertTessellation ' +
      'convertUnit copyArray copyFlexor copyKey copySkinWeights cos cpButton cpCache ' +
      'cpClothSet cpCollision cpConstraint cpConvClothToMesh cpForces cpGetSolverAttr cpPanel ' +
      'cpProperty cpRigidCollisionFilter cpSeam cpSetEdit cpSetSolverAttr cpSolver ' +
      'cpSolverTypes cpTool cpUpdateClothUVs createDisplayLayer createDrawCtx createEditor ' +
      'createLayeredPsdFile createMotionField createNewShelf createNode createRenderLayer ' +
      'createSubdivRegion cross crossProduct ctxAbort ctxCompletion ctxEditMode ctxTraverse ' +
      'currentCtx currentTime currentTimeCtx currentUnit curve curveAddPtCtx ' +
      'curveCVCtx curveEPCtx curveEditorCtx curveIntersect curveMoveEPCtx curveOnSurface ' +
      'curveSketchCtx cutKey cycleCheck cylinder dagPose date defaultLightListCheckBox ' +
      'defaultNavigation defineDataServer defineVirtualDevice deformer deg_to_rad delete ' +
      'deleteAttr deleteShadingGroupsAndMaterials deleteShelfTab deleteUI deleteUnusedBrushes ' +
      'delrandstr detachCurve detachDeviceAttr detachSurface deviceEditor devicePanel dgInfo ' +
      'dgdirty dgeval dgtimer dimWhen directKeyCtx directionalLight dirmap dirname disable ' +
      'disconnectAttr disconnectJoint diskCache displacementToPoly displayAffected ' +
      'displayColor displayCull displayLevelOfDetail displayPref displayRGBColor ' +
      'displaySmoothness displayStats displayString displaySurface distanceDimContext ' +
      'distanceDimension doBlur dolly dollyCtx dopeSheetEditor dot dotProduct ' +
      'doubleProfileBirailSurface drag dragAttrContext draggerContext dropoffLocator ' +
      'duplicate duplicateCurve duplicateSurface dynCache dynControl dynExport dynExpression ' +
      'dynGlobals dynPaintEditor dynParticleCtx dynPref dynRelEdPanel dynRelEditor ' +
      'dynamicLoad editAttrLimits editDisplayLayerGlobals editDisplayLayerMembers ' +
      'editRenderLayerAdjustment editRenderLayerGlobals editRenderLayerMembers editor ' +
      'editorTemplate effector emit emitter enableDevice encodeString endString endsWith env ' +
      'equivalent equivalentTol erf error eval evalDeferred evalEcho event ' +
      'exactWorldBoundingBox exclusiveLightCheckBox exec executeForEachObject exists exp ' +
      'expression expressionEditorListen extendCurve extendSurface extrude fcheck fclose feof ' +
      'fflush fgetline fgetword file fileBrowserDialog fileDialog fileExtension fileInfo ' +
      'filetest filletCurve filter filterCurve filterExpand filterStudioImport ' +
      'findAllIntersections findAnimCurves findKeyframe findMenuItem findRelatedSkinCluster ' +
      'finder firstParentOf fitBspline flexor floatEq floatField floatFieldGrp floatScrollBar ' +
      'floatSlider floatSlider2 floatSliderButtonGrp floatSliderGrp floor flow fluidCacheInfo ' +
      'fluidEmitter fluidVoxelInfo flushUndo fmod fontDialog fopen formLayout format fprint ' +
      'frameLayout fread freeFormFillet frewind fromNativePath fwrite gamma gauss ' +
      'geometryConstraint getApplicationVersionAsFloat getAttr getClassification ' +
      'getDefaultBrush getFileList getFluidAttr getInputDeviceRange getMayaPanelTypes ' +
      'getModifiers getPanel getParticleAttr getPluginResource getenv getpid glRender ' +
      'glRenderEditor globalStitch gmatch goal gotoBindPose grabColor gradientControl ' +
      'gradientControlNoAttr graphDollyCtx graphSelectContext graphTrackCtx gravity grid ' +
      'gridLayout group groupObjectsByName HfAddAttractorToAS HfAssignAS HfBuildEqualMap ' +
      'HfBuildFurFiles HfBuildFurImages HfCancelAFR HfConnectASToHF HfCreateAttractor ' +
      'HfDeleteAS HfEditAS HfPerformCreateAS HfRemoveAttractorFromAS HfSelectAttached ' +
      'HfSelectAttractors HfUnAssignAS hardenPointCurve hardware hardwareRenderPanel ' +
      'headsUpDisplay headsUpMessage help helpLine hermite hide hilite hitTest hotBox hotkey ' +
      'hotkeyCheck hsv_to_rgb hudButton hudSlider hudSliderButton hwReflectionMap hwRender ' +
      'hwRenderLoad hyperGraph hyperPanel hyperShade hypot iconTextButton iconTextCheckBox ' +
      'iconTextRadioButton iconTextRadioCollection iconTextScrollList iconTextStaticLabel ' +
      'ikHandle ikHandleCtx ikHandleDisplayScale ikSolver ikSplineHandleCtx ikSystem ' +
      'ikSystemInfo ikfkDisplayMethod illustratorCurves image imfPlugins inheritTransform ' +
      'insertJoint insertJointCtx insertKeyCtx insertKnotCurve insertKnotSurface instance ' +
      'instanceable instancer intField intFieldGrp intScrollBar intSlider intSliderGrp ' +
      'interToUI internalVar intersect iprEngine isAnimCurve isConnected isDirty isParentOf ' +
      'isSameObject isTrue isValidObjectName isValidString isValidUiName isolateSelect ' +
      'itemFilter itemFilterAttr itemFilterRender itemFilterType joint jointCluster jointCtx ' +
      'jointDisplayScale jointLattice keyTangent keyframe keyframeOutliner ' +
      'keyframeRegionCurrentTimeCtx keyframeRegionDirectKeyCtx keyframeRegionDollyCtx ' +
      'keyframeRegionInsertKeyCtx keyframeRegionMoveKeyCtx keyframeRegionScaleKeyCtx ' +
      'keyframeRegionSelectKeyCtx keyframeRegionSetKeyCtx keyframeRegionTrackCtx ' +
      'keyframeStats lassoContext lattice latticeDeformKeyCtx launch launchImageEditor ' +
      'layerButton layeredShaderPort layeredTexturePort layout layoutDialog lightList ' +
      'lightListEditor lightListPanel lightlink lineIntersection linearPrecision linstep ' +
      'listAnimatable listAttr listCameras listConnections listDeviceAttachments listHistory ' +
      'listInputDeviceAxes listInputDeviceButtons listInputDevices listMenuAnnotation ' +
      'listNodeTypes listPanelCategories listRelatives listSets listTransforms ' +
      'listUnselected listerEditor loadFluid loadNewShelf loadPlugin ' +
      'loadPluginLanguageResources loadPrefObjects localizedPanelLabel lockNode loft log ' +
      'longNameOf lookThru ls lsThroughFilter lsType lsUI Mayatomr mag makeIdentity makeLive ' +
      'makePaintable makeRoll makeSingleSurface makeTubeOn makebot manipMoveContext ' +
      'manipMoveLimitsCtx manipOptions manipRotateContext manipRotateLimitsCtx ' +
      'manipScaleContext manipScaleLimitsCtx marker match max memory menu menuBarLayout ' +
      'menuEditor menuItem menuItemToShelf menuSet menuSetPref messageLine min minimizeApp ' +
      'mirrorJoint modelCurrentTimeCtx modelEditor modelPanel mouse movIn movOut move ' +
      'moveIKtoFK moveKeyCtx moveVertexAlongDirection multiProfileBirailSurface mute ' +
      'nParticle nameCommand nameField namespace namespaceInfo newPanelItems newton nodeCast ' +
      'nodeIconButton nodeOutliner nodePreset nodeType noise nonLinear normalConstraint ' +
      'normalize nurbsBoolean nurbsCopyUVSet nurbsCube nurbsEditUV nurbsPlane nurbsSelect ' +
      'nurbsSquare nurbsToPoly nurbsToPolygonsPref nurbsToSubdiv nurbsToSubdivPref ' +
      'nurbsUVSet nurbsViewDirectionVector objExists objectCenter objectLayer objectType ' +
      'objectTypeUI obsoleteProc oceanNurbsPreviewPlane offsetCurve offsetCurveOnSurface ' +
      'offsetSurface openGLExtension openMayaPref optionMenu optionMenuGrp optionVar orbit ' +
      'orbitCtx orientConstraint outlinerEditor outlinerPanel overrideModifier ' +
      'paintEffectsDisplay pairBlend palettePort paneLayout panel panelConfiguration ' +
      'panelHistory paramDimContext paramDimension paramLocator parent parentConstraint ' +
      'particle particleExists particleInstancer particleRenderInfo partition pasteKey ' +
      'pathAnimation pause pclose percent performanceOptions pfxstrokes pickWalk picture ' +
      'pixelMove planarSrf plane play playbackOptions playblast plugAttr plugNode pluginInfo ' +
      'pluginResourceUtil pointConstraint pointCurveConstraint pointLight pointMatrixMult ' +
      'pointOnCurve pointOnSurface pointPosition poleVectorConstraint polyAppend ' +
      'polyAppendFacetCtx polyAppendVertex polyAutoProjection polyAverageNormal ' +
      'polyAverageVertex polyBevel polyBlendColor polyBlindData polyBoolOp polyBridgeEdge ' +
      'polyCacheMonitor polyCheck polyChipOff polyClipboard polyCloseBorder polyCollapseEdge ' +
      'polyCollapseFacet polyColorBlindData polyColorDel polyColorPerVertex polyColorSet ' +
      'polyCompare polyCone polyCopyUV polyCrease polyCreaseCtx polyCreateFacet ' +
      'polyCreateFacetCtx polyCube polyCut polyCutCtx polyCylinder polyCylindricalProjection ' +
      'polyDelEdge polyDelFacet polyDelVertex polyDuplicateAndConnect polyDuplicateEdge ' +
      'polyEditUV polyEditUVShell polyEvaluate polyExtrudeEdge polyExtrudeFacet ' +
      'polyExtrudeVertex polyFlipEdge polyFlipUV polyForceUV polyGeoSampler polyHelix ' +
      'polyInfo polyInstallAction polyLayoutUV polyListComponentConversion polyMapCut ' +
      'polyMapDel polyMapSew polyMapSewMove polyMergeEdge polyMergeEdgeCtx polyMergeFacet ' +
      'polyMergeFacetCtx polyMergeUV polyMergeVertex polyMirrorFace polyMoveEdge ' +
      'polyMoveFacet polyMoveFacetUV polyMoveUV polyMoveVertex polyNormal polyNormalPerVertex ' +
      'polyNormalizeUV polyOptUvs polyOptions polyOutput polyPipe polyPlanarProjection ' +
      'polyPlane polyPlatonicSolid polyPoke polyPrimitive polyPrism polyProjection ' +
      'polyPyramid polyQuad polyQueryBlindData polyReduce polySelect polySelectConstraint ' +
      'polySelectConstraintMonitor polySelectCtx polySelectEditCtx polySeparate ' +
      'polySetToFaceNormal polySewEdge polyShortestPathCtx polySmooth polySoftEdge ' +
      'polySphere polySphericalProjection polySplit polySplitCtx polySplitEdge polySplitRing ' +
      'polySplitVertex polyStraightenUVBorder polySubdivideEdge polySubdivideFacet ' +
      'polyToSubdiv polyTorus polyTransfer polyTriangulate polyUVSet polyUnite polyWedgeFace ' +
      'popen popupMenu pose pow preloadRefEd print progressBar progressWindow projFileViewer ' +
      'projectCurve projectTangent projectionContext projectionManip promptDialog propModCtx ' +
      'propMove psdChannelOutliner psdEditTextureFile psdExport psdTextureFile putenv pwd ' +
      'python querySubdiv quit rad_to_deg radial radioButton radioButtonGrp radioCollection ' +
      'radioMenuItemCollection rampColorPort rand randomizeFollicles randstate rangeControl ' +
      'readTake rebuildCurve rebuildSurface recordAttr recordDevice redo reference ' +
      'referenceEdit referenceQuery refineSubdivSelectionList refresh refreshAE ' +
      'registerPluginResource rehash reloadImage removeJoint removeMultiInstance ' +
      'removePanelCategory rename renameAttr renameSelectionList renameUI render ' +
      'renderGlobalsNode renderInfo renderLayerButton renderLayerParent ' +
      'renderLayerPostProcess renderLayerUnparent renderManip renderPartition ' +
      'renderQualityNode renderSettings renderThumbnailUpdate renderWindowEditor ' +
      'renderWindowSelectContext renderer reorder reorderDeformers requires reroot ' +
      'resampleFluid resetAE resetPfxToPolyCamera resetTool resolutionNode retarget ' +
      'reverseCurve reverseSurface revolve rgb_to_hsv rigidBody rigidSolver roll rollCtx ' +
      'rootOf rot rotate rotationInterpolation roundConstantRadius rowColumnLayout rowLayout ' +
      'runTimeCommand runup sampleImage saveAllShelves saveAttrPreset saveFluid saveImage ' +
      'saveInitialState saveMenu savePrefObjects savePrefs saveShelf saveToolSettings scale ' +
      'scaleBrushBrightness scaleComponents scaleConstraint scaleKey scaleKeyCtx sceneEditor ' +
      'sceneUIReplacement scmh scriptCtx scriptEditorInfo scriptJob scriptNode scriptTable ' +
      'scriptToShelf scriptedPanel scriptedPanelType scrollField scrollLayout sculpt ' +
      'searchPathArray seed selLoadSettings select selectContext selectCurveCV selectKey ' +
      'selectKeyCtx selectKeyframeRegionCtx selectMode selectPref selectPriority selectType ' +
      'selectedNodes selectionConnection separator setAttr setAttrEnumResource ' +
      'setAttrMapping setAttrNiceNameResource setConstraintRestPosition ' +
      'setDefaultShadingGroup setDrivenKeyframe setDynamic setEditCtx setEditor setFluidAttr ' +
      'setFocus setInfinity setInputDeviceMapping setKeyCtx setKeyPath setKeyframe ' +
      'setKeyframeBlendshapeTargetWts setMenuMode setNodeNiceNameResource setNodeTypeFlag ' +
      'setParent setParticleAttr setPfxToPolyCamera setPluginResource setProject ' +
      'setStampDensity setStartupMessage setState setToolTo setUITemplate setXformManip sets ' +
      'shadingConnection shadingGeometryRelCtx shadingLightRelCtx shadingNetworkCompare ' +
      'shadingNode shapeCompare shelfButton shelfLayout shelfTabLayout shellField ' +
      'shortNameOf showHelp showHidden showManipCtx showSelectionInTitle ' +
      'showShadingGroupAttrEditor showWindow sign simplify sin singleProfileBirailSurface ' +
      'size sizeBytes skinCluster skinPercent smoothCurve smoothTangentSurface smoothstep ' +
      'snap2to2 snapKey snapMode snapTogetherCtx snapshot soft softMod softModCtx sort sound ' +
      'soundControl source spaceLocator sphere sphrand spotLight spotLightPreviewPort ' +
      'spreadSheetEditor spring sqrt squareSurface srtContext stackTrace startString ' +
      'startsWith stitchAndExplodeShell stitchSurface stitchSurfacePoints strcmp ' +
      'stringArrayCatenate stringArrayContains stringArrayCount stringArrayInsertAtIndex ' +
      'stringArrayIntersector stringArrayRemove stringArrayRemoveAtIndex ' +
      'stringArrayRemoveDuplicates stringArrayRemoveExact stringArrayToString ' +
      'stringToStringArray strip stripPrefixFromName stroke subdAutoProjection ' +
      'subdCleanTopology subdCollapse subdDuplicateAndConnect subdEditUV ' +
      'subdListComponentConversion subdMapCut subdMapSewMove subdMatchTopology subdMirror ' +
      'subdToBlind subdToPoly subdTransferUVsToCache subdiv subdivCrease ' +
      'subdivDisplaySmoothness substitute substituteAllString substituteGeometry substring ' +
      'surface surfaceSampler surfaceShaderList swatchDisplayPort switchTable symbolButton ' +
      'symbolCheckBox sysFile system tabLayout tan tangentConstraint texLatticeDeformContext ' +
      'texManipContext texMoveContext texMoveUVShellContext texRotateContext texScaleContext ' +
      'texSelectContext texSelectShortestPathCtx texSmudgeUVContext texWinToolCtx text ' +
      'textCurves textField textFieldButtonGrp textFieldGrp textManip textScrollList ' +
      'textToShelf textureDisplacePlane textureHairColor texturePlacementContext ' +
      'textureWindow threadCount threePointArcCtx timeControl timePort timerX toNativePath ' +
      'toggle toggleAxis toggleWindowVisibility tokenize tokenizeList tolerance tolower ' +
      'toolButton toolCollection toolDropped toolHasOptions toolPropertyWindow torus toupper ' +
      'trace track trackCtx transferAttributes transformCompare transformLimits translator ' +
      'trim trunc truncateFluidCache truncateHairCache tumble tumbleCtx turbulence ' +
      'twoPointArcCtx uiRes uiTemplate unassignInputDevice undo undoInfo ungroup uniform unit ' +
      'unloadPlugin untangleUV untitledFileName untrim upAxis updateAE userCtx uvLink ' +
      'uvSnapshot validateShelfName vectorize view2dToolCtx viewCamera viewClipPlane ' +
      'viewFit viewHeadOn viewLookAt viewManip viewPlace viewSet visor volumeAxis vortex ' +
      'waitCursor warning webBrowser webBrowserPrefs whatIs window windowPref wire ' +
      'wireContext workspace wrinkle wrinkleContext writeTake xbmLangPathList xform',
    illegal: '</',
    contains: [
      hljs.C_NUMBER_MODE,
      hljs.APOS_STRING_MODE,
      hljs.QUOTE_STRING_MODE,
      {
        className: 'string',
        begin: '`',
        end: '`',
        contains: [ hljs.BACKSLASH_ESCAPE ]
      },
      { // eats variables
        begin: /[$%@](\^\w\b|#\w+|[^\s\w{]|\{\w+\}|\w+)/
      },
      hljs.C_LINE_COMMENT_MODE,
      hljs.C_BLOCK_COMMENT_MODE
    ]
  };
}

module.exports = mel;


/***/ }),

/***/ 91788:
/***/ ((module) => {

/*
Language: Mizar
Description: The Mizar Language is a formal language derived from the mathematical vernacular.
Author: Kelley van Evert <<EMAIL>>
Website: http://mizar.org/language/
Category: scientific
*/

function mizar(hljs) {
  return {
    name: 'Mizar',
    keywords:
      'environ vocabularies notations constructors definitions ' +
      'registrations theorems schemes requirements begin end definition ' +
      'registration cluster existence pred func defpred deffunc theorem ' +
      'proof let take assume then thus hence ex for st holds consider ' +
      'reconsider such that and in provided of as from be being by means ' +
      'equals implies iff redefine define now not or attr is mode ' +
      'suppose per cases set thesis contradiction scheme reserve struct ' +
      'correctness compatibility coherence symmetry assymetry ' +
      'reflexivity irreflexivity connectedness uniqueness commutativity ' +
      'idempotence involutiveness projectivity',
    contains: [
      hljs.COMMENT('::', '$')
    ]
  };
}

module.exports = mizar;


/***/ }),

/***/ 96503:
/***/ ((module) => {

/**
 * @param {string} value
 * @returns {RegExp}
 * */

/**
 * @param {RegExp | string } re
 * @returns {string}
 */
function source(re) {
  if (!re) return null;
  if (typeof re === "string") return re;

  return re.source;
}

/**
 * @param {...(RegExp | string) } args
 * @returns {string}
 */
function concat(...args) {
  const joined = args.map((x) => source(x)).join("");
  return joined;
}

/*
Language: Markdown
Requires: xml.js
Author: John Crepezzi <<EMAIL>>
Website: https://daringfireball.net/projects/markdown/
Category: common, markup
*/

function markdown(hljs) {
  const INLINE_HTML = {
    begin: /<\/?[A-Za-z_]/,
    end: '>',
    subLanguage: 'xml',
    relevance: 0
  };
  const HORIZONTAL_RULE = {
    begin: '^[-\\*]{3,}',
    end: '$'
  };
  const CODE = {
    className: 'code',
    variants: [
      // TODO: fix to allow these to work with sublanguage also
      {
        begin: '(`{3,})[^`](.|\\n)*?\\1`*[ ]*'
      },
      {
        begin: '(~{3,})[^~](.|\\n)*?\\1~*[ ]*'
      },
      // needed to allow markdown as a sublanguage to work
      {
        begin: '```',
        end: '```+[ ]*$'
      },
      {
        begin: '~~~',
        end: '~~~+[ ]*$'
      },
      {
        begin: '`.+?`'
      },
      {
        begin: '(?=^( {4}|\\t))',
        // use contains to gobble up multiple lines to allow the block to be whatever size
        // but only have a single open/close tag vs one per line
        contains: [
          {
            begin: '^( {4}|\\t)',
            end: '(\\n)$'
          }
        ],
        relevance: 0
      }
    ]
  };
  const LIST = {
    className: 'bullet',
    begin: '^[ \t]*([*+-]|(\\d+\\.))(?=\\s+)',
    end: '\\s+',
    excludeEnd: true
  };
  const LINK_REFERENCE = {
    begin: /^\[[^\n]+\]:/,
    returnBegin: true,
    contains: [
      {
        className: 'symbol',
        begin: /\[/,
        end: /\]/,
        excludeBegin: true,
        excludeEnd: true
      },
      {
        className: 'link',
        begin: /:\s*/,
        end: /$/,
        excludeBegin: true
      }
    ]
  };
  const URL_SCHEME = /[A-Za-z][A-Za-z0-9+.-]*/;
  const LINK = {
    variants: [
      // too much like nested array access in so many languages
      // to have any real relevance
      {
        begin: /\[.+?\]\[.*?\]/,
        relevance: 0
      },
      // popular internet URLs
      {
        begin: /\[.+?\]\(((data|javascript|mailto):|(?:http|ftp)s?:\/\/).*?\)/,
        relevance: 2
      },
      {
        begin: concat(/\[.+?\]\(/, URL_SCHEME, /:\/\/.*?\)/),
        relevance: 2
      },
      // relative urls
      {
        begin: /\[.+?\]\([./?&#].*?\)/,
        relevance: 1
      },
      // whatever else, lower relevance (might not be a link at all)
      {
        begin: /\[.+?\]\(.*?\)/,
        relevance: 0
      }
    ],
    returnBegin: true,
    contains: [
      {
        className: 'string',
        relevance: 0,
        begin: '\\[',
        end: '\\]',
        excludeBegin: true,
        returnEnd: true
      },
      {
        className: 'link',
        relevance: 0,
        begin: '\\]\\(',
        end: '\\)',
        excludeBegin: true,
        excludeEnd: true
      },
      {
        className: 'symbol',
        relevance: 0,
        begin: '\\]\\[',
        end: '\\]',
        excludeBegin: true,
        excludeEnd: true
      }
    ]
  };
  const BOLD = {
    className: 'strong',
    contains: [], // defined later
    variants: [
      {
        begin: /_{2}/,
        end: /_{2}/
      },
      {
        begin: /\*{2}/,
        end: /\*{2}/
      }
    ]
  };
  const ITALIC = {
    className: 'emphasis',
    contains: [], // defined later
    variants: [
      {
        begin: /\*(?!\*)/,
        end: /\*/
      },
      {
        begin: /_(?!_)/,
        end: /_/,
        relevance: 0
      }
    ]
  };
  BOLD.contains.push(ITALIC);
  ITALIC.contains.push(BOLD);

  let CONTAINABLE = [
    INLINE_HTML,
    LINK
  ];

  BOLD.contains = BOLD.contains.concat(CONTAINABLE);
  ITALIC.contains = ITALIC.contains.concat(CONTAINABLE);

  CONTAINABLE = CONTAINABLE.concat(BOLD, ITALIC);

  const HEADER = {
    className: 'section',
    variants: [
      {
        begin: '^#{1,6}',
        end: '$',
        contains: CONTAINABLE
      },
      {
        begin: '(?=^.+?\\n[=-]{2,}$)',
        contains: [
          {
            begin: '^[=-]*$'
          },
          {
            begin: '^',
            end: "\\n",
            contains: CONTAINABLE
          }
        ]
      }
    ]
  };

  const BLOCKQUOTE = {
    className: 'quote',
    begin: '^>\\s+',
    contains: CONTAINABLE,
    end: '$'
  };

  return {
    name: 'Markdown',
    aliases: [
      'md',
      'mkdown',
      'mkd'
    ],
    contains: [
      HEADER,
      INLINE_HTML,
      LIST,
      BOLD,
      ITALIC,
      BLOCKQUOTE,
      CODE,
      HORIZONTAL_RULE,
      LINK,
      LINK_REFERENCE
    ]
  };
}

module.exports = markdown;


/***/ })

}]);