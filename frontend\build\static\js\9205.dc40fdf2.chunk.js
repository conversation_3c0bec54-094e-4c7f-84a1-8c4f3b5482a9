"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9205],{

/***/ 19205:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ enhanced_EnhancedCodeExporter)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js
var es = __webpack_require__(1807);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1 modules
var icons_es = __webpack_require__(35346);
// EXTERNAL MODULE: ./src/design-system/index.js + 7 modules
var design_system = __webpack_require__(79146);
// EXTERNAL MODULE: ./src/design-system/theme.js
var theme = __webpack_require__(86020);
;// ./src/utils/enhanced_code_generators.js


/**
 * Enhanced code generators for multiple frameworks and project structures
 * This file contains generators for Vue, Angular, Svelte, Next.js, React Native, Flutter, etc.
 */

/**
 * Generate Vue.js code
 */
var generateVueCode = function generateVueCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility,
    projectStructure = options.projectStructure;
  if (projectStructure === 'full-project') {
    return generateVueProject(appData, options);
  }
  var fileExtension = typescript ? 'vue' : 'vue';
  var vueCode = "<template>\n  <div class=\"app\"".concat(includeAccessibility ? ' role="main"' : '', ">");

  // Generate template structure
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    vueCode += "\n    <div class=\"".concat(layoutName, "-container\">");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          vueCode += "\n      <".concat(component.type.toLowerCase());
          Object.entries(component.props || {}).forEach(function (_ref) {
            var _ref2 = (0,slicedToArray/* default */.A)(_ref, 2),
              prop = _ref2[0],
              value = _ref2[1];
            if (typeof value === 'string') {
              vueCode += " ".concat(prop, "=\"").concat(value, "\"");
            } else {
              vueCode += " :".concat(prop, "=\"").concat(JSON.stringify(value), "\"");
            }
          });
          vueCode += " />";
        }
      });
    }
    vueCode += "\n    </div>";
  });
  vueCode += "\n  </div>\n</template>\n\n<script".concat(typescript ? ' lang="ts"' : '', ">\nimport { defineComponent, ref, reactive } from 'vue';\n\nexport default defineComponent({\n  name: 'App',\n  setup() {");

  // Add reactive data
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(function (_ref3) {
      var _ref4 = (0,slicedToArray/* default */.A)(_ref3, 2),
        key = _ref4[0],
        value = _ref4[1];
      vueCode += "\n    const ".concat(key, " = ref(").concat(JSON.stringify(value), ");");
    });
  }
  vueCode += "\n    \n    return {";
  if (data && Object.keys(data).length > 0) {
    Object.keys(data).forEach(function (key) {
      vueCode += "\n      ".concat(key, ",");
    });
  }
  vueCode += "\n    };\n  }\n});\n</script>\n\n<style scoped>\n.app {\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n}\n\n".concat(generateVueStyles(styles, components, layouts), "\n</style>");
  return vueCode;
};

/**
 * Generate Angular code
 */
var generateAngularCode = function generateAngularCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var _options$typescript = options.typescript,
    typescript = _options$typescript === void 0 ? true : _options$typescript,
    includeAccessibility = options.includeAccessibility,
    projectStructure = options.projectStructure;
  if (projectStructure === 'full-project') {
    return generateAngularProject(appData, options);
  }

  // Component TypeScript
  var componentCode = "import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Generated App';";

  // Add component properties
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(function (_ref5) {
      var _ref6 = (0,slicedToArray/* default */.A)(_ref5, 2),
        key = _ref6[0],
        value = _ref6[1];
      componentCode += "\n  ".concat(key, " = ").concat(JSON.stringify(value), ";");
    });
  }
  componentCode += "\n}";

  // Template HTML
  var templateCode = "<div class=\"app\"".concat(includeAccessibility ? ' role="main"' : '', ">");
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    templateCode += "\n  <div class=\"".concat(layoutName, "-container\">");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          templateCode += "\n    <app-".concat(component.type.toLowerCase());
          Object.entries(component.props || {}).forEach(function (_ref7) {
            var _ref8 = (0,slicedToArray/* default */.A)(_ref7, 2),
              prop = _ref8[0],
              value = _ref8[1];
            if (typeof value === 'string') {
              templateCode += " ".concat(prop, "=\"").concat(value, "\"");
            } else {
              templateCode += " [".concat(prop, "]=\"").concat(JSON.stringify(value), "\"");
            }
          });
          templateCode += "></app-".concat(component.type.toLowerCase(), ">");
        }
      });
    }
    templateCode += "\n  </div>";
  });
  templateCode += "\n</div>";

  // Styles CSS
  var stylesCode = ".app {\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n}\n\n".concat(generateAngularStyles(styles, components, layouts));
  return {
    'app.component.ts': componentCode,
    'app.component.html': templateCode,
    'app.component.css': stylesCode
  };
};

/**
 * Generate Svelte code
 */
var generateSvelteCode = function generateSvelteCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility;
  var svelteCode = "<script".concat(typescript ? ' lang="ts"' : '', ">");

  // Add reactive data
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(function (_ref9) {
      var _ref0 = (0,slicedToArray/* default */.A)(_ref9, 2),
        key = _ref0[0],
        value = _ref0[1];
      svelteCode += "\n  let ".concat(key, " = ").concat(JSON.stringify(value), ";");
    });
  }
  svelteCode += "\n</script>\n\n<div class=\"app\"".concat(includeAccessibility ? ' role="main"' : '', ">");

  // Generate template structure
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    svelteCode += "\n  <div class=\"".concat(layoutName, "-container\">");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          svelteCode += "\n    <".concat(component.type);
          Object.entries(component.props || {}).forEach(function (_ref1) {
            var _ref10 = (0,slicedToArray/* default */.A)(_ref1, 2),
              prop = _ref10[0],
              value = _ref10[1];
            if (typeof value === 'string') {
              svelteCode += " ".concat(prop, "=\"").concat(value, "\"");
            } else {
              svelteCode += " ".concat(prop, "={").concat(JSON.stringify(value), "}");
            }
          });
          svelteCode += " />";
        }
      });
    }
    svelteCode += "\n  </div>";
  });
  svelteCode += "\n</div>\n\n<style>\n  .app {\n    min-height: 100vh;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n    line-height: 1.6;\n    color: #333;\n  }\n\n  ".concat(generateSvelteStyles(styles, components, layouts), "\n</style>");
  return svelteCode;
};

/**
 * Generate Next.js code
 */
var generateNextJSCode = function generateNextJSCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility,
    projectStructure = options.projectStructure;
  if (projectStructure === 'full-project') {
    return generateNextJSProject(appData, options);
  }
  var fileExtension = typescript ? 'tsx' : 'jsx';
  var nextCode = "import React from 'react';\nimport Head from 'next/head';\nimport styles from '../styles/Home.module.css';\n\nexport default function Home() {\n  return (\n    <div className={styles.container}>\n      <Head>\n        <title>Generated App</title>\n        <meta name=\"description\" content=\"Generated by App Builder\" />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n      </Head>\n\n      <main className={styles.main}".concat(includeAccessibility ? ' role="main"' : '', ">");

  // Generate layout structure
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    nextCode += "\n        <div className={styles.".concat(layoutName, "Container}>");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          nextCode += "\n          <".concat(component.type);
          Object.entries(component.props || {}).forEach(function (_ref11) {
            var _ref12 = (0,slicedToArray/* default */.A)(_ref11, 2),
              prop = _ref12[0],
              value = _ref12[1];
            if (typeof value === 'string') {
              nextCode += " ".concat(prop, "=\"").concat(value, "\"");
            } else {
              nextCode += " ".concat(prop, "={").concat(JSON.stringify(value), "}");
            }
          });
          nextCode += " />";
        }
      });
    }
    nextCode += "\n        </div>";
  });
  nextCode += "\n      </main>\n    </div>\n  );\n}";
  return (0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)({}, "pages/index.".concat(fileExtension), nextCode), 'styles/Home.module.css', generateNextJSStyles(styles, components, layouts)), 'package.json', generatePackageJson('nextjs-app', options));
};

/**
 * Generate React Native code
 */
var generateReactNativeCode = function generateReactNativeCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility;
  var fileExtension = typescript ? 'tsx' : 'jsx';
  var rnCode = "import React".concat(data && Object.keys(data).length > 0 ? ', { useState }' : '', " from 'react';\nimport {\n  SafeAreaView,\n  ScrollView,\n  StatusBar,\n  StyleSheet,\n  Text,\n  View,\n} from 'react-native';\n\nconst App = () => {");

  // Add state management
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(function (_ref14) {
      var _ref15 = (0,slicedToArray/* default */.A)(_ref14, 2),
        key = _ref15[0],
        value = _ref15[1];
      rnCode += "\n  const [".concat(key, ", set").concat(pascalCase(key), "] = useState(").concat(JSON.stringify(value), ");");
    });
  }
  rnCode += "\n  return (\n    <SafeAreaView style={styles.container}>\n      <StatusBar barStyle=\"dark-content\" />\n      <ScrollView contentInsetAdjustmentBehavior=\"automatic\">";

  // Generate layout structure
  layouts.forEach(function (layout) {
    rnCode += "\n        <View style={styles.".concat(layout.name || layout.type || 'layout', "Container}>");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          rnCode += "\n          <".concat(mapToReactNativeComponent(component.type));
          Object.entries(component.props || {}).forEach(function (_ref16) {
            var _ref17 = (0,slicedToArray/* default */.A)(_ref16, 2),
              prop = _ref17[0],
              value = _ref17[1];
            var mappedProp = mapToReactNativeProp(prop, component.type);
            if (typeof value === 'string') {
              rnCode += " ".concat(mappedProp, "=\"").concat(value, "\"");
            } else {
              rnCode += " ".concat(mappedProp, "={").concat(JSON.stringify(value), "}");
            }
          });
          rnCode += " />";
        }
      });
    }
    rnCode += "\n        </View>";
  });
  rnCode += "\n      </ScrollView>\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n  },\n  ".concat(generateReactNativeStyles(styles, components, layouts), "\n});\n\nexport default App;");
  return rnCode;
};

// Helper functions for style generation
var generateVueStyles = function generateVueStyles(styles, components, layouts) {
  // Implementation for Vue-specific styles
  return Object.entries(styles).map(function (_ref18) {
    var _ref19 = (0,slicedToArray/* default */.A)(_ref18, 2),
      selector = _ref19[0],
      style = _ref19[1];
    return "".concat(selector, " {\n  ").concat(Object.entries(style).map(function (_ref20) {
      var _ref21 = (0,slicedToArray/* default */.A)(_ref20, 2),
        prop = _ref21[0],
        value = _ref21[1];
      return "".concat(prop, ": ").concat(value, ";");
    }).join('\n  '), "\n}");
  }).join('\n\n');
};
var generateAngularStyles = function generateAngularStyles(styles, components, layouts) {
  // Implementation for Angular-specific styles
  return Object.entries(styles).map(function (_ref22) {
    var _ref23 = (0,slicedToArray/* default */.A)(_ref22, 2),
      selector = _ref23[0],
      style = _ref23[1];
    return "".concat(selector, " {\n  ").concat(Object.entries(style).map(function (_ref24) {
      var _ref25 = (0,slicedToArray/* default */.A)(_ref24, 2),
        prop = _ref25[0],
        value = _ref25[1];
      return "".concat(prop, ": ").concat(value, ";");
    }).join('\n  '), "\n}");
  }).join('\n\n');
};
var generateSvelteStyles = function generateSvelteStyles(styles, components, layouts) {
  // Implementation for Svelte-specific styles
  return Object.entries(styles).map(function (_ref26) {
    var _ref27 = (0,slicedToArray/* default */.A)(_ref26, 2),
      selector = _ref27[0],
      style = _ref27[1];
    return "".concat(selector, " {\n    ").concat(Object.entries(style).map(function (_ref28) {
      var _ref29 = (0,slicedToArray/* default */.A)(_ref28, 2),
        prop = _ref29[0],
        value = _ref29[1];
      return "".concat(prop, ": ").concat(value, ";");
    }).join('\n    '), "\n  }");
  }).join('\n\n');
};
var generateNextJSStyles = function generateNextJSStyles(styles, components, layouts) {
  // Implementation for Next.js CSS modules
  return ".container {\n  padding: 0 2rem;\n}\n\n.main {\n  min-height: 100vh;\n  padding: 4rem 0;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n".concat(Object.entries(styles).map(function (_ref30) {
    var _ref31 = (0,slicedToArray/* default */.A)(_ref30, 2),
      selector = _ref31[0],
      style = _ref31[1];
    return "".concat(selector.replace(/[^a-zA-Z0-9]/g, ''), " {\n  ").concat(Object.entries(style).map(function (_ref32) {
      var _ref33 = (0,slicedToArray/* default */.A)(_ref32, 2),
        prop = _ref33[0],
        value = _ref33[1];
      return "".concat(prop, ": ").concat(value, ";");
    }).join('\n  '), "\n}");
  }).join('\n\n'));
};
var generateReactNativeStyles = function generateReactNativeStyles(styles, components, layouts) {
  // Implementation for React Native StyleSheet
  return layouts.map(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    return "".concat(layoutName, "Container: {\n    padding: 16,\n    marginVertical: 8,\n  },");
  }).join('\n  ');
};

// Helper functions
var pascalCase = function pascalCase(str) {
  return str.replace(/(?:^|[\s-_])(\w)/g, function (match, letter) {
    return letter.toUpperCase();
  }).replace(/[\s-_]/g, '');
};
var mapToReactNativeComponent = function mapToReactNativeComponent(type) {
  var componentMap = {
    'Button': 'TouchableOpacity',
    'Text': 'Text',
    'Input': 'TextInput',
    'Image': 'Image',
    'Card': 'View',
    'Section': 'View',
    'Header': 'View'
  };
  return componentMap[type] || 'View';
};
var mapToReactNativeProp = function mapToReactNativeProp(prop, componentType) {
  var propMap = {
    'text': 'title',
    'content': 'children',
    'src': 'source'
  };
  return propMap[prop] || prop;
};
;// ./src/utils/project_generators.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Project structure generators and configuration file generators
 * This file contains functions to generate complete project structures with build configs, package.json, etc.
 */

/**
 * Generate package.json for different project types
 */
var project_generators_generatePackageJson = function generatePackageJson(projectType) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$packageManag = options.packageManager,
    packageManager = _options$packageManag === void 0 ? 'npm' : _options$packageManag,
    _options$typescript = options.typescript,
    typescript = _options$typescript === void 0 ? false : _options$typescript,
    _options$styleFramewo = options.styleFramework,
    styleFramework = _options$styleFramewo === void 0 ? 'styled-components' : _options$styleFramewo;
  var basePackage = {
    name: 'app-builder-generated-app',
    version: '0.1.0',
    "private": true,
    description: 'Generated by App Builder',
    author: 'App Builder',
    license: 'MIT'
  };
  switch (projectType) {
    case 'react-app':
      return JSON.stringify(_objectSpread(_objectSpread({}, basePackage), {}, {
        scripts: {
          start: 'react-scripts start',
          build: 'react-scripts build',
          test: 'react-scripts test',
          eject: 'react-scripts eject',
          lint: 'eslint src --ext .js,.jsx,.ts,.tsx',
          'lint:fix': 'eslint src --ext .js,.jsx,.ts,.tsx --fix'
        },
        dependencies: _objectSpread(_objectSpread(_objectSpread(_objectSpread({
          react: '^18.2.0',
          'react-dom': '^18.2.0',
          'react-scripts': '5.0.1',
          'web-vitals': '^2.1.4'
        }, styleFramework === 'styled-components' && {
          'styled-components': '^5.3.9'
        }), styleFramework === 'emotion' && {
          '@emotion/react': '^11.10.6',
          '@emotion/styled': '^11.10.6'
        }), styleFramework === 'tailwind' && {
          'tailwindcss': '^3.2.7'
        }), !typescript && {
          'prop-types': '^15.8.1'
        }),
        devDependencies: _objectSpread(_objectSpread({
          '@testing-library/jest-dom': '^5.16.4',
          '@testing-library/react': '^13.4.0',
          '@testing-library/user-event': '^13.5.0'
        }, typescript && {
          '@types/react': '^18.0.28',
          '@types/react-dom': '^18.0.11',
          '@types/node': '^16.18.23',
          typescript: '^4.9.5'
        }), {}, {
          eslint: '^8.36.0',
          'eslint-plugin-react': '^7.32.2',
          'eslint-plugin-react-hooks': '^4.6.0'
        }),
        browserslist: {
          production: ['>0.2%', 'not dead', 'not op_mini all'],
          development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version']
        }
      }), null, 2);
    case 'nextjs-app':
      return JSON.stringify(_objectSpread(_objectSpread({}, basePackage), {}, {
        scripts: {
          dev: 'next dev',
          build: 'next build',
          start: 'next start',
          lint: 'next lint',
          "export": 'next export'
        },
        dependencies: _objectSpread(_objectSpread({
          next: '^13.2.4',
          react: '^18.2.0',
          'react-dom': '^18.2.0'
        }, styleFramework === 'styled-components' && {
          'styled-components': '^5.3.9'
        }), styleFramework === 'tailwind' && {
          'tailwindcss': '^3.2.7',
          'autoprefixer': '^10.4.14',
          'postcss': '^8.4.21'
        }),
        devDependencies: _objectSpread(_objectSpread({}, typescript && {
          '@types/node': '^18.15.3',
          '@types/react': '^18.0.28',
          '@types/react-dom': '^18.0.11',
          typescript: '^5.0.2'
        }), {}, {
          eslint: '^8.36.0',
          'eslint-config-next': '^13.2.4'
        })
      }), null, 2);
    case 'vue-app':
      return JSON.stringify(_objectSpread(_objectSpread({}, basePackage), {}, {
        scripts: {
          serve: 'vue-cli-service serve',
          build: 'vue-cli-service build',
          test: 'vue-cli-service test:unit',
          lint: 'vue-cli-service lint'
        },
        dependencies: _objectSpread({
          'core-js': '^3.8.3',
          vue: '^3.2.13'
        }, typescript && {
          'vue-tsc': '^1.2.0'
        }),
        devDependencies: _objectSpread({
          '@babel/core': '^7.12.16',
          '@babel/eslint-parser': '^7.12.16',
          '@vue/cli-plugin-babel': '~5.0.0',
          '@vue/cli-plugin-eslint': '~5.0.0',
          '@vue/cli-service': '~5.0.0',
          eslint: '^7.32.0',
          'eslint-plugin-vue': '^8.0.3'
        }, typescript && {
          '@vue/cli-plugin-typescript': '~5.0.0',
          '@vue/eslint-config-typescript': '^9.1.0',
          typescript: '~4.5.5'
        })
      }), null, 2);
    case 'angular-app':
      return JSON.stringify(_objectSpread(_objectSpread({}, basePackage), {}, {
        scripts: {
          ng: 'ng',
          start: 'ng serve',
          build: 'ng build',
          watch: 'ng build --watch --configuration development',
          test: 'ng test'
        },
        dependencies: {
          '@angular/animations': '^15.2.0',
          '@angular/common': '^15.2.0',
          '@angular/compiler': '^15.2.0',
          '@angular/core': '^15.2.0',
          '@angular/forms': '^15.2.0',
          '@angular/platform-browser': '^15.2.0',
          '@angular/platform-browser-dynamic': '^15.2.0',
          '@angular/router': '^15.2.0',
          rxjs: '~7.8.0',
          tslib: '^2.3.0',
          'zone.js': '~0.12.0'
        },
        devDependencies: {
          '@angular-devkit/build-angular': '^15.2.4',
          '@angular/cli': '~15.2.4',
          '@angular/compiler-cli': '^15.2.0',
          '@types/jasmine': '~4.3.0',
          '@types/node': '^12.11.1',
          jasmine: '~4.5.0',
          'karma': '~6.4.0',
          'karma-chrome-launcher': '~3.1.0',
          'karma-coverage': '~2.2.0',
          'karma-jasmine': '~5.1.0',
          'karma-jasmine-html-reporter': '~2.0.0',
          typescript: '~4.9.4'
        }
      }), null, 2);
    default:
      return JSON.stringify(basePackage, null, 2);
  }
};

/**
 * Generate README.md file
 */
var generateReadme = function generateReadme(framework) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$projectName = options.projectName,
    projectName = _options$projectName === void 0 ? 'Generated App' : _options$projectName,
    _options$typescript2 = options.typescript,
    typescript = _options$typescript2 === void 0 ? false : _options$typescript2;
  return "# ".concat(projectName, "\n\nThis project was generated using App Builder.\n\n## Framework: ").concat(framework).concat(typescript ? ' with TypeScript' : '', "\n\n## Getting Started\n\n### Prerequisites\n\n- Node.js (version 14 or higher)\n- npm, yarn, or pnpm\n\n### Installation\n\n```bash\n# Install dependencies\nnpm install\n\n# Or with yarn\nyarn install\n\n# Or with pnpm\npnpm install\n```\n\n### Development\n\n```bash\n# Start development server\nnpm start\n\n# Or with yarn\nyarn start\n\n# Or with pnpm\npnpm start\n```\n\n### Building for Production\n\n```bash\n# Build for production\nnpm run build\n\n# Or with yarn\nyarn build\n\n# Or with pnpm\npnpm build\n```\n\n## Project Structure\n\n```\nsrc/\n\u251C\u2500\u2500 components/     # Reusable components\n\u251C\u2500\u2500 pages/         # Page components\n\u251C\u2500\u2500 styles/        # Stylesheets\n\u251C\u2500\u2500 utils/         # Utility functions\n\u2514\u2500\u2500 App.").concat(typescript ? 'tsx' : 'jsx', "        # Main application component\n```\n\n## Features\n\n- \u2705 Modern ").concat(framework, " application\n- \u2705 Responsive design\n- \u2705 Accessibility features\n").concat(typescript ? '- ✅ TypeScript support' : '', "\n- \u2705 ESLint configuration\n- \u2705 Production-ready build\n\n## Generated by App Builder\n\nThis application was automatically generated by App Builder. You can customize and extend it according to your needs.\n\n## Learn More\n\n- [").concat(framework, " Documentation](").concat(getFrameworkDocsUrl(framework), ")\n- [App Builder Documentation](https://app-builder.example.com/docs)\n\n## License\n\nMIT\n");
};

/**
 * Generate TypeScript configuration
 */
var generateTSConfig = function generateTSConfig() {
  return JSON.stringify({
    compilerOptions: {
      target: 'es5',
      lib: ['dom', 'dom.iterable', 'es6'],
      allowJs: true,
      skipLibCheck: true,
      esModuleInterop: true,
      allowSyntheticDefaultImports: true,
      strict: true,
      forceConsistentCasingInFileNames: true,
      noFallthroughCasesInSwitch: true,
      module: 'esnext',
      moduleResolution: 'node',
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: 'react-jsx'
    },
    include: ['src'],
    exclude: ['node_modules']
  }, null, 2);
};

/**
 * Generate ESLint configuration
 */
var generateESLintConfig = function generateESLintConfig(framework) {
  var typescript = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  var baseConfig = {
    env: {
      browser: true,
      es2021: true,
      node: true
    },
    "extends": ['eslint:recommended'],
    parserOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module'
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-console': 'warn',
      'prefer-const': 'error'
    }
  };
  if (framework === 'react') {
    baseConfig["extends"].push('plugin:react/recommended', 'plugin:react-hooks/recommended');
    baseConfig.plugins = ['react', 'react-hooks'];
    baseConfig.parserOptions.ecmaFeatures = {
      jsx: true
    };
    baseConfig.settings = {
      react: {
        version: 'detect'
      }
    };
  }
  if (typescript) {
    baseConfig["extends"].push('@typescript-eslint/recommended');
    baseConfig.parser = '@typescript-eslint/parser';
    baseConfig.plugins = [].concat((0,toConsumableArray/* default */.A)(baseConfig.plugins || []), ['@typescript-eslint']);
  }
  return JSON.stringify(baseConfig, null, 2);
};

/**
 * Generate Dockerfile
 */
var generateDockerfile = function generateDockerfile(framework) {
  return "# Use official Node.js runtime as base image\nFROM node:18-alpine\n\n# Set working directory\nWORKDIR /app\n\n# Copy package files\nCOPY package*.json ./\n\n# Install dependencies\nRUN npm ci --only=production\n\n# Copy source code\nCOPY . .\n\n# Build the application\nRUN npm run build\n\n# Expose port\nEXPOSE 3000\n\n# Start the application\nCMD [\"npm\", \"start\"]\n";
};

/**
 * Generate Docker Compose file
 */
var generateDockerCompose = function generateDockerCompose() {
  return "version: '3.8'\n\nservices:\n  app:\n    build: .\n    ports:\n      - \"3000:3000\"\n    environment:\n      - NODE_ENV=production\n    volumes:\n      - .:/app\n      - /app/node_modules\n    restart: unless-stopped\n\n  nginx:\n    image: nginx:alpine\n    ports:\n      - \"80:80\"\n    volumes:\n      - ./nginx.conf:/etc/nginx/nginx.conf\n    depends_on:\n      - app\n    restart: unless-stopped\n";
};

/**
 * Generate GitHub Actions workflow
 */
var generateGitHubActions = function generateGitHubActions(framework) {
  return "name: CI/CD Pipeline\n\non:\n  push:\n    branches: [ main, develop ]\n  pull_request:\n    branches: [ main ]\n\njobs:\n  test:\n    runs-on: ubuntu-latest\n    \n    strategy:\n      matrix:\n        node-version: [16.x, 18.x]\n    \n    steps:\n    - uses: actions/checkout@v3\n    \n    - name: Use Node.js ${{ matrix.node-version }}\n      uses: actions/setup-node@v3\n      with:\n        node-version: ${{ matrix.node-version }}\n        cache: 'npm'\n    \n    - name: Install dependencies\n      run: npm ci\n    \n    - name: Run tests\n      run: npm test\n    \n    - name: Build application\n      run: npm run build\n\n  deploy:\n    needs: test\n    runs-on: ubuntu-latest\n    if: github.ref == 'refs/heads/main'\n    \n    steps:\n    - uses: actions/checkout@v3\n    \n    - name: Setup Node.js\n      uses: actions/setup-node@v3\n      with:\n        node-version: '18.x'\n        cache: 'npm'\n    \n    - name: Install dependencies\n      run: npm ci\n    \n    - name: Build application\n      run: npm run build\n    \n    - name: Deploy to production\n      run: echo \"Deploy to your hosting platform\"\n";
};

/**
 * Generate Vite configuration
 */
var generateViteConfig = function generateViteConfig(framework) {
  var typescript = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  var ext = typescript ? 'ts' : 'js';
  return "import { defineConfig } from 'vite'\n".concat(framework === 'react' ? "import react from '@vitejs/plugin-react'" : '', "\n").concat(framework === 'vue' ? "import vue from '@vitejs/plugin-vue'" : '', "\n\nexport default defineConfig({\n  plugins: [").concat(framework === 'react' ? 'react()' : framework === 'vue' ? 'vue()' : '', "],\n  server: {\n    port: 3000,\n    open: true\n  },\n  build: {\n    outDir: 'dist',\n    sourcemap: true\n  }\n})\n");
};

// Helper functions
var getFrameworkDocsUrl = function getFrameworkDocsUrl(framework) {
  var urls = {
    'React': 'https://reactjs.org/docs',
    'Vue': 'https://vuejs.org/guide/',
    'Angular': 'https://angular.io/docs',
    'Svelte': 'https://svelte.dev/docs',
    'Next.js': 'https://nextjs.org/docs'
  };
  return urls[framework] || 'https://developer.mozilla.org/';
};
;// ./src/utils/code_generator.js



function code_generator_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function code_generator_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? code_generator_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : code_generator_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Enhanced code generation utilities for App Builder
 * Supports multiple frameworks, TypeScript, accessibility, and modern best practices
 */




/**
 * Generate code based on the app structure
 * @param {Object} appData - The app data structure
 * @param {string} format - The format to generate (react, html, css, etc.)
 * @param {Object} options - Generation options
 * @returns {string|Object} - The generated code or project structure
 */
var generateCode = function generateCode(appData) {
  var format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'react';
  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  var defaultOptions = code_generator_objectSpread({
    typescript: false,
    includeAccessibility: true,
    includeTests: false,
    includeStorybook: false,
    styleFramework: 'styled-components',
    // 'styled-components', 'emotion', 'tailwind', 'css-modules'
    stateManagement: 'useState',
    // 'useState', 'redux', 'zustand', 'context'
    projectStructure: 'single-file',
    // 'single-file', 'multi-file', 'full-project'
    bundler: 'vite',
    // 'vite', 'webpack', 'parcel'
    packageManager: 'npm'
  }, options);
  switch (format) {
    case 'react':
      return generateReactCode(appData, defaultOptions);
    case 'react-ts':
      return generateReactCode(appData, code_generator_objectSpread(code_generator_objectSpread({}, defaultOptions), {}, {
        typescript: true
      }));
    case 'vue':
      return generateVueCode(appData, defaultOptions);
    case 'vue-ts':
      return generateVueCode(appData, code_generator_objectSpread(code_generator_objectSpread({}, defaultOptions), {}, {
        typescript: true
      }));
    case 'angular':
      return generateAngularCode(appData, defaultOptions);
    case 'svelte':
      return generateSvelteCode(appData, defaultOptions);
    case 'next':
      return generateNextJSCode(appData, defaultOptions);
    case 'nuxt':
      return generateNuxtCode(appData, defaultOptions);
    case 'html':
      return generateHtmlCode(appData, defaultOptions);
    case 'css':
      return generateCssCode(appData, defaultOptions);
    case 'react-native':
      return generateReactNativeCode(appData, defaultOptions);
    case 'flutter':
      return generateFlutterCode(appData, defaultOptions);
    case 'ionic':
      return generateIonicCode(appData, defaultOptions);
    default:
      return JSON.stringify(appData, null, 2);
  }
};

/**
 * Generate enhanced React code with modern best practices
 * @param {Object} appData - The app data structure
 * @param {Object} options - Generation options
 * @returns {string|Object} - The generated React code or project structure
 */
var generateReactCode = function generateReactCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility,
    projectStructure = options.projectStructure,
    styleFramework = options.styleFramework,
    stateManagement = options.stateManagement;
  if (projectStructure === 'full-project') {
    return generateReactProject(appData, options);
  }

  // Generate imports
  var imports = generateReactImports(components, options);

  // Generate TypeScript interfaces if needed
  var interfaces = '';
  if (typescript) {
    interfaces = generateTypeScriptInterfaces(components, layouts);
  }

  // Generate styled components or CSS
  var stylesCode = generateReactStyles(styles, components, layouts, options);

  // Generate component definitions
  var componentDefinitions = generateReactComponents(components, options);

  // Generate main App component
  var appComponent = generateReactAppComponent(components, layouts, data, options);

  // Combine all parts
  var fileExtension = typescript ? 'tsx' : 'jsx';
  var code = "".concat(imports, "\n").concat(interfaces, "\n").concat(stylesCode, "\n").concat(componentDefinitions, "\n").concat(appComponent);
  if (projectStructure === 'single-file') {
    return code;
  } else {
    return code_generator_objectSpread((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)({}, "App.".concat(fileExtension), code), 'package.json', project_generators_generatePackageJson('react-app', options)), 'README.md', generateReadme('React', options)), typescript && {
      'tsconfig.json': generateTSConfig()
    });
  }
};

/**
 * Generate React imports based on components and options
 */
var generateReactImports = function generateReactImports(components, options) {
  var typescript = options.typescript,
    styleFramework = options.styleFramework,
    stateManagement = options.stateManagement;
  var imports = "import React".concat(stateManagement === 'useState' ? ', { useState, useEffect }' : '', " from 'react';\n");

  // Style framework imports
  if (styleFramework === 'styled-components') {
    imports += "import styled from 'styled-components';\n";
  } else if (styleFramework === 'emotion') {
    imports += "import styled from '@emotion/styled';\n";
  }

  // State management imports
  if (stateManagement === 'redux') {
    imports += "import { useSelector, useDispatch } from 'react-redux';\n";
  } else if (stateManagement === 'zustand') {
    imports += "import { useStore } from './store';\n";
  }

  // PropTypes for non-TypeScript projects
  if (!typescript) {
    imports += "import PropTypes from 'prop-types';\n";
  }
  return imports + '\n';
};

/**
 * Generate TypeScript interfaces
 */
var generateTypeScriptInterfaces = function generateTypeScriptInterfaces(components, layouts) {
  var interfaces = '// TypeScript Interfaces\n';

  // Component props interfaces
  var componentTypes = (0,toConsumableArray/* default */.A)(new Set(components.map(function (c) {
    return c.type;
  })));
  componentTypes.forEach(function (type) {
    var component = components.find(function (c) {
      return c.type === type;
    });
    if (component && component.props) {
      interfaces += "interface ".concat(type, "Props {\n");
      Object.entries(component.props).forEach(function (_ref) {
        var _ref2 = (0,slicedToArray/* default */.A)(_ref, 2),
          prop = _ref2[0],
          value = _ref2[1];
        var propType = typeof value === 'string' ? 'string' : typeof value === 'number' ? 'number' : typeof value === 'boolean' ? 'boolean' : 'any';
        interfaces += "  ".concat(prop, "?: ").concat(propType, ";\n");
      });
      interfaces += "  className?: string;\n";
      interfaces += "  children?: React.ReactNode;\n";
      interfaces += "}\n\n";
    }
  });

  // App data interface
  interfaces += "interface AppData {\n";
  interfaces += "  components: ComponentData[];\n";
  interfaces += "  layouts: LayoutData[];\n";
  interfaces += "}\n\n";
  return interfaces;
};

/**
 * Generate React styles based on framework choice
 */
var generateReactStyles = function generateReactStyles(styles, components, layouts, options) {
  var styleFramework = options.styleFramework;
  if (styleFramework === 'styled-components' || styleFramework === 'emotion') {
    return generateStyledComponents(styles, components, layouts);
  } else if (styleFramework === 'tailwind') {
    return '// Tailwind CSS classes will be used inline\n\n';
  } else {
    return generateCSSModules(styles);
  }
};

/**
 * Generate styled-components
 */
var generateStyledComponents = function generateStyledComponents(styles, components, layouts) {
  var styledCode = '// Styled Components\n';

  // Container styles
  styledCode += "const AppContainer = styled.div`\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n`;\n\n";

  // Layout styles
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'Layout';
    styledCode += "const ".concat(code_generator_pascalCase(layoutName), "Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  padding: 1rem;\n  ").concat(layout.styles ? Object.entries(layout.styles).map(function (_ref3) {
      var _ref4 = (0,slicedToArray/* default */.A)(_ref3, 2),
        prop = _ref4[0],
        value = _ref4[1];
      return "".concat(kebabToCamelCase(prop), ": ").concat(value, ";");
    }).join('\n  ') : '', "\n`;\n\n");
  });

  // Component styles
  var componentTypes = (0,toConsumableArray/* default */.A)(new Set(components.map(function (c) {
    return c.type;
  })));
  componentTypes.forEach(function (type) {
    styledCode += "const Styled".concat(type, " = styled.div`\n  ").concat(generateComponentStyles(type), "\n`;\n\n");
  });
  return styledCode;
};

/**
 * Generate HTML code
 * @param {Object} appData - The app data structure
 * @param {Object} options - Generation options
 * @returns {string} - The generated HTML code
 */
var generateHtmlCode = function generateHtmlCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles;

  // Generate CSS
  var css = '';
  Object.entries(styles).forEach(function (_ref5) {
    var _ref6 = (0,slicedToArray/* default */.A)(_ref5, 2),
      selector = _ref6[0],
      style = _ref6[1];
    css += "".concat(selector, " {\n");
    Object.entries(style).forEach(function (_ref7) {
      var _ref8 = (0,slicedToArray/* default */.A)(_ref7, 2),
        prop = _ref8[0],
        value = _ref8[1];
      css += "  ".concat(prop, ": ").concat(value, ";\n");
    });
    css += '}\n\n';
  });

  // Generate HTML
  var html = '';
  layouts.forEach(function (layout) {
    html += "<div class=\"".concat(layout.type, "\">\n");
    layout.components.forEach(function (componentId) {
      var component = components.find(function (c) {
        return c.id === componentId;
      });
      if (component) {
        switch (component.type) {
          case 'Button':
            html += "  <button class=\"".concat(component.props.className || '', "\">").concat(component.props.text || 'Button', "</button>\n");
            break;
          case 'Input':
            html += "  <input type=\"".concat(component.props.type || 'text', "\" placeholder=\"").concat(component.props.placeholder || '', "\" class=\"").concat(component.props.className || '', "\" />\n");
            break;
          case 'Text':
            html += "  <p class=\"".concat(component.props.className || '', "\">").concat(component.props.content || '', "</p>\n");
            break;
          case 'Image':
            html += "  <img src=\"".concat(component.props.src || '', "\" alt=\"").concat(component.props.alt || '', "\" class=\"").concat(component.props.className || '', "\" />\n");
            break;
          default:
            html += "  <div class=\"".concat(component.type.toLowerCase(), "\">").concat(component.props.content || component.type, "</div>\n");
        }
      }
    });
    html += '</div>\n';
  });
  return "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Generated App</title>\n  <style>\n".concat(css, "\n  </style>\n</head>\n<body>\n  <div class=\"app\">\n").concat(html, "\n  </div>\n</body>\n</html>\n");
};

/**
 * Generate CSS code
 * @param {Object} appData - The app data structure
 * @returns {string} - The generated CSS code
 */
var generateCssCode = function generateCssCode(appData) {
  var styles = appData.styles;
  var css = '';
  Object.entries(styles).forEach(function (_ref9) {
    var _ref0 = (0,slicedToArray/* default */.A)(_ref9, 2),
      selector = _ref0[0],
      style = _ref0[1];
    css += "".concat(selector, " {\n");
    Object.entries(style).forEach(function (_ref1) {
      var _ref10 = (0,slicedToArray/* default */.A)(_ref1, 2),
        prop = _ref10[0],
        value = _ref10[1];
      css += "  ".concat(prop, ": ").concat(value, ";\n");
    });
    css += '}\n\n';
  });
  return css;
};

/**
 * Generate React component definitions
 */
var generateReactComponents = function generateReactComponents(components, options) {
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility;
  var componentCode = '// Component Definitions\n';
  var componentTypes = (0,toConsumableArray/* default */.A)(new Set(components.map(function (c) {
    return c.type;
  })));
  componentTypes.forEach(function (type) {
    var component = components.find(function (c) {
      return c.type === type;
    });
    var propsType = typescript ? "".concat(type, "Props") : '';
    componentCode += "const ".concat(type, " = (").concat(typescript ? "props: ".concat(propsType) : 'props', ") => {\n");
    componentCode += "  const { ".concat(Object.keys(component.props || {}).join(', '), ", className, children, ...rest } = props;\n\n");
    componentCode += "  return (\n";
    componentCode += "    <Styled".concat(type, "\n");
    componentCode += "      className={className}\n";
    if (includeAccessibility) {
      componentCode += "      role=\"".concat(getAriaRole(type), "\"\n");
      if (type === 'Button') {
        componentCode += "      aria-label={props['aria-label'] || props.text || 'Button'}\n";
      }
    }
    componentCode += "      {...rest}\n";
    componentCode += "    >\n";
    componentCode += "      ".concat(generateComponentContent(type, component.props), "\n");
    componentCode += "      {children}\n";
    componentCode += "    </Styled".concat(type, ">\n");
    componentCode += "  );\n";
    componentCode += "};\n\n";

    // Add PropTypes for non-TypeScript projects
    if (!typescript) {
      componentCode += "".concat(type, ".propTypes = {\n");
      Object.entries(component.props || {}).forEach(function (_ref11) {
        var _ref12 = (0,slicedToArray/* default */.A)(_ref11, 2),
          prop = _ref12[0],
          value = _ref12[1];
        var propType = typeof value === 'string' ? 'PropTypes.string' : typeof value === 'number' ? 'PropTypes.number' : typeof value === 'boolean' ? 'PropTypes.bool' : 'PropTypes.any';
        componentCode += "  ".concat(prop, ": ").concat(propType, ",\n");
      });
      componentCode += "  className: PropTypes.string,\n";
      componentCode += "  children: PropTypes.node\n";
      componentCode += "};\n\n";
    }
  });
  return componentCode;
};

/**
 * Generate React App component
 */
var generateReactAppComponent = function generateReactAppComponent(components, layouts, data, options) {
  var typescript = options.typescript,
    stateManagement = options.stateManagement,
    includeAccessibility = options.includeAccessibility;
  var appCode = '// Main App Component\n';

  // State management setup
  if (stateManagement === 'useState' && data && Object.keys(data).length > 0) {
    appCode += "const App = () => {\n";
    Object.entries(data).forEach(function (_ref13) {
      var _ref14 = (0,slicedToArray/* default */.A)(_ref13, 2),
        key = _ref14[0],
        value = _ref14[1];
      appCode += "  const [".concat(key, ", set").concat(code_generator_pascalCase(key), "] = useState(").concat(JSON.stringify(value), ");\n");
    });
    appCode += '\n';
  } else {
    appCode += "const App = () => {\n";
  }
  appCode += "  return (\n";
  appCode += "    <AppContainer".concat(includeAccessibility ? ' role="main"' : '', ">\n");

  // Generate layout structure
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'Layout';
    appCode += "      <".concat(code_generator_pascalCase(layoutName), "Container>\n");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          appCode += "        <".concat(component.type);
          Object.entries(component.props || {}).forEach(function (_ref15) {
            var _ref16 = (0,slicedToArray/* default */.A)(_ref15, 2),
              prop = _ref16[0],
              value = _ref16[1];
            if (typeof value === 'string') {
              appCode += "\n          ".concat(prop, "=\"").concat(value, "\"");
            } else {
              appCode += "\n          ".concat(prop, "={").concat(JSON.stringify(value), "}");
            }
          });
          appCode += "\n        />\n";
        }
      });
    } else {
      appCode += "        {/* Add components here */}\n";
    }
    appCode += "      </".concat(code_generator_pascalCase(layoutName), "Container>\n");
  });
  appCode += "    </AppContainer>\n";
  appCode += "  );\n";
  appCode += "};\n\n";
  appCode += "export default App;\n";
  return appCode;
};

/**
 * Utility functions
 */

/**
 * Convert kebab-case to camelCase
 * @param {string} str - The string to convert
 * @returns {string} - The camelCase string
 */
var camelCase = function camelCase(str) {
  return str.replace(/-([a-z])/g, function (g) {
    return g[1].toUpperCase();
  });
};

/**
 * Convert string to PascalCase
 */
var code_generator_pascalCase = function pascalCase(str) {
  return str.replace(/(?:^|[\s-_])(\w)/g, function (match, letter) {
    return letter.toUpperCase();
  }).replace(/[\s-_]/g, '');
};

/**
 * Convert kebab-case to camelCase for CSS properties
 */
var kebabToCamelCase = function kebabToCamelCase(str) {
  return str.replace(/-([a-z])/g, function (g) {
    return g[1].toUpperCase();
  });
};

/**
 * Get appropriate ARIA role for component type
 */
var getAriaRole = function getAriaRole(type) {
  var roleMap = {
    'Button': 'button',
    'Input': 'textbox',
    'Text': 'text',
    'Image': 'img',
    'Header': 'banner',
    'Section': 'region',
    'Card': 'article',
    'List': 'list'
  };
  return roleMap[type] || 'generic';
};

/**
 * Generate component content based on type
 */
var generateComponentContent = function generateComponentContent(type, props) {
  switch (type) {
    case 'Button':
      return (props === null || props === void 0 ? void 0 : props.text) || 'Button';
    case 'Text':
      return (props === null || props === void 0 ? void 0 : props.content) || 'Text content';
    case 'Input':
      return '';
    // Self-closing
    case 'Image':
      return '';
    // Self-closing
    default:
      return (props === null || props === void 0 ? void 0 : props.content) || type;
  }
};

/**
 * Generate component styles based on type
 */
var generateComponentStyles = function generateComponentStyles(type) {
  var styleMap = {
    'Button': "\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 0.25rem;\n  background-color: #007bff;\n  color: white;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #0056b3;\n  }\n\n  &:focus {\n    outline: 2px solid #007bff;\n    outline-offset: 2px;\n  }",
    'Input': "\n  padding: 0.5rem;\n  border: 1px solid #ccc;\n  border-radius: 0.25rem;\n  font-size: 1rem;\n\n  &:focus {\n    outline: 2px solid #007bff;\n    border-color: #007bff;\n  }",
    'Text': "\n  margin: 0;\n  line-height: 1.5;",
    'Image': "\n  max-width: 100%;\n  height: auto;",
    'Card': "\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 0.5rem;\n  background-color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);"
  };
  return styleMap[type] || "\n  /* Add styles for ".concat(type, " */");
};

/**
 * Generate CSS modules styles
 */
var generateCSSModules = function generateCSSModules(styles) {
  var css = '';
  Object.entries(styles).forEach(function (_ref17) {
    var _ref18 = (0,slicedToArray/* default */.A)(_ref17, 2),
      selector = _ref18[0],
      style = _ref18[1];
    css += "".concat(selector, " {\n");
    Object.entries(style).forEach(function (_ref19) {
      var _ref20 = (0,slicedToArray/* default */.A)(_ref19, 2),
        prop = _ref20[0],
        value = _ref20[1];
      css += "  ".concat(prop, ": ").concat(value, ";\n");
    });
    css += '}\n\n';
  });
  return css;
};

/**
 * Generate full React project structure
 */
var generateReactProject = function generateReactProject(appData, options) {
  var typescript = options.typescript,
    _options$bundler = options.bundler,
    bundler = _options$bundler === void 0 ? 'vite' : _options$bundler;
  var fileExtension = typescript ? 'tsx' : 'jsx';
  var projectStructure = (0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)((0,defineProperty/* default */.A)({
    'package.json': project_generators_generatePackageJson('react-app', options),
    'README.md': generateReadme('React', options)
  }, "src/App.".concat(fileExtension), generateReactCode(appData, code_generator_objectSpread(code_generator_objectSpread({}, options), {}, {
    projectStructure: 'single-file'
  }))), "src/index.".concat(fileExtension), generateReactIndex(typescript)), 'src/index.css', generateGlobalStyles()), 'public/index.html', generateIndexHTML()), '.gitignore', generateGitIgnore()), '.eslintrc.json', generateESLintConfig('react', typescript)), 'Dockerfile', generateDockerfile('react')), 'docker-compose.yml', generateDockerCompose()), '.github/workflows/ci.yml', generateGitHubActions('react'));
  if (typescript) {
    projectStructure['tsconfig.json'] = generateTSConfig();
  }
  if (bundler === 'vite') {
    projectStructure['vite.config.js'] = generateViteConfig('react', typescript);
  }
  return projectStructure;
};

/**
 * Generate React index file
 */
var generateReactIndex = function generateReactIndex(typescript) {
  return "import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root')".concat(typescript ? ' as HTMLElement' : '', "\n);\n\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);");
};

/**
 * Generate global styles
 */
var generateGlobalStyles = function generateGlobalStyles() {
  return "body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\nbutton {\n  cursor: pointer;\n}\n\ninput, textarea, select {\n  font-family: inherit;\n}\n\nimg {\n  max-width: 100%;\n  height: auto;\n}";
};

/**
 * Generate index.html
 */
var generateIndexHTML = function generateIndexHTML() {
  return "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\" />\n    <link rel=\"icon\" href=\"%PUBLIC_URL%/favicon.ico\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n    <meta name=\"theme-color\" content=\"#000000\" />\n    <meta name=\"description\" content=\"Generated by App Builder\" />\n    <link rel=\"apple-touch-icon\" href=\"%PUBLIC_URL%/logo192.png\" />\n    <link rel=\"manifest\" href=\"%PUBLIC_URL%/manifest.json\" />\n    <title>Generated App</title>\n  </head>\n  <body>\n    <noscript>You need to enable JavaScript to run this app.</noscript>\n    <div id=\"root\"></div>\n  </body>\n</html>";
};

/**
 * Generate .gitignore
 */
var generateGitIgnore = function generateGitIgnore() {
  return "# Dependencies\nnode_modules/\n/.pnp\n.pnp.js\n\n# Testing\n/coverage\n\n# Production\n/build\n/dist\n\n# Environment variables\n.env\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\n# Logs\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\nlerna-debug.log*\n\n# Runtime data\npids\n*.pid\n*.seed\n*.pid.lock\n\n# IDE\n.vscode/\n.idea/\n*.swp\n*.swo\n\n# OS\n.DS_Store\nThumbs.db\n\n# Temporary folders\ntmp/\ntemp/";
};
;// ./src/components/enhanced/EnhancedCodeExporter.js





var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;
function EnhancedCodeExporter_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function EnhancedCodeExporter_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? EnhancedCodeExporter_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : EnhancedCodeExporter_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }








var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;
var TabPane = es/* Tabs */.tU.TabPane;
var Option = es/* Select */.l6.Option;
var Panel = es/* Collapse */.SD.Panel;
var ExporterContainer = design_system.styled.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: ", ";\n  max-width: 1200px;\n  margin: 0 auto;\n"])), theme/* default.spacing */.Ay.spacing[4]);
var PreviewContainer = design_system.styled.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  background-color: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  padding: ", ";\n  margin: ", " 0;\n  max-height: 600px;\n  overflow: auto;\n"])), theme/* default.colors */.Ay.colors.neutral[50], theme/* default.colors */.Ay.colors.neutral[300], theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.spacing */.Ay.spacing[3], theme/* default.spacing */.Ay.spacing[3]);
var CodePreview = design_system.styled.pre(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  background-color: ", ";\n  color: ", ";\n  border-radius: ", ";\n  padding: ", ";\n  overflow: auto;\n  max-height: 500px;\n  font-family: ", ";\n  font-size: ", ";\n  line-height: 1.5;\n  margin: 0;\n"])), theme/* default.colors */.Ay.colors.neutral[900], theme/* default.colors */.Ay.colors.neutral[100], theme/* default.borderRadius */.Ay.borderRadius.md, theme/* default.spacing */.Ay.spacing[3], theme/* default.typography */.Ay.typography.fontFamily.code, theme/* default.typography */.Ay.typography.fontSize.sm);
var OptionsGrid = design_system.styled.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: ", ";\n  margin: ", " 0;\n"])), theme/* default.spacing */.Ay.spacing[4], theme/* default.spacing */.Ay.spacing[4]);
var OptionCard = (0,design_system.styled)(es/* Card */.Zp)(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  .ant-card-body {\n    padding: ", ";\n  }\n"])), theme/* default.spacing */.Ay.spacing[3]);
var ExportHistoryItem = design_system.styled.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  margin-bottom: ", ";\n"])), theme/* default.spacing */.Ay.spacing[2], theme/* default.colors */.Ay.colors.neutral[200], theme/* default.borderRadius */.Ay.borderRadius.sm, theme/* default.spacing */.Ay.spacing[2]);

/**
 * Enhanced Code Exporter Component
 * Provides comprehensive export functionality with multiple frameworks, preview, and advanced options
 */
var EnhancedCodeExporter = function EnhancedCodeExporter() {
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var components = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$app;
    return (state === null || state === void 0 || (_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || [];
  });
  var layouts = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$app2;
    return (state === null || state === void 0 || (_state$app2 = state.app) === null || _state$app2 === void 0 ? void 0 : _state$app2.layouts) || [];
  });
  var activeTheme = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$themes;
    return (state === null || state === void 0 || (_state$themes = state.themes) === null || _state$themes === void 0 ? void 0 : _state$themes.activeTheme) || 'default';
  });
  var themes = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$themes2;
    return (state === null || state === void 0 || (_state$themes2 = state.themes) === null || _state$themes2 === void 0 ? void 0 : _state$themes2.themes) || [];
  });
  var websocketConnected = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$websocket;
    return (state === null || state === void 0 || (_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
  });
  var templates = (0,react_redux/* useSelector */.d4)(function (state) {
    return (state === null || state === void 0 ? void 0 : state.templates) || {};
  });

  // Export configuration state
  var _useState = (0,react.useState)('react'),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    exportFormat = _useState2[0],
    setExportFormat = _useState2[1];
  var _useState3 = (0,react.useState)({
      typescript: false,
      includeAccessibility: true,
      includeTests: false,
      includeStorybook: false,
      styleFramework: 'styled-components',
      stateManagement: 'useState',
      projectStructure: 'single-file',
      bundler: 'vite',
      packageManager: 'npm',
      includeDocker: false,
      includeCiCd: false
    }),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    exportOptions = _useState4[0],
    setExportOptions = _useState4[1];

  // UI state
  var _useState5 = (0,react.useState)('configure'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    activeTab = _useState6[0],
    setActiveTab = _useState6[1];
  var _useState7 = (0,react.useState)(''),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    codePreview = _useState8[0],
    setCodePreview = _useState8[1];
  var _useState9 = (0,react.useState)(false),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    previewLoading = _useState0[0],
    setPreviewLoading = _useState0[1];
  var _useState1 = (0,react.useState)(false),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    exportLoading = _useState10[0],
    setExportLoading = _useState10[1];
  var _useState11 = (0,react.useState)([]),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    exportHistory = _useState12[0],
    setExportHistory = _useState12[1];
  var _useState13 = (0,react.useState)([]),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    selectedComponents = _useState14[0],
    setSelectedComponents = _useState14[1];
  var _useState15 = (0,react.useState)([]),
    _useState16 = (0,slicedToArray/* default */.A)(_useState15, 2),
    selectedLayouts = _useState16[0],
    setSelectedLayouts = _useState16[1];
  var _useState17 = (0,react.useState)(false),
    _useState18 = (0,slicedToArray/* default */.A)(_useState17, 2),
    showAdvancedOptions = _useState18[0],
    setShowAdvancedOptions = _useState18[1];
  var _useState19 = (0,react.useState)(0),
    _useState20 = (0,slicedToArray/* default */.A)(_useState19, 2),
    exportProgress = _useState20[0],
    setExportProgress = _useState20[1];
  var _useState21 = (0,react.useState)(false),
    _useState22 = (0,slicedToArray/* default */.A)(_useState21, 2),
    previewModalVisible = _useState22[0],
    setPreviewModalVisible = _useState22[1];
  var _useState23 = (0,react.useState)(false),
    _useState24 = (0,slicedToArray/* default */.A)(_useState23, 2),
    exportModalVisible = _useState24[0],
    setExportModalVisible = _useState24[1];
  var _useState25 = (0,react.useState)(false),
    _useState26 = (0,slicedToArray/* default */.A)(_useState25, 2),
    batchExportMode = _useState26[0],
    setBatchExportMode = _useState26[1];
  var _useState27 = (0,react.useState)([]),
    _useState28 = (0,slicedToArray/* default */.A)(_useState27, 2),
    selectedExports = _useState28[0],
    setSelectedExports = _useState28[1];

  // Available export formats
  var exportFormats = [{
    value: 'react',
    label: 'React',
    icon: '⚛️',
    description: 'Modern React with hooks'
  }, {
    value: 'react-ts',
    label: 'React + TypeScript',
    icon: '⚛️',
    description: 'React with TypeScript support'
  }, {
    value: 'vue',
    label: 'Vue.js',
    icon: '🟢',
    description: 'Vue 3 with Composition API'
  }, {
    value: 'vue-ts',
    label: 'Vue + TypeScript',
    icon: '🟢',
    description: 'Vue 3 with TypeScript'
  }, {
    value: 'angular',
    label: 'Angular',
    icon: '🔴',
    description: 'Angular with TypeScript'
  }, {
    value: 'svelte',
    label: 'Svelte',
    icon: '🧡',
    description: 'Svelte with modern features'
  }, {
    value: 'next',
    label: 'Next.js',
    icon: '⚫',
    description: 'Next.js with SSR support'
  }, {
    value: 'nuxt',
    label: 'Nuxt.js',
    icon: '🟢',
    description: 'Nuxt.js for Vue'
  }, {
    value: 'html',
    label: 'HTML/CSS/JS',
    icon: '🌐',
    description: 'Vanilla web technologies'
  }, {
    value: 'react-native',
    label: 'React Native',
    icon: '📱',
    description: 'Mobile app with React Native'
  }, {
    value: 'flutter',
    label: 'Flutter',
    icon: '🐦',
    description: 'Cross-platform with Flutter'
  }, {
    value: 'ionic',
    label: 'Ionic',
    icon: '⚡',
    description: 'Hybrid mobile apps'
  }];

  // Style frameworks
  var styleFrameworks = [{
    value: 'styled-components',
    label: 'Styled Components',
    description: 'CSS-in-JS with styled-components'
  }, {
    value: 'emotion',
    label: 'Emotion',
    description: 'CSS-in-JS with Emotion'
  }, {
    value: 'tailwind',
    label: 'Tailwind CSS',
    description: 'Utility-first CSS framework'
  }, {
    value: 'css-modules',
    label: 'CSS Modules',
    description: 'Scoped CSS with modules'
  }, {
    value: 'material-ui',
    label: 'Material-UI',
    description: 'React Material Design components'
  }, {
    value: 'chakra-ui',
    label: 'Chakra UI',
    description: 'Simple and modular React components'
  }, {
    value: 'bootstrap',
    label: 'Bootstrap',
    description: 'Popular CSS framework'
  }];

  // State management options
  var stateManagementOptions = [{
    value: 'useState',
    label: 'React Hooks (useState)',
    description: 'Built-in React state management'
  }, {
    value: 'redux',
    label: 'Redux Toolkit',
    description: 'Predictable state container'
  }, {
    value: 'zustand',
    label: 'Zustand',
    description: 'Lightweight state management'
  }, {
    value: 'context',
    label: 'React Context',
    description: 'Built-in context API'
  }];

  // Project structure options
  var projectStructures = [{
    value: 'single-file',
    label: 'Single File',
    description: 'All code in one file'
  }, {
    value: 'multi-file',
    label: 'Multiple Files',
    description: 'Organized file structure'
  }, {
    value: 'full-project',
    label: 'Full Project',
    description: 'Complete project with configs'
  }];

  // Initialize selected components and layouts
  (0,react.useEffect)(function () {
    if (components.length > 0 && selectedComponents.length === 0) {
      setSelectedComponents(components.map(function (c) {
        return c.id;
      }));
    }
    if (layouts.length > 0 && selectedLayouts.length === 0) {
      setSelectedLayouts(layouts.map(function (l) {
        return l.id;
      }));
    }
  }, [components, layouts, selectedComponents.length, selectedLayouts.length]);

  // Load export history from localStorage
  (0,react.useEffect)(function () {
    var savedHistory = localStorage.getItem('exportHistory');
    if (savedHistory) {
      try {
        setExportHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Failed to load export history:', error);
      }
    }
  }, []);

  // Generate code preview
  var generatePreview = (0,react.useCallback)(/*#__PURE__*/(0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
    var appData, code;
    return regenerator_default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setPreviewLoading(true);
          try {
            appData = {
              components: components.filter(function (c) {
                return selectedComponents.includes(c.id);
              }),
              layouts: layouts.filter(function (l) {
                return selectedLayouts.includes(l.id);
              }),
              styles: {},
              data: {}
            };
            code = generateCode(appData, exportFormat, exportOptions);
            setCodePreview(typeof code === 'string' ? code : JSON.stringify(code, null, 2));
          } catch (error) {
            console.error('Preview generation failed:', error);
            es/* message */.iU.error('Failed to generate preview');
            setCodePreview('// Preview generation failed');
          } finally {
            setPreviewLoading(false);
          }
        case 1:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [components, layouts, selectedComponents, selectedLayouts, exportFormat, exportOptions]);

  // Auto-generate preview when options change
  (0,react.useEffect)(function () {
    if (activeTab === 'preview') {
      generatePreview();
    }
  }, [activeTab, generatePreview]);

  // WebSocket integration for real-time updates
  (0,react.useEffect)(function () {
    if (websocketConnected && activeTab === 'preview') {
      // Send export configuration to WebSocket for real-time updates
      var wsMessage = {
        type: 'export_config_update',
        data: {
          format: exportFormat,
          options: exportOptions,
          components: selectedComponents,
          layouts: selectedLayouts
        }
      };

      // This would be handled by the WebSocket service
      console.log('WebSocket export config update:', wsMessage);
    }
  }, [websocketConnected, activeTab, exportFormat, exportOptions, selectedComponents, selectedLayouts]);

  // Handle export option changes
  var handleOptionChange = function handleOptionChange(key, value) {
    setExportOptions(function (prev) {
      return EnhancedCodeExporter_objectSpread(EnhancedCodeExporter_objectSpread({}, prev), {}, (0,defineProperty/* default */.A)({}, key, value));
    });
  };

  // Handle enhanced export
  var handleEnhancedExport = /*#__PURE__*/function () {
    var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2() {
      var appData, progressInterval, response, result, exportRecord, newHistory, _exportRecord, _newHistory, _t;
      return regenerator_default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            setExportLoading(true);
            setExportProgress(0);
            _context2.prev = 1;
            appData = {
              components: components.filter(function (c) {
                return selectedComponents.includes(c.id);
              }),
              layouts: layouts.filter(function (l) {
                return selectedLayouts.includes(l.id);
              }),
              styles: {},
              data: {},
              // Include template information for enhanced export
              layout_templates: templates.layoutTemplates || [],
              app_templates: templates.appTemplates || [],
              active_theme: activeTheme,
              theme_data: themes.find(function (t) {
                return t.id === activeTheme;
              }) || {}
            }; // Simulate progress
            progressInterval = setInterval(function () {
              setExportProgress(function (prev) {
                return Math.min(prev + 10, 90);
              });
            }, 200);
            _context2.next = 2;
            return fetch('/api/enhanced-export/', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(localStorage.getItem('token'))
              },
              body: JSON.stringify({
                app_id: 1,
                // Replace with actual app ID
                format: exportFormat,
                options: exportOptions
              })
            });
          case 2:
            response = _context2.sent;
            clearInterval(progressInterval);
            setExportProgress(100);
            if (response.ok) {
              _context2.next = 3;
              break;
            }
            throw new Error('Export failed');
          case 3:
            _context2.next = 4;
            return response.json();
          case 4:
            result = _context2.sent;
            // Save to export history
            exportRecord = {
              id: Date.now(),
              format: exportFormat,
              options: exportOptions,
              timestamp: new Date().toISOString(),
              status: 'success',
              type: result.type,
              size: result.code ? result.code.length : Object.keys(result.files || {}).length
            };
            newHistory = [exportRecord].concat((0,toConsumableArray/* default */.A)(exportHistory.slice(0, 9))); // Keep last 10
            setExportHistory(newHistory);
            localStorage.setItem('exportHistory', JSON.stringify(newHistory));

            // Handle download based on result type
            if (result.type === 'single-file') {
              downloadFile(result.code, "app.".concat(getFileExtension(exportFormat)));
            } else if (result.type === 'multi-file') {
              downloadMultipleFiles(result.files);
            } else if (result.type === 'zip') {
              downloadZipFile(result.zip_data, 'app-export.zip');
            }
            es/* message */.iU.success('Export completed successfully!');
            _context2.next = 6;
            break;
          case 5:
            _context2.prev = 5;
            _t = _context2["catch"](1);
            console.error('Export failed:', _t);
            es/* message */.iU.error('Export failed. Please try again.');

            // Save failed export to history
            _exportRecord = {
              id: Date.now(),
              format: exportFormat,
              options: exportOptions,
              timestamp: new Date().toISOString(),
              status: 'failed',
              error: _t.message
            };
            _newHistory = [_exportRecord].concat((0,toConsumableArray/* default */.A)(exportHistory.slice(0, 9)));
            setExportHistory(_newHistory);
            localStorage.setItem('exportHistory', JSON.stringify(_newHistory));
          case 6:
            _context2.prev = 6;
            setExportLoading(false);
            setExportProgress(0);
            return _context2.finish(6);
          case 7:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 5, 6, 7]]);
    }));
    return function handleEnhancedExport() {
      return _ref2.apply(this, arguments);
    };
  }();

  // Download single file
  var downloadFile = function downloadFile(content, filename) {
    var blob = new Blob([content], {
      type: 'text/plain'
    });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Download multiple files as zip
  var downloadMultipleFiles = /*#__PURE__*/function () {
    var _ref3 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3(files) {
      var mainFile;
      return regenerator_default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            // This would require a zip library like JSZip
            // For now, download the main file
            mainFile = files['App.jsx'] || files['App.tsx'] || files['index.html'] || Object.values(files)[0];
            if (mainFile) {
              downloadFile(mainFile, 'App.jsx');
            }
          case 1:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function downloadMultipleFiles(_x) {
      return _ref3.apply(this, arguments);
    };
  }();

  // Download zip file
  var downloadZipFile = function downloadZipFile(base64Data, filename) {
    var binaryString = atob(base64Data);
    var bytes = new Uint8Array(binaryString.length);
    for (var i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    var blob = new Blob([bytes], {
      type: 'application/zip'
    });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Get file extension for format
  var getFileExtension = function getFileExtension(format) {
    var extensions = {
      'react': 'jsx',
      'react-ts': 'tsx',
      'vue': 'vue',
      'vue-ts': 'vue',
      'angular': 'ts',
      'svelte': 'svelte',
      'html': 'html',
      'react-native': 'jsx',
      'flutter': 'dart',
      'ionic': 'ts'
    };
    return extensions[format] || 'js';
  };

  // Copy code to clipboard
  var handleCopyCode = function handleCopyCode() {
    navigator.clipboard.writeText(codePreview).then(function () {
      es/* message */.iU.success('Code copied to clipboard');
    })["catch"](function (error) {
      console.error('Failed to copy code:', error);
      es/* message */.iU.error('Failed to copy code');
    });
  };

  // Handle template export
  var handleTemplateExport = /*#__PURE__*/function () {
    var _ref4 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4(templateId, templateType) {
      var response, result, _t2;
      return regenerator_default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            setExportLoading(true);
            setExportProgress(0);
            _context4.prev = 1;
            _context4.next = 2;
            return fetch('/api/export-template/', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(localStorage.getItem('token'))
              },
              body: JSON.stringify({
                template_id: templateId,
                template_type: templateType,
                format: exportFormat,
                options: exportOptions
              })
            });
          case 2:
            response = _context4.sent;
            if (response.ok) {
              _context4.next = 3;
              break;
            }
            throw new Error('Template export failed');
          case 3:
            _context4.next = 4;
            return response.json();
          case 4:
            result = _context4.sent;
            if (!(result.type === 'project')) {
              _context4.next = 6;
              break;
            }
            _context4.next = 5;
            return downloadMultipleFiles(result.files);
          case 5:
            es/* message */.iU.success('Template exported successfully!');
          case 6:
            _context4.next = 8;
            break;
          case 7:
            _context4.prev = 7;
            _t2 = _context4["catch"](1);
            console.error('Template export failed:', _t2);
            es/* message */.iU.error('Template export failed. Please try again.');
          case 8:
            _context4.prev = 8;
            setExportLoading(false);
            setExportProgress(0);
            return _context4.finish(8);
          case 9:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 7, 8, 9]]);
    }));
    return function handleTemplateExport(_x2, _x3) {
      return _ref4.apply(this, arguments);
    };
  }();

  // Handle batch export
  var handleBatchExport = /*#__PURE__*/function () {
    var _ref5 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee5(appIds) {
      var response, blob, url, a, _t3;
      return regenerator_default().wrap(function (_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            setExportLoading(true);
            setExportProgress(0);
            _context5.prev = 1;
            _context5.next = 2;
            return fetch('/api/batch-export/', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(localStorage.getItem('token'))
              },
              body: JSON.stringify({
                app_ids: appIds,
                format: exportFormat,
                options: exportOptions
              })
            });
          case 2:
            response = _context5.sent;
            if (response.ok) {
              _context5.next = 3;
              break;
            }
            throw new Error('Batch export failed');
          case 3:
            _context5.next = 4;
            return response.blob();
          case 4:
            blob = _context5.sent;
            url = URL.createObjectURL(blob);
            a = document.createElement('a');
            a.href = url;
            a.download = "batch-export-".concat(exportFormat, ".zip");
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            es/* message */.iU.success('Batch export completed successfully!');
            _context5.next = 6;
            break;
          case 5:
            _context5.prev = 5;
            _t3 = _context5["catch"](1);
            console.error('Batch export failed:', _t3);
            es/* message */.iU.error('Batch export failed. Please try again.');
          case 6:
            _context5.prev = 6;
            setExportLoading(false);
            setExportProgress(0);
            return _context5.finish(6);
          case 7:
          case "end":
            return _context5.stop();
        }
      }, _callee5, null, [[1, 5, 6, 7]]);
    }));
    return function handleBatchExport(_x4) {
      return _ref5.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/react.createElement(ExporterContainer, null, /*#__PURE__*/react.createElement(Title, {
    level: 2
  }, /*#__PURE__*/react.createElement(icons_es/* CodeOutlined */.C$o, null), " Enhanced Code Exporter"), /*#__PURE__*/react.createElement(Paragraph, null, "Export your application to multiple frameworks with advanced configuration options, preview functionality, and project management features."), /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    size: "large"
  }, /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null), "Configure"),
    key: "configure"
  }, /*#__PURE__*/react.createElement(OptionsGrid, null, /*#__PURE__*/react.createElement(OptionCard, {
    title: "Export Format",
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
    value: exportFormat,
    onChange: setExportFormat,
    style: {
      width: '100%'
    },
    size: "large"
  }, exportFormats.map(function (format) {
    return /*#__PURE__*/react.createElement(Option, {
      key: format.value,
      value: format.value
    }, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement("span", null, format.icon), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", null, format.label), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, format.description))));
  }))), /*#__PURE__*/react.createElement(OptionCard, {
    title: "Project Structure",
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Radio */.sx.Group, {
    value: exportOptions.projectStructure,
    onChange: function onChange(e) {
      return handleOptionChange('projectStructure', e.target.value);
    },
    style: {
      width: '100%'
    }
  }, projectStructures.map(function (structure) {
    return /*#__PURE__*/react.createElement(es/* Radio */.sx, {
      key: structure.value,
      value: structure.value,
      style: {
        display: 'block',
        marginBottom: 8
      }
    }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", null, structure.label), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, structure.description)));
  }))), /*#__PURE__*/react.createElement(OptionCard, {
    title: "Style Framework",
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
    value: exportOptions.styleFramework,
    onChange: function onChange(value) {
      return handleOptionChange('styleFramework', value);
    },
    style: {
      width: '100%'
    }
  }, styleFrameworks.map(function (framework) {
    return /*#__PURE__*/react.createElement(Option, {
      key: framework.value,
      value: framework.value
    }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", null, framework.label), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, framework.description)));
  }))), /*#__PURE__*/react.createElement(OptionCard, {
    title: "State Management",
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
    value: exportOptions.stateManagement,
    onChange: function onChange(value) {
      return handleOptionChange('stateManagement', value);
    },
    style: {
      width: '100%'
    }
  }, stateManagementOptions.map(function (option) {
    return /*#__PURE__*/react.createElement(Option, {
      key: option.value,
      value: option.value
    }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", null, option.label), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, option.description)));
  })))), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "Basic Options",
    size: "small",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(es/* Checkbox */.Sc, {
    checked: exportOptions.typescript,
    onChange: function onChange(e) {
      return handleOptionChange('typescript', e.target.checked);
    }
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement("span", null, "TypeScript Support"), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Generate TypeScript code with type definitions"
  }, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)))), /*#__PURE__*/react.createElement(es/* Checkbox */.Sc, {
    checked: exportOptions.includeAccessibility,
    onChange: function onChange(e) {
      return handleOptionChange('includeAccessibility', e.target.checked);
    }
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement("span", null, "Accessibility Features"), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Include ARIA labels, roles, and other accessibility attributes"
  }, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)))), /*#__PURE__*/react.createElement(es/* Checkbox */.Sc, {
    checked: exportOptions.includeTests,
    onChange: function onChange(e) {
      return handleOptionChange('includeTests', e.target.checked);
    }
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement("span", null, "Include Tests"), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Generate test files for components"
  }, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)))), /*#__PURE__*/react.createElement(es/* Checkbox */.Sc, {
    checked: exportOptions.includeStorybook,
    onChange: function onChange(e) {
      return handleOptionChange('includeStorybook', e.target.checked);
    }
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement("span", null, "Storybook Stories"), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
    title: "Generate Storybook stories for components"
  }, /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)))))), /*#__PURE__*/react.createElement(es/* Collapse */.SD, null, /*#__PURE__*/react.createElement(Panel, {
    header: "Advanced Options",
    key: "advanced"
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Package Manager:"), /*#__PURE__*/react.createElement(es/* Radio */.sx.Group, {
    value: exportOptions.packageManager,
    onChange: function onChange(e) {
      return handleOptionChange('packageManager', e.target.value);
    },
    style: {
      marginLeft: 16
    }
  }, /*#__PURE__*/react.createElement(es/* Radio */.sx, {
    value: "npm"
  }, "npm"), /*#__PURE__*/react.createElement(es/* Radio */.sx, {
    value: "yarn"
  }, "yarn"), /*#__PURE__*/react.createElement(es/* Radio */.sx, {
    value: "pnpm"
  }, "pnpm"))), /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Bundler:"), /*#__PURE__*/react.createElement(es/* Radio */.sx.Group, {
    value: exportOptions.bundler,
    onChange: function onChange(e) {
      return handleOptionChange('bundler', e.target.value);
    },
    style: {
      marginLeft: 16
    }
  }, /*#__PURE__*/react.createElement(es/* Radio */.sx, {
    value: "vite"
  }, "Vite"), /*#__PURE__*/react.createElement(es/* Radio */.sx, {
    value: "webpack"
  }, "Webpack"), /*#__PURE__*/react.createElement(es/* Radio */.sx, {
    value: "parcel"
  }, "Parcel"))), /*#__PURE__*/react.createElement(es/* Checkbox */.Sc, {
    checked: exportOptions.includeDocker,
    onChange: function onChange(e) {
      return handleOptionChange('includeDocker', e.target.checked);
    }
  }, "Include Docker Configuration"), /*#__PURE__*/react.createElement(es/* Checkbox */.Sc, {
    checked: exportOptions.includeCiCd,
    onChange: function onChange(e) {
      return handleOptionChange('includeCiCd', e.target.checked);
    }
  }, "Include CI/CD Pipeline")))), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    size: "large",
    icon: /*#__PURE__*/react.createElement(icons_es/* ExportOutlined */.PZg, null),
    onClick: handleEnhancedExport,
    loading: exportLoading
  }, "Export Application"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    size: "large",
    icon: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
    onClick: function onClick() {
      return setActiveTab('preview');
    }
  }, "Preview Code")), exportLoading && /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react.createElement(es/* Progress */.ke, {
    percent: exportProgress,
    status: "active"
  }), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Generating your application..."))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null), "Preview"),
    key: "preview"
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "Code Preview",
    description: "This is a preview of the generated code. Use the Configure tab to adjust export options.",
    type: "info",
    showIcon: true,
    action: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      size: "small",
      onClick: generatePreview,
      loading: previewLoading
    }, "Refresh"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
      size: "small",
      icon: /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, null),
      onClick: handleCopyCode
    }, "Copy"))
  }), /*#__PURE__*/react.createElement(PreviewContainer, null, previewLoading ? /*#__PURE__*/react.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '40px'
    }
  }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
    size: "large"
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 16
    }
  }, "Generating preview...")) : /*#__PURE__*/react.createElement(CodePreview, null, codePreview)))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* HistoryOutlined */.dUu, null), "History"),
    key: "history"
  }, /*#__PURE__*/react.createElement(es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Export History"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null),
    onClick: function onClick() {
      setExportHistory([]);
      localStorage.removeItem('exportHistory');
      es/* message */.iU.success('Export history cleared');
    }
  }, "Clear History")), exportHistory.length === 0 ? /*#__PURE__*/react.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '40px'
    }
  }, /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "No export history yet")) : /*#__PURE__*/react.createElement(es/* List */.B8, {
    dataSource: exportHistory,
    renderItem: function renderItem(item) {
      var _exportFormats$find;
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, {
        actions: [/*#__PURE__*/react.createElement(es/* Button */.$n, {
          size: "small",
          icon: /*#__PURE__*/react.createElement(icons_es/* DownloadOutlined */.jsW, null)
        }, "Re-export")]
      }, /*#__PURE__*/react.createElement(es/* List */.B8.Item.Meta, {
        title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement("span", null, ((_exportFormats$find = exportFormats.find(function (f) {
          return f.value === item.format;
        })) === null || _exportFormats$find === void 0 ? void 0 : _exportFormats$find.label) || item.format), /*#__PURE__*/react.createElement(es/* Tag */.vw, {
          color: item.status === 'success' ? 'green' : 'red'
        }, item.status)),
        description: /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", null, "Exported on ", new Date(item.timestamp).toLocaleString()), item.size && /*#__PURE__*/react.createElement("div", null, "Size: ", item.size, " characters"), item.error && /*#__PURE__*/react.createElement("div", {
          style: {
            color: 'red'
          }
        }, "Error: ", item.error))
      }));
    }
  })))));
};
/* harmony default export */ const enhanced_EnhancedCodeExporter = (EnhancedCodeExporter);

/***/ })

}]);