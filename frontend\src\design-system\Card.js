import React from 'react';
import PropTypes from 'prop-types';
import { styled } from './styled-components';
import theme from './theme';

const StyledCard = styled.div.withConfig({
  shouldForwardProp: (prop) => !['elevation', 'radius', 'fullWidth', 'fullHeight'].includes(prop),
})`
  background-color: white;
  border-radius: ${props => theme.borderRadius[props.radius]};
  box-shadow: ${props => theme.shadows[props.elevation]};
  overflow: hidden;
  width: ${props => props.fullWidth ? '100%' : 'auto'};
  height: ${props => props.fullHeight ? '100%' : 'auto'};
  display: flex;
  flex-direction: column;
`;

const CardHeader = styled.div.withConfig({
  shouldForwardProp: (prop) => !['divider'].includes(prop),
})`
  padding: ${theme.spacing[4]};
  border-bottom: ${props => props.divider ? `1px solid ${theme.colors.neutral[200]}` : 'none'};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const CardTitle = styled.h3`
  margin: 0;
  font-size: ${theme.typography.fontSize.lg};
  font-weight: ${theme.typography.fontWeight.semibold};
  color: ${theme.colors.neutral[900]};
`;

const CardContent = styled.div`
  padding: ${theme.spacing[4]};
  flex: 1;
`;

const CardFooter = styled.div.withConfig({
  shouldForwardProp: (prop) => !['divider', 'align'].includes(prop),
})`
  padding: ${theme.spacing[4]};
  border-top: ${props => props.divider ? `1px solid ${theme.colors.neutral[200]}` : 'none'};
  display: flex;
  align-items: center;
  justify-content: ${props => props.align === 'right' ? 'flex-end' : props.align === 'center' ? 'center' : 'flex-start'};
  gap: ${theme.spacing[2]};
`;

const Card = ({
  children,
  elevation = 'md',
  radius = 'md',
  fullWidth = false,
  fullHeight = false,
  ...props
}) => {
  return (
    <StyledCard
      elevation={elevation}
      radius={radius}
      fullWidth={fullWidth}
      fullHeight={fullHeight}
      {...props}
    >
      {children}
    </StyledCard>
  );
};

Card.Header = ({ children, divider = false, ...props }) => (
  <CardHeader divider={divider} {...props}>
    {children}
  </CardHeader>
);

Card.Title = ({ children, ...props }) => (
  <CardTitle {...props}>{children}</CardTitle>
);

Card.Content = ({ children, ...props }) => (
  <CardContent {...props}>{children}</CardContent>
);

Card.Footer = ({ children, divider = true, align = 'right', ...props }) => (
  <CardFooter divider={divider} align={align} {...props}>
    {children}
  </CardFooter>
);

Card.propTypes = {
  children: PropTypes.node.isRequired,
  elevation: PropTypes.oneOf(['none', 'sm', 'md', 'lg', 'xl', '2xl']),
  radius: PropTypes.oneOf(['none', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', 'full']),
  fullWidth: PropTypes.bool,
  fullHeight: PropTypes.bool
};

Card.Header.propTypes = {
  children: PropTypes.node.isRequired,
  divider: PropTypes.bool
};

Card.Title.propTypes = {
  children: PropTypes.node.isRequired
};

Card.Content.propTypes = {
  children: PropTypes.node.isRequired
};

Card.Footer.propTypes = {
  children: PropTypes.node.isRequired,
  divider: PropTypes.bool,
  align: PropTypes.oneOf(['left', 'center', 'right'])
};

export default Card;
